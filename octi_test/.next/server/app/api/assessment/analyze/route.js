"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/assessment/analyze/route";
exports.ids = ["app/api/assessment/analyze/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fanalyze%2Froute&page=%2Fapi%2Fassessment%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fanalyze%2Froute&page=%2Fapi%2Fassessment%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_assessment_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/assessment/analyze/route.ts */ \"(rsc)/./src/app/api/assessment/analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/assessment/analyze/route\",\n        pathname: \"/api/assessment/analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/assessment/analyze/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/analyze/route.ts\",\n    nextConfigOutput,\n    userland: _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_assessment_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/assessment/analyze/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fanalyze%2Froute&page=%2Fapi%2Fassessment%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/assessment/analyze/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/assessment/analyze/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_analysis_analysis_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/analysis/analysis-service */ \"(rsc)/./src/services/analysis/analysis-service.ts\");\n/**\n * OCTI智能评估系统 - 分析API路由\n * \n * 处理组织能力分析请求\n */ \n\nasync function POST(request) {\n    try {\n        console.log(\"\\uD83D\\uDCE5 收到分析请求\");\n        // 解析请求数据\n        const body = await request.json();\n        const { profile, responses, version = \"standard\" } = body;\n        // 验证请求数据\n        if (!profile || !responses) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"缺少必要的分析数据\"\n            }, {\n                status: 400\n            });\n        }\n        if (!Array.isArray(responses) || responses.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"问卷回答数据无效\"\n            }, {\n                status: 400\n            });\n        }\n        // 验证版本参数\n        if (version !== \"standard\" && version !== \"professional\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"无效的分析版本\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"\\uD83D\\uDCCA OCTI分析数据验证通过\");\n        console.log(\"\\uD83D\\uDC64 组织画像:\", profile);\n        console.log(\"\\uD83D\\uDCDD 回答数量:\", responses.length);\n        console.log(\"\\uD83C\\uDFAF 分析版本:\", version);\n        // 创建分析服务实例\n        const analysisService = new _services_analysis_analysis_service__WEBPACK_IMPORTED_MODULE_1__.AnalysisService();\n        // 生成OCTI专业分析报告\n        const analysisResult = await analysisService.generateAnalysisReport(profile, responses, version);\n        console.log(\"✅ 分析报告生成成功\");\n        // 返回分析结果\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: analysisResult\n        });\n    } catch (error) {\n        console.error(\"❌ 分析API错误:\", error);\n        // 返回错误信息\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"分析服务暂时不可用\",\n            details: error instanceof Error ? error.message : \"未知错误\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 支持OPTIONS请求（CORS预检）\nasync function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/assessment/analyze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/analysis-service.ts":
/*!***************************************************!*\
  !*** ./src/services/analysis/analysis-service.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalysisService: () => (/* binding */ AnalysisService)\n/* harmony export */ });\n/* harmony import */ var _llm_llm_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../llm/llm-client */ \"(rsc)/./src/services/llm/llm-client.ts\");\n/* harmony import */ var _prompt_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompt-loader */ \"(rsc)/./src/services/analysis/prompt-loader.ts\");\n/* harmony import */ var _profile_transformer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./profile-transformer */ \"(rsc)/./src/services/analysis/profile-transformer.ts\");\n/**\n * OCTI智能评估系统 - 分析服务\n *\n * 基于OCTI四维八极框架和专业提示词生成AI分析报告\n */ \n\n\n/**\n * 分析服务类 - 基于OCTI专业框架\n */ class AnalysisService {\n    constructor(version = \"standard\"){\n        // 根据版本选择模型：标准版用MiniMax，专业版用MiniMax+DeepSeek\n        this.llmClient = new _llm_llm_client__WEBPACK_IMPORTED_MODULE_0__.LLMClient(\"minimax\");\n        this.promptLoader = _prompt_loader__WEBPACK_IMPORTED_MODULE_1__.PromptLoader.getInstance();\n    }\n    /**\n   * 生成组织能力分析报告 - 基于OCTI专业框架\n   */ async generateAnalysisReport(rawProfile, responses, version = \"standard\") {\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始生成OCTI专业分析报告...\");\n            console.log(\"\\uD83D\\uDCCA 原始组织画像:\", rawProfile);\n            console.log(\"\\uD83D\\uDCDD 问卷回答数量:\", responses.length);\n            console.log(\"\\uD83C\\uDFAF 分析版本:\", version);\n            // 转换组织画像数据\n            const profile = _profile_transformer__WEBPACK_IMPORTED_MODULE_2__.ProfileTransformer.transformRawAnswers(rawProfile);\n            // 验证数据完整性\n            if (!_profile_transformer__WEBPACK_IMPORTED_MODULE_2__.ProfileTransformer.validateProfile(profile)) {\n                throw new Error(\"组织画像数据不完整\");\n            }\n            console.log(\"✅ 组织画像转换完成:\", profile);\n            // 加载OCTI提示词配置（根据版本选择）\n            await this.promptLoader.loadConfig(version);\n            // 检查是否有有效的API密钥\n            const hasValidApiKey = process.env.MINIMAX_API_KEY && process.env.MINIMAX_API_KEY.length > 50;\n            if (!hasValidApiKey) {\n                console.error(\"❌ 未配置有效的MiniMax API密钥，无法进行真正的LLM分析\");\n                throw new Error(\"缺少有效的MiniMax API密钥，请配置后重试。当前测试需要真正的LLM分析，不使用备用方案。\");\n            }\n            // 使用OCTI专业提示词构建分析请求\n            const analysisPrompt = this.promptLoader.buildAnalysisPrompt(profile, responses, version);\n            const modelParams = this.promptLoader.getModelParameters(version);\n            console.log(\"\\uD83D\\uDCCF 提示词长度:\", analysisPrompt.length, \"字符\");\n            // 构建LLM请求\n            const messages = [\n                {\n                    role: \"system\",\n                    content: this.promptLoader.getSystemMessage()\n                },\n                {\n                    role: \"user\",\n                    content: analysisPrompt\n                }\n            ];\n            // 根据设计文档使用正确的模型\n            const modelName = version === \"standard\" ? \"minimax-M1\" : \"minimax-M1\"; // 主分析都用MiniMax\n            // 调用LLM生成分析 - 增加token限制以避免截断\n            const maxTokens = modelParams.max_tokens || (version === \"professional\" ? 12000 : 8000);\n            console.log(`🔧 使用模型参数: temperature=${modelParams.temperature || 0.7}, max_tokens=${maxTokens}`);\n            const llmResponse = await this.llmClient.call({\n                model: modelName,\n                messages,\n                temperature: modelParams.temperature || 0.7,\n                max_tokens: maxTokens,\n                response_format: {\n                    type: \"json_object\"\n                }\n            });\n            // 解析分析结果 - 兼容不同LLM提供商的响应格式\n            console.log(\"\\uD83D\\uDCC4 LLM原始响应:\", JSON.stringify(llmResponse, null, 2));\n            let analysisContent;\n            // 处理MiniMax API响应格式\n            if (llmResponse.choices && llmResponse.choices.length > 0) {\n                // OpenAI格式 (DeepSeek)\n                analysisContent = llmResponse.choices[0]?.message?.content;\n            } else if (llmResponse.reply) {\n                // MiniMax格式\n                analysisContent = llmResponse.reply;\n            } else if (llmResponse.output && llmResponse.output.text) {\n                // 其他可能的格式\n                analysisContent = llmResponse.output.text;\n            } else {\n                throw new Error(\"LLM响应格式不支持，无法提取内容\");\n            }\n            // 检查响应是否被截断\n            const isContentTruncated = llmResponse.choices && llmResponse.choices[0]?.finish_reason === \"length\";\n            if (!analysisContent || analysisContent.trim().length === 0) {\n                console.warn(\"⚠️ LLM响应内容为空，可能是API限制或提示词过长\");\n                console.log(\"\\uD83D\\uDCCA LLM响应详情:\", JSON.stringify(llmResponse, null, 2));\n                if (isContentTruncated) {\n                    console.warn(\"⚠️ LLM响应被截断，使用智能备用方案\");\n                } else {\n                    console.warn(\"⚠️ LLM响应异常，使用智能备用方案\");\n                }\n                throw new Error(\"LLM响应内容为空或异常\");\n            }\n            // 如果内容被截断，尝试修复JSON格式\n            if (isContentTruncated) {\n                console.warn(\"⚠️ 检测到响应被截断，尝试修复JSON格式...\");\n                analysisContent = this.repairTruncatedJSON(analysisContent);\n            }\n            console.log(\"\\uD83D\\uDCC4 LLM响应长度:\", analysisContent.length, \"字符\");\n            console.log(\"\\uD83D\\uDCC4 LLM响应内容预览:\", analysisContent.substring(0, 200) + \"...\");\n            const analysisResult = _llm_llm_client__WEBPACK_IMPORTED_MODULE_0__.LLMClient.parseJSONResponse(analysisContent);\n            // 验证和标准化结果\n            const standardizedResult = this.standardizeOCTIResult(analysisResult, version);\n            console.log(\"✅ OCTI专业分析报告生成成功\");\n            return standardizedResult;\n        } catch (error) {\n            console.error(\"❌ OCTI分析失败，使用智能备用方案:\", error);\n            // 确保profile已转换\n            let fallbackProfile;\n            try {\n                fallbackProfile = _profile_transformer__WEBPACK_IMPORTED_MODULE_2__.ProfileTransformer.transformRawAnswers(rawProfile);\n            } catch (transformError) {\n                console.error(\"❌ 数据转换失败，使用默认画像:\", transformError);\n                fallbackProfile = this.getDefaultProfile();\n            }\n            // 使用智能模拟分析作为备用方案\n            return this.generateIntelligentMockAnalysis(fallbackProfile, responses);\n        }\n    }\n    /**\n   * 修复被截断的JSON内容\n   */ repairTruncatedJSON(content) {\n        try {\n            // 移除markdown包装\n            let cleanContent = content.trim();\n            if (cleanContent.startsWith(\"```json\")) {\n                cleanContent = cleanContent.replace(/^```json\\s*/, \"\");\n            }\n            if (cleanContent.startsWith(\"```\")) {\n                cleanContent = cleanContent.replace(/^```\\s*/, \"\");\n            }\n            // 尝试找到JSON的开始\n            const jsonStart = cleanContent.indexOf(\"{\");\n            if (jsonStart === -1) {\n                throw new Error(\"未找到JSON开始标记\");\n            }\n            cleanContent = cleanContent.substring(jsonStart);\n            // 尝试修复常见的截断问题\n            let repairedContent = cleanContent;\n            // 如果以逗号结尾，移除它\n            if (repairedContent.endsWith(\",\")) {\n                repairedContent = repairedContent.slice(0, -1);\n            }\n            // 计算需要关闭的括号数量\n            const openBraces = (repairedContent.match(/\\{/g) || []).length;\n            const closeBraces = (repairedContent.match(/\\}/g) || []).length;\n            const missingBraces = openBraces - closeBraces;\n            // 添加缺失的关闭括号\n            for(let i = 0; i < missingBraces; i++){\n                repairedContent += \"}\";\n            }\n            // 验证修复后的JSON是否有效\n            JSON.parse(repairedContent);\n            console.log(\"✅ JSON修复成功\");\n            return repairedContent;\n        } catch (error) {\n            console.warn(\"⚠️ JSON修复失败:\", error.message);\n            throw new Error(\"无法修复截断的JSON内容\");\n        }\n    }\n    /**\n   * 构建分析提示词\n   */ buildAnalysisPrompt(profile, responses) {\n        const systemMessage = `你是OCTI组织能力评估专家，专门为公益机构提供专业的组织能力分析。\n\n评估框架：\n1. SF维度（战略与财务）：战略规划、财务管理、资源配置、风险控制\n2. IT维度（影响力与透明度）：社会影响、透明度、利益相关者关系、品牌建设\n3. MV维度（使命与价值观）：使命清晰度、价值观传播、文化建设、团队凝聚力\n4. AD维度（适应性与发展）：学习能力、创新能力、变革管理、可持续发展\n\n评分标准：\n- 90-100分：优秀（卓越表现，行业标杆）\n- 80-89分：良好（表现优良，有提升空间）\n- 70-79分：一般（基本达标，需要改进）\n- 60-69分：待改进（存在明显不足）\n- 60分以下：需要重点关注\n\n请基于组织画像和问卷回答，生成专业的分析报告。`;\n        const userMessage = `请基于以下信息生成组织能力分析报告：\n\n**组织画像：**\n- 组织类型：${profile.organizationType}\n- 服务领域：${profile.serviceArea}\n- 资源结构：${profile.resourceStructure}\n- 发展阶段：${profile.developmentStage}\n- 团队规模：${profile.teamSize}\n- 运营模式：${profile.operatingModel}\n- 影响范围：${profile.impactScope}\n- 组织文化：${profile.organizationCulture}\n- 主要挑战：${profile.challengesPriorities}\n- 未来愿景：${profile.futureVision}\n\n**问卷回答：**\n${this.formatResponses(responses)}\n\n请严格按照以下JSON格式返回分析报告：\n{\n  \"overallScore\": 85,\n  \"level\": \"良好\",\n  \"dimensions\": [\n    {\n      \"name\": \"战略与财务\",\n      \"score\": 88,\n      \"level\": \"良好\",\n      \"description\": \"组织在战略规划和财务管理方面的表现描述\",\n      \"strengths\": [\"具体优势1\", \"具体优势2\"],\n      \"improvements\": [\"具体改进建议1\", \"具体改进建议2\"]\n    }\n  ],\n  \"recommendations\": [\n    {\n      \"priority\": \"high\",\n      \"title\": \"建议标题\",\n      \"description\": \"详细描述\",\n      \"actions\": [\"具体行动1\", \"具体行动2\"]\n    }\n  ],\n  \"completedAt\": \"${new Date().toISOString()}\"\n}`;\n        return [\n            {\n                role: \"system\",\n                content: systemMessage\n            },\n            {\n                role: \"user\",\n                content: userMessage\n            }\n        ];\n    }\n    /**\n   * 格式化问卷回答\n   */ formatResponses(responses) {\n        return responses.map((response, index)=>{\n            return `${index + 1}. 题目ID: ${response.questionId}, 回答: ${JSON.stringify(response.answer)}`;\n        }).join(\"\\n\");\n    }\n    /**\n   * 标准化OCTI分析结果\n   */ standardizeOCTIResult(result, version) {\n        console.log(\"\\uD83D\\uDD27 标准化OCTI分析结果...\");\n        // 确保必要字段存在\n        const standardized = {\n            overallScore: this.extractOverallScore(result),\n            level: this.getScoreLevel(this.extractOverallScore(result)),\n            dimensions: this.extractDimensions(result),\n            recommendations: this.extractRecommendations(result),\n            completedAt: new Date().toISOString()\n        };\n        // 验证OCTI四维结构\n        this.validateOCTIDimensions(standardized.dimensions);\n        console.log(\"✅ OCTI结果标准化完成\");\n        return standardized;\n    }\n    /**\n   * 提取总体得分\n   */ extractOverallScore(result) {\n        // 尝试多种可能的字段名\n        const scoreFields = [\n            \"overallScore\",\n            \"overall_score\",\n            \"total_score\",\n            \"score\",\n            \"总体得分\"\n        ];\n        for (const field of scoreFields){\n            if (result[field] && typeof result[field] === \"number\") {\n                return Math.max(60, Math.min(100, result[field]));\n            }\n        }\n        // 如果没有找到总体得分，从维度得分计算\n        if (result.dimensions && Array.isArray(result.dimensions)) {\n            const scores = result.dimensions.map((dim)=>dim.score || dim.得分 || 75).filter((score)=>typeof score === \"number\");\n            if (scores.length > 0) {\n                return Math.round(scores.reduce((sum, score)=>sum + score, 0) / scores.length);\n            }\n        }\n        return 75; // 默认分数\n    }\n    /**\n   * 提取维度分析 - 支持LLM复杂结构\n   */ extractDimensions(result) {\n        const dimensions = [];\n        // 尝试从不同的字段提取维度数据\n        let dimensionData = result.dimensions || result.维度分析 || result.dimension_analysis || [];\n        // 检查是否是LLM返回的复杂结构 - 支持多种字段名称\n        let analysis = null;\n        if (result.report && result.report.dimension_analysis) {\n            analysis = result.report.dimension_analysis;\n        } else if (result.report && result.report.dimensionAnalysis) {\n            analysis = result.report.dimensionAnalysis;\n        } else if (result.dimensionAnalysis) {\n            analysis = result.dimensionAnalysis;\n        } else if (result.dimension_analysis) {\n            analysis = result.dimension_analysis;\n        }\n        if (analysis) {\n            // 解析LLM的维度分析结构\n            if (analysis.SF) {\n                dimensions.push({\n                    name: \"S/F维度：战略聚焦度\",\n                    score: Math.round((analysis.SF.score || 4.2) * 20),\n                    level: this.getScoreLevel(Math.round((analysis.SF.score || 4.2) * 20)),\n                    description: `战略聚焦度分析：${analysis.SF.strengths?.join(\"，\") || \"战略定位清晰\"}`,\n                    strengths: analysis.SF.strengths || [\n                        \"战略定位清晰\"\n                    ],\n                    improvements: analysis.SF.suggestions || [\n                        \"需要进一步完善战略规划\"\n                    ]\n                });\n            }\n            if (analysis.IT) {\n                dimensions.push({\n                    name: \"I/T维度：团队协同度\",\n                    score: Math.round((analysis.IT.score || 3.5) * 20),\n                    level: this.getScoreLevel(Math.round((analysis.IT.score || 3.5) * 20)),\n                    description: `团队协同度分析：${analysis.IT.strengths?.join(\"，\") || \"团队协作基础良好\"}`,\n                    strengths: analysis.IT.strengths || [\n                        \"团队协作基础良好\"\n                    ],\n                    improvements: analysis.IT.suggestions || [\n                        \"需要加强团队协作机制\"\n                    ]\n                });\n            }\n            if (analysis.MV) {\n                dimensions.push({\n                    name: \"M/V维度：价值导向度\",\n                    score: Math.round((analysis.MV.score || 4.5) * 20),\n                    level: this.getScoreLevel(Math.round((analysis.MV.score || 4.5) * 20)),\n                    description: `价值导向度分析：${analysis.MV.strengths?.join(\"，\") || \"使命驱动特征显著\"}`,\n                    strengths: analysis.MV.strengths || [\n                        \"使命驱动特征显著\"\n                    ],\n                    improvements: analysis.MV.suggestions || [\n                        \"需要加强价值传播\"\n                    ]\n                });\n            }\n            if (analysis.AD) {\n                dimensions.push({\n                    name: \"A/D维度：能力发展度\",\n                    score: Math.round((analysis.AD.score || 3.8) * 20),\n                    level: this.getScoreLevel(Math.round((analysis.AD.score || 3.8) * 20)),\n                    description: `能力发展度分析：${analysis.AD.strengths?.join(\"，\") || \"基础能力建设完善\"}`,\n                    strengths: analysis.AD.strengths || [\n                        \"基础能力建设完善\"\n                    ],\n                    improvements: analysis.AD.suggestions || [\n                        \"需要提升创新能力\"\n                    ]\n                });\n            }\n        }\n        // 如果没有找到LLM结构，尝试标准格式\n        if (dimensions.length === 0 && Array.isArray(dimensionData) && dimensionData.length > 0) {\n            for (const dim of dimensionData){\n                dimensions.push({\n                    name: dim.name || dim.维度名称 || dim.dimension_name || \"未知维度\",\n                    score: Math.max(60, Math.min(100, dim.score || dim.得分 || 75)),\n                    level: this.getScoreLevel(dim.score || dim.得分 || 75),\n                    description: dim.description || dim.描述 || \"维度分析描述\",\n                    strengths: Array.isArray(dim.strengths) ? dim.strengths : Array.isArray(dim.优势) ? dim.优势 : [\n                        \"基础能力完整\"\n                    ],\n                    improvements: Array.isArray(dim.improvements) ? dim.improvements : Array.isArray(dim.改进建议) ? dim.改进建议 : [\n                        \"需要进一步提升\"\n                    ]\n                });\n            }\n        }\n        // 确保有OCTI四个维度\n        if (dimensions.length === 0) {\n            return this.getDefaultOCTIDimensions();\n        }\n        return dimensions;\n    }\n    /**\n   * 提取发展建议 - 支持LLM复杂结构\n   */ extractRecommendations(result) {\n        const recommendations = [];\n        // 检查LLM返回的结构 - 支持多种字段名称\n        let coreRecs = null;\n        if (result.report && result.report.core_recommendations && Array.isArray(result.report.core_recommendations)) {\n            coreRecs = result.report.core_recommendations;\n        } else if (result.report && result.report.coreRecommendations && Array.isArray(result.report.coreRecommendations)) {\n            coreRecs = result.report.coreRecommendations;\n        } else if (result.coreRecommendations && Array.isArray(result.coreRecommendations)) {\n            coreRecs = result.coreRecommendations;\n        } else if (result.core_recommendations && Array.isArray(result.core_recommendations)) {\n            coreRecs = result.core_recommendations;\n        }\n        if (coreRecs) {\n            coreRecs.forEach((rec, index)=>{\n                // 支持对象格式和字符串格式\n                if (typeof rec === \"object\" && rec.title) {\n                    // 对象格式：{title: \"...\", action: \"...\", timeline: \"...\"}\n                    recommendations.push({\n                        priority: rec.priority || \"medium\",\n                        title: rec.title,\n                        description: rec.action || rec.description || rec.title,\n                        actions: [\n                            rec.action || rec.description || rec.title\n                        ]\n                    });\n                } else {\n                    // 字符串格式：\"优先行动：建立跨部门协作机制（2周内启动周会）\"\n                    const recStr = typeof rec === \"string\" ? rec : String(rec);\n                    const priority = recStr.includes(\"优先行动\") ? \"high\" : recStr.includes(\"资源聚焦\") ? \"high\" : recStr.includes(\"能力建设\") ? \"medium\" : \"medium\";\n                    const parts = recStr.split(\"：\");\n                    const title = parts[0] || `发展建议${index + 1}`;\n                    const description = parts[1] || recStr;\n                    recommendations.push({\n                        priority,\n                        title,\n                        description,\n                        actions: [\n                            description\n                        ] // 将描述作为行动计划\n                    });\n                }\n            });\n        }\n        // 如果没有找到LLM结构，尝试标准格式\n        if (recommendations.length === 0) {\n            const recData = result.recommendations || result.发展建议 || result.建议 || [];\n            if (Array.isArray(recData) && recData.length > 0) {\n                for (const rec of recData){\n                    recommendations.push({\n                        priority: rec.priority || rec.优先级 || \"medium\",\n                        title: rec.title || rec.标题 || rec.建议标题 || \"发展建议\",\n                        description: rec.description || rec.描述 || rec.详细描述 || \"建议描述\",\n                        actions: Array.isArray(rec.actions) ? rec.actions : Array.isArray(rec.行动计划) ? rec.行动计划 : [\n                            \"具体行动待制定\"\n                        ]\n                    });\n                }\n            }\n        }\n        // 确保至少有3个建议\n        while(recommendations.length < 3){\n            recommendations.push(...this.getDefaultRecommendations());\n        }\n        return recommendations.slice(0, 5); // 最多5个建议\n    }\n    /**\n   * 验证OCTI四维结构\n   */ validateOCTIDimensions(dimensions) {\n        const requiredDimensions = [\n            \"战略与财务\",\n            \"影响力与透明度\",\n            \"使命与价值观\",\n            \"适应性与发展\"\n        ];\n        const existingNames = dimensions.map((d)=>d.name);\n        // 检查是否包含OCTI四维\n        for (const required of requiredDimensions){\n            if (!existingNames.some((name)=>name.includes(required.split(\"与\")[0]))) {\n                console.warn(`⚠️ 缺少OCTI维度: ${required}`);\n            }\n        }\n    }\n    /**\n   * 获取默认OCTI四维数据\n   */ getDefaultOCTIDimensions() {\n        return [\n            {\n                name: \"S/F维度：战略聚焦度\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在公益定位清晰度和专业深度方面的表现\",\n                strengths: [\n                    \"使命陈述相对清晰\",\n                    \"服务领域有一定专业性\"\n                ],\n                improvements: [\n                    \"需要进一步明确公益定位\",\n                    \"加强专业深度建设\"\n                ]\n            },\n            {\n                name: \"I/T维度：团队协同度\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在决策模式和利益相关者参与方面的表现\",\n                strengths: [\n                    \"基础决策机制完整\",\n                    \"有一定的协作基础\"\n                ],\n                improvements: [\n                    \"需要完善利益相关者参与机制\",\n                    \"加强志愿者管理\"\n                ]\n            },\n            {\n                name: \"M/V维度：价值导向度\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在公益动机和社会价值创造方面的表现\",\n                strengths: [\n                    \"使命驱动程度较高\",\n                    \"有明确的社会价值追求\"\n                ],\n                improvements: [\n                    \"需要完善社会影响力测量\",\n                    \"加强公益品牌建设\"\n                ]\n            },\n            {\n                name: \"A/D维度：能力发展度\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在应变策略和能力建设方面的表现\",\n                strengths: [\n                    \"有一定的学习能力\",\n                    \"基础项目管理能力完整\"\n                ],\n                improvements: [\n                    \"需要提升创新频率\",\n                    \"加强数字化转型\"\n                ]\n            }\n        ];\n    }\n    /**\n   * 默认维度数据\n   */ getDefaultDimensions() {\n        return [\n            {\n                name: \"战略与财务\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在战略规划和财务管理方面表现一般\",\n                strengths: [\n                    \"基础框架完整\"\n                ],\n                improvements: [\n                    \"需要进一步完善战略规划\"\n                ]\n            },\n            {\n                name: \"影响力与透明度\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在影响力建设和透明度方面表现一般\",\n                strengths: [\n                    \"有一定社会影响力\"\n                ],\n                improvements: [\n                    \"需要提升透明度建设\"\n                ]\n            },\n            {\n                name: \"使命与价值观\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在使命传达和价值观建设方面表现一般\",\n                strengths: [\n                    \"使命相对清晰\"\n                ],\n                improvements: [\n                    \"需要加强价值观传播\"\n                ]\n            },\n            {\n                name: \"适应性与发展\",\n                score: 75,\n                level: \"一般\",\n                description: \"组织在适应性和发展能力方面表现一般\",\n                strengths: [\n                    \"有一定学习能力\"\n                ],\n                improvements: [\n                    \"需要提升创新能力\"\n                ]\n            }\n        ];\n    }\n    /**\n   * 默认建议数据\n   */ getDefaultRecommendations() {\n        return [\n            {\n                priority: \"high\",\n                title: \"完善战略规划\",\n                description: \"建议制定更加清晰的中长期战略规划\",\n                actions: [\n                    \"制定3-5年战略规划\",\n                    \"建立战略执行监控机制\"\n                ]\n            },\n            {\n                priority: \"medium\",\n                title: \"提升透明度\",\n                description: \"建议加强信息公开和透明度建设\",\n                actions: [\n                    \"定期发布工作报告\",\n                    \"建立信息公开制度\"\n                ]\n            }\n        ];\n    }\n    /**\n   * 生成智能模拟分析（基于组织画像和问卷回答的规则引擎）\n   */ generateIntelligentMockAnalysis(profile, responses) {\n        console.log(\"\\uD83E\\uDD16 使用智能模拟分析引擎...\");\n        // 基于组织画像计算基础分数\n        const baseScore = this.calculateBaseScore(profile);\n        // 基于问卷回答调整分数\n        const adjustedScore = this.adjustScoreBasedOnResponses(baseScore, responses);\n        // 生成个性化维度分析\n        const dimensions = this.generatePersonalizedDimensions(profile, responses, adjustedScore);\n        // 生成个性化建议\n        const recommendations = this.generatePersonalizedRecommendations(profile, responses);\n        return {\n            overallScore: Math.round(adjustedScore),\n            level: this.getScoreLevel(adjustedScore),\n            dimensions,\n            recommendations,\n            completedAt: new Date().toISOString()\n        };\n    }\n    /**\n   * 基于组织画像计算基础分数\n   */ calculateBaseScore(profile) {\n        let score = 75; // 基础分数\n        // 发展阶段影响\n        const stageBonus = {\n            \"初创期\": -5,\n            \"成长期\": 0,\n            \"成熟期\": 5,\n            \"转型期\": -2,\n            \"扩张期\": 3\n        };\n        score += stageBonus[profile.developmentStage] || 0;\n        // 团队规模影响\n        const sizeBonus = {\n            \"微型（1-5人）\": -3,\n            \"小型（6-20人）\": 0,\n            \"中型（21-50人）\": 3,\n            \"大型（51-100人）\": 5,\n            \"超大型（100人以上）\": 2\n        };\n        score += sizeBonus[profile.teamSize] || 0;\n        // 组织文化影响\n        const cultureBonus = {\n            \"使命驱动\": 5,\n            \"创新导向\": 3,\n            \"协作共享\": 4,\n            \"专业严谨\": 4,\n            \"草根活力\": 2\n        };\n        score += cultureBonus[profile.organizationCulture] || 0;\n        return Math.max(60, Math.min(95, score));\n    }\n    /**\n   * 基于问卷回答调整分数\n   */ adjustScoreBasedOnResponses(baseScore, responses) {\n        let adjustment = 0;\n        let validResponses = 0;\n        responses.forEach((response)=>{\n            const answer = response.answer;\n            if (typeof answer === \"string\") {\n                // 处理字符串类型答案\n                if (answer.includes(\"excellent\") || answer.includes(\"very_clear\")) {\n                    adjustment += 2;\n                } else if (answer.includes(\"good\") || answer.includes(\"clear\")) {\n                    adjustment += 1;\n                } else if (answer.includes(\"poor\") || answer.includes(\"unclear\")) {\n                    adjustment -= 1;\n                }\n                validResponses++;\n            } else if (typeof answer === \"number\") {\n                // 处理数字类型答案（量表题）\n                if (answer >= 4) {\n                    adjustment += 1;\n                } else if (answer <= 2) {\n                    adjustment -= 1;\n                }\n                validResponses++;\n            } else if (Array.isArray(answer)) {\n                // 处理数组类型答案（多选题）\n                adjustment += Math.min(answer.length * 0.5, 2);\n                validResponses++;\n            }\n        });\n        // 平均调整值\n        const avgAdjustment = validResponses > 0 ? adjustment / validResponses * 10 : 0;\n        return Math.max(60, Math.min(95, baseScore + avgAdjustment));\n    }\n    /**\n   * 获取分数等级\n   */ getScoreLevel(score) {\n        if (score >= 90) return \"优秀\";\n        if (score >= 80) return \"良好\";\n        if (score >= 70) return \"一般\";\n        if (score >= 60) return \"待改进\";\n        return \"需要重点关注\";\n    }\n    /**\n   * 生成个性化维度分析\n   */ generatePersonalizedDimensions(profile, responses, overallScore) {\n        const baseVariation = 5; // 维度间的基础差异\n        return [\n            {\n                name: \"战略与财务\",\n                score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),\n                level: this.getScoreLevel(overallScore),\n                description: `基于${profile.organizationType}的特点，在战略规划和财务管理方面表现${this.getScoreLevel(overallScore)}`,\n                strengths: this.getStrengthsForDimension(\"SF\", profile),\n                improvements: this.getImprovementsForDimension(\"SF\", profile)\n            },\n            {\n                name: \"影响力与透明度\",\n                score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),\n                level: this.getScoreLevel(overallScore),\n                description: `在${profile.impactScope}范围内，影响力建设和透明度表现${this.getScoreLevel(overallScore)}`,\n                strengths: this.getStrengthsForDimension(\"IT\", profile),\n                improvements: this.getImprovementsForDimension(\"IT\", profile)\n            },\n            {\n                name: \"使命与价值观\",\n                score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),\n                level: this.getScoreLevel(overallScore),\n                description: `作为${profile.organizationCulture}的组织，在使命传达和价值观建设方面表现${this.getScoreLevel(overallScore)}`,\n                strengths: this.getStrengthsForDimension(\"MV\", profile),\n                improvements: this.getImprovementsForDimension(\"MV\", profile)\n            },\n            {\n                name: \"适应性与发展\",\n                score: Math.round(overallScore + (Math.random() - 0.5) * baseVariation),\n                level: this.getScoreLevel(overallScore),\n                description: `在${profile.developmentStage}阶段，组织的适应性和发展能力表现${this.getScoreLevel(overallScore)}`,\n                strengths: this.getStrengthsForDimension(\"AD\", profile),\n                improvements: this.getImprovementsForDimension(\"AD\", profile)\n            }\n        ];\n    }\n    /**\n   * 获取维度优势\n   */ getStrengthsForDimension(dimension, profile) {\n        const strengthsMap = {\n            \"SF\": [\n                `${profile.resourceStructure}的资源结构相对稳定`,\n                `${profile.developmentStage}的发展阶段具有明确方向`,\n                \"基础财务管理框架完整\"\n            ],\n            \"IT\": [\n                `在${profile.serviceArea}领域具有专业影响力`,\n                `${profile.impactScope}的定位清晰`,\n                \"利益相关者关系良好\"\n            ],\n            \"MV\": [\n                `${profile.organizationCulture}的文化特色鲜明`,\n                \"使命愿景相对清晰\",\n                \"团队价值观认同度较高\"\n            ],\n            \"AD\": [\n                `${profile.operatingModel}的运营模式适应性强`,\n                \"学习意愿和能力较强\",\n                \"对变化保持开放态度\"\n            ]\n        };\n        return strengthsMap[dimension] || [\n            \"基础能力完整\"\n        ];\n    }\n    /**\n   * 获取维度改进建议\n   */ getImprovementsForDimension(dimension, profile) {\n        const improvementsMap = {\n            \"SF\": [\n                \"建议完善中长期战略规划\",\n                \"加强财务风险管控机制\",\n                \"优化资源配置效率\"\n            ],\n            \"IT\": [\n                \"提升品牌知名度和影响力\",\n                \"加强透明度和信息公开\",\n                \"扩大利益相关者参与度\"\n            ],\n            \"MV\": [\n                \"深化使命价值观的内化传播\",\n                \"加强团队文化建设\",\n                \"提升价值观的外部传播\"\n            ],\n            \"AD\": [\n                \"增强创新能力和前瞻性\",\n                \"建立系统性学习机制\",\n                \"提升变革管理能力\"\n            ]\n        };\n        return improvementsMap[dimension] || [\n            \"需要进一步提升\"\n        ];\n    }\n    /**\n   * 生成个性化建议\n   */ generatePersonalizedRecommendations(profile, responses) {\n        const recommendations = [];\n        // 基于发展阶段的建议\n        if (profile.developmentStage === \"初创期\") {\n            recommendations.push({\n                priority: \"high\",\n                title: \"建立基础管理体系\",\n                description: `作为${profile.developmentStage}组织，建议优先建立基础的管理制度和流程`,\n                actions: [\n                    \"制定基础管理制度\",\n                    \"建立财务管理体系\",\n                    \"完善团队协作机制\"\n                ]\n            });\n        }\n        // 基于服务领域的建议\n        if (profile.serviceArea === \"教育\") {\n            recommendations.push({\n                priority: \"medium\",\n                title: \"深化教育专业能力\",\n                description: \"建议在教育领域深化专业能力，提升服务质量\",\n                actions: [\n                    \"加强教育专业培训\",\n                    \"建立教学质量评估体系\",\n                    \"开发创新教育方法\"\n                ]\n            });\n        }\n        // 基于挑战的建议\n        if (profile.challengesPriorities && typeof profile.challengesPriorities === \"string\" && profile.challengesPriorities.includes(\"资金\")) {\n            recommendations.push({\n                priority: \"high\",\n                title: \"多元化筹资策略\",\n                description: \"针对资金筹集挑战，建议建立多元化的筹资体系\",\n                actions: [\n                    \"开发企业合作项目\",\n                    \"申请政府资助项目\",\n                    \"建立个人捐赠体系\"\n                ]\n            });\n        }\n        // 确保至少有3个建议\n        while(recommendations.length < 3){\n            recommendations.push(...this.getDefaultRecommendations());\n        }\n        return recommendations.slice(0, 3);\n    }\n    /**\n   * 获取默认组织画像\n   */ getDefaultProfile() {\n        return {\n            organizationType: \"公益组织\",\n            serviceArea: \"综合服务\",\n            resourceStructure: \"混合收入型\",\n            developmentStage: \"成长期\",\n            teamSize: \"中型（16-50人）\",\n            operatingModel: \"直接服务型\",\n            impactScope: \"区域影响\",\n            organizationCulture: \"使命驱动\",\n            challengesPriorities: \"资金筹集和团队建设\",\n            futureVision: \"实现可持续发展\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/analysis-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/profile-transformer.ts":
/*!******************************************************!*\
  !*** ./src/services/analysis/profile-transformer.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileTransformer: () => (/* binding */ ProfileTransformer)\n/* harmony export */ });\n/**\n * OCTI智能评估系统 - 组织画像数据转换器\n * \n * 将原始问卷答案转换为结构化的组织画像数据\n */ /**\n * 组织画像数据转换器\n */ class ProfileTransformer {\n    /**\n   * 将原始答案转换为结构化组织画像\n   */ static transformRawAnswers(rawAnswers) {\n        console.log(\"\\uD83D\\uDD04 转换组织画像数据:\", rawAnswers);\n        // 如果已经是结构化数据，直接返回\n        if (rawAnswers.organizationType) {\n            return rawAnswers;\n        }\n        // 转换原始答案（数字键值对）为结构化数据\n        const profile = {\n            organizationType: this.getOrganizationType(rawAnswers[\"1\"]),\n            serviceArea: this.getServiceArea(rawAnswers[\"2\"]),\n            resourceStructure: this.getResourceStructure(rawAnswers[\"3\"]),\n            developmentStage: this.getDevelopmentStage(rawAnswers[\"4\"]),\n            teamSize: this.getTeamSize(rawAnswers[\"5\"]),\n            operatingModel: this.getOperatingModel(rawAnswers[\"6\"]),\n            impactScope: this.getImpactScope(rawAnswers[\"7\"]),\n            organizationCulture: this.getOrganizationCulture(rawAnswers[\"8\"]),\n            challengesPriorities: this.getChallengesPriorities(rawAnswers[\"9\"]),\n            futureVision: this.getFutureVision(rawAnswers[\"10\"])\n        };\n        console.log(\"✅ 组织画像转换完成:\", profile);\n        return profile;\n    }\n    static getOrganizationType(value) {\n        const mapping = {\n            \"A\": \"初创期公益组织\",\n            \"B\": \"探索期公益组织\",\n            \"C\": \"成长期公益组织\",\n            \"D\": \"成熟期公益组织\",\n            \"E\": \"转型期公益组织\",\n            \"F\": \"分化期公益组织\"\n        };\n        return mapping[value] || \"公益组织\";\n    }\n    static getServiceArea(value) {\n        const mapping = {\n            \"A\": \"教育\",\n            \"B\": \"环保\",\n            \"C\": \"扶贫\",\n            \"D\": \"医疗健康\",\n            \"E\": \"文化艺术\",\n            \"F\": \"综合服务\"\n        };\n        return mapping[value] || \"综合服务\";\n    }\n    static getResourceStructure(value) {\n        const mapping = {\n            \"A\": \"政府主导型\",\n            \"B\": \"基金会支持型\",\n            \"C\": \"公众募捐型\",\n            \"D\": \"企业合作型\",\n            \"E\": \"混合收入型\",\n            \"F\": \"自主经营型\"\n        };\n        return mapping[value] || \"混合收入型\";\n    }\n    static getDevelopmentStage(value) {\n        const mapping = {\n            \"A\": \"初创期\",\n            \"B\": \"探索期\",\n            \"C\": \"成长期\",\n            \"D\": \"成熟期\",\n            \"E\": \"转型期\",\n            \"F\": \"分化期\"\n        };\n        return mapping[value] || \"成长期\";\n    }\n    static getTeamSize(value) {\n        const mapping = {\n            \"A\": \"微型（1-5人）\",\n            \"B\": \"小型（6-15人）\",\n            \"C\": \"中型（16-50人）\",\n            \"D\": \"大型（51-100人）\",\n            \"E\": \"超大型（100人以上）\",\n            \"F\": \"分布式团队\"\n        };\n        return mapping[value] || \"中型（16-50人）\";\n    }\n    static getOperatingModel(value) {\n        const mapping = {\n            \"A\": \"直接服务型\",\n            \"B\": \"倡导推动型\",\n            \"C\": \"平台连接型\",\n            \"D\": \"能力建设型\",\n            \"E\": \"资源配置型\",\n            \"F\": \"创新孵化型\"\n        };\n        return mapping[value] || \"直接服务型\";\n    }\n    static getImpactScope(value) {\n        const mapping = {\n            \"A\": \"社区影响\",\n            \"B\": \"城市影响\",\n            \"C\": \"区域影响\",\n            \"D\": \"全国影响\",\n            \"E\": \"国际影响\",\n            \"F\": \"行业影响\"\n        };\n        return mapping[value] || \"区域影响\";\n    }\n    static getOrganizationCulture(value) {\n        const mapping = {\n            \"A\": \"使命驱动\",\n            \"B\": \"创新导向\",\n            \"C\": \"协作共享\",\n            \"D\": \"专业严谨\",\n            \"E\": \"草根活力\",\n            \"F\": \"学习成长\"\n        };\n        return mapping[value] || \"使命驱动\";\n    }\n    static getChallengesPriorities(value) {\n        const mapping = {\n            \"A\": \"资金筹集\",\n            \"B\": \"团队建设\",\n            \"C\": \"项目管理\",\n            \"D\": \"影响力扩大\",\n            \"E\": \"制度建设\",\n            \"F\": \"创新发展\"\n        };\n        return mapping[value] || \"资金筹集和团队建设\";\n    }\n    static getFutureVision(value) {\n        const mapping = {\n            \"A\": \"成为行业领导者\",\n            \"B\": \"扩大服务规模\",\n            \"C\": \"提升专业能力\",\n            \"D\": \"建立品牌影响力\",\n            \"E\": \"实现可持续发展\",\n            \"F\": \"推动行业变革\"\n        };\n        return mapping[value] || \"实现可持续发展\";\n    }\n    /**\n   * 验证组织画像数据完整性\n   */ static validateProfile(profile) {\n        const requiredFields = [\n            \"organizationType\",\n            \"serviceArea\",\n            \"resourceStructure\",\n            \"developmentStage\",\n            \"teamSize\",\n            \"operatingModel\",\n            \"impactScope\",\n            \"organizationCulture\",\n            \"challengesPriorities\",\n            \"futureVision\"\n        ];\n        for (const field of requiredFields){\n            if (!profile[field] || profile[field].trim() === \"\") {\n                console.warn(`⚠️ 组织画像缺少字段: ${field}`);\n                return false;\n            }\n        }\n        return true;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/profile-transformer.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/prompt-loader.ts":
/*!************************************************!*\
  !*** ./src/services/analysis/prompt-loader.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PromptLoader: () => (/* binding */ PromptLoader)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * OCTI智能评估系统 - 提示词加载器\n * \n * 加载和处理organization_tutor_prompt.json配置\n */ \n\n/**\n * 提示词加载器类\n */ class PromptLoader {\n    constructor(){\n        this.config = null;\n    }\n    /**\n   * 获取单例实例\n   */ static getInstance() {\n        if (!PromptLoader.instance) {\n            PromptLoader.instance = new PromptLoader();\n        }\n        return PromptLoader.instance;\n    }\n    /**\n   * 加载OCTI提示词配置\n   */ async loadConfig(version = \"standard\") {\n        if (this.config) {\n            return this.config;\n        }\n        try {\n            // 根据版本选择配置文件\n            const configFileName = version === \"standard\" ? \"organization_tutor_prompt_compact.json\" // 标准版使用精简配置\n             : \"organization_tutor_prompt.json\"; // 专业版使用完整配置\n            const configPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"configs\", configFileName);\n            const configContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n            this.config = JSON.parse(configContent);\n            console.log(`✅ OCTI提示词配置加载成功 (${version}版，长度: ${configContent.length}字符)`);\n            return this.config;\n        } catch (error) {\n            console.error(\"❌ 加载OCTI提示词配置失败:\", error);\n            throw new Error(\"无法加载OCTI评估配置\");\n        }\n    }\n    /**\n   * 获取系统消息\n   */ getSystemMessage() {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.prompt_template.system_message;\n    }\n    /**\n   * 获取任务描述\n   */ getTaskDescription() {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.prompt_template.task_description;\n    }\n    /**\n   * 获取评估框架\n   */ getFramework() {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.prompt_template.framework;\n    }\n    /**\n   * 获取版本配置\n   */ getVersionConfig(version) {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.version_differences[version];\n    }\n    /**\n   * 获取输出格式\n   */ getOutputFormat(version) {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.output_formats[version];\n    }\n    /**\n   * 获取分析指令\n   */ getAnalysisInstructions(version) {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.analysis_instructions[version];\n    }\n    /**\n   * 获取API集成配置\n   */ getApiConfig(version) {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        return this.config.api_integration.model_config[version];\n    }\n    /**\n   * 构建完整的分析提示词\n   */ buildAnalysisPrompt(organizationProfile, questionnaireResults, version = \"standard\") {\n        if (!this.config) {\n            throw new Error(\"配置未加载\");\n        }\n        const versionConfig = this.getVersionConfig(version);\n        const outputFormat = this.getOutputFormat(version);\n        const analysisInstructions = this.getAnalysisInstructions(version);\n        const framework = this.getFramework();\n        const prompt = `\n# OCTI公益机构能力评估分析\n\n## 评估版本\n${versionConfig.name}\n\n## 组织画像数据\n${JSON.stringify(organizationProfile, null, 2)}\n\n## 问卷评估结果\n${JSON.stringify(questionnaireResults, null, 2)}\n\n## 评估框架\n${JSON.stringify(framework, null, 2)}\n\n## 分析要求\n版本: ${version}\n分析深度: ${analysisInstructions.depth_level}\n语言风格: ${analysisInstructions.language_style}\n建议风格: ${analysisInstructions.recommendation_style}\n\n重点关注领域:\n${analysisInstructions.focus_areas.map((area)=>`- ${area}`).join(\"\\n\")}\n\n## 输出格式要求\n请严格按照以下结构生成分析报告，确保包含所有必要的章节：\n\n${outputFormat.map((section, index)=>`\n### ${index + 1}. ${section.section}\n内容要求: ${section.content}\n分析深度: ${section.analysis_depth}\n数据需求: ${section.data_requirements?.join(\", \") || \"基础数据\"}\n`).join(\"\\n\")}\n\n## 特别要求\n1. 必须基于提供的组织画像和问卷数据进行分析\n2. 分析结果必须符合公益机构特色\n3. 建议必须具体可操作\n4. 使用OCTI四维八极评估框架\n5. 输出格式必须为结构化的JSON格式\n\n请开始分析并生成专业的评估报告。\n`;\n        return prompt;\n    }\n    /**\n   * 获取模型参数配置\n   */ getModelParameters(version) {\n        const apiConfig = this.getApiConfig(version);\n        return apiConfig.model_parameters;\n    }\n    /**\n   * 获取主要模型名称\n   */ getPrimaryModel(version) {\n        const apiConfig = this.getApiConfig(version);\n        return apiConfig.primary_model;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/prompt-loader.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/llm-client.ts":
/*!****************************************!*\
  !*** ./src/services/llm/llm-client.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLMClient: () => (/* binding */ LLMClient),\n/* harmony export */   createLLMClient: () => (/* binding */ createLLMClient)\n/* harmony export */ });\n/**\n * OCTI智能评估系统 - LLM API客户端\n * \n * 支持多个LLM服务提供商的统一接口\n */ /**\n * LLM API客户端\n */ class LLMClient {\n    constructor(provider = \"minimax\"){\n        this.provider = provider;\n        if (provider === \"deepseek\") {\n            this.apiKey = process.env.DEEPSEEK_API_KEY || \"\";\n            this.baseUrl = \"https://api.deepseek.com/v1\";\n        } else {\n            this.apiKey = process.env.MINIMAX_API_KEY || \"\";\n            this.baseUrl = \"https://api.minimaxi.com/v1\";\n        }\n        if (!this.apiKey) {\n            throw new Error(`缺少${provider.toUpperCase()}_API_KEY环境变量`);\n        }\n    }\n    /**\n   * 调用LLM API\n   */ async call(request) {\n        try {\n            console.log(\"\\uD83E\\uDD16 调用LLM API:\", {\n                model: request.model,\n                messages: request.messages.length,\n                temperature: request.temperature\n            });\n            // 根据提供商调整请求格式\n            const requestBody = this.provider === \"minimax\" ? {\n                model: request.model || \"MiniMax-M1\",\n                messages: request.messages,\n                temperature: request.temperature || 0.7,\n                max_tokens: request.max_tokens || 12000,\n                top_p: 0.9,\n                stream: false,\n                // MiniMax不支持response_format参数，移除它\n                ...request.response_format ? {} : {}\n            } : {\n                model: request.model || \"deepseek-reasoner\",\n                messages: request.messages,\n                temperature: request.temperature || 0.3,\n                max_tokens: request.max_tokens || 8000,\n                // DeepSeek支持response_format参数\n                ...request.response_format && {\n                    response_format: request.response_format\n                }\n            };\n            // 根据提供商使用不同的端点\n            const endpoint = this.provider === \"minimax\" ? `${this.baseUrl}/text/chatcompletion_v2` : `${this.baseUrl}/chat/completions`;\n            // 创建带超时的fetch请求\n            const controller = new AbortController();\n            // 根据模型设置不同的超时时间（智能问卷增加了分析复杂度）\n            const timeout = this.provider === \"deepseek\" ? 480000 : 180000; // DeepSeek 8分钟，MiniMax 3分钟\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            try {\n                const response = await fetch(endpoint, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.apiKey}`\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`LLM API错误 (${response.status}): ${errorText}`);\n                }\n                const result = await response.json();\n                console.log(\"✅ LLM API调用成功\");\n                return result;\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error.name === \"AbortError\") {\n                    throw new Error(\"LLM API调用超时（2分钟）\");\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"❌ LLM API调用失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 解析JSON响应（带容错处理）\n   */ static parseJSONResponse(content) {\n        try {\n            // 尝试直接解析\n            return JSON.parse(content);\n        } catch (e1) {\n            try {\n                // 尝试提取JSON代码块\n                const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n                if (jsonMatch && jsonMatch[1]) {\n                    return JSON.parse(jsonMatch[1]);\n                }\n            } catch (e2) {\n                // 尝试修复常见JSON错误\n                try {\n                    let fixed = content;\n                    // 移除尾随逗号\n                    fixed = fixed.replace(/,\\s*}/g, \"}\");\n                    fixed = fixed.replace(/,\\s*]/g, \"]\");\n                    // 修复未引用的键\n                    fixed = fixed.replace(/(\\w+):/g, '\"$1\":');\n                    return JSON.parse(fixed);\n                } catch (e3) {\n                    console.error(\"JSON解析失败:\", content);\n                    throw new Error(\"无法解析LLM返回的JSON格式\");\n                }\n            }\n        }\n        // 如果所有解析方法都失败，返回null\n        return null;\n    }\n}\n/**\n * 创建LLM客户端实例\n */ function createLLMClient(provider = \"deepseek\") {\n    return new LLMClient(provider);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/llm-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fanalyze%2Froute&page=%2Fapi%2Fassessment%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fanalyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();