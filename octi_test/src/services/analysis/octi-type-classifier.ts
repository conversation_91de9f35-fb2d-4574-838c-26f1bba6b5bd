/**
 * OCTI四维八极组织类型分类器
 * 基于问卷结果快速判断组织类型，无需复杂的LLM调用
 */

export interface OCTITypeResult {
  code: string;
  name: string;
  description: string;
  characteristics: string[];
  strengths: string[];
  challenges: string[];
}

/**
 * OCTI组织类型定义
 */
const OCTI_TYPES: Record<string, OCTITypeResult> = {
  'SFMV': {
    code: 'SFMV',
    name: '战略价值型',
    description: '具有清晰战略定位和强烈价值导向的组织，在使命驱动和专业聚焦方面表现突出。',
    characteristics: [
      '使命陈述清晰明确',
      '价值观深入人心',
      '专业领域聚焦',
      '社会影响力导向'
    ],
    strengths: [
      '战略方向明确',
      '团队价值认同度高',
      '专业能力突出',
      '社会公信力强'
    ],
    challenges: [
      '团队协作机制需完善',
      '适应性发展能力待提升',
      '资源整合效率可优化'
    ]
  },
  'ITAD': {
    code: 'ITAD',
    name: '协同发展型',
    description: '注重团队协作和能力建设的组织，在内部管理和学习发展方面具有优势。',
    characteristics: [
      '团队协作机制完善',
      '学习发展氛围浓厚',
      '内部沟通顺畅',
      '适应变化能力强'
    ],
    strengths: [
      '团队凝聚力强',
      '组织学习能力突出',
      '变革适应性好',
      '内部管理规范'
    ],
    challenges: [
      '战略聚焦度需加强',
      '价值传播影响力待提升',
      '外部资源整合能力需完善'
    ]
  },
  'SFAD': {
    code: 'SFAD',
    name: '战略发展型',
    description: '战略清晰且具备强适应能力的组织，能够在变化中保持发展方向。',
    characteristics: [
      '战略规划完善',
      '发展目标明确',
      '创新能力较强',
      '资源配置合理'
    ],
    strengths: [
      '长期发展规划清晰',
      '创新发展能力强',
      '资源利用效率高',
      '市场适应性好'
    ],
    challenges: [
      '团队协同效率需提升',
      '价值文化建设待加强',
      '利益相关者参与度可提高'
    ]
  },
  'MVIT': {
    code: 'MVIT',
    name: '价值协同型',
    description: '价值导向明确且团队协作良好的组织，注重文化建设和团队凝聚。',
    characteristics: [
      '组织文化浓厚',
      '价值观统一',
      '团队合作默契',
      '志愿者参与度高'
    ],
    strengths: [
      '组织文化凝聚力强',
      '团队协作效率高',
      '价值传播能力突出',
      '社会认同度高'
    ],
    challenges: [
      '战略执行力需加强',
      '创新发展能力待提升',
      '专业技能建设需完善'
    ]
  },
  'BALANCED': {
    code: 'BALANCED',
    name: '均衡发展型',
    description: '四个维度发展相对均衡的组织，具有全面但不突出的特征。',
    characteristics: [
      '各维度发展均衡',
      '综合能力较强',
      '发展潜力较大',
      '适应性较好'
    ],
    strengths: [
      '综合发展能力强',
      '各方面基础扎实',
      '发展潜力大',
      '风险抵御能力较强'
    ],
    challenges: [
      '缺乏突出优势',
      '特色不够鲜明',
      '需要找到发展重点',
      '资源分散风险'
    ]
  }
};

/**
 * 基于问卷结果分类OCTI组织类型
 */
export class OCTITypeClassifier {
  
  /**
   * 根据问卷结果判断组织类型
   */
  static classifyOrganizationType(responses: any[]): OCTITypeResult {
    const scores = this.calculateDimensionScores(responses);
    const typeCode = this.determineTypeCode(scores);
    
    return OCTI_TYPES[typeCode] || OCTI_TYPES['BALANCED'];
  }
  
  /**
   * 计算四个维度的得分
   */
  private static calculateDimensionScores(responses: any[]): {
    SF: number;
    IT: number;
    MV: number;
    AD: number;
  } {
    const scores = { SF: 0, IT: 0, MV: 0, AD: 0 };
    const counts = { SF: 0, IT: 0, MV: 0, AD: 0 };
    
    responses.forEach(response => {
      const dimension = this.getDimensionFromQuestionId(response.questionId);
      if (dimension) {
        const score = this.convertAnswerToScore(response.answer);
        scores[dimension] += score;
        counts[dimension]++;
      }
    });
    
    // 计算平均分
    Object.keys(scores).forEach(dim => {
      const dimension = dim as keyof typeof scores;
      if (counts[dimension] > 0) {
        scores[dimension] = scores[dimension] / counts[dimension];
      } else {
        scores[dimension] = 3; // 默认中等分数
      }
    });
    
    return scores;
  }
  
  /**
   * 从问题ID判断维度
   */
  private static getDimensionFromQuestionId(questionId: string): 'SF' | 'IT' | 'MV' | 'AD' | null {
    if (questionId.startsWith('SF_')) return 'SF';
    if (questionId.startsWith('IT_')) return 'IT';
    if (questionId.startsWith('MV_')) return 'MV';
    if (questionId.startsWith('AD_')) return 'AD';
    return null;
  }
  
  /**
   * 将答案转换为分数
   */
  private static convertAnswerToScore(answer: any): number {
    if (typeof answer === 'number') {
      return Math.max(1, Math.min(5, answer));
    }
    
    if (typeof answer === 'string') {
      const lowerAnswer = answer.toLowerCase();
      if (lowerAnswer.includes('excellent') || lowerAnswer.includes('very_clear')) return 5;
      if (lowerAnswer.includes('good') || lowerAnswer.includes('clear')) return 4;
      if (lowerAnswer.includes('average') || lowerAnswer.includes('moderate')) return 3;
      if (lowerAnswer.includes('poor') || lowerAnswer.includes('unclear')) return 2;
      if (lowerAnswer.includes('very_poor') || lowerAnswer.includes('very_unclear')) return 1;
    }
    
    if (Array.isArray(answer)) {
      // 多选题按选择数量评分
      return Math.min(5, Math.max(1, answer.length));
    }
    
    return 3; // 默认中等分数
  }
  
  /**
   * 根据维度得分确定组织类型代码
   */
  private static determineTypeCode(scores: { SF: number; IT: number; MV: number; AD: number }): string {
    const threshold = 3.5; // 高分阈值
    
    const highDimensions: string[] = [];
    if (scores.SF >= threshold) highDimensions.push('SF');
    if (scores.IT >= threshold) highDimensions.push('IT');
    if (scores.MV >= threshold) highDimensions.push('MV');
    if (scores.AD >= threshold) highDimensions.push('AD');
    
    // 根据高分维度组合确定类型
    if (highDimensions.includes('SF') && highDimensions.includes('MV')) {
      return 'SFMV';
    }
    if (highDimensions.includes('IT') && highDimensions.includes('AD')) {
      return 'ITAD';
    }
    if (highDimensions.includes('SF') && highDimensions.includes('AD')) {
      return 'SFAD';
    }
    if (highDimensions.includes('MV') && highDimensions.includes('IT')) {
      return 'MVIT';
    }
    
    return 'BALANCED';
  }
}
