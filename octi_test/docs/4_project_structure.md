# OCTI智能评估系统 - 项目结构文档

**版本**: 4.0 - 智能体配置架构  
**日期**: 2025年7月27日  
**项目**: 组织能力OCTI评估系统 (Organizational Capacity Type Indicator)  
**架构类型**: 配置驱动 + 智能体模块化

---

## 📋 文档概述

本文档详细描述了OCTI智能评估系统的完整项目结构，基于智能体配置化架构设计，采用Next.js 14 + TypeScript技术栈，实现配置驱动的智能问卷生成与分析系统。

---

## 1. 项目根目录结构

```
octi_test/
├── .augment/                    # Augment AI规则配置
│   └── rules/
│       ├── agent_config_rules.md
│       ├── api_design_rules.md
│       ├── project_rules.md
│       ├── testing_rules.md
│       └── ui_component_rules.md
├── .env                         # 环境变量配置
├── .env.example                 # 环境变量示例
├── .env.local                   # 本地环境变量
├── .eslintrc.json              # ESLint配置
├── .github/                     # GitHub Actions配置
│   └── workflows/
│       └── ci-cd.yml
├── .next/                       # Next.js构建输出
├── .trae/                       # Trae AI规则
│   └── rules/
│       └── project_rules.md
├── .vercel/                     # Vercel部署配置
├── Dockerfile                   # Docker容器配置
├── configs/                     # 智能体配置文件
│   ├── organization_tutor_prompt.json
│   └── question_design_prompt.json
├── development-task.md          # 开发任务清单
├── docker-compose.dev.yml       # 开发环境Docker配置
├── docker-compose.yml           # 生产环境Docker配置
├── docker/                      # Docker相关配置
├── docs/                        # 项目文档
├── memory-bank/                 # 项目记忆库
├── next-env.d.ts               # Next.js类型定义
├── next.config.js              # Next.js配置
├── package.json                # 项目依赖配置
├── package-lock.json           # 依赖锁定文件
├── postcss.config.js           # PostCSS配置
├── prisma/                     # 数据库配置
├── public/                     # 静态资源
├── scripts/                    # 部署和运维脚本
├── src/                        # 源代码目录
├── tailwind.config.ts          # Tailwind CSS配置
├── test-db.js                  # 数据库测试脚本
├── tsconfig.json               # TypeScript配置
└── tsconfig.tsbuildinfo         # TypeScript构建信息
```

---

## 2. 核心配置目录详解

### 2.1 智能体配置目录 (`configs/`)

```
configs/
├── organization_tutor_prompt.json    # 分析智能体配置
└── question_design_prompt.json       # 问卷智能体配置
```

**配置文件说明**:
- `question_design_prompt.json`: 问卷设计师智能体配置，控制问卷生成逻辑
- `organization_tutor_prompt.json`: 组织评估导师智能体配置，控制分析深度和报告结构

### 2.2 Docker配置目录 (`docker/`)

```
docker/
├── grafana/                    # Grafana监控配置
│   └── provisioning/
├── nginx/                      # Nginx代理配置
│   ├── conf.d/
│   ├── dev.conf
│   └── nginx.conf
├── postgres/                   # PostgreSQL配置
│   └── init/
├── prometheus/                 # Prometheus监控配置
│   ├── alert_rules.yml
│   └── prometheus.yml
└── redis/                      # Redis缓存配置
    └── redis.conf
```

### 2.3 数据库配置目录 (`prisma/`)

```
prisma/
├── dev.db                      # 开发数据库文件
├── migrations/                 # 数据库迁移文件
│   ├── 20250728071614_init/
│   ├── 20250729011251_nonprofit_upgrade_v2/
│   ├── 20250729085659_add_organization_types/
│   └── migration_lock.toml
└── schema.prisma               # 数据库模式定义
```

### 2.4 运维脚本目录 (`scripts/`)

```
scripts/
├── backup.sh                  # 数据备份脚本
├── deploy.sh                   # 部署脚本
├── health-check.sh             # 健康检查脚本
├── pre-start-check.sh          # 启动前检查脚本
├── restore.sh                  # 数据恢复脚本
├── start-dev.sh                # 开发环境启动脚本
└── test-dev-server.sh          # 开发服务器测试脚本
```

---

## 3. 源代码目录结构 (`src/`)

### 3.1 应用层目录 (`src/app/`)

```
src/app/
├── api/                        # API路由
│   ├── v1/
│   │   ├── assessments/        # 评估管理API
│   │   ├── agents/             # 智能体服务API
│   │   ├── organizations/      # 组织管理API
│   │   └── reports/            # 报告生成API
│   └── auth/                   # 认证API
├── assessment/                 # 评估相关页面
│   ├── page.tsx               # 评估首页
│   ├── [id]/                  # 动态评估页面
│   │   ├── page.tsx          # 评估详情
│   │   └── report/           # 报告页面
│   │       └── page.tsx
│   └── create/                # 创建评估
│       └── page.tsx
├── dashboard/                  # 仪表板页面
│   ├── page.tsx
│   └── organizations/
│       └── page.tsx
├── nonprofit-profile/          # 公益机构画像页面
│   └── page.tsx
├── questionnaire/              # 问卷相关页面
│   ├── page.tsx               # 问卷首页
│   ├── profile/               # 组织画像收集
│   │   └── page.tsx
│   └── [assessmentId]/        # 问卷答题
│       └── page.tsx
├── questionnaire-batch/        # 批量问卷页面
│   └── page.tsx
├── report/                     # 报告页面
│   └── page.tsx
├── simple-test/                # 简单测试页面
│   └── page.tsx
├── test/                       # 测试页面集合
│   └── page.tsx
├── test-hybrid/                # 混合问卷测试
│   └── page.tsx
├── test-intelligent/           # 智能问卷测试
│   └── page.tsx
├── test-questionnaire/         # 问卷测试
│   └── page.tsx
├── test-stream/                # 流式处理测试
│   └── page.tsx
├── test-stream-questionnaire/  # 流式问卷测试
│   └── page.tsx
├── globals.css                 # 全局样式
├── layout.tsx                  # 根布局组件
├── page.tsx                    # 首页
├── page-lightweight.tsx        # 轻量级首页
└── page-simple.tsx             # 简化首页
```

### 3.2 组件目录 (`src/components/`)

```
src/components/
├── charts/                     # 图表组件
│   ├── OCTIRadarChart.tsx     # OCTI雷达图
│   ├── DimensionChart.tsx     # 维度图表
│   └── ComparisonChart.tsx    # 对比图表
├── forms/                      # 表单组件
│   ├── OrganizationProfileForm.tsx  # 组织画像表单
│   ├── QuestionnaireForm.tsx        # 问卷表单
│   └── AssessmentForm.tsx           # 评估表单
├── home/                       # 首页组件
│   ├── HeroSection.tsx        # 英雄区域
│   ├── FeatureSection.tsx     # 功能介绍
│   └── PricingSection.tsx     # 价格方案
├── layout/                     # 布局组件
│   ├── Header.tsx             # 页头组件
│   ├── Sidebar.tsx            # 侧边栏组件
│   ├── Footer.tsx             # 页脚组件
│   └── Navigation.tsx         # 导航组件
├── questionnaire/              # 问卷组件
│   ├── QuestionRenderer.tsx   # 问题渲染器
│   ├── ProgressIndicator.tsx  # 进度指示器
│   ├── QuestionnaireWizard.tsx # 问卷向导
│   └── ResponseCollector.tsx  # 答案收集器
├── reports/                    # 报告组件
│   ├── ReportViewer.tsx       # 报告查看器
│   ├── AnalysisSection.tsx    # 分析章节
│   ├── RecommendationSection.tsx # 建议章节
│   └── ExportButton.tsx       # 导出按钮
└── ui/                         # 基础UI组件 (shadcn/ui)
    ├── button.tsx             # 按钮组件
    ├── input.tsx              # 输入框组件
    ├── dialog.tsx             # 对话框组件
    ├── card.tsx               # 卡片组件
    ├── chart.tsx              # 图表组件
    ├── progress.tsx           # 进度条组件
    └── toast.tsx              # 提示组件
```

### 3.3 服务层目录 (`src/services/`)

```
src/services/
├── agents/                     # 智能体服务
│   ├── QuestionDesignerAgent.ts    # 问卷设计师智能体
│   ├── OrganizationTutorAgent.ts   # 组织评估导师智能体
│   ├── AgentConfigManager.ts       # 智能体配置管理器
│   └── AgentOrchestrator.ts        # 智能体编排器
├── api/                        # API服务
│   ├── AssessmentService.ts    # 评估服务
│   ├── OrganizationService.ts  # 组织服务
│   ├── ReportService.ts        # 报告服务
│   └── UserService.ts          # 用户服务
├── config/                     # 配置服务
│   ├── ConfigLoader.ts         # 配置加载器
│   ├── ConfigValidator.ts      # 配置验证器
│   └── ConfigVersionManager.ts # 配置版本管理器
├── data/                       # 数据服务
│   ├── DatabaseService.ts      # 数据库服务
│   ├── CacheService.ts         # 缓存服务
│   └── MigrationService.ts     # 迁移服务
├── llm/                        # LLM服务
│   ├── MinimaxClient.ts        # Minimax客户端
│   ├── DeepSeekClient.ts       # DeepSeek客户端
│   ├── LLMProxy.ts             # LLM代理服务
│   └── LLMOrchestrator.ts      # LLM编排器
└── validation/                 # 验证服务
    ├── QuestionnaireValidator.ts # 问卷验证器
    ├── ResponseValidator.ts      # 答案验证器
    └── ConfigValidator.ts        # 配置验证器
```

### 3.4 工具库目录 (`src/lib/`)

```
src/lib/
├── auth/                       # 认证相关
│   ├── jwt.ts                 # JWT工具
│   ├── session.ts             # 会话管理
│   └── permissions.ts         # 权限管理
├── cache/                      # 缓存相关
│   ├── redis.ts               # Redis客户端
│   ├── memory.ts              # 内存缓存
│   └── strategies.ts          # 缓存策略
├── cdn/                        # CDN相关
│   ├── upload.ts              # 文件上传
│   └── optimize.ts            # 资源优化
├── monitoring/                 # 监控相关
│   ├── metrics.ts             # 指标收集
│   ├── alerts.ts              # 告警管理
│   └── tracing.ts             # 链路追踪
├── api-cache.ts               # API缓存
├── api-response.ts            # API响应格式
├── cache.ts                   # 缓存工具
├── logger.ts                  # 日志工具
├── prisma.ts                  # Prisma客户端
└── utils.ts                   # 通用工具
```

### 3.5 中间件目录 (`src/middleware/`)

```
src/middleware/
├── api-gateway.ts              # API网关中间件
├── rate-limiter.ts             # 限流中间件
├── auth.ts                     # 认证中间件
├── cors.ts                     # 跨域中间件
├── logging.ts                  # 日志中间件
└── error-handler.ts            # 错误处理中间件
```

### 3.6 类型定义目录 (`src/types/`)

```
src/types/
├── agents.ts                   # 智能体类型定义
├── config.ts                   # 配置类型定义
├── assessment.ts               # 评估类型定义
├── organization.ts             # 组织类型定义
├── questionnaire.ts            # 问卷类型定义
├── report.ts                   # 报告类型定义
├── api.ts                      # API类型定义
└── index.ts                    # 类型导出
```

### 3.7 数据目录 (`src/data/`)

```
src/data/
├── standard-questions.ts       # 标准问题库
├── organization-types.ts       # 组织类型数据
├── assessment-templates.ts     # 评估模板
└── report-templates.ts         # 报告模板
```

### 3.8 钩子目录 (`src/hooks/`)

```
src/hooks/
├── useQuestionnaire.ts         # 问卷钩子
├── useAssessment.ts            # 评估钩子
├── useOrganization.ts          # 组织钩子
├── useAuth.ts                  # 认证钩子
├── useCache.ts                 # 缓存钩子
└── useAnalytics.ts             # 分析钩子
```

### 3.9 常量目录 (`src/constants/`)

```
src/constants/
├── api.ts                      # API常量
├── cache.ts                    # 缓存常量
├── config.ts                   # 配置常量
├── dimensions.ts               # 维度常量
├── questions.ts                # 问题常量
└── routes.ts                   # 路由常量
```

### 3.10 测试目录 (`src/test/`)

```
src/test/
├── enhanced-json-processor-test.js      # 增强JSON处理器测试
├── intelligent-questionnaire-test.ts    # 智能问卷测试
├── questionnaire-generation-test.js     # 问卷生成测试
├── simple-questionnaire-test.js         # 简单问卷测试
├── unit/                                # 单元测试
│   ├── agents/
│   ├── services/
│   └── utils/
├── integration/                         # 集成测试
│   ├── api/
│   └── database/
└── e2e/                                # 端到端测试
    ├── assessment-flow.test.ts
    └── questionnaire-flow.test.ts
```

---

## 4. 关键架构模块说明

### 4.1 智能体配置系统

**核心文件**:
- `configs/question_design_prompt.json`: 问卷设计师配置
- `configs/organization_tutor_prompt.json`: 组织评估导师配置
- `src/services/config/ConfigManager.ts`: 配置管理器

**功能特性**:
- 配置热更新，无需重启系统
- 版本控制和回滚机制
- JSON Schema验证
- A/B测试支持

### 4.2 混合问卷系统

**核心文件**:
- `src/data/standard-questions.ts`: 32道预设标准题目
- `src/services/agents/QuestionDesignerAgent.ts`: 智能问卷生成
- `src/components/questionnaire/`: 问卷渲染组件

**工作流程**:
1. 组织画像收集（10题）
2. 加载32道预设题目
3. 基于画像生成28道个性化题目
4. 融合生成60题完整问卷

### 4.3 双模型分析系统

**核心文件**:
- `src/services/llm/MinimaxClient.ts`: Minimax模型客户端
- `src/services/llm/DeepSeekClient.ts`: DeepSeek模型客户端
- `src/services/agents/OrganizationTutorAgent.ts`: 分析智能体

**分析策略**:
- 标准版：单一模型分析（Minimax）
- 专业版：双模型协作分析（Minimax + DeepSeek）
- 交叉验证和结果融合

### 4.4 缓存优化系统

**核心文件**:
- `src/lib/cache/redis.ts`: Redis缓存客户端
- `src/lib/cache/strategies.ts`: 缓存策略
- `src/lib/api-cache.ts`: API缓存

**缓存层级**:
- L1: 内存缓存（组件级）
- L2: Redis缓存（应用级）
- L3: CDN缓存（静态资源）

### 4.5 安全代理系统

**核心文件**:
- `src/services/llm/LLMProxy.ts`: LLM API代理
- `src/middleware/auth.ts`: 认证中间件
- `src/lib/auth/`: 认证工具库

**安全特性**:
- API密钥后端代理
- JWT认证机制
- 请求限流保护
- 数据加密存储

---

## 5. 开发环境配置

### 5.1 环境变量配置

**必需环境变量** (`.env`):
```bash
# 数据库配置
DATABASE_URL="postgresql://..."
REDIS_URL="redis://..."

# LLM API配置
MINIMAX_API_KEY="your_minimax_key"
DEEPSEEK_API_KEY="your_deepseek_key"

# 认证配置
JWT_SECRET="your_jwt_secret"
NEXTAUTH_SECRET="your_nextauth_secret"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NODE_ENV="development"
```

### 5.2 开发脚本

**package.json核心脚本**:
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio",
    "test": "jest",
    "test:e2e": "playwright test",
    "docker:dev": "docker-compose -f docker-compose.dev.yml up",
    "docker:prod": "docker-compose up"
  }
}
```

### 5.3 开发工作流

1. **环境准备**:
   ```bash
   npm install
   cp .env.example .env
   # 配置环境变量
   ```

2. **数据库初始化**:
   ```bash
   npm run db:push
   npm run db:migrate
   ```

3. **启动开发服务器**:
   ```bash
   npm run dev
   # 或使用Docker
   npm run docker:dev
   ```

4. **代码质量检查**:
   ```bash
   npm run lint
   npm run test
   ```

---

## 6. 部署架构

### 6.1 Docker容器化

**多阶段构建** (`Dockerfile`):
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 运行阶段
FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 6.2 微服务架构

**服务拆分**:
- **Web服务**: Next.js应用主体
- **API服务**: 智能体和业务逻辑
- **数据库服务**: PostgreSQL + Redis
- **监控服务**: Prometheus + Grafana
- **代理服务**: Nginx负载均衡

### 6.3 CI/CD流水线

**GitHub Actions** (`.github/workflows/ci-cd.yml`):
1. 代码检查和测试
2. Docker镜像构建
3. 安全扫描
4. 自动化部署
5. 健康检查

---

## 7. 监控与运维

### 7.1 监控体系

**监控组件**:
- **应用监控**: `src/lib/monitoring/metrics.ts`
- **性能监控**: API响应时间、数据库查询性能
- **业务监控**: 评估完成率、用户满意度
- **基础设施监控**: 服务器资源、网络状态

### 7.2 日志系统

**日志分级**:
- **ERROR**: 系统错误和异常
- **WARN**: 警告信息和性能问题
- **INFO**: 业务流程和关键操作
- **DEBUG**: 调试信息和详细日志

### 7.3 备份策略

**备份脚本** (`scripts/backup.sh`):
- 数据库定期备份
- 配置文件版本控制
- 用户数据加密存储
- 灾难恢复预案

---

## 8. 扩展性设计

### 8.1 水平扩展

**扩展策略**:
- **无状态设计**: 应用服务器可水平扩展
- **数据库分片**: 支持读写分离和分库分表
- **缓存集群**: Redis集群支持
- **CDN加速**: 静态资源全球分发

### 8.2 功能扩展

**扩展点**:
- **新智能体**: 通过配置文件添加新的智能体类型
- **新评估模型**: 支持插件化评估算法
- **多语言支持**: 国际化框架集成
- **第三方集成**: 开放API接口

### 8.3 性能优化

**优化策略**:
- **代码分割**: 按需加载减少首屏时间
- **图片优化**: WebP格式和懒加载
- **API优化**: GraphQL和批量请求
- **缓存优化**: 多级缓存和智能预热

---

## 9. 安全考虑

### 9.1 数据安全

**安全措施**:
- **数据加密**: 敏感数据AES-256加密
- **传输安全**: HTTPS和TLS 1.3
- **访问控制**: RBAC权限模型
- **审计日志**: 操作记录和追踪

### 9.2 API安全

**安全策略**:
- **认证授权**: JWT + OAuth 2.0
- **请求限流**: 防止API滥用
- **输入验证**: 严格的参数校验
- **错误处理**: 安全的错误信息返回

---

## 10. 总结

OCTI智能评估系统采用现代化的技术架构，通过智能体配置化设计实现了高度的灵活性和可扩展性。项目结构清晰，模块化程度高，支持快速迭代和持续优化。

**核心优势**:
1. **配置驱动**: 业务逻辑与代码解耦，支持快速调整
2. **智能体模块化**: 独立的问卷生成和分析智能体
3. **混合问卷设计**: 标准化与个性化的完美结合
4. **双模型协作**: 提供差异化的分析深度
5. **安全可靠**: 完善的安全机制和监控体系

**技术亮点**:
- Next.js 14 + TypeScript全栈开发
- Prisma ORM + PostgreSQL数据管理
- Redis多级缓存优化
- Docker容器化部署
- 智能体配置热更新
- 双模型LLM集成

该项目结构为OCTI智能评估系统的成功实施提供了坚实的技术基础，支持从MVP到规模化产品的平滑演进。