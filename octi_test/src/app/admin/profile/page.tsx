/**
 * OCTI智能评估系统 - 用户资料页面
 * 
 * 用户个人信息管理界面
 */

'use client';

import React, { useState } from 'react';
import { AdminLayout, AdminPageContainer } from '@/components/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// ============================================================================
// 简单组件替代
// ============================================================================

const CardContainer = ({ title, description, children }: {
  title?: string;
  description?: string;
  children: React.ReactNode;
}) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    {(title || description) && (
      <div className="mb-4">
        {title && <h3 className="text-lg font-semibold text-gray-900">{title}</h3>}
        {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
      </div>
    )}
    {children}
  </div>
);

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
  avatar: '',
  profile: {
    phone: '+86 138 0013 8000',
    title: '项目经理',
    bio: '专注于公益项目管理和组织发展，有5年公益行业经验。',
    location: '北京市',
    website: 'https://example.com',
    linkedin: 'https://linkedin.com/in/zhangsan',
    organization: {
      name: '北京环保基金会',
      type: 'FOUNDATION',
      role: 'manager',
    },
  },
  preferences: {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    emailNotifications: true,
    smsNotifications: false,
    weeklyReport: true,
  },
  stats: {
    assessmentsCompleted: 12,
    organizationsManaged: 3,
    reportsGenerated: 8,
    joinDate: '2023-06-15',
  },
};

// ============================================================================
// 用户资料页面组件
// ============================================================================

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: mockUser.name,
    email: mockUser.email,
    phone: mockUser.profile.phone,
    title: mockUser.profile.title,
    bio: mockUser.profile.bio,
    location: mockUser.profile.location,
    website: mockUser.profile.website,
    linkedin: mockUser.profile.linkedin,
  });
  const [preferences, setPreferences] = useState(mockUser.preferences);
  const [message, setMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePreferenceChange = (key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: 实现实际的保存逻辑
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage('个人资料已更新');
      setIsEditing(false);
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setMessage('保存失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: mockUser.name,
      email: mockUser.email,
      phone: mockUser.profile.phone,
      title: mockUser.profile.title,
      bio: mockUser.profile.bio,
      location: mockUser.profile.location,
      website: mockUser.profile.website,
      linkedin: mockUser.profile.linkedin,
    });
    setIsEditing(false);
  };

  const adminUser = {
    name: '管理员',
    email: '<EMAIL>',
  };

  return (
    <AdminLayout user={adminUser}>
      <AdminPageContainer
        title="个人资料"
        description="管理您的个人信息和系统偏好设置"
        actions={
          !isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              编辑资料
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button onClick={handleSave} disabled={isLoading}>
                {isLoading ? '保存中...' : '保存'}
              </Button>
            </div>
          )
        }
      >
        {message && (
          <Alert className="mb-6">
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 md:grid-cols-3">
          {/* 基本信息 */}
          <div className="md:col-span-2 space-y-6">
            <CardContainer title="基本信息" description="您的个人基本信息">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">姓名</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">电话</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="title">职位</Label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">个人简介</Label>
                  <Textarea
                    id="bio"
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="location">所在地</Label>
                    <Input
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">个人网站</Label>
                    <Input
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            </CardContainer>

            {/* 系统偏好 */}
            <CardContainer title="系统偏好" description="个性化您的系统体验">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>语言</Label>
                    <Select
                      value={preferences.language}
                      onValueChange={(value) => handlePreferenceChange('language', value)}
                      disabled={!isEditing}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="zh-CN">简体中文</SelectItem>
                        <SelectItem value="en-US">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>时区</Label>
                    <Select
                      value={preferences.timezone}
                      onValueChange={(value) => handlePreferenceChange('timezone', value)}
                      disabled={!isEditing}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Shanghai">北京时间</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>通知设置</Label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">邮件通知</span>
                      <Button
                        variant={preferences.emailNotifications ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePreferenceChange('emailNotifications', !preferences.emailNotifications)}
                        disabled={!isEditing}
                      >
                        {preferences.emailNotifications ? '开启' : '关闭'}
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">短信通知</span>
                      <Button
                        variant={preferences.smsNotifications ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePreferenceChange('smsNotifications', !preferences.smsNotifications)}
                        disabled={!isEditing}
                      >
                        {preferences.smsNotifications ? '开启' : '关闭'}
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">周报推送</span>
                      <Button
                        variant={preferences.weeklyReport ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePreferenceChange('weeklyReport', !preferences.weeklyReport)}
                        disabled={!isEditing}
                      >
                        {preferences.weeklyReport ? '开启' : '关闭'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContainer>
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 组织信息 */}
            <CardContainer title="所属组织">
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium">{mockUser.profile.organization.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {mockUser.profile.organization.type === 'FOUNDATION' ? '基金会' : '其他'}
                  </p>
                </div>
                <Badge variant="outline">
                  {mockUser.profile.organization.role === 'manager' ? '项目经理' : '其他'}
                </Badge>
              </div>
            </CardContainer>

            {/* 统计信息 */}
            <CardContainer title="使用统计">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">完成评估</span>
                  <span className="font-medium">{mockUser.stats.assessmentsCompleted}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">管理组织</span>
                  <span className="font-medium">{mockUser.stats.organizationsManaged}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">生成报告</span>
                  <span className="font-medium">{mockUser.stats.reportsGenerated}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">加入时间</span>
                  <span className="font-medium">{mockUser.stats.joinDate}</span>
                </div>
              </div>
            </CardContainer>

            {/* 快捷操作 */}
            <CardContainer title="快捷操作">
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  🔒 修改密码
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  📧 验证邮箱
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  📱 绑定手机
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  🔗 连接社交账户
                </Button>
              </div>
            </CardContainer>
          </div>
        </div>
      </AdminPageContainer>
    </AdminLayout>
  );
}
