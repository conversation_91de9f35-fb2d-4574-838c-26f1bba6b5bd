'use client';

import React from 'react';
import { SimpleMermaidRenderer } from './mermaid-diagram';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

/**
 * 清理和格式化分析文本
 * 去除 Markdown 符号，优化分行和层次结构
 */
function cleanAnalysisText(text: string): string {
  return text
    // 去除 Markdown 格式符号
    .replace(/\*\*(.*?)\*\*/g, '$1')  // 去除粗体标记
    .replace(/\*(.*?)\*/g, '$1')      // 去除斜体标记
    .replace(/#{1,6}\s*/g, '')        // 去除标题标记
    .replace(/`(.*?)`/g, '$1')        // 去除代码标记

    // 优化分行和格式
    .replace(/---+/g, '\n---\n')      // 分隔线前后加换行
    .replace(/(\d+\.\s*\*\*[^*]+\*\*)/g, '\n$1')  // 数字标题前加换行
    .replace(/(\*\*[^*]+\*\*：)/g, '\n$1')         // 粗体标题后加换行
    .replace(/(\*\*[^*]+\*\*)/g, '\n$1\n')        // 独立粗体标题前后加换行

    // 处理列表项
    .replace(/^(\s*)-\s+/gm, '\n- ')   // 列表项前加换行
    .replace(/^(\s*)•\s+/gm, '\n- ')   // 统一列表符号

    // 清理多余的换行
    .replace(/\n{3,}/g, '\n\n')        // 多个换行合并为两个
    .trim();
}

/**
 * Markdown渲染组件
 * 专门处理OCTI分析结果中的表格、列表等格式
 */
export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {

  // 首先清理和格式化文本
  const cleanedContent = cleanAnalysisText(content);
  
  /**
   * 解析并渲染表格
   */
  const renderTable = (tableText: string) => {
    const lines = tableText.trim().split('\n');
    if (lines.length < 3) return null; // 至少需要标题行、分隔行、数据行
    
    // 解析表头
    const headers = lines[0].split('|').map(h => h.trim()).filter(h => h);
    
    // 解析数据行（跳过分隔行）
    const rows = lines.slice(2).map(line => 
      line.split('|').map(cell => cell.trim()).filter(cell => cell)
    ).filter(row => row.length > 0);
    
    return (
      <div className="overflow-x-auto my-6">
        <table className="min-w-full border-collapse border border-gray-300 bg-white rounded-lg shadow-sm">
          <thead className="bg-gradient-to-r from-blue-50 to-purple-50">
            <tr>
              {headers.map((header, index) => (
                <th 
                  key={index} 
                  className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                {row.map((cell, cellIndex) => (
                  <td 
                    key={cellIndex} 
                    className="border border-gray-300 px-4 py-3 text-sm text-gray-600"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  /**
   * 解析并渲染Mermaid图表
   */
  const renderMermaid = (mermaidCode: string, index: number) => {
    // 提取mermaid代码块内容
    const codeMatch = mermaidCode.match(/```mermaid\s*([\s\S]*?)\s*```/);
    if (!codeMatch) return null;

    const diagramCode = codeMatch[1].trim();

    return (
      <SimpleMermaidRenderer
        key={`mermaid-${index}`}
        code={diagramCode}
        className="my-6"
      />
    );
  };

  /**
   * 解析并渲染内容
   */
  const renderContent = () => {
    // 分割清理后的内容为段落
    const paragraphs = cleanedContent.split('\n\n');

    return paragraphs.map((paragraph, index) => {
      const trimmed = paragraph.trim();
      if (!trimmed) return null;

      // 检查是否为Mermaid图表
      if (trimmed.includes('```mermaid')) {
        return renderMermaid(trimmed, index);
      }

      // 检查是否为表格
      if (trimmed.includes('|') && trimmed.includes('---')) {
        return (
          <div key={index}>
            {renderTable(trimmed)}
          </div>
        );
      }

      // 检查是否为分隔线
      if (trimmed === '---') {
        return (
          <hr key={index} className="my-6 border-gray-300" />
        );
      }

      // 检查是否为标题
      if (trimmed.match(/^[一二三四五六七八九十]+、/)) {
        return (
          <h3 key={index} className="text-lg font-semibold text-gray-800 mt-6 mb-3 border-l-4 border-blue-500 pl-3">
            {trimmed}
          </h3>
        );
      }

      // 检查是否为子标题
      if (trimmed.match(/^\d+\.\s+/)) {
        return (
          <h4 key={index} className="text-md font-medium text-gray-700 mt-5 mb-3 bg-gray-50 p-3 rounded-lg border-l-4 border-gray-400">
            {trimmed}
          </h4>
        );
      }

      // 检查是否为重要标题（如"当前表现："、"主要优势："等）
      if (trimmed.match(/^(当前表现|主要优势|改进空间|核心建议|分析说明)：?\s*$/)) {
        return (
          <h5 key={index} className="text-sm font-semibold text-green-700 mt-4 mb-2 uppercase tracking-wide bg-green-50 px-2 py-1 rounded">
            {trimmed}
          </h5>
        );
      }

      // 检查是否为列表项
      if (trimmed.startsWith('- ') || trimmed.startsWith('• ')) {
        const items = trimmed.split('\n').filter(line => line.trim() && (line.startsWith('- ') || line.startsWith('• ')));
        return (
          <ul key={index} className="list-none space-y-2 my-3 ml-4">
            {items.map((item, itemIndex) => (
              <li key={itemIndex} className="text-gray-600 flex items-start">
                <span className="text-blue-500 mr-2 mt-1">•</span>
                <span>{item.replace(/^[-•]\s*/, '')}</span>
              </li>
            ))}
          </ul>
        );
      }

      // 检查是否为带冒号的描述性内容
      if (trimmed.includes('：') && !trimmed.match(/^(当前表现|主要优势|改进空间|核心建议|分析说明)：?\s*$/)) {
        const [label, ...contentParts] = trimmed.split('：');
        const content = contentParts.join('：');
        return (
          <div key={index} className="my-3">
            <span className="font-medium text-gray-700">{label}：</span>
            <span className="text-gray-600 ml-1">{content}</span>
          </div>
        );
      }

      // 普通段落
      return (
        <p key={index} className="text-gray-600 leading-relaxed my-3 text-justify">
          {trimmed}
        </p>
      );
    }).filter(Boolean);
  };

  return (
    <div className={`markdown-content ${className}`}>
      {renderContent()}
    </div>
  );
}
