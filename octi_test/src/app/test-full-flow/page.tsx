'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Loader2, Play, CheckCircle, XCircle, Clock, ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';

interface TestStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'error';
  duration?: number;
  error?: string;
  data?: any;
}

interface TestResult {
  totalSteps: number;
  completedSteps: number;
  successSteps: number;
  errorSteps: number;
  totalDuration: number;
  status: 'idle' | 'running' | 'completed' | 'error';
}

/**
 * OCTI全流程自动化测试脚本
 */
export default function TestFullFlowPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<TestStep[]>([]);
  const [result, setResult] = useState<TestResult | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const logsRef = useRef<HTMLDivElement>(null);

  // 测试步骤定义
  const testSteps: Omit<TestStep, 'status' | 'duration' | 'error' | 'data'>[] = [
    {
      id: 'init',
      name: '初始化测试环境',
      description: '清理localStorage，准备测试数据'
    },
    {
      id: 'profile',
      name: '生成组织画像',
      description: '模拟用户填写组织信息和画像问题'
    },
    {
      id: 'questionnaire-load',
      name: '加载问卷页面',
      description: '测试32道预设题目立即加载'
    },
    {
      id: 'intelligent-generation',
      name: '智能问卷生成',
      description: '后台生成28道智能题目（XML+分批+并发）'
    },
    {
      id: 'questionnaire-simulation',
      name: '模拟答题过程',
      description: '自动回答60道题目并保存'
    },
    {
      id: 'data-persistence',
      name: '数据持久化测试',
      description: '验证答案保存到服务器'
    },
    {
      id: 'standard-analysis',
      name: '标准版分析',
      description: '测试MiniMax标准分析功能'
    },
    {
      id: 'professional-analysis',
      name: '专业版分析',
      description: '测试MiniMax+DeepSeek双模型分析'
    },
    {
      id: 'data-recovery',
      name: '数据恢复测试',
      description: '清除本地数据，测试服务器恢复'
    },
    {
      id: 'report-generation',
      name: '报告生成验证',
      description: '验证最终分析报告完整性'
    }
  ];

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    setLogs(prev => [...prev, logMessage]);
    console.log(logMessage);
    
    // 自动滚动到底部
    setTimeout(() => {
      if (logsRef.current) {
        logsRef.current.scrollTop = logsRef.current.scrollHeight;
      }
    }, 100);
  };

  const updateStep = (stepIndex: number, updates: Partial<TestStep>) => {
    setSteps(prev => prev.map((step, index) => 
      index === stepIndex ? { ...step, ...updates } : step
    ));
  };

  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  /**
   * 执行单个测试步骤
   */
  const executeStep = async (stepIndex: number): Promise<boolean> => {
    const step = testSteps[stepIndex];
    const startTime = Date.now();
    
    updateStep(stepIndex, { status: 'running' });
    addLog(`🚀 开始执行: ${step.name}`);
    
    try {
      let success = false;
      
      switch (step.id) {
        case 'init':
          success = await testInit();
          break;
        case 'profile':
          success = await testProfile();
          break;
        case 'questionnaire-load':
          success = await testQuestionnaireLoad();
          break;
        case 'intelligent-generation':
          success = await testIntelligentGeneration();
          break;
        case 'questionnaire-simulation':
          success = await testQuestionnaireSimulation();
          break;
        case 'data-persistence':
          success = await testDataPersistence();
          break;
        case 'standard-analysis':
          success = await testStandardAnalysis();
          break;
        case 'professional-analysis':
          success = await testProfessionalAnalysis();
          break;
        case 'data-recovery':
          success = await testDataRecovery();
          break;
        case 'report-generation':
          success = await testReportGeneration();
          break;
        default:
          throw new Error(`未知的测试步骤: ${step.id}`);
      }
      
      const duration = Date.now() - startTime;
      
      if (success) {
        updateStep(stepIndex, { status: 'success', duration });
        addLog(`✅ 完成: ${step.name} (${duration}ms)`);
        return true;
      } else {
        updateStep(stepIndex, { status: 'error', duration, error: '测试失败' });
        addLog(`❌ 失败: ${step.name} (${duration}ms)`);
        return false;
      }
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      updateStep(stepIndex, { status: 'error', duration, error: errorMessage });
      addLog(`❌ 错误: ${step.name} - ${errorMessage} (${duration}ms)`);
      return false;
    }
  };

  /**
   * 运行完整测试流程
   */
  const runFullTest = async () => {
    setIsRunning(true);
    setCurrentStep(0);
    setLogs([]);
    
    // 初始化步骤状态
    const initialSteps = testSteps.map(step => ({
      ...step,
      status: 'pending' as const
    }));
    setSteps(initialSteps);
    
    const startTime = Date.now();
    let successCount = 0;
    let errorCount = 0;
    
    addLog('🧪 开始OCTI全流程自动化测试');
    
    // 逐步执行测试
    for (let i = 0; i < testSteps.length; i++) {
      setCurrentStep(i);
      const success = await executeStep(i);
      
      if (success) {
        successCount++;
      } else {
        errorCount++;
        // 可以选择在错误时继续或停止
        // break; // 如果要在第一个错误时停止
      }
      
      // 步骤间短暂延迟
      await sleep(500);
    }
    
    const totalDuration = Date.now() - startTime;
    
    setResult({
      totalSteps: testSteps.length,
      completedSteps: successCount + errorCount,
      successSteps: successCount,
      errorSteps: errorCount,
      totalDuration,
      status: errorCount === 0 ? 'completed' : 'error'
    });
    
    addLog(`🏁 测试完成: ${successCount}/${testSteps.length} 成功, 总耗时 ${(totalDuration/1000).toFixed(1)}秒`);
    setIsRunning(false);
  };

  // 测试步骤实现函数
  const testInit = async (): Promise<boolean> => {
    localStorage.clear();
    addLog('🧹 清理localStorage完成');
    await sleep(500);
    return true;
  };

  const testProfile = async (): Promise<boolean> => {
    const mockProfile = {
      organizationType: '社会服务机构',
      serviceArea: '教育支持',
      resourceStructure: '基金会支持型',
      developmentStage: '成长期',
      teamSize: '中型（21-50人）',
      operatingModel: '直接服务型',
      impactScope: '区域影响',
      organizationCulture: '使命驱动',
      challengesPriorities: '资金筹集',
      futureVision: '成为区域领先的教育支持机构'
    };
    
    localStorage.setItem('profileAnswers', JSON.stringify(mockProfile));
    addLog('📋 组织画像数据生成完成');
    await sleep(1000);
    return true;
  };

  const testQuestionnaireLoad = async (): Promise<boolean> => {
    // 模拟加载问卷页面
    addLog('📄 模拟加载问卷页面...');
    await sleep(1500);
    addLog('✅ 32道预设题目加载完成');
    return true;
  };

  const testIntelligentGeneration = async (): Promise<boolean> => {
    const profileData = localStorage.getItem('profileAnswers');
    if (!profileData) {
      throw new Error('缺少组织画像数据');
    }
    
    const profile = JSON.parse(profileData);
    addLog('🤖 开始调用智能问卷生成API...');
    
    const response = await fetch('/api/questionnaire/generate-background', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ profile }),
    });
    
    if (!response.ok) {
      throw new Error(`智能生成API失败: ${response.status}`);
    }
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || '智能生成失败');
    }
    
    const questions = result.data.questions;
    addLog(`✅ 智能生成完成: ${questions.length}道题目`);
    
    // 保存生成的题目
    localStorage.setItem('intelligentQuestions', JSON.stringify(questions));
    return true;
  };

  const testQuestionnaireSimulation = async (): Promise<boolean> => {
    // 模拟生成60道题目的答案
    const mockResponses = [];
    
    // 32道预设题目答案
    for (let i = 1; i <= 32; i++) {
      mockResponses.push({
        questionId: `PRESET_${i.toString().padStart(3, '0')}`,
        answer: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
        confidence: Math.floor(Math.random() * 3) + 3, // 3-5
        timeSpent: Math.floor(Math.random() * 30) + 10 // 10-40秒
      });
    }
    
    // 28道智能题目答案
    const intelligentQuestions = JSON.parse(localStorage.getItem('intelligentQuestions') || '[]');
    intelligentQuestions.forEach((q: any, index: number) => {
      mockResponses.push({
        questionId: q.id,
        answer: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
        confidence: Math.floor(Math.random() * 3) + 3,
        timeSpent: Math.floor(Math.random() * 40) + 15
      });
    });
    
    localStorage.setItem('assessmentResponses', JSON.stringify(mockResponses));
    addLog(`📝 模拟答题完成: ${mockResponses.length}道题目`);
    await sleep(2000);
    return true;
  };

  const testDataPersistence = async (): Promise<boolean> => {
    const sessionId = 'test-session-' + Date.now();
    const profileData = localStorage.getItem('profileAnswers');
    const responsesData = localStorage.getItem('assessmentResponses');
    
    if (!profileData || !responsesData) {
      throw new Error('缺少测试数据');
    }
    
    const response = await fetch('/api/assessment/responses', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sessionId,
        profile: JSON.parse(profileData),
        responses: JSON.parse(responsesData),
        status: 'COMPLETED'
      }),
    });
    
    if (!response.ok) {
      throw new Error(`数据保存失败: ${response.status}`);
    }
    
    localStorage.setItem('assessmentSessionId', sessionId);
    addLog('💾 数据持久化保存成功');
    return true;
  };

  const testStandardAnalysis = async (): Promise<boolean> => {
    const profileData = localStorage.getItem('profileAnswers');
    const responsesData = localStorage.getItem('assessmentResponses');
    
    if (!profileData || !responsesData) {
      throw new Error('缺少分析数据');
    }
    
    addLog('🔍 开始标准版分析...');
    
    const response = await fetch('/api/assessment/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        profile: JSON.parse(profileData),
        responses: JSON.parse(responsesData),
        version: 'standard'
      }),
    });
    
    if (!response.ok) {
      throw new Error(`标准版分析失败: ${response.status}`);
    }
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || '标准版分析失败');
    }
    
    localStorage.setItem('standardAnalysisResult', JSON.stringify(result.data));
    addLog('✅ 标准版分析完成');
    return true;
  };

  const testProfessionalAnalysis = async (): Promise<boolean> => {
    const profileData = localStorage.getItem('profileAnswers');
    const responsesData = localStorage.getItem('assessmentResponses');
    
    if (!profileData || !responsesData) {
      throw new Error('缺少分析数据');
    }
    
    addLog('🔍 开始专业版分析（可能需要8分钟）...');
    
    const response = await fetch('/api/assessment/professional-analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        profile: JSON.parse(profileData),
        responses: JSON.parse(responsesData),
        version: 'professional'
      }),
    });
    
    if (!response.ok) {
      throw new Error(`专业版分析失败: ${response.status}`);
    }
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || '专业版分析失败');
    }
    
    localStorage.setItem('professionalAnalysisResult', JSON.stringify(result.data));
    addLog('✅ 专业版分析完成');
    return true;
  };

  const testDataRecovery = async (): Promise<boolean> => {
    const sessionId = localStorage.getItem('assessmentSessionId');
    if (!sessionId) {
      throw new Error('缺少sessionId');
    }
    
    // 清除本地数据
    localStorage.removeItem('profileAnswers');
    localStorage.removeItem('assessmentResponses');
    addLog('🗑️ 清除本地数据');
    
    // 从服务器恢复
    const response = await fetch(`/api/assessment/responses?sessionId=${sessionId}`);
    if (!response.ok) {
      throw new Error(`数据恢复失败: ${response.status}`);
    }
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || '数据恢复失败');
    }
    
    // 恢复数据
    localStorage.setItem('profileAnswers', JSON.stringify(result.data.profile));
    localStorage.setItem('assessmentResponses', JSON.stringify(result.data.responses));
    addLog('🔄 数据恢复成功');
    return true;
  };

  const testReportGeneration = async (): Promise<boolean> => {
    const standardResult = localStorage.getItem('standardAnalysisResult');
    const professionalResult = localStorage.getItem('professionalAnalysisResult');

    if (!standardResult || !professionalResult) {
      throw new Error('缺少分析结果');
    }

    const standard = JSON.parse(standardResult);
    const professional = JSON.parse(professionalResult);

    addLog(`📊 标准版结果字段: ${Object.keys(standard).join(', ')}`);
    addLog(`📊 专业版结果字段: ${Object.keys(professional).join(', ')}`);

    // 验证报告完整性 - 标准版和专业版使用不同的字段验证
    const standardRequiredFields = ['overallScore', 'level', 'dimensions', 'recommendations'];
    const professionalRequiredFields = ['organizationType', 'basicAnalysis', 'enhancedAnalysis', 'timestamp', 'version'];

    const standardValid = standardRequiredFields.every(field => {
      const hasField = standard.hasOwnProperty(field);
      if (!hasField) {
        addLog(`⚠️ 标准版缺少字段: ${field}`);
      }
      return hasField;
    });

    const professionalValid = professionalRequiredFields.every(field => {
      const hasField = professional.hasOwnProperty(field);
      if (!hasField) {
        addLog(`⚠️ 专业版缺少字段: ${field}`);
      }
      return hasField;
    });

    if (!standardValid || !professionalValid) {
      throw new Error(`报告数据不完整 - 标准版:${standardValid}, 专业版:${professionalValid}`);
    }

    // 验证标准版数据类型和内容
    if (typeof standard.overallScore !== 'number' || standard.overallScore < 0 || standard.overallScore > 100) {
      throw new Error('标准版总分数据异常');
    }

    if (!standard.dimensions || typeof standard.dimensions !== 'object') {
      throw new Error('标准版维度数据异常');
    }

    // 验证专业版数据类型和内容（专业版返回的是字符串类型的分析）
    if (!professional.basicAnalysis || typeof professional.basicAnalysis !== 'string') {
      throw new Error('专业版基础分析数据异常');
    }

    if (!professional.enhancedAnalysis || typeof professional.enhancedAnalysis !== 'string') {
      throw new Error('专业版增强分析数据异常');
    }

    if (!professional.organizationType || typeof professional.organizationType !== 'object' || !professional.organizationType.code) {
      throw new Error('专业版组织类型数据异常');
    }

    // 尝试从专业版数据中提取分数信息
    let professionalScore = 'N/A';
    if (professional.basicAnalysis && professional.basicAnalysis.overallScore) {
      professionalScore = professional.basicAnalysis.overallScore;
    } else if (professional.enhancedAnalysis && professional.enhancedAnalysis.overallScore) {
      professionalScore = professional.enhancedAnalysis.overallScore;
    }

    addLog(`✅ 标准版总分: ${standard.overallScore}, 等级: ${standard.level}`);
    addLog(`✅ 专业版组织类型: ${professional.organizationType.code} - ${professional.organizationType.name}`);
    addLog(`✅ 专业版版本: ${professional.version}, 完成时间: ${professional.timestamp}`);
    addLog(`✅ 专业版基础分析长度: ${professional.basicAnalysis.length} 字符`);
    addLog(`✅ 专业版增强分析长度: ${professional.enhancedAnalysis.length} 字符`);
    addLog('📊 报告完整性验证通过');
    return true;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <Link href="/test-llm">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回测试页面
          </Button>
        </Link>
        <Link href="/">
          <Button variant="outline" size="sm">
            返回首页
          </Button>
        </Link>
      </div>

      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">🧪 OCTI全流程自动化测试</h1>
        <p className="text-muted-foreground">
          自动化测试完整的评估流程：组织画像 → 智能问卷 → 数据存储 → 双模型分析 → 报告生成
        </p>
      </div>

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            测试控制面板
          </CardTitle>
          <CardDescription>
            点击开始按钮运行完整的自动化测试流程
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={runFullTest}
              disabled={isRunning}
              className="flex-1"
              size="lg"
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  测试进行中...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  开始全流程测试
                </>
              )}
            </Button>
            
            <Button 
              onClick={() => {
                setSteps([]);
                setResult(null);
                setLogs([]);
                setCurrentStep(0);
              }}
              variant="outline"
              disabled={isRunning}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              重置
            </Button>
          </div>

          {/* 进度条 */}
          {isRunning && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>步骤 {currentStep + 1} / {testSteps.length}</span>
                <span>{Math.round(((currentStep + 1) / testSteps.length) * 100)}%</span>
              </div>
              <Progress value={((currentStep + 1) / testSteps.length) * 100} />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.status === 'completed' ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              测试结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{result.totalSteps}</div>
                <div className="text-sm text-muted-foreground">总步骤</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{result.successSteps}</div>
                <div className="text-sm text-muted-foreground">成功</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{result.errorSteps}</div>
                <div className="text-sm text-muted-foreground">失败</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{(result.totalDuration / 1000).toFixed(1)}s</div>
                <div className="text-sm text-muted-foreground">总耗时</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 测试步骤 */}
      {steps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>测试步骤</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0">
                    {step.status === 'pending' && (
                      <div className="w-6 h-6 rounded-full border-2 border-gray-300" />
                    )}
                    {step.status === 'running' && (
                      <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                    )}
                    {step.status === 'success' && (
                      <CheckCircle className="w-6 h-6 text-green-500" />
                    )}
                    {step.status === 'error' && (
                      <XCircle className="w-6 h-6 text-red-500" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="font-medium">{step.name}</div>
                    <div className="text-sm text-muted-foreground">{step.description}</div>
                    {step.error && (
                      <div className="text-sm text-red-600 mt-1">{step.error}</div>
                    )}
                  </div>
                  
                  <div className="flex-shrink-0 text-right">
                    <Badge variant={
                      step.status === 'success' ? 'default' :
                      step.status === 'error' ? 'destructive' :
                      step.status === 'running' ? 'secondary' : 'outline'
                    }>
                      {step.status === 'pending' && '等待中'}
                      {step.status === 'running' && '运行中'}
                      {step.status === 'success' && '成功'}
                      {step.status === 'error' && '失败'}
                    </Badge>
                    {step.duration && (
                      <div className="text-xs text-muted-foreground mt-1">
                        <Clock className="inline w-3 h-3 mr-1" />
                        {step.duration}ms
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 实时日志 */}
      {logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>实时日志</CardTitle>
          </CardHeader>
          <CardContent>
            <div 
              ref={logsRef}
              className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto"
            >
              {logs.map((log, index) => (
                <div key={index} className="mb-1">{log}</div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
