/**
 * OCTI智能评估系统 - 多轮对话管理器
 * 
 * 负责通过多轮对话收集公益机构组织画像信息
 * 智能引导用户提供完整的组织背景信息
 */

import { chatWithMinimax, LLMMessage } from './llm-client';
import { prisma } from '@/lib/prisma';
import { generateUUID } from '@/lib/utils';
import type {
  DialogueSession,
  DialogueMessage,
  DialogueStatus,
  MessageType,
  NonprofitProfile,
  Organization,
} from '@prisma/client';

// ============================================================================
// 类型定义
// ============================================================================

export interface DialogueContext {
  organizationId: string;
  sessionId?: string;
  currentRound: number;
  totalRounds: number;
  collectedInfo: Partial<NonprofitProfileData>;
  conversationHistory: DialogueMessage[];
}

export interface NonprofitProfileData {
  organizationType: string;
  serviceArea: string[];
  organizationScale: string;
  developmentStage: string;
  operatingModel: string;
  impactPositioning: string;
  organizationalCulture: string;
  missionVision: {
    mission: string;
    vision: string;
    values: string[];
    theory?: string;
  };
  governance: {
    boardStructure: string;
    decisionMaking: string;
    transparency: string;
    accountability: string[];
  };
  resourceProfile: {
    fundingSources: string[];
    volunteerBase: string;
    partnerships: string[];
    capacity: string;
  };
  impactMeasurement: {
    hasTheory: boolean;
    measurementTools: string[];
    reportingFrequency: string;
    stakeholderFeedback: boolean;
  };
  challenges: string[];
  goals: string[];
  region?: string;
  foundedYear?: number;
  keyMetrics?: Record<string, any>;
}

export interface DialogueResponse {
  message: string;
  isComplete: boolean;
  nextQuestion?: string;
  progress: number; // 0-100
  extractedInfo?: Partial<NonprofitProfileData>;
  sessionId: string;
}

// ============================================================================
// 多轮对话管理器类
// ============================================================================

export class DialogueManager {
  private readonly DIALOGUE_ROUNDS = 5;
  private readonly SYSTEM_PROMPT = `你是OCTI智能评估系统的专业对话引导师，专门帮助公益机构完善组织画像信息。

你的任务是通过友好、专业的对话，逐步收集组织的关键信息，包括：
1. 基本信息（类型、规模、发展阶段等）
2. 使命愿景和价值观
3. 治理结构和决策机制
4. 资源状况和合作伙伴
5. 影响力测量和评估方法
6. 当前挑战和发展目标

请遵循以下原则：
- 使用温暖、专业的语调
- 每次只询问1-2个相关问题
- 根据已收集的信息调整后续问题
- 提供选择项帮助用户更好地回答
- 适时总结和确认收集到的信息`;

  /**
   * 开始新的对话会话
   * 
   * @param organizationId - 组织ID
   * @returns 对话响应
   */
  async startDialogue(organizationId: string): Promise<DialogueResponse> {
    try {
      // 获取组织基本信息
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new Error('组织不存在');
      }

      // 创建对话会话
      const session = await prisma.dialogueSession.create({
        data: {
          id: generateUUID(),
          organizationId,
          status: 'ACTIVE',
          currentRound: 1,
          totalRounds: this.DIALOGUE_ROUNDS,
          conversationHistory: [],
          extractedInsights: {},
        },
      });

      // 生成开场问题
      const firstQuestion = await this.generateFirstQuestion(organization);

      // 保存系统消息
      await this.saveMessage(session.id, 'SYSTEM', firstQuestion, 1);

      return {
        message: firstQuestion,
        isComplete: false,
        progress: 0,
        sessionId: session.id,
      };
    } catch (error) {
      console.error('Failed to start dialogue:', error);
      throw new Error('对话启动失败');
    }
  }

  /**
   * 处理用户回答
   * 
   * @param sessionId - 会话ID
   * @param userMessage - 用户消息
   * @returns 对话响应
   */
  async processUserMessage(sessionId: string, userMessage: string): Promise<DialogueResponse> {
    try {
      // 获取会话信息
      const session = await prisma.dialogueSession.findUnique({
        where: { id: sessionId },
        include: {
          messages: { orderBy: { timestamp: 'asc' } },
          organization: true,
        },
      });

      if (!session || session.status !== 'ACTIVE') {
        throw new Error('会话不存在或已结束');
      }

      // 保存用户消息
      await this.saveMessage(sessionId, 'USER', userMessage, session.currentRound);

      // 提取信息
      const extractedInfo = await this.extractInformation(session.messages, userMessage);

      // 更新会话洞察
      const updatedInsights = {
        ...((session.extractedInsights as any) || {}),
        ...extractedInfo,
      };

      // 检查是否完成收集
      const isComplete = this.isInformationComplete(updatedInsights) || session.currentRound >= session.totalRounds;

      let nextQuestion = '';
      let progress = (session.currentRound / session.totalRounds) * 100;

      if (!isComplete) {
        // 生成下一个问题
        nextQuestion = await this.generateNextQuestion(session.messages, updatedInsights, session.currentRound + 1);
        
        // 更新会话状态
        await prisma.dialogueSession.update({
          where: { id: sessionId },
          data: {
            currentRound: session.currentRound + 1,
            extractedInsights: updatedInsights,
          },
        });

        // 保存助手消息
        await this.saveMessage(sessionId, 'ASSISTANT', nextQuestion, session.currentRound + 1);
        
        progress = ((session.currentRound + 1) / session.totalRounds) * 100;
      } else {
        // 完成对话
        await this.completeDialogue(sessionId, updatedInsights);
        progress = 100;
      }

      return {
        message: isComplete ? '感谢您提供的详细信息！我们已经收集到足够的组织画像数据，现在可以为您生成个性化的评估问卷。' : nextQuestion,
        isComplete,
        nextQuestion: isComplete ? undefined : nextQuestion,
        progress,
        extractedInfo: updatedInsights,
        sessionId,
      };
    } catch (error) {
      console.error('Failed to process user message:', error);
      throw new Error('消息处理失败');
    }
  }

  /**
   * 生成开场问题
   */
  private async generateFirstQuestion(organization: Organization): string {
    return `您好！欢迎使用OCTI智能评估系统。我是您的专属对话引导师，将帮助您完善组织画像信息，以便为您生成最适合的个性化评估问卷。

我看到您的组织是"${organization.name}"，让我们从一些基本信息开始：

1. 请问您的组织属于以下哪种类型？
   - 基金会
   - 公益组织/NGO
   - 社会团体
   - 民办非企业单位
   - 国际NGO
   - 政府机构
   - 其他

2. 您的组织主要服务于哪些领域？（可多选）
   - 教育
   - 环保
   - 扶贫/乡村振兴
   - 医疗健康
   - 养老服务
   - 儿童保护
   - 妇女发展
   - 残障服务
   - 文化艺术
   - 科技创新
   - 其他

请简单介绍一下您的组织类型和主要服务领域。`;
  }

  /**
   * 生成下一个问题
   */
  private async generateNextQuestion(
    messages: DialogueMessage[],
    extractedInfo: any,
    round: number
  ): Promise<string> {
    // 构建对话历史
    const conversationHistory = messages.map(msg => ({
      role: msg.type.toLowerCase() as 'system' | 'user' | 'assistant',
      content: msg.content,
    }));

    const llmMessages: LLMMessage[] = [
      { role: 'system', content: this.SYSTEM_PROMPT },
      ...conversationHistory,
      {
        role: 'user',
        content: `当前是第${round}轮对话，已收集信息：${JSON.stringify(extractedInfo, null, 2)}。请生成下一个问题，继续收集缺失的关键信息。`,
      },
    ];

    const response = await chatWithMinimax({
      messages: llmMessages,
      temperature: 0.7,
      maxTokens: 1000,
    });

    return response.content;
  }

  /**
   * 提取信息
   */
  private async extractInformation(messages: DialogueMessage[], latestMessage: string): Promise<Partial<NonprofitProfileData>> {
    const conversationHistory = messages.map(msg => ({
      role: msg.type.toLowerCase() as 'system' | 'user' | 'assistant',
      content: msg.content,
    }));

    const llmMessages: LLMMessage[] = [
      {
        role: 'system',
        content: `你是一个信息提取专家，负责从对话中提取公益机构的组织画像信息。

请从用户的回答中提取以下信息（如果提到的话）：
- organizationType: 组织类型
- serviceArea: 服务领域（数组）
- organizationScale: 组织规模
- developmentStage: 发展阶段
- operatingModel: 运营模式
- impactPositioning: 影响定位
- organizationalCulture: 组织文化
- missionVision: 使命愿景对象
- governance: 治理结构对象
- resourceProfile: 资源状况对象
- impactMeasurement: 影响力测量对象
- challenges: 挑战（数组）
- goals: 目标（数组）
- region: 服务地区
- foundedYear: 成立年份
- keyMetrics: 关键指标对象

请以JSON格式返回提取的信息，只包含明确提到的字段。`,
      },
      ...conversationHistory,
      {
        role: 'user',
        content: `请从最新的用户回答中提取组织信息：${latestMessage}`,
      },
    ];

    try {
      const response = await chatWithMinimax({
        messages: llmMessages,
        temperature: 0.3,
        maxTokens: 1500,
      });

      // 解析JSON响应
      const jsonMatch = response.content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      
      return JSON.parse(response.content);
    } catch (error) {
      console.error('Failed to extract information:', error);
      return {};
    }
  }

  /**
   * 检查信息是否完整
   */
  private isInformationComplete(info: any): boolean {
    const requiredFields = [
      'organizationType',
      'serviceArea',
      'organizationScale',
      'developmentStage',
      'operatingModel',
    ];

    return requiredFields.every(field => info[field] && info[field] !== '');
  }

  /**
   * 完成对话
   */
  private async completeDialogue(sessionId: string, extractedInfo: any): Promise<void> {
    // 更新会话状态
    await prisma.dialogueSession.update({
      where: { id: sessionId },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        extractedInsights: extractedInfo,
      },
    });

    // 创建或更新公益机构画像
    const session = await prisma.dialogueSession.findUnique({
      where: { id: sessionId },
    });

    if (session) {
      await this.createOrUpdateNonprofitProfile(session.organizationId, extractedInfo);
    }
  }

  /**
   * 创建或更新公益机构画像
   */
  private async createOrUpdateNonprofitProfile(organizationId: string, info: any): Promise<void> {
    try {
      const existingProfile = await prisma.nonprofitProfile.findUnique({
        where: { organizationId },
      });

      const profileData = {
        organizationType: info.organizationType || '',
        serviceArea: info.serviceArea || [],
        organizationScale: info.organizationScale || '',
        developmentStage: info.developmentStage || '',
        operatingModel: info.operatingModel || '',
        impactPositioning: info.impactPositioning || '',
        organizationalCulture: info.organizationalCulture || '',
        missionVision: info.missionVision || {},
        governance: info.governance || {},
        resourceProfile: info.resourceProfile || {},
        impactMeasurement: info.impactMeasurement || {},
        challenges: info.challenges || [],
        goals: info.goals || [],
        region: info.region,
        foundedYear: info.foundedYear,
        keyMetrics: info.keyMetrics || {},
      };

      if (existingProfile) {
        await prisma.nonprofitProfile.update({
          where: { organizationId },
          data: profileData,
        });
      } else {
        await prisma.nonprofitProfile.create({
          data: {
            id: generateUUID(),
            organizationId,
            ...profileData,
          },
        });
      }
    } catch (error) {
      console.error('Failed to create/update nonprofit profile:', error);
    }
  }

  /**
   * 保存消息
   */
  private async saveMessage(
    sessionId: string,
    type: MessageType,
    content: string,
    roundNumber: number
  ): Promise<void> {
    await prisma.dialogueMessage.create({
      data: {
        id: generateUUID(),
        sessionId,
        type,
        content,
        roundNumber,
        metadata: {},
      },
    });
  }

  /**
   * 获取对话历史
   */
  async getDialogueHistory(sessionId: string): Promise<DialogueMessage[]> {
    return await prisma.dialogueMessage.findMany({
      where: { sessionId },
      orderBy: { timestamp: 'asc' },
    });
  }

  /**
   * 获取会话状态
   */
  async getSessionStatus(sessionId: string): Promise<DialogueSession | null> {
    return await prisma.dialogueSession.findUnique({
      where: { id: sessionId },
      include: {
        messages: { orderBy: { timestamp: 'asc' } },
      },
    });
  }
}

// ============================================================================
// 导出实例
// ============================================================================

export const dialogueManager = new DialogueManager();
