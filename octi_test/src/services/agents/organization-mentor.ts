/**
 * OCTI智能评估系统 - 组织评估导师智能体
 * 
 * 负责分析问卷回答，生成组织能力评估报告
 * 实现MiniMax + DeepSeek双模型协作分析
 */

import { configEngine } from '../config-engine';
import { chatWithMinimax, chatWithDeepSeek, LLMMessage } from '../llm-client';
import { prisma } from '@/lib/prisma';
import { generateUUID } from '@/lib/utils';
import { CAPABILITY_DIMENSION_LABELS } from '@/constants';
import type {
  Assessment,
  AnalysisResult,
  AnalysisModel,
  Question,
  Response,
  Questionnaire,
  NonprofitProfile,
} from '@prisma/client';

// ============================================================================
// 类型定义
// ============================================================================

export interface AnalysisContext {
  assessment: Assessment;
  questionnaire: Questionnaire;
  questions: Question[];
  responses: Response[];
  organizationProfile: NonprofitProfile;
}

export interface CapabilityScore {
  dimension: string;
  score: number; // 0-100分
  level: 'beginner' | 'developing' | 'proficient' | 'advanced' | 'expert';
  confidence: number; // 置信度 0-1
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

export interface AnalysisOutput {
  overallScore: number;
  capabilityScores: CapabilityScore[];
  keyFindings: string[];
  recommendations: string[];
  developmentPriorities: string[];
  nextSteps: string[];
  confidence: number;
  analysisModel: AnalysisModel;
  primaryAnalysis?: any;
  secondaryAnalysis?: any;
  fusionMetadata?: any;
}

// ============================================================================
// 组织评估导师智能体类
// ============================================================================

export class OrganizationMentorAgent {
  private config: any;

  constructor() {
    this.loadConfig();
  }

  /**
   * 加载智能体配置
   */
  private async loadConfig(): Promise<void> {
    try {
      this.config = await configEngine.getAgentConfig('ORGANIZATION_MENTOR');
    } catch (error) {
      console.error('Failed to load organization mentor config:', error);
      // 使用默认配置
      this.config = {
        model: 'minimax',
        temperature: 0.5,
        maxTokens: 4000,
        systemPrompt: '你是一个专业的组织能力评估导师，专门为公益机构提供能力评估和发展建议。',
        userPromptTemplate: '请基于以下问卷回答分析组织能力：{context}',
      };
    }
  }

  /**
   * 分析评估结果
   * 
   * @param context - 分析上下文
   * @param useDualModel - 是否使用双模型分析
   * @returns 分析结果
   */
  async analyzeAssessment(context: AnalysisContext, useDualModel: boolean = false): Promise<AnalysisOutput> {
    try {
      if (useDualModel) {
        return await this.performDualModelAnalysis(context);
      } else {
        return await this.performSingleModelAnalysis(context);
      }
    } catch (error) {
      console.error('Failed to analyze assessment:', error);
      throw new Error('评估分析失败');
    }
  }

  /**
   * 执行单模型分析（MiniMax）
   */
  private async performSingleModelAnalysis(context: AnalysisContext): Promise<AnalysisOutput> {
    const messages: LLMMessage[] = [
      {
        role: 'system',
        content: this.buildSystemPrompt(),
      },
      {
        role: 'user',
        content: this.buildUserPrompt(context),
      },
    ];

    const response = await chatWithMinimax({
      messages,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
    });

    const analysisResult = this.parseAnalysisResponse(response.content);
    
    return {
      ...analysisResult,
      analysisModel: 'MINIMAX',
      primaryAnalysis: analysisResult,
    };
  }

  /**
   * 执行双模型分析（MiniMax + DeepSeek）
   */
  private async performDualModelAnalysis(context: AnalysisContext): Promise<AnalysisOutput> {
    // 1. 主模型分析（MiniMax）
    const primaryAnalysis = await this.performSingleModelAnalysis(context);
    
    // 2. 辅助模型分析（DeepSeek）
    const secondaryAnalysis = await this.performSecondaryAnalysis(context, primaryAnalysis);
    
    // 3. 融合分析结果
    const fusedAnalysis = await this.fuseAnalysisResults(primaryAnalysis, secondaryAnalysis);
    
    return {
      ...fusedAnalysis,
      analysisModel: 'HYBRID',
      primaryAnalysis,
      secondaryAnalysis,
      fusionMetadata: {
        fusionStrategy: 'weighted',
        primaryWeight: 0.7,
        secondaryWeight: 0.3,
        fusionTimestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * 执行辅助模型分析（DeepSeek）
   */
  private async performSecondaryAnalysis(context: AnalysisContext, primaryAnalysis: AnalysisOutput): Promise<AnalysisOutput> {
    const messages: LLMMessage[] = [
      {
        role: 'system',
        content: this.buildSecondarySystemPrompt(),
      },
      {
        role: 'user',
        content: this.buildSecondaryUserPrompt(context, primaryAnalysis),
      },
    ];

    const response = await chatWithDeepSeek({
      messages,
      temperature: 0.3, // 更低的温度，更确定性的输出
      maxTokens: this.config.maxTokens,
    });

    const analysisResult = this.parseAnalysisResponse(response.content);
    
    return {
      ...analysisResult,
      analysisModel: 'DEEPSEEK',
    };
  }

  /**
   * 融合分析结果
   */
  private async fuseAnalysisResults(primary: AnalysisOutput, secondary: AnalysisOutput): Promise<AnalysisOutput> {
    // 加权平均总分
    const overallScore = primary.overallScore * 0.7 + secondary.overallScore * 0.3;
    
    // 融合能力维度评分
    const capabilityScores = this.fuseCapabilityScores(primary.capabilityScores, secondary.capabilityScores);
    
    // 合并关键发现和建议
    const keyFindings = this.mergeUniqueItems([...primary.keyFindings, ...secondary.keyFindings]);
    const recommendations = this.mergeUniqueItems([...primary.recommendations, ...secondary.recommendations]);
    const developmentPriorities = this.mergeUniqueItems([...primary.developmentPriorities, ...secondary.developmentPriorities]);
    const nextSteps = this.mergeUniqueItems([...primary.nextSteps, ...secondary.nextSteps]);
    
    // 取平均置信度
    const confidence = (primary.confidence + secondary.confidence) / 2;
    
    return {
      overallScore,
      capabilityScores,
      keyFindings,
      recommendations,
      developmentPriorities,
      nextSteps,
      confidence,
      analysisModel: 'HYBRID',
    };
  }

  /**
   * 融合能力维度评分
   */
  private fuseCapabilityScores(primary: CapabilityScore[], secondary: CapabilityScore[]): CapabilityScore[] {
    const dimensions = Object.keys(CAPABILITY_DIMENSION_LABELS);
    const result: CapabilityScore[] = [];
    
    for (const dimension of dimensions) {
      const primaryScore = primary.find(s => s.dimension === dimension);
      const secondaryScore = secondary.find(s => s.dimension === dimension);
      
      if (primaryScore && secondaryScore) {
        // 两个模型都有评分，进行融合
        result.push({
          dimension,
          score: primaryScore.score * 0.7 + secondaryScore.score * 0.3,
          level: this.determineLevel(primaryScore.score * 0.7 + secondaryScore.score * 0.3),
          confidence: (primaryScore.confidence + secondaryScore.confidence) / 2,
          strengths: this.mergeUniqueItems([...primaryScore.strengths, ...secondaryScore.strengths]),
          weaknesses: this.mergeUniqueItems([...primaryScore.weaknesses, ...secondaryScore.weaknesses]),
          recommendations: this.mergeUniqueItems([...primaryScore.recommendations, ...secondaryScore.recommendations]),
        });
      } else if (primaryScore) {
        // 只有主模型有评分
        result.push(primaryScore);
      } else if (secondaryScore) {
        // 只有辅助模型有评分
        result.push(secondaryScore);
      }
    }
    
    return result;
  }

  /**
   * 合并唯一项目
   */
  private mergeUniqueItems(items: string[]): string[] {
    const uniqueItems = new Set<string>();
    
    for (const item of items) {
      // 简单的相似度检查，避免重复项
      let isDuplicate = false;
      for (const existingItem of uniqueItems) {
        if (this.calculateSimilarity(item, existingItem) > 0.7) {
          isDuplicate = true;
          break;
        }
      }
      
      if (!isDuplicate) {
        uniqueItems.add(item);
      }
    }
    
    return Array.from(uniqueItems);
  }

  /**
   * 计算文本相似度（简化版）
   */
  private calculateSimilarity(a: string, b: string): number {
    const aWords = new Set(a.toLowerCase().split(/\s+/));
    const bWords = new Set(b.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...aWords].filter(x => bWords.has(x)));
    const union = new Set([...aWords, ...bWords]);
    
    return intersection.size / union.size;
  }

  /**
   * 根据分数确定能力等级
   */
  private determineLevel(score: number): 'beginner' | 'developing' | 'proficient' | 'advanced' | 'expert' {
    if (score < 20) return 'beginner';
    if (score < 40) return 'developing';
    if (score < 60) return 'proficient';
    if (score < 80) return 'advanced';
    return 'expert';
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(): string {
    return `你是OCTI智能评估系统的专业组织评估导师，专门为公益机构提供能力评估和发展建议。

你的任务是基于问卷回答，全面分析组织的能力现状，并提供有针对性的发展建议。请遵循以下原则：

1. **客观公正**：基于事实和数据进行分析，避免主观臆断
2. **全面系统**：覆盖治理能力、战略能力、运营能力、财务能力等核心维度
3. **深入洞察**：挖掘组织能力的根本问题和发展机会
4. **实用建议**：提供具体可行的改进措施和发展路径
5. **优势导向**：关注组织的优势和潜力，而不仅是问题和不足

请以JSON格式返回分析结果，包含以下字段：
- overallScore: 总体评分（0-100）
- capabilityScores: 各维度能力评分
- keyFindings: 关键发现
- recommendations: 建议
- developmentPriorities: 发展优先事项
- nextSteps: 下一步行动
- confidence: 分析置信度（0-1）`;
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(context: AnalysisContext): string {
    const { questionnaire, questions, responses, organizationProfile } = context;
    
    // 构建问题和回答的映射
    const questionResponseMap = new Map<string, any>();
    for (const response of responses) {
      questionResponseMap.set(response.questionId, response.answer);
    }
    
    // 构建问题和回答的文本表示
    const questionsAndResponses = questions.map(q => {
      const response = questionResponseMap.get(q.id);
      let responseText = '未回答';
      
      if (response) {
        if (typeof response === 'string') {
          responseText = response;
        } else if (Array.isArray(response)) {
          responseText = response.join(', ');
        } else if (typeof response === 'object') {
          responseText = JSON.stringify(response);
        } else {
          responseText = String(response);
        }
      }
      
      return `问题 [${q.category}]: ${q.title}\n回答: ${responseText}`;
    }).join('\n\n');
    
    return `请分析以下公益机构的问卷回答，评估其组织能力现状，并提供发展建议：

**组织基本信息：**
- 组织类型：${organizationProfile.organizationType}
- 服务领域：${Array.isArray(organizationProfile.serviceArea) ? (organizationProfile.serviceArea as string[]).join('、') : organizationProfile.serviceArea}
- 组织规模：${organizationProfile.organizationScale}
- 发展阶段：${organizationProfile.developmentStage}
- 运营模式：${organizationProfile.operatingModel}
- 影响定位：${organizationProfile.impactPositioning}

**问卷回答：**
${questionsAndResponses}

请基于以上信息，全面分析该组织的能力现状，评估其在各维度的表现，并提供有针对性的发展建议。

返回格式：
\`\`\`json
{
  "overallScore": 75,
  "capabilityScores": [
    {
      "dimension": "governance",
      "score": 80,
      "level": "advanced",
      "confidence": 0.85,
      "strengths": ["明确的治理结构", "有效的决策机制"],
      "weaknesses": ["董事会多样性不足"],
      "recommendations": ["增加董事会成员多样性"]
    }
  ],
  "keyFindings": ["组织具有清晰的使命愿景", "战略规划能力需要提升"],
  "recommendations": ["加强战略规划能力", "优化资源配置"],
  "developmentPriorities": ["提升筹资能力", "完善绩效评估体系"],
  "nextSteps": ["制定详细的战略规划", "建立多元化筹资渠道"],
  "confidence": 0.9
}
\`\`\``;
  }

  /**
   * 构建辅助模型系统提示词
   */
  private buildSecondarySystemPrompt(): string {
    return `你是OCTI智能评估系统的专业组织评估导师，专门为公益机构提供能力评估和发展建议。

你的任务是基于问卷回答和初步分析结果，提供更深入的洞察和建议。请特别关注：

1. **初步分析的盲点**：找出主分析可能忽略的关键问题
2. **潜在机会**：发掘组织可能未充分利用的发展机会
3. **创新思路**：提供创新性的解决方案和发展路径
4. **风险预警**：识别组织发展中的潜在风险和挑战
5. **跨维度关联**：分析不同能力维度之间的关联和影响

请以JSON格式返回分析结果，与主分析保持相同的结构。`;
  }

  /**
   * 构建辅助模型用户提示词
   */
  private buildSecondaryUserPrompt(context: AnalysisContext, primaryAnalysis: AnalysisOutput): string {
    const { organizationProfile } = context;
    
    return `请对以下公益机构的评估结果进行深入分析，提供更全面的洞察和建议：

**组织基本信息：**
- 组织类型：${organizationProfile.organizationType}
- 服务领域：${Array.isArray(organizationProfile.serviceArea) ? (organizationProfile.serviceArea as string[]).join('、') : organizationProfile.serviceArea}
- 组织规模：${organizationProfile.organizationScale}
- 发展阶段：${organizationProfile.developmentStage}

**初步分析结果：**
\`\`\`json
${JSON.stringify(primaryAnalysis, null, 2)}
\`\`\`

请基于以上信息，提供你的独立分析和见解，特别关注初步分析可能忽略的方面。

返回格式应与初步分析相同，包含overallScore, capabilityScores, keyFindings, recommendations, developmentPriorities, nextSteps, confidence字段。`;
  }

  /**
   * 解析分析响应
   */
  private parseAnalysisResponse(content: string): AnalysisOutput {
    try {
      // 提取JSON内容
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      
      // 尝试直接解析
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse analysis response:', error);
      throw new Error('分析响应解析失败');
    }
  }

  /**
   * 保存分析结果
   */
  async saveAnalysisResult(assessmentId: string, analysis: AnalysisOutput): Promise<AnalysisResult> {
    try {
      return await prisma.analysisResult.create({
        data: {
          id: generateUUID(),
          assessmentId,
          overallScore: analysis.overallScore,
          capabilityScores: analysis.capabilityScores,
          keyFindings: analysis.keyFindings,
          recommendations: analysis.recommendations,
          developmentPriorities: analysis.developmentPriorities,
          nextSteps: analysis.nextSteps,
          confidence: analysis.confidence,
          analysisModel: analysis.analysisModel,
          primaryAnalysis: analysis.primaryAnalysis,
          secondaryAnalysis: analysis.secondaryAnalysis,
          fusionMetadata: analysis.fusionMetadata,
        },
      });
    } catch (error) {
      console.error('Failed to save analysis result:', error);
      throw new Error('分析结果保存失败');
    }
  }
}

// ============================================================================
// 导出实例
// ============================================================================

export const organizationMentorAgent = new OrganizationMentorAgent();
