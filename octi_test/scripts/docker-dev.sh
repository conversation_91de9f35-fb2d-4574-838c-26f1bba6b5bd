#!/bin/bash

# OCTI智能评估系统 - Docker开发环境脚本
# 用于快速启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 检查环境变量
check_environment() {
    log_info "检查开发环境配置..."
    
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，从模板创建..."
        cp .env.example .env
        log_info "已创建.env文件，使用默认开发配置"
    fi
    
    log_success "环境配置检查完成"
}

# 启动开发环境
start_dev() {
    log_info "启动开发环境..."
    
    # 使用开发环境配置
    docker-compose -f docker-compose.dev.yml up -d
    
    log_success "开发环境启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查各个服务
    services=("postgres" "redis" "app")
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.dev.yml ps | grep -q "${service}.*Up"; then
            log_success "${service} 服务运行正常"
        else
            log_error "${service} 服务启动失败"
            docker-compose -f docker-compose.dev.yml logs $service
            exit 1
        fi
    done
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待数据库完全启动
    sleep 15
    
    # 运行Prisma迁移
    docker-compose -f docker-compose.dev.yml exec app npx prisma migrate dev --name init
    
    log_success "数据库迁移完成"
}

# 显示开发环境信息
show_dev_info() {
    log_success "OCTI开发环境启动完成！"
    echo ""
    echo "访问信息："
    echo "  应用地址: http://localhost:3000"
    echo "  API地址: http://localhost:3000/api"
    echo "  健康检查: http://localhost:3000/api/health"
    echo ""
    echo "数据库管理："
    echo "  Adminer: http://localhost:8080"
    echo "  数据库: postgres"
    echo "  用户: octi_user"
    echo "  密码: octi_password"
    echo ""
    echo "Redis管理："
    echo "  Redis Commander: http://localhost:8081"
    echo ""
    echo "邮件测试："
    echo "  MailHog: http://localhost:8025"
    echo ""
    echo "开发命令："
    echo "  查看日志: docker-compose -f docker-compose.dev.yml logs -f"
    echo "  停止服务: docker-compose -f docker-compose.dev.yml down"
    echo "  重启服务: docker-compose -f docker-compose.dev.yml restart"
    echo "  进入容器: docker-compose -f docker-compose.dev.yml exec app bash"
    echo ""
}

# 安装依赖
install_deps() {
    log_info "安装项目依赖..."
    
    docker-compose -f docker-compose.dev.yml exec app npm install
    
    log_success "依赖安装完成"
}

# 生成Prisma客户端
generate_prisma() {
    log_info "生成Prisma客户端..."
    
    docker-compose -f docker-compose.dev.yml exec app npx prisma generate
    
    log_success "Prisma客户端生成完成"
}

# 主函数
main() {
    log_info "启动OCTI开发环境..."
    
    check_dependencies
    check_environment
    start_dev
    check_services
    
    # 如果是首次启动，运行初始化
    if [ "${2}" = "--init" ]; then
        install_deps
        generate_prisma
        run_migrations
    fi
    
    show_dev_info
    
    log_success "开发环境准备完成！"
}

# 处理命令行参数
case "${1:-start}" in
    "start")
        main $@
        ;;
    "stop")
        log_info "停止开发环境..."
        docker-compose -f docker-compose.dev.yml down
        log_success "开发环境已停止"
        ;;
    "restart")
        log_info "重启开发环境..."
        docker-compose -f docker-compose.dev.yml restart
        log_success "开发环境已重启"
        ;;
    "logs")
        docker-compose -f docker-compose.dev.yml logs -f
        ;;
    "status")
        docker-compose -f docker-compose.dev.yml ps
        ;;
    "shell")
        docker-compose -f docker-compose.dev.yml exec app bash
        ;;
    "db-reset")
        log_warning "重置数据库..."
        read -p "确定要重置数据库吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose -f docker-compose.dev.yml exec app npx prisma migrate reset --force
            log_success "数据库重置完成"
        else
            log_info "取消重置操作"
        fi
        ;;
    "clean")
        log_warning "清理开发环境..."
        read -p "确定要删除所有开发数据吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose -f docker-compose.dev.yml down -v
            docker system prune -f
            log_success "清理完成"
        else
            log_info "取消清理操作"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|logs|status|shell|db-reset|clean}"
        echo ""
        echo "命令说明："
        echo "  start [--init] - 启动开发环境（--init首次初始化）"
        echo "  stop           - 停止开发环境"
        echo "  restart        - 重启开发环境"
        echo "  logs           - 查看实时日志"
        echo "  status         - 查看服务状态"
        echo "  shell          - 进入应用容器"
        echo "  db-reset       - 重置数据库"
        echo "  clean          - 清理所有开发数据"
        exit 1
        ;;
esac
