// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// --- 用户与组织模型 ---

model User {
  id             String         @id @default(uuid())
  email          String         @unique
  name           String?
  passwordHash   String
  role           Role           @default(USER)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizations  Organization[]
  assessments    Assessment[]
  securityEvents SecurityEvent[]
}

model Organization {
  id                      String                    @id @default(uuid())
  name                    String
  industry                String?
  size                    String?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  user                    User                      @relation(fields: [userId], references: [id])
  userId                  String
  nonprofitProfile        NonprofitProfile?
  assessments             Assessment[]
  dialogueSessions        DialogueSession[]
  questionnaires          Questionnaire[]
}

enum Role {
  USER
  ADMIN
}

// --- 【新增】公益机构组织画像模型 ---

model NonprofitProfile {
  id                      String                    @id @default(uuid())
  organizationType        String // '基金会' | '公益组织' | '社会团体' | '民办非企业' | '国际NGO' | '政府机构'
  serviceArea             Json // string[] 服务领域
  organizationScale       String // '微型' | '小型' | '中型' | '大型' | '超大型'
  developmentStage        String // '初创期' | '成长期' | '成熟期' | '转型期' | '扩张期'
  operatingModel          String // '直接服务' | '资助型' | '倡导型' | '研究型' | '平台型' | '混合型'
  impactPositioning       String // '本地影响' | '区域影响' | '全国影响' | '国际影响'
  organizationalCulture    String // '使命驱动' | '创新导向' | '协作共享' | '专业严谨' | '草根活力'
  missionVision           Json // { mission, vision, values, theory }
  governance              Json // { boardStructure, decisionMaking, transparency, accountability }
  resourceProfile         Json // { fundingSources, volunteerBase, partnerships, capacity }
  impactMeasurement       Json // { hasTheory, measurementTools, reportingFrequency, stakeholderFeedback }
  challenges              Json // string[] 当前面临的主要挑战
  goals                   Json // string[] 组织目标
  region                  String? // 服务地区
  foundedYear             Int? // 成立年份
  keyMetrics              Json? // 关键指标
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  organization            Organization              @relation(fields: [organizationId], references: [id])
  organizationId          String                    @unique
  assessments             Assessment[]
  questionnaires          Questionnaire[]
  dialogueSessions        DialogueSession[]
}

// --- 【新增】多轮对话模型 ---

model DialogueSession {
  id                    String                    @id @default(uuid())
  status                DialogueStatus            @default(ACTIVE)
  currentRound          Int                       @default(1)
  totalRounds           Int                       @default(5)
  conversationHistory   Json // 完整对话历史
  extractedInsights     Json? // 从对话中提取的组织洞察
  createdAt             DateTime                  @default(now())
  completedAt           DateTime?
  organization          Organization              @relation(fields: [organizationId], references: [id])
  organizationId        String
  nonprofitProfile      NonprofitProfile?         @relation(fields: [nonprofitProfileId], references: [id])
  nonprofitProfileId    String?
  messages              DialogueMessage[]
  questionnaires        Questionnaire[]
}

model DialogueMessage {
  id           String          @id @default(uuid())
  type         MessageType // SYSTEM, USER, ASSISTANT
  content      String
  metadata     Json? // 消息元数据（如AI配置、处理时间等）
  roundNumber  Int // 对话轮次
  timestamp    DateTime        @default(now())
  session      DialogueSession @relation(fields: [sessionId], references: [id])
  sessionId    String
}

enum DialogueStatus {
  ACTIVE
  COMPLETED
  PAUSED
  FAILED
}

enum MessageType {
  SYSTEM
  USER
  ASSISTANT
}

// 已移除IntelligentQuestionnaire模型，使用新的Questionnaire模型

// --- 评估核心模型 ---

model Assessment {
  id                        String                    @id @default(uuid())
  title                     String
  description               String?
  type                      AssessmentType            @default(STANDARD)
  status                    AssessmentStatus          @default(DRAFT)

  // 关联关系
  organizationId            String
  organization              Organization              @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId                    String
  user                      User                      @relation(fields: [userId], references: [id])
  nonprofitProfileId        String?
  nonprofitProfile          NonprofitProfile?         @relation(fields: [nonprofitProfileId], references: [id])
  questionnaireId           String?
  questionnaire             Questionnaire?            @relation(fields: [questionnaireId], references: [id])

  responses                 Response[]
  analysisResults           AnalysisResult[]
  reports                   Report[]
  externalDataSources       ExternalDataSource[]

  // 时间戳
  createdAt                 DateTime                  @default(now())
  startedAt                 DateTime?
  completedAt               DateTime?
  updatedAt                 DateTime                  @updatedAt

  @@map("assessments")
}

enum AssessmentStatus {
  DRAFT
  PROFILE_COLLECTION
  QUESTIONNAIRE_GENERATION
  IN_PROGRESS
  ANALYSIS
  COMPLETED
  ARCHIVED
  FAILED
  CANCELLED
}

enum AssessmentType {
  STANDARD
  PROFESSIONAL
}

// --- 分析结果模型 ---

model AnalysisResult {
  id                String              @id @default(uuid())
  overallScore      Float
  capabilityScores  Json // CapabilityScore[]
  keyFindings       String[]
  recommendations   String[]
  developmentPriorities String[]
  nextSteps         String[]
  confidence        Float
  analysisModel     AnalysisModel

  // 双模型协作相关
  primaryAnalysis   Json? // 主模型分析结果
  secondaryAnalysis Json? // 副模型分析结果
  fusionMetadata    Json? // 融合元数据

  // 关联关系
  assessmentId      String
  assessment        Assessment          @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  reports           Report[]

  // 时间戳
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("analysis_results")
}

enum AnalysisModel {
  MINIMAX
  DEEPSEEK
  HYBRID
}

// --- 报告模型 ---

model Report {
  id                String              @id @default(uuid())
  title             String
  type              ReportType
  format            ReportFormat        @default(PDF)
  content           Json
  downloadUrl       String?
  shareUrl          String?
  expiresAt         DateTime?

  // 关联关系
  assessmentId      String
  assessment        Assessment          @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  analysisResultId  String?
  analysisResult    AnalysisResult?     @relation(fields: [analysisResultId], references: [id])

  // 时间戳
  generatedAt       DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("reports")
}

enum ReportType {
  SUMMARY
  DETAILED
  COMPARISON
  DEVELOPMENT
}

enum ReportFormat {
  PDF
  HTML
  JSON
}

enum AgentType {
  QUESTION_DESIGNER
  ORGANIZATION_TUTOR
}

enum OrganizationSize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
}

// 移除重复的枚举定义，已在上面定义过

enum ConfigStatus {
  DRAFT
  ACTIVE
  DEPRECATED
  ARCHIVED
}

// --- 问卷和问题模型 ---

model Questionnaire {
  id                    String                    @id @default(uuid())
  title                 String
  description           String?
  type                  QuestionnaireType         @default(HYBRID)
  status                QuestionnaireStatus       @default(GENERATED)
  totalQuestions        Int                       @default(60)
  presetQuestions       Int                       @default(32)
  aiGeneratedQuestions  Int                       @default(28)
  estimatedDuration     Int                       @default(90) // 分钟

  // 关联关系
  organizationId        String
  organization          Organization              @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  nonprofitProfileId    String?
  nonprofitProfile      NonprofitProfile?         @relation(fields: [nonprofitProfileId], references: [id])
  sessionId             String?
  dialogueSession       DialogueSession?          @relation(fields: [sessionId], references: [id])

  questions             Question[]
  responses             Response[]
  assessments           Assessment[]

  // 时间戳
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt

  @@map("questionnaires")
}

enum QuestionnaireType {
  PRESET_ONLY
  AI_GENERATED_ONLY
  HYBRID
}

enum QuestionnaireStatus {
  DRAFT
  GENERATED
  REVIEWED
  APPROVED
  ACTIVE
  DEPRECATED
}

model Question {
  id              String          @id @default(uuid())
  type            QuestionType
  source          QuestionSource
  category        String // 能力维度
  subCategory     String? // 子维度
  title           String
  description     String?
  options         Json? // 选项数据
  required        Boolean         @default(true)
  order           Int
  metadata        Json? // 额外元数据

  // 关联关系
  questionnaireId String
  questionnaire   Questionnaire   @relation(fields: [questionnaireId], references: [id], onDelete: Cascade)
  responses       Response[]

  // 时间戳
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@map("questions")
}

enum QuestionType {
  SINGLE_CHOICE
  MULTIPLE_CHOICE
  SCALE
  TEXT
  BOOLEAN
  RANKING
  SCENARIO
}

enum QuestionSource {
  PRESET
  AI_GENERATED
}

model Response {
  id              String          @id @default(uuid())
  answer          Json // 支持各种类型的答案
  confidence      Float? // 回答置信度 0-1
  timeSpent       Int? // 回答耗时（秒）

  // 关联关系
  questionId      String
  question        Question        @relation(fields: [questionId], references: [id], onDelete: Cascade)
  questionnaireId String
  questionnaire   Questionnaire   @relation(fields: [questionnaireId], references: [id], onDelete: Cascade)
  assessmentId    String
  assessment      Assessment      @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // 时间戳
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@unique([questionId, assessmentId])
  @@map("responses")
}

// --- 【新增】预设题目模型 ---

model PresetQuestion {
  id              String   @id @default(uuid())
  dimension       String // "SF", "IT", "MV", "AD"
  subDimension    String // 子维度
  questionType    QuestionType
  questionContent Json   // 题目内容（支持多语言）
  displayOrder    Int    // 显示顺序
  version         String // 题目版本
  isActive        Boolean @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([dimension, subDimension])
  @@index([version, isActive])
  @@map("preset_questions")
}

// --- 【新增】AI智能体配置模型（增强版）---

model AIAgentConfig {
  id             String   @id @default(uuid())
  agentType      String // "QUESTION_DESIGNER" | "ORGANIZATION_TUTOR" | "NONPROFIT_DIALOGUE"
  configName     String // 配置名称
  promptTemplate Json // 提示词模板
  parameters     Json // 配置参数
  version        String // 配置版本
  configSchema   Json? // 配置Schema验证
  isActive       Boolean  @default(true)
  parentConfigId String? // 父配置ID（支持继承）
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 关联关系
  versions       ConfigVersion[]
  usageStats     ConfigUsageStats[]
  parentConfig   AIAgentConfig? @relation("ConfigInheritance", fields: [parentConfigId], references: [id])
  childConfigs   AIAgentConfig[] @relation("ConfigInheritance")

  @@unique([agentType, configName, version])
  @@index([agentType, isActive])
  @@index([version, isActive])
}

// --- 【新增】配置版本管理模型 ---

model ConfigVersion {
  id           String   @id @default(uuid())
  configId     String
  version      String
  changeLog    Json // 变更日志
  status       String // "DRAFT", "ACTIVE", "DEPRECATED"
  createdBy    String
  createdAt    DateTime @default(now())
  activatedAt  DateTime?

  // 关联关系
  config       AIAgentConfig @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@unique([configId, version])
  @@index([status, activatedAt])
}

// --- 【新增】配置使用统计模型 ---

model ConfigUsageStats {
  id                 String   @id @default(uuid())
  configId           String
  agentType          String
  usageCount         Int      @default(0)
  performanceMetrics Json // 性能指标
  date               DateTime @db.Date
  createdAt          DateTime @default(now())

  // 关联关系
  config             AIAgentConfig @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@unique([configId, date])
  @@index([agentType, date])
}

// --- 【新增】外部数据融合模型 ---

model ExternalDataSource {
  id             String           @id @default(uuid())
  type           DataSourceType // "FILE" or "URL" or "IMPACT_DATA"
  source         String // 文件在COS中的路径或网页URL
  status         ProcessingStatus @default(PENDING)
  errorMessage   String? // 处理失败时的错误信息
  metadata       Json? // 数据源元数据（文件类型、大小、来源等）
  createdAt      DateTime         @default(now())
  assessment     Assessment       @relation(fields: [assessmentId], references: [id])
  assessmentId   String
  processedData  ProcessedData?
}

enum DataSourceType {
  FILE
  URL
  IMPACT_DATA
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

model ProcessedData {
  id               String             @id @default(uuid())
  extractedContent String? // 从文件或网页中提取的原始文本
  structuredData   Json? // LLM初步处理后的结构化数据
  processedAt      DateTime           @default(now())
  dataSource       ExternalDataSource @relation(fields: [dataSourceId], references: [id])
  dataSourceId     String             @unique
}

// --- 系统与安全模型 ---

model ApiKey {
  id           String   @id @default(uuid())
  provider     String   @unique // e.g., "minimax", "deepseek"
  keyEncrypted String
  keyIv        String
  keyTag       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model SecurityEvent {
  id        String            @id @default(uuid())
  type      SecurityEventType
  details   Json?
  ip        String?
  userAgent String?
  timestamp DateTime          @default(now())
  user      User?             @relation(fields: [userId], references: [id])
  userId    String?
}

enum SecurityEventType {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  PASSWORD_CHANGE
  ACCOUNT_LOCKOUT
  PERMISSION_CHANGE
  CONFIG_CHANGE
  API_KEY_USAGE
  SUSPICIOUS_ACTIVITY
}
