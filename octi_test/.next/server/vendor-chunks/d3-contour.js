"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-contour";
exports.ids = ["vendor-chunks/d3-contour"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-contour/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring) {\n  var i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n  return area;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL2FyZWEuanM/YTcxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihyaW5nKSB7XG4gIHZhciBpID0gMCwgbiA9IHJpbmcubGVuZ3RoLCBhcmVhID0gcmluZ1tuIC0gMV1bMV0gKiByaW5nWzBdWzBdIC0gcmluZ1tuIC0gMV1bMF0gKiByaW5nWzBdWzFdO1xuICB3aGlsZSAoKytpIDwgbikgYXJlYSArPSByaW5nW2kgLSAxXVsxXSAqIHJpbmdbaV1bMF0gLSByaW5nW2kgLSAxXVswXSAqIHJpbmdbaV1bMV07XG4gIHJldHVybiBhcmVhO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/array.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/array.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\n\nvar slice = array.slice;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJyYXkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9hcnJheS5qcz81MDc3Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBhcnJheSA9IEFycmF5LnByb3RvdHlwZTtcblxuZXhwb3J0IHZhciBzbGljZSA9IGFycmF5LnNsaWNlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/ascending.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-contour/src/ascending.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n  return a - b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXNjZW5kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9hc2NlbmRpbmcuanM/ZTE0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiKSB7XG4gIHJldHVybiBhIC0gYjtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/constant.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/constant.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (x => () => x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29uc3RhbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLFlBQVksRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29uc3RhbnQuanM/MmJhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB4ID0+ICgpID0+IHg7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring, hole) {\n  var i = -1, n = hole.length, c;\n  while (++i < n) if (c = ringContains(ring, hole[i])) return c;\n  return 0;\n}\n\nfunction ringContains(ring, point) {\n  var x = point[0], y = point[1], contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;\n  }\n  return contains;\n}\n\nfunction segmentContains(a, b, c) {\n  var i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\n\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\n\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29udGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSw4Q0FBOEMsT0FBTztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29udGFpbnMuanM/N2QwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihyaW5nLCBob2xlKSB7XG4gIHZhciBpID0gLTEsIG4gPSBob2xlLmxlbmd0aCwgYztcbiAgd2hpbGUgKCsraSA8IG4pIGlmIChjID0gcmluZ0NvbnRhaW5zKHJpbmcsIGhvbGVbaV0pKSByZXR1cm4gYztcbiAgcmV0dXJuIDA7XG59XG5cbmZ1bmN0aW9uIHJpbmdDb250YWlucyhyaW5nLCBwb2ludCkge1xuICB2YXIgeCA9IHBvaW50WzBdLCB5ID0gcG9pbnRbMV0sIGNvbnRhaW5zID0gLTE7XG4gIGZvciAodmFyIGkgPSAwLCBuID0gcmluZy5sZW5ndGgsIGogPSBuIC0gMTsgaSA8IG47IGogPSBpKyspIHtcbiAgICB2YXIgcGkgPSByaW5nW2ldLCB4aSA9IHBpWzBdLCB5aSA9IHBpWzFdLCBwaiA9IHJpbmdbal0sIHhqID0gcGpbMF0sIHlqID0gcGpbMV07XG4gICAgaWYgKHNlZ21lbnRDb250YWlucyhwaSwgcGosIHBvaW50KSkgcmV0dXJuIDA7XG4gICAgaWYgKCgoeWkgPiB5KSAhPT0gKHlqID4geSkpICYmICgoeCA8ICh4aiAtIHhpKSAqICh5IC0geWkpIC8gKHlqIC0geWkpICsgeGkpKSkgY29udGFpbnMgPSAtY29udGFpbnM7XG4gIH1cbiAgcmV0dXJuIGNvbnRhaW5zO1xufVxuXG5mdW5jdGlvbiBzZWdtZW50Q29udGFpbnMoYSwgYiwgYykge1xuICB2YXIgaTsgcmV0dXJuIGNvbGxpbmVhcihhLCBiLCBjKSAmJiB3aXRoaW4oYVtpID0gKyhhWzBdID09PSBiWzBdKV0sIGNbaV0sIGJbaV0pO1xufVxuXG5mdW5jdGlvbiBjb2xsaW5lYXIoYSwgYiwgYykge1xuICByZXR1cm4gKGJbMF0gLSBhWzBdKSAqIChjWzFdIC0gYVsxXSkgPT09IChjWzBdIC0gYVswXSkgKiAoYlsxXSAtIGFbMV0pO1xufVxuXG5mdW5jdGlvbiB3aXRoaW4ocCwgcSwgcikge1xuICByZXR1cm4gcCA8PSBxICYmIHEgPD0gciB8fCByIDw9IHEgJiYgcSA8PSBwO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contours.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contours.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-contour/src/ascending.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-contour/src/area.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-contour/src/contains.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-contour/src/noop.js\");\n\n\n\n\n\n\n\n\nvar cases = [\n  [],\n  [[[1.0, 1.5], [0.5, 1.0]]],\n  [[[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [0.5, 1.0]]],\n  [[[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 1.5], [0.5, 1.0]], [[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 0.5], [1.0, 1.5]]],\n  [[[1.0, 0.5], [0.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 0.5]]],\n  [[[1.0, 1.5], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.0, 0.5]], [[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.5, 1.0]]],\n  [[[1.0, 1.5], [1.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 1.5]]],\n  []\n];\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var dx = 1,\n      dy = 1,\n      threshold = d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n      smooth = smoothLinear;\n\n  function contours(values) {\n    var tz = threshold(values);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      const e = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, finite);\n      tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(...(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e[0], e[1], tz), tz);\n      while (tz[tz.length - 1] >= e[1]) tz.pop();\n      while (tz[1] < e[0]) tz.shift();\n    } else {\n      tz = tz.slice().sort(_ascending_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n    }\n\n    return tz.map(value => contour(values, value));\n  }\n\n  // Accumulate, smooth contour rings, assign holes to exterior rings.\n  // Based on https://github.com/mbostock/shapefile/blob/v0.6.2/shp/polygon.js\n  function contour(values, value) {\n    const v = value == null ? NaN : +value;\n    if (isNaN(v)) throw new Error(`invalid value: ${value}`);\n\n    var polygons = [],\n        holes = [];\n\n    isorings(values, v, function(ring) {\n      smooth(ring, values, v);\n      if ((0,_area_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ring) > 0) polygons.push([ring]);\n      else holes.push(ring);\n    });\n\n    holes.forEach(function(hole) {\n      for (var i = 0, n = polygons.length, polygon; i < n; ++i) {\n        if ((0,_contains_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((polygon = polygons[i])[0], hole) !== -1) {\n          polygon.push(hole);\n          return;\n        }\n      }\n    });\n\n    return {\n      type: \"MultiPolygon\",\n      value: value,\n      coordinates: polygons\n    };\n  }\n\n  // Marching squares with isolines stitched into rings.\n  // Based on https://github.com/topojson/topojson-client/blob/v3.0.0/src/stitch.js\n  function isorings(values, value, callback) {\n    var fragmentByStart = new Array,\n        fragmentByEnd = new Array,\n        x, y, t0, t1, t2, t3;\n\n    // Special case for the first row (y = -1, t2 = t3 = 0).\n    x = y = -1;\n    t1 = above(values[0], value);\n    cases[t1 << 1].forEach(stitch);\n    while (++x < dx - 1) {\n      t0 = t1, t1 = above(values[x + 1], value);\n      cases[t0 | t1 << 1].forEach(stitch);\n    }\n    cases[t1 << 0].forEach(stitch);\n\n    // General case for the intermediate rows.\n    while (++y < dy - 1) {\n      x = -1;\n      t1 = above(values[y * dx + dx], value);\n      t2 = above(values[y * dx], value);\n      cases[t1 << 1 | t2 << 2].forEach(stitch);\n      while (++x < dx - 1) {\n        t0 = t1, t1 = above(values[y * dx + dx + x + 1], value);\n        t3 = t2, t2 = above(values[y * dx + x + 1], value);\n        cases[t0 | t1 << 1 | t2 << 2 | t3 << 3].forEach(stitch);\n      }\n      cases[t1 | t2 << 3].forEach(stitch);\n    }\n\n    // Special case for the last row (y = dy - 1, t0 = t1 = 0).\n    x = -1;\n    t2 = values[y * dx] >= value;\n    cases[t2 << 2].forEach(stitch);\n    while (++x < dx - 1) {\n      t3 = t2, t2 = above(values[y * dx + x + 1], value);\n      cases[t2 << 2 | t3 << 3].forEach(stitch);\n    }\n    cases[t2 << 3].forEach(stitch);\n\n    function stitch(line) {\n      var start = [line[0][0] + x, line[0][1] + y],\n          end = [line[1][0] + x, line[1][1] + y],\n          startIndex = index(start),\n          endIndex = index(end),\n          f, g;\n      if (f = fragmentByEnd[startIndex]) {\n        if (g = fragmentByStart[endIndex]) {\n          delete fragmentByEnd[f.end];\n          delete fragmentByStart[g.start];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[f.start] = fragmentByEnd[g.end] = {start: f.start, end: g.end, ring: f.ring.concat(g.ring)};\n          }\n        } else {\n          delete fragmentByEnd[f.end];\n          f.ring.push(end);\n          fragmentByEnd[f.end = endIndex] = f;\n        }\n      } else if (f = fragmentByStart[endIndex]) {\n        if (g = fragmentByEnd[startIndex]) {\n          delete fragmentByStart[f.start];\n          delete fragmentByEnd[g.end];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[g.start] = fragmentByEnd[f.end] = {start: g.start, end: f.end, ring: g.ring.concat(f.ring)};\n          }\n        } else {\n          delete fragmentByStart[f.start];\n          f.ring.unshift(start);\n          fragmentByStart[f.start = startIndex] = f;\n        }\n      } else {\n        fragmentByStart[startIndex] = fragmentByEnd[endIndex] = {start: startIndex, end: endIndex, ring: [start, end]};\n      }\n    }\n  }\n\n  function index(point) {\n    return point[0] * 2 + point[1] * (dx + 1) * 4;\n  }\n\n  function smoothLinear(ring, values, value) {\n    ring.forEach(function(point) {\n      var x = point[0],\n          y = point[1],\n          xt = x | 0,\n          yt = y | 0,\n          v1 = valid(values[yt * dx + xt]);\n      if (x > 0 && x < dx && xt === x) {\n        point[0] = smooth1(x, valid(values[yt * dx + xt - 1]), v1, value);\n      }\n      if (y > 0 && y < dy && yt === y) {\n        point[1] = smooth1(y, valid(values[(yt - 1) * dx + xt]), v1, value);\n      }\n    });\n  }\n\n  contours.contour = contour;\n\n  contours.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = Math.floor(_[0]), _1 = Math.floor(_[1]);\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, contours;\n  };\n\n  contours.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_8__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_), contours) : threshold;\n  };\n\n  contours.smooth = function(_) {\n    return arguments.length ? (smooth = _ ? smoothLinear : _noop_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"], contours) : smooth === smoothLinear;\n  };\n\n  return contours;\n}\n\n// When computing the extent, ignore infinite values (as well as invalid ones).\nfunction finite(x) {\n  return isFinite(x) ? x : NaN;\n}\n\n// Is the (possibly invalid) x greater than or equal to the (known valid) value?\n// Treat any invalid value as below negative infinity.\nfunction above(x, value) {\n  return x == null ? false : +x >= value;\n}\n\n// During smoothing, treat any invalid value as negative infinity.\nfunction valid(v) {\n  return v == null || isNaN(v = +v) ? -Infinity : v;\n}\n\nfunction smooth1(x, v0, v1, value) {\n  const a = value - v0;\n  const b = v1 - v0;\n  const d = isFinite(a) || isFinite(b) ? a / b : Math.sign(a) / Math.sign(b);\n  return isNaN(d) ? x : x + d - 0.5;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contours.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/density.js":
/*!************************************************!*\
  !*** ./node_modules/d3-contour/src/density.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n\n\n\n\n\nfunction defaultX(d) {\n  return d[0];\n}\n\nfunction defaultY(d) {\n  return d[1];\n}\n\nfunction defaultWeight() {\n  return 1;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var x = defaultX,\n      y = defaultY,\n      weight = defaultWeight,\n      dx = 960,\n      dy = 500,\n      r = 20, // blur radius\n      k = 2, // log2(grid cell size)\n      o = r * 3, // grid offset, to pad for blur\n      n = (dx + o * 2) >> k, // grid width\n      m = (dy + o * 2) >> k, // grid height\n      threshold = (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(20);\n\n  function grid(data) {\n    var values = new Float32Array(n * m),\n        pow2k = Math.pow(2, -k),\n        i = -1;\n\n    for (const d of data) {\n      var xi = (x(d, ++i, data) + o) * pow2k,\n          yi = (y(d, i, data) + o) * pow2k,\n          wi = +weight(d, i, data);\n      if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n        var x0 = Math.floor(xi),\n            y0 = Math.floor(yi),\n            xt = xi - x0 - 0.5,\n            yt = yi - y0 - 0.5;\n        values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n        values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n        values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n        values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n      }\n    }\n\n    (0,d3_array__WEBPACK_IMPORTED_MODULE_1__.blur2)({data: values, width: n, height: m}, r * pow2k);\n    return values;\n  }\n\n  function density(data) {\n    var values = grid(data),\n        tz = threshold(values),\n        pow4k = Math.pow(2, 2 * k);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Number.MIN_VALUE, (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k, tz);\n    }\n\n    return (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n        .size([n, m])\n        .thresholds(tz.map(d => d * pow4k))\n      (values)\n        .map((c, i) => (c.value = +tz[i], transform(c)));\n  }\n\n  density.contours = function(data) {\n    var values = grid(data),\n        contours = (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([n, m]),\n        pow4k = Math.pow(2, 2 * k),\n        contour = value => {\n          value = +value;\n          var c = transform(contours.contour(values, value * pow4k));\n          c.value = value; // preserve exact threshold value\n          return c;\n        };\n    Object.defineProperty(contour, \"max\", {get: () => (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k});\n    return contour;\n  };\n\n  function transform(geometry) {\n    geometry.coordinates.forEach(transformPolygon);\n    return geometry;\n  }\n\n  function transformPolygon(coordinates) {\n    coordinates.forEach(transformRing);\n  }\n\n  function transformRing(coordinates) {\n    coordinates.forEach(transformPoint);\n  }\n\n  // TODO Optimize.\n  function transformPoint(coordinates) {\n    coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n    coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n  }\n\n  function resize() {\n    o = r * 3;\n    n = (dx + o * 2) >> k;\n    m = (dy + o * 2) >> k;\n    return density;\n  }\n\n  density.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : x;\n  };\n\n  density.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : y;\n  };\n\n  density.weight = function(_) {\n    return arguments.length ? (weight = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : weight;\n  };\n\n  density.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = +_[0], _1 = +_[1];\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, resize();\n  };\n\n  density.cellSize = function(_) {\n    if (!arguments.length) return 1 << k;\n    if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n    return k = Math.floor(Math.log(_) / Math.LN2), resize();\n  };\n\n  density.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_5__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_), density) : threshold;\n  };\n\n  density.bandwidth = function(_) {\n    if (!arguments.length) return Math.sqrt(r * (r + 1));\n    if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n    return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n  };\n\n  return density;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/density.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contourDensity: () => (/* reexport safe */ _density_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   contours: () => (/* reexport safe */ _contours_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n/* harmony import */ var _density_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./density.js */ \"(ssr)/./node_modules/d3-contour/src/density.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRDtBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1jb250b3VyL3NyYy9pbmRleC5qcz85MjViIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7ZGVmYXVsdCBhcyBjb250b3Vyc30gZnJvbSBcIi4vY29udG91cnMuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBjb250b3VyRGVuc2l0eX0gZnJvbSBcIi4vZGVuc2l0eS5qc1wiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/noop.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/noop.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvbm9vcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsc0NBQVciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWNvbnRvdXIvc3JjL25vb3AuanM/YzBlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHt9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/noop.js\n");

/***/ })

};
;