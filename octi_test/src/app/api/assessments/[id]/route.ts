/**
 * OCTI智能评估系统 - 单个评估API路由
 * 
 * 提供单个评估的详细操作接口
 * 支持获取、更新、删除评估等操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { assessmentService } from '@/services/assessment-service';
import { prisma } from '@/lib/prisma';
import { createErrorResponse, createSuccessResponse, validatePathParams } from '@/lib/api-utils';

// ============================================================================
// 请求验证Schema
// ============================================================================

const PathParamsSchema = z.object({
  id: z.string().uuid('评估ID格式无效'),
});

const UpdateAssessmentSchema = z.object({
  title: z.string().min(1, '标题不能为空').optional(),
  description: z.string().optional(),
  status: z.enum(['DRAFT', 'PROFILE_COLLECTION', 'QUESTIONNAIRE_GENERATION', 'IN_PROGRESS', 'ANALYSIS', 'COMPLETED', 'ARCHIVED', 'CANCELLED']).optional(),
});

// ============================================================================
// GET /api/assessments/[id] - 获取评估详情
// ============================================================================

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证路径参数
    const { id } = PathParamsSchema.parse(params);

    // 查询评估详情
    const assessment = await prisma.assessment.findUnique({
      where: { id },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true,
            scale: true,
            stage: true,
            description: true,
          },
        },
        nonprofitProfile: {
          select: {
            id: true,
            organizationType: true,
            serviceArea: true,
            organizationScale: true,
            developmentStage: true,
            operatingModel: true,
            impactPositioning: true,
            missionVision: true,
            challenges: true,
            goals: true,
          },
        },
        questionnaire: {
          include: {
            questions: {
              orderBy: { order: 'asc' },
              select: {
                id: true,
                type: true,
                source: true,
                category: true,
                subCategory: true,
                title: true,
                description: true,
                options: true,
                required: true,
                order: true,
              },
            },
          },
        },
        responses: {
          select: {
            id: true,
            questionId: true,
            answer: true,
            confidence: true,
            timeSpent: true,
            createdAt: true,
          },
        },
        analysisResults: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          select: {
            id: true,
            overallScore: true,
            capabilityScores: true,
            keyFindings: true,
            recommendations: true,
            developmentPriorities: true,
            nextSteps: true,
            confidence: true,
            analysisModel: true,
            createdAt: true,
          },
        },
        reports: {
          orderBy: { generatedAt: 'desc' },
          select: {
            id: true,
            title: true,
            type: true,
            format: true,
            downloadUrl: true,
            shareUrl: true,
            generatedAt: true,
          },
        },
      },
    });

    if (!assessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    // 获取评估进度
    const progress = await assessmentService.getAssessmentProgress(id);

    // 格式化响应数据
    const responseData = {
      ...assessment,
      progress,
      latestAnalysis: assessment.analysisResults[0] || null,
      responseCount: assessment.responses.length,
      questionCount: assessment.questionnaire?.questions.length || 0,
    };

    return createSuccessResponse(responseData);
  } catch (error) {
    console.error('Failed to get assessment:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '路径参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '获取评估详情失败', 500);
  }
}

// ============================================================================
// PUT /api/assessments/[id] - 更新评估
// ============================================================================

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证路径参数
    const { id } = PathParamsSchema.parse(params);

    // 验证请求体
    const body = await request.json();
    const validatedData = UpdateAssessmentSchema.parse(body);

    // 检查评估是否存在
    const existingAssessment = await prisma.assessment.findUnique({
      where: { id },
    });

    if (!existingAssessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    // 检查状态转换是否合法
    if (validatedData.status && !isValidStatusTransition(existingAssessment.status, validatedData.status)) {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '无效的状态转换', 400);
    }

    // 更新评估
    const updatedAssessment = await prisma.assessment.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });

    return createSuccessResponse(updatedAssessment);
  } catch (error) {
    console.error('Failed to update assessment:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '评估更新失败', 500);
  }
}

// ============================================================================
// DELETE /api/assessments/[id] - 删除评估
// ============================================================================

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证路径参数
    const { id } = PathParamsSchema.parse(params);

    // 检查评估是否存在
    const existingAssessment = await prisma.assessment.findUnique({
      where: { id },
    });

    if (!existingAssessment) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    // 检查是否可以删除
    if (existingAssessment.status === 'COMPLETED') {
      return createErrorResponse('BUSINESS_LOGIC_ERROR', '已完成的评估不能删除', 400);
    }

    // 软删除评估（更新状态为CANCELLED）
    await prisma.assessment.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date(),
      },
    });

    return createSuccessResponse({ message: '评估已删除' });
  } catch (error) {
    console.error('Failed to delete assessment:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '路径参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '评估删除失败', 500);
  }
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 检查状态转换是否合法
 * 
 * @param currentStatus - 当前状态
 * @param newStatus - 新状态
 * @returns 是否合法
 */
function isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
  const validTransitions: Record<string, string[]> = {
    'DRAFT': ['PROFILE_COLLECTION', 'CANCELLED'],
    'PROFILE_COLLECTION': ['QUESTIONNAIRE_GENERATION', 'CANCELLED'],
    'QUESTIONNAIRE_GENERATION': ['IN_PROGRESS', 'CANCELLED'],
    'IN_PROGRESS': ['ANALYSIS', 'CANCELLED'],
    'ANALYSIS': ['COMPLETED', 'FAILED'],
    'COMPLETED': ['ARCHIVED'],
    'FAILED': ['DRAFT', 'CANCELLED'],
    'CANCELLED': ['DRAFT'],
    'ARCHIVED': [],
  };

  return validTransitions[currentStatus]?.includes(newStatus) || false;
}
