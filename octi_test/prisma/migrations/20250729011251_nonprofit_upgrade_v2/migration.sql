/*
  Warnings:

  - The values [CHARITY,SOCIAL_ENTERPRISE,COMMUNITY_GROUP] on the enum `OrganizationType` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "DialogueStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'PAUSED', 'CANCELLED');

-- Create<PERSON>num
CREATE TYPE "MessageType" AS ENUM ('SYSTEM', 'USER', 'ASSISTANT');

-- CreateEnum
CREATE TYPE "QuestionnaireStatus" AS ENUM ('DRAFT', 'ACTIVE', 'COMPLETED', 'ARCHIVED');

-- AlterEnum
BEGIN;
CREATE TYPE "OrganizationType_new" AS ENUM ('FOUNDATION', 'NGO', 'SOCIAL_GROUP', 'PRIVATE_NON_ENTERPRISE', 'INTERNATIONAL_NGO', 'GOVERNMENT_AGENCY', 'OTHER');
ALTER TABLE "organizations" ALTER COLUMN "type" TYPE "OrganizationType_new" USING ("type"::text::"OrganizationType_new");
ALTER TYPE "OrganizationType" RENAME TO "OrganizationType_old";
ALTER TYPE "OrganizationType_new" RENAME TO "OrganizationType";
DROP TYPE "OrganizationType_old";
COMMIT;

-- AlterTable
ALTER TABLE "assessments" ADD COLUMN     "contextualFactors" JSONB,
ADD COLUMN     "nonprofitProfileId" TEXT,
ADD COLUMN     "nonprofitSpecificAnalysis" JSONB,
ADD COLUMN     "questionnaireId" TEXT;

-- CreateTable
CREATE TABLE "nonprofit_profiles" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "organizationType" TEXT NOT NULL,
    "serviceArea" JSONB NOT NULL,
    "organizationScale" TEXT NOT NULL,
    "developmentStage" TEXT NOT NULL,
    "operatingModel" TEXT NOT NULL,
    "impactPositioning" TEXT NOT NULL,
    "organizationalCulture" TEXT NOT NULL,
    "missionVision" JSONB NOT NULL,
    "governance" JSONB NOT NULL,
    "resourceProfile" JSONB NOT NULL,
    "impactMeasurement" JSONB NOT NULL,
    "challenges" JSONB NOT NULL,
    "goals" JSONB NOT NULL,
    "region" TEXT NOT NULL,
    "foundedYear" INTEGER,
    "keyMetrics" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "nonprofit_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dialogue_sessions" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "nonprofitProfileId" TEXT,
    "status" "DialogueStatus" NOT NULL DEFAULT 'ACTIVE',
    "currentRound" INTEGER NOT NULL DEFAULT 1,
    "totalRounds" INTEGER NOT NULL DEFAULT 5,
    "conversationHistory" JSONB NOT NULL,
    "extractedInsights" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "dialogue_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dialogue_messages" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "type" "MessageType" NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" JSONB,
    "roundNumber" INTEGER NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "dialogue_messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "intelligent_questionnaires" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "nonprofitProfileId" TEXT,
    "sessionId" TEXT,
    "questionnaireStructure" JSONB NOT NULL,
    "generationContext" JSONB NOT NULL,
    "status" "QuestionnaireStatus" NOT NULL DEFAULT 'DRAFT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "intelligent_questionnaires_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_agent_configs" (
    "id" TEXT NOT NULL,
    "agentType" TEXT NOT NULL,
    "configName" TEXT NOT NULL,
    "promptTemplate" JSONB NOT NULL,
    "parameters" JSONB NOT NULL,
    "version" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ai_agent_configs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "nonprofit_profiles_organizationId_key" ON "nonprofit_profiles"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "ai_agent_configs_agentType_version_key" ON "ai_agent_configs"("agentType", "version");

-- AddForeignKey
ALTER TABLE "nonprofit_profiles" ADD CONSTRAINT "nonprofit_profiles_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dialogue_sessions" ADD CONSTRAINT "dialogue_sessions_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dialogue_sessions" ADD CONSTRAINT "dialogue_sessions_nonprofitProfileId_fkey" FOREIGN KEY ("nonprofitProfileId") REFERENCES "nonprofit_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dialogue_messages" ADD CONSTRAINT "dialogue_messages_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "dialogue_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "intelligent_questionnaires" ADD CONSTRAINT "intelligent_questionnaires_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "intelligent_questionnaires" ADD CONSTRAINT "intelligent_questionnaires_nonprofitProfileId_fkey" FOREIGN KEY ("nonprofitProfileId") REFERENCES "nonprofit_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "intelligent_questionnaires" ADD CONSTRAINT "intelligent_questionnaires_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "dialogue_sessions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessments" ADD CONSTRAINT "assessments_nonprofitProfileId_fkey" FOREIGN KEY ("nonprofitProfileId") REFERENCES "nonprofit_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessments" ADD CONSTRAINT "assessments_questionnaireId_fkey" FOREIGN KEY ("questionnaireId") REFERENCES "intelligent_questionnaires"("id") ON DELETE SET NULL ON UPDATE CASCADE;
