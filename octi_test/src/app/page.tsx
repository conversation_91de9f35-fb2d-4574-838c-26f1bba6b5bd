import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Brain, Clock, FileText, CheckCircle, Zap, Star } from 'lucide-react';
import { UserLayout, UserPageContainer } from '@/components/layouts/user-layout';

export const metadata: Metadata = {
  title: 'OCTI组织能力评估',
  description: 'OCTI智能评估系统 - 为公益机构提供专业的组织能力评估服务',
};

/**
 * OCTI智能评估系统 - 用户端首页
 *
 * 专注于评估的简洁首页，为用户提供直观的评估入口
 */
export default function HomePage() {
  return (
    <UserLayout>
      <UserPageContainer className="text-center py-12">

        {/* 主标题区域 */}
        <div className="mb-12">
          <div className="mb-6">
            <Brain className="h-16 w-16 text-primary mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              OCTI组织能力评估
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              专业的公益机构组织能力评估工具，帮助您了解组织现状，制定发展计划
            </p>
          </div>
        </div>

        {/* 评估信息概览 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8 max-w-4xl mx-auto">
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <Clock className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">30-45分钟</div>
              <div className="text-sm text-gray-600">完成时间</div>
            </div>
            <div className="text-center">
              <FileText className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">60道题目</div>
              <div className="text-sm text-gray-600">评估问题</div>
            </div>
            <div className="text-center">
              <CheckCircle className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">专业报告</div>
              <div className="text-sm text-gray-600">分析结果</div>
            </div>
          </div>

          {/* 版本选择 */}
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">选择评估版本</h2>
            <p className="text-gray-600">根据您的需求选择合适的分析深度</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* 标准版 */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-6 hover:border-blue-300 transition-colors">
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                  <Brain className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">标准版分析</h3>
                <p className="text-sm text-gray-600 mt-1">专业AI分析，全面评估</p>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>组织能力评估师智能体</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>OCTI四维能力评估</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>基础发展建议报告</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>核心能力优化方案</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">¥99</div>
                <Link
                  href="/assessment/start?version=standard"
                  className="inline-flex items-center justify-center w-full gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  选择标准版
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* 专业版 */}
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 border-2 border-purple-300 rounded-xl p-6 hover:border-purple-400 transition-colors relative">
              {/* 推荐标签 */}
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-semibold flex items-center">
                  <Star className="h-3 w-3 mr-1" />
                  推荐
                </div>
              </div>

              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">专业版分析</h3>
                <p className="text-sm text-gray-600 mt-1">双模型深度分析，战略洞察</p>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>双智能体协作分析</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>深度推理与战略洞察</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>增强版分析报告</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>系统性发展规划</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">¥199</div>
                <Link
                  href="/assessment/start?version=professional"
                  className="inline-flex items-center justify-center w-full gap-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg text-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors"
                >
                  选择专业版
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* 评估流程说明 */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">评估流程</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  1
                </div>
                <h3 className="text-lg font-semibold text-gray-900">组织信息收集</h3>
              </div>
              <p className="text-gray-600">
                填写您的组织基本信息，包括组织类型、规模、主要业务领域等，帮助我们为您定制评估内容。
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  2
                </div>
                <h3 className="text-lg font-semibold text-gray-900">能力评估问卷</h3>
              </div>
              <p className="text-gray-600">
                回答60道专业设计的问题，涵盖组织治理、项目管理、资源筹集、影响力评估等核心能力领域。
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  3
                </div>
                <h3 className="text-lg font-semibold text-gray-900">智能分析</h3>
              </div>
              <p className="text-gray-600">
                AI系统将分析您的回答，识别组织优势和改进空间，生成个性化的能力评估报告。
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  4
                </div>
                <h3 className="text-lg font-semibold text-gray-900">发展建议</h3>
              </div>
              <p className="text-gray-600">
                获得针对性的组织发展建议和具体的行动计划，助力您的组织持续成长。
              </p>
            </div>
          </div>
        </div>

      </UserPageContainer>
    </UserLayout>
  );
}
