/**
 * OCTI智能评估系统 - 认证页面布局组件
 * 
 * 用于登录、注册、找回密码等认证相关页面
 */

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  footer?: React.ReactNode;
  showLogo?: boolean;
  className?: string;
}

// ============================================================================
// 认证布局组件
// ============================================================================

export function AuthLayout({
  children,
  title,
  description,
  footer,
  showLogo = true,
  className,
}: AuthLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex flex-1 flex-col items-center justify-center p-4 sm:p-8">
        <div className={cn(
          'mx-auto w-full max-w-md space-y-6 rounded-lg border bg-card p-6 shadow-sm',
          className
        )}>
          {/* 标题和Logo */}
          <div className="space-y-2 text-center">
            {showLogo && (
              <Link href="/" className="inline-block">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-green-600 mx-auto">
                  <span className="text-xl font-bold text-white">O</span>
                </div>
              </Link>
            )}
            <h1 className="text-2xl font-bold">{title}</h1>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>

          {/* 主要内容 */}
          <div>{children}</div>

          {/* 页脚 */}
          {footer && <div className="text-center text-sm">{footer}</div>}
        </div>
      </div>

      {/* 页面底部 */}
      <footer className="py-6 text-center text-sm text-muted-foreground">
        <div className="container mx-auto">
          <p>© 2024 OCTI智能评估系统 v4.0 - 基于配置驱动和智能体模块化架构</p>
        </div>
      </footer>
    </div>
  );
}

// ============================================================================
// 认证页面分隔线
// ============================================================================

interface AuthDividerProps {
  text?: string;
  className?: string;
}

export function AuthDivider({ text = '或', className }: AuthDividerProps) {
  return (
    <div className={cn('relative my-6', className)}>
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t"></div>
      </div>
      {text && (
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-card px-2 text-muted-foreground">{text}</span>
        </div>
      )}
    </div>
  );
}

// ============================================================================
// 认证页面错误消息
// ============================================================================

interface AuthErrorProps {
  message: string;
  className?: string;
}

export function AuthError({ message, className }: AuthErrorProps) {
  if (!message) return null;
  
  return (
    <div className={cn(
      'rounded-md bg-destructive/10 p-3 text-sm text-destructive',
      className
    )}>
      <p>{message}</p>
    </div>
  );
}

// ============================================================================
// 认证页面成功消息
// ============================================================================

interface AuthSuccessProps {
  message: string;
  className?: string;
}

export function AuthSuccess({ message, className }: AuthSuccessProps) {
  if (!message) return null;
  
  return (
    <div className={cn(
      'rounded-md bg-green-50 p-3 text-sm text-green-600',
      className
    )}>
      <p>{message}</p>
    </div>
  );
}
