/**
 * OCTI智能评估系统 - 评估对话API路由
 * 
 * 提供评估过程中的对话交互接口
 * 支持开始对话、发送消息、获取对话历史等操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { assessmentService } from '@/services/assessment-service';
import { dialogueManager } from '@/services/dialogue-manager';
import { createErrorResponse, createSuccessResponse, validatePathParams } from '@/lib/api-utils';

// ============================================================================
// 请求验证Schema
// ============================================================================

const PathParamsSchema = z.object({
  id: z.string().uuid('评估ID格式无效'),
});

const SendMessageSchema = z.object({
  sessionId: z.string().uuid('会话ID格式无效').optional(),
  message: z.string().min(1, '消息内容不能为空').max(2000, '消息内容过长'),
  messageType: z.enum(['text', 'selection']).default('text'),
});

// ============================================================================
// POST /api/assessments/[id]/dialogue - 开始对话或发送消息
// ============================================================================

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 验证请求体
    const body = await request.json();
    const validatedData = SendMessageSchema.parse(body);

    let response;

    if (validatedData.sessionId) {
      // 发送消息到现有会话
      response = await assessmentService.processDialogueMessage(
        validatedData.sessionId,
        validatedData.message
      );
    } else {
      // 开始新的对话会话
      response = await assessmentService.startProfileCollection(assessmentId);
    }

    return createSuccessResponse(response);
  } catch (error) {
    console.error('Failed to handle dialogue:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    if (error.message.includes('评估不存在')) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '评估不存在', 404);
    }

    return createErrorResponse('INTERNAL_ERROR', '对话处理失败', 500);
  }
}

// ============================================================================
// GET /api/assessments/[id]/dialogue - 获取对话历史
// ============================================================================

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return createErrorResponse('VALIDATION_ERROR', '缺少会话ID参数', 400);
    }

    // 验证会话ID格式
    const sessionIdSchema = z.string().uuid('会话ID格式无效');
    const validatedSessionId = sessionIdSchema.parse(sessionId);

    // 获取对话历史
    const dialogueHistory = await dialogueManager.getDialogueHistory(validatedSessionId);

    // 获取会话状态
    const sessionStatus = await dialogueManager.getSessionStatus(validatedSessionId);

    if (!sessionStatus) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '对话会话不存在', 404);
    }

    // 验证会话是否属于该评估
    if (sessionStatus.organizationId !== assessmentId) {
      // 需要通过评估获取组织ID进行验证
      const assessment = await prisma.assessment.findUnique({
        where: { id: assessmentId },
        select: { organizationId: true },
      });

      if (!assessment || assessment.organizationId !== sessionStatus.organizationId) {
        return createErrorResponse('RESOURCE_NOT_FOUND', '对话会话不属于该评估', 404);
      }
    }

    // 格式化响应数据
    const responseData = {
      sessionId: validatedSessionId,
      assessmentId,
      status: sessionStatus.status,
      currentRound: sessionStatus.currentRound,
      totalRounds: sessionStatus.totalRounds,
      progress: (sessionStatus.currentRound / sessionStatus.totalRounds) * 100,
      extractedInsights: sessionStatus.extractedInsights,
      messages: dialogueHistory.map(msg => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        roundNumber: msg.roundNumber,
        timestamp: msg.timestamp,
      })),
      createdAt: sessionStatus.createdAt,
      completedAt: sessionStatus.completedAt,
    };

    return createSuccessResponse(responseData);
  } catch (error) {
    console.error('Failed to get dialogue history:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '获取对话历史失败', 500);
  }
}

// ============================================================================
// PUT /api/assessments/[id]/dialogue - 更新对话状态
// ============================================================================

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证路径参数
    const { id: assessmentId } = PathParamsSchema.parse(params);

    // 验证请求体
    const body = await request.json();
    const updateSchema = z.object({
      sessionId: z.string().uuid('会话ID格式无效'),
      action: z.enum(['complete', 'restart', 'cancel']),
    });

    const { sessionId, action } = updateSchema.parse(body);

    // 获取会话状态
    const sessionStatus = await dialogueManager.getSessionStatus(sessionId);

    if (!sessionStatus) {
      return createErrorResponse('RESOURCE_NOT_FOUND', '对话会话不存在', 404);
    }

    let updatedSession;

    switch (action) {
      case 'complete':
        // 强制完成对话
        updatedSession = await prisma.dialogueSession.update({
          where: { id: sessionId },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
          },
        });
        break;

      case 'restart':
        // 重新开始对话
        updatedSession = await prisma.dialogueSession.update({
          where: { id: sessionId },
          data: {
            status: 'ACTIVE',
            currentRound: 1,
            completedAt: null,
            extractedInsights: {},
          },
        });
        break;

      case 'cancel':
        // 取消对话
        updatedSession = await prisma.dialogueSession.update({
          where: { id: sessionId },
          data: {
            status: 'CANCELLED',
            completedAt: new Date(),
          },
        });
        break;

      default:
        return createErrorResponse('VALIDATION_ERROR', '无效的操作类型', 400);
    }

    return createSuccessResponse({
      sessionId,
      action,
      status: updatedSession.status,
      message: `对话已${action === 'complete' ? '完成' : action === 'restart' ? '重新开始' : '取消'}`,
    });
  } catch (error) {
    console.error('Failed to update dialogue status:', error);

    if (error instanceof z.ZodError) {
      return createErrorResponse('VALIDATION_ERROR', '请求参数验证失败', 400, {
        errors: error.errors,
      });
    }

    return createErrorResponse('INTERNAL_ERROR', '更新对话状态失败', 500);
  }
}
