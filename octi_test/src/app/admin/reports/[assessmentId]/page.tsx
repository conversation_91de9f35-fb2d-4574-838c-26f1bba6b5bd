/**
 * OCTI智能评估系统 - 评估报告页面
 * 
 * 展示详细的评估分析报告和可视化结果
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { MainLayout, PageContainer, CardContainer, LoadingState } from '@/components/layout/main-layout';
import { OCTIRadarChart, ComparisonRadarChart, OCTIDimension } from '@/components/charts/octi-radar-chart';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

const mockAssessmentReport = {
  id: 'assessment-1',
  title: '北京环保基金会 - 组织能力评估报告',
  organization: {
    name: '北京环保基金会',
    type: 'FOUNDATION',
    establishedYear: 2018,
    staffCount: 25,
    volunteerCount: 120,
  },
  assessment: {
    type: 'PROFESSIONAL',
    completedAt: '2024-01-20T16:30:00Z',
    totalQuestions: 60,
    answeredQuestions: 60,
    timeSpent: 2640, // 44分钟
  },
  overallScore: 78.5,
  level: 'GOOD',
  dimensions: [
    {
      dimension: '战略规划与治理',
      code: 'SF',
      score: 82,
      maxScore: 100,
      level: 'GOOD' as const,
      description: '组织在战略规划和治理结构方面表现良好，有明确的使命愿景和较为完善的治理机制。',
      subDimensions: [
        { name: '使命愿景', score: 85, maxScore: 100 },
        { name: '战略规划', score: 78, maxScore: 100 },
        { name: '治理结构', score: 83, maxScore: 100 },
      ],
    },
    {
      dimension: '内部治理与管理',
      code: 'IT',
      score: 75,
      maxScore: 100,
      level: 'AVERAGE' as const,
      description: '内部管理体系基本完善，但在人力资源管理和信息化建设方面还有提升空间。',
      subDimensions: [
        { name: '人力资源', score: 72, maxScore: 100 },
        { name: '财务管理', score: 80, maxScore: 100 },
        { name: '信息系统', score: 73, maxScore: 100 },
      ],
    },
    {
      dimension: '使命价值与影响',
      code: 'MV',
      score: 80,
      maxScore: 100,
      level: 'GOOD' as const,
      description: '在环保领域具有较强的专业能力和社会影响力，项目执行效果良好。',
      subDimensions: [
        { name: '项目执行', score: 82, maxScore: 100 },
        { name: '影响评估', score: 76, maxScore: 100 },
        { name: '社会认知', score: 83, maxScore: 100 },
      ],
    },
    {
      dimension: '适应发展能力',
      code: 'AD',
      score: 77,
      maxScore: 100,
      level: 'AVERAGE' as const,
      description: '组织具备一定的学习和适应能力，但在创新发展和数字化转型方面需要加强。',
      subDimensions: [
        { name: '学习能力', score: 79, maxScore: 100 },
        { name: '创新发展', score: 74, maxScore: 100 },
        { name: '风险管理', score: 78, maxScore: 100 },
      ],
    },
  ] as OCTIDimension[],
  benchmarkData: [
    { dimension: '战略规划与治理', code: 'SF', score: 75, maxScore: 100, level: 'AVERAGE' as const, description: '' },
    { dimension: '内部治理与管理', code: 'IT', score: 72, maxScore: 100, level: 'AVERAGE' as const, description: '' },
    { dimension: '使命价值与影响', code: 'MV', score: 78, maxScore: 100, level: 'AVERAGE' as const, description: '' },
    { dimension: '适应发展能力', code: 'AD', score: 74, maxScore: 100, level: 'AVERAGE' as const, description: '' },
  ] as OCTIDimension[],
  recommendations: [
    {
      category: '战略规划与治理',
      priority: 'HIGH',
      title: '完善战略规划体系',
      description: '建议制定3-5年中长期战略规划，明确发展目标和实施路径。',
      actions: [
        '组织战略规划工作坊，邀请理事会和核心团队参与',
        '制定年度工作计划和关键绩效指标',
        '建立战略执行监督和评估机制',
      ],
    },
    {
      category: '内部治理与管理',
      priority: 'MEDIUM',
      title: '加强人力资源管理',
      description: '完善人员招聘、培训、考核和激励机制，提升团队专业能力。',
      actions: [
        '建立岗位职责说明书和绩效考核体系',
        '制定员工培训发展计划',
        '完善薪酬福利和激励机制',
      ],
    },
    {
      category: '适应发展能力',
      priority: 'MEDIUM',
      title: '推进数字化转型',
      description: '加强信息化建设，提升组织运营效率和服务能力。',
      actions: [
        '引入项目管理和客户关系管理系统',
        '建设官方网站和社交媒体平台',
        '开展数字化技能培训',
      ],
    },
  ],
  insights: [
    '您的组织在战略规划方面表现突出，高于行业平均水平7分',
    '环保项目执行能力强，在同类型基金会中排名前25%',
    '建议重点关注人力资源管理和数字化建设',
    '整体发展态势良好，具备进一步提升的潜力',
  ],
};

// ============================================================================
// 评估报告页面组件
// ============================================================================

export default function AssessmentReportPage() {
  const params = useParams();
  const assessmentId = params.assessmentId as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [report, setReport] = useState(mockAssessmentReport);
  const [activeTab, setActiveTab] = useState<'overview' | 'dimensions' | 'recommendations' | 'comparison'>('overview');

  useEffect(() => {
    const loadReport = async () => {
      try {
        setIsLoading(true);
        
        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        setReport(mockAssessmentReport);
      } catch (err) {
        console.error('加载报告失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (assessmentId) {
      loadReport();
    }
  }, [assessmentId]);

  const getLevelBadge = (level: string) => {
    const levelConfig = {
      'EXCELLENT': { label: '优秀', color: 'bg-green-100 text-green-800' },
      'GOOD': { label: '良好', color: 'bg-blue-100 text-blue-800' },
      'AVERAGE': { label: '一般', color: 'bg-yellow-100 text-yellow-800' },
      'NEEDS_IMPROVEMENT': { label: '待改进', color: 'bg-orange-100 text-orange-800' },
      'POOR': { label: '较差', color: 'bg-red-100 text-red-800' },
    };
    const config = levelConfig[level as keyof typeof levelConfig] || { label: level, color: 'bg-gray-100 text-gray-800' };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      'HIGH': { label: '高优先级', color: 'bg-red-100 text-red-800' },
      'MEDIUM': { label: '中优先级', color: 'bg-yellow-100 text-yellow-800' },
      'LOW': { label: '低优先级', color: 'bg-green-100 text-green-800' },
    };
    const config = priorityConfig[priority as keyof typeof priorityConfig] || { label: priority, color: 'bg-gray-100 text-gray-800' };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  if (isLoading) {
    return (
      <MainLayout user={mockUser}>
        <PageContainer>
          <LoadingState
            title="正在生成评估报告..."
            description="AI正在分析您的评估结果，请稍候"
          />
        </PageContainer>
      </MainLayout>
    );
  }

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* 总体评分 */}
      <CardContainer title="总体评估结果">
        <div className="text-center space-y-4">
          <div>
            <div className="text-4xl font-bold text-primary mb-2">{report.overallScore}</div>
            <div className="text-lg text-muted-foreground">总体评分</div>
            {getLevelBadge(report.level)}
          </div>
          
          <div className="grid md:grid-cols-4 gap-4 mt-6">
            {report.dimensions.map((dim, index) => (
              <div key={index} className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold mb-1">{dim.score}</div>
                <div className="text-sm text-muted-foreground mb-2">{dim.code}</div>
                {getLevelBadge(dim.level)}
              </div>
            ))}
          </div>
        </div>
      </CardContainer>

      {/* OCTI雷达图 */}
      <CardContainer title="OCTI能力雷达图">
        <OCTIRadarChart
          data={report.dimensions}
          showSubDimensions={false}
          size="lg"
        />
      </CardContainer>

      {/* 关键洞察 */}
      <CardContainer title="关键洞察" description="基于AI分析的重要发现">
        <div className="space-y-3">
          {report.insights.map((insight, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
              <div className="text-primary font-bold">•</div>
              <p className="text-sm">{insight}</p>
            </div>
          ))}
        </div>
      </CardContainer>
    </div>
  );

  const renderDimensionsTab = () => (
    <div className="space-y-6">
      <OCTIRadarChart
        data={report.dimensions}
        showSubDimensions={true}
        size="lg"
      />
    </div>
  );

  const renderRecommendationsTab = () => (
    <div className="space-y-6">
      {report.recommendations.map((rec, index) => (
        <CardContainer
          key={index}
          title={rec.title}
          description={rec.description}
          actions={getPriorityBadge(rec.priority)}
        >
          <div className="space-y-3">
            <h4 className="font-medium">建议行动:</h4>
            <ul className="space-y-2">
              {rec.actions.map((action, actionIndex) => (
                <li key={actionIndex} className="flex items-start space-x-2">
                  <span className="text-primary">•</span>
                  <span className="text-sm">{action}</span>
                </li>
              ))}
            </ul>
          </div>
        </CardContainer>
      ))}
    </div>
  );

  const renderComparisonTab = () => (
    <div className="space-y-6">
      <ComparisonRadarChart
        currentData={report.dimensions}
        benchmarkData={report.benchmarkData}
        currentLabel={report.organization.name}
        benchmarkLabel="行业基准"
      />
      
      <CardContainer title="对比分析">
        <div className="space-y-4">
          {report.dimensions.map((dim, index) => {
            const benchmark = report.benchmarkData[index];
            const difference = dim.score - benchmark.score;
            return (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">{dim.dimension}</h4>
                  <p className="text-sm text-muted-foreground">{dim.description}</p>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-bold ${difference >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {difference >= 0 ? '+' : ''}{difference}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    vs 基准 {benchmark.score}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContainer>
    </div>
  );

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title={report.title}
        description={`评估完成时间: ${new Date(report.assessment.completedAt).toLocaleString('zh-CN')}`}
        breadcrumb={[
          { title: '首页', href: '/' },
          { title: '评估管理', href: '/assessments' },
          { title: '评估报告' },
        ]}
        actions={
          <div className="flex space-x-2">
            <Button variant="outline">分享报告</Button>
            <Button>下载PDF</Button>
          </div>
        }
      >
        {/* 基本信息 */}
        <Alert className="mb-6">
          <AlertDescription>
            <div className="grid md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">组织类型:</span>
                <div className="font-medium">{report.organization.type === 'FOUNDATION' ? '基金会' : '其他'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">评估类型:</span>
                <div className="font-medium">{report.assessment.type === 'PROFESSIONAL' ? '专业版' : '标准版'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">完成度:</span>
                <div className="font-medium">{report.assessment.answeredQuestions}/{report.assessment.totalQuestions}</div>
              </div>
              <div>
                <span className="text-muted-foreground">用时:</span>
                <div className="font-medium">{Math.floor(report.assessment.timeSpent / 60)}分钟</div>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        {/* 标签页导航 */}
        <div className="flex space-x-1 p-1 bg-muted rounded-lg w-fit mb-6">
          {[
            { key: 'overview', label: '总览' },
            { key: 'dimensions', label: '维度分析' },
            { key: 'recommendations', label: '改进建议' },
            { key: 'comparison', label: '对比分析' },
          ].map((tab) => (
            <button
              key={tab.key}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 标签页内容 */}
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'dimensions' && renderDimensionsTab()}
        {activeTab === 'recommendations' && renderRecommendationsTab()}
        {activeTab === 'comparison' && renderComparisonTab()}
      </PageContainer>
    </MainLayout>
  );
}
