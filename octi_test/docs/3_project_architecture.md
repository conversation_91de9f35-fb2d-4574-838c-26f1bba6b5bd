# OCTI智能评估系统 - 项目架构文档

**版本**: 4.0 - 智能体配置架构  
**日期**: 2025年7月27日  
**项目**: 组织能力OCTI评估系统 (Organizational Capacity Type Indicator)  
**架构类型**: 配置驱动 + 智能体模块化

---

## 📋 架构变更记录

| 版本 | 日期 | 架构变更 | 影响范围 |
|------|------|---------|----------|
| v3.0 | 2025-07-27 | 统一问卷架构，优化数据流 | 前端组件、API设计 |
| v4.0 | 2025-07-27 | **智能体配置化架构重构** | 全栈架构升级 |

---

## 1. 架构总览

### 1.1 核心架构理念

**配置驱动 + 智能体模块化**
- 业务逻辑与配置解耦，支持快速迭代
- 智能体模块化设计，支持独立优化
- 微服务化API设计，支持水平扩展
- 前后端分离，支持多端适配

### 1.2 技术栈架构

```mermaid
graph TB
    subgraph "前端层 - Next.js 14"
        A["React组件层"]
        B["状态管理 - Zustand"]
        C["UI组件库 - Shadcn/ui"]
        D["表单处理 - React Hook Form"]
    end
    
    subgraph "API网关层"
        E["Next.js API Routes"]
        F["中间件 - 限流/认证"]
        G["API缓存层"]
    end
    
    subgraph "智能体服务层"
        H["问卷智能体"]
        I["分析智能体"]
        J["配置管理器"]
    end
    
    subgraph "LLM服务层"
        K["MiniMax API"]
        L["DeepSeek API"]
        M["API代理服务"]
    end
    
    subgraph "数据层"
        N["PostgreSQL"]
        O["Redis缓存"]
        P["Prisma ORM"]
    end
    
    A --> E
    B --> E
    E --> H
    E --> I
    H --> K
    I --> K
    I --> L
    E --> N
    E --> O
    N --> P
```

### 1.3 系统分层架构

| 层级 | 技术栈 | 职责 | 关键组件 |
|------|--------|------|----------|
| **表现层** | Next.js 14 + TypeScript | 用户界面、交互逻辑 | React组件、页面路由 |
| **API层** | Next.js API Routes | 业务接口、数据转换 | RESTful API、中间件 |
| **服务层** | Node.js + TypeScript | 业务逻辑、智能体服务 | 问卷生成、结果分析 |
| **集成层** | HTTP Client + 代理 | 外部服务集成 | LLM API、第三方服务 |
| **数据层** | PostgreSQL + Redis | 数据存储、缓存 | 用户数据、评估结果 |

---

## 2. 智能体架构设计

### 2.1 智能体系统总览

```mermaid
graph LR
    subgraph "智能体配置系统"
        A["配置管理器"]
        B["版本控制"]
        C["热更新机制"]
    end
    
    subgraph "问卷智能体"
        D["组织画像分析"]
        E["问题生成引擎"]
        F["混合问卷融合"]
    end
    
    subgraph "分析智能体"
        G["单模型分析"]
        H["双模型协作"]
        I["报告生成"]
    end
    
    A --> D
    A --> G
    D --> E
    E --> F
    G --> I
    H --> I
```

### 2.2 问卷智能体架构

#### 2.2.1 组件设计
```typescript
/**
 * 问卷智能体核心接口
 */
interface QuestionnaireAgent {
  /** 配置文件路径 */
  configPath: string;
  
  /** 生成个性化问卷 */
  generateQuestionnaire(profile: OrganizationProfile): Promise<Question[]>;
  
  /** 融合预设问题与智能生成问题 */
  mergeQuestions(preset: Question[], generated: Question[]): Question[];
  
  /** 验证问卷质量 */
  validateQuestionnaire(questions: Question[]): ValidationResult;
}

/**
 * 组织画像接口
 */
interface OrganizationProfile {
  /** 组织类型 */
  organizationType: string;
  
  /** 规模大小 */
  organizationSize: 'small' | 'medium' | 'large';
  
  /** 发展阶段 */
  developmentStage: 'startup' | 'growth' | 'mature';
  
  /** 核心领域 */
  focusAreas: string[];
  
  /** 评估版本 */
  assessmentVersion: 'standard' | 'professional';
}
```

#### 2.2.2 配置驱动流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 画像收集器
    participant QA as 问卷智能体
    participant C as 配置管理器
    participant LLM as LLM服务
    
    U->>P: 完成10题画像收集
    P->>QA: 传递组织画像
    QA->>C: 读取配置文件
    C->>QA: 返回生成策略
    QA->>LLM: 基于画像生成28题
    LLM->>QA: 返回个性化题目
    QA->>QA: 融合32预设+28生成
    QA->>U: 返回60题完整问卷
```

### 2.3 分析智能体架构

#### 2.3.1 双模型协作设计
```typescript
/**
 * 分析智能体接口
 */
interface AnalysisAgent {
  /** 配置文件路径 */
  configPath: string;
  
  /** 单模型分析（标准版） */
  analyzeSingle(responses: QuestionnaireResponse[]): Promise<AnalysisResult>;
  
  /** 双模型协作分析（专业版） */
  analyzeDual(responses: QuestionnaireResponse[]): Promise<EnhancedAnalysisResult>;
  
  /** 生成结构化报告 */
  generateReport(analysis: AnalysisResult, version: 'standard' | 'professional'): Promise<Report>;
}

/**
 * 双模型协作流程
 */
interface DualModelAnalysis {
  /** MiniMax综合分析 */
  primaryAnalysis: AnalysisResult;
  
  /** DeepSeek深度推理 */
  deepReasoningAnalysis: ReasoningResult;
  
  /** 交叉验证结果 */
  crossValidation: ValidationResult;
  
  /** 融合分析结果 */
  fusedAnalysis: EnhancedAnalysisResult;
}
```

#### 2.3.2 分析流程架构
```mermaid
sequenceDiagram
    participant U as 用户
    participant AA as 分析智能体
    participant MM as MiniMax
    participant DS as DeepSeek
    participant RM as 报告模块
    
    U->>AA: 提交问卷答案
    
    alt 标准版分析
        AA->>MM: 单模型分析请求
        MM->>AA: 返回基础分析
    else 专业版分析
        AA->>MM: 综合分析请求
        AA->>DS: 深度推理请求
        MM->>AA: 返回综合分析
        DS->>AA: 返回深度洞察
        AA->>AA: 交叉验证与融合
    end
    
    AA->>RM: 生成结构化报告
    RM->>U: 返回个性化报告
```

---

## 3. 数据架构设计

### 3.1 数据库架构

#### 3.1.1 核心数据模型
```mermaid
erDiagram
    User {
        string id PK
        string email
        string name
        datetime createdAt
        datetime updatedAt
    }
    
    Organization {
        string id PK
        string name
        string type
        string size
        string stage
        json profile
        datetime createdAt
    }
    
    Assessment {
        string id PK
        string userId FK
        string organizationId FK
        string type
        string status
        json responses
        json analysis
        json report
        datetime createdAt
        datetime completedAt
    }
    
    Question {
        string id PK
        string dimension
        string subDimension
        string type
        json content
        boolean isPreset
        datetime createdAt
    }
    
    AgentConfig {
        string id PK
        string agentType
        string version
        json config
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    User ||--o{ Assessment : "创建"
    Organization ||--o{ Assessment : "评估"
    Assessment ||--o{ Question : "包含"
```

#### 3.1.2 Prisma Schema设计
```prisma
model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  assessments  Assessment[]
  organizations Organization[]
  
  @@map("users")
}

model Organization {
  id           String   @id @default(cuid())
  name         String
  type         String
  size         OrganizationSize
  stage        DevelopmentStage
  profile      Json?
  userId       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  user         User     @relation(fields: [userId], references: [id])
  assessments  Assessment[]
  
  @@map("organizations")
}

model Assessment {
  id             String   @id @default(cuid())
  userId         String
  organizationId String?
  type           AssessmentType
  status         AssessmentStatus
  responses      Json?
  analysis       Json?
  report         Json?
  createdAt      DateTime @default(now())
  completedAt    DateTime?
  updatedAt      DateTime @updatedAt
  
  user           User     @relation(fields: [userId], references: [id])
  organization   Organization? @relation(fields: [organizationId], references: [id])
  
  @@map("assessments")
}

model AgentConfig {
  id         String   @id @default(cuid())
  agentType  AgentType
  version    String
  config     Json
  isActive   Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  @@unique([agentType, version])
  @@map("agent_configs")
}

enum AssessmentType {
  STANDARD
  PROFESSIONAL
}

enum AssessmentStatus {
  DRAFT
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum AgentType {
  QUESTION_DESIGNER
  ORGANIZATION_TUTOR
}

enum OrganizationSize {
  SMALL
  MEDIUM
  LARGE
}

enum DevelopmentStage {
  STARTUP
  GROWTH
  MATURE
}
```

### 3.2 缓存架构

#### 3.2.1 Redis缓存策略
```typescript
/**
 * 缓存键命名规范
 */
const CACHE_KEYS = {
  // 配置缓存
  AGENT_CONFIG: (type: string, version: string) => `config:${type}:${version}`,
  
  // 问卷缓存
  PRESET_QUESTIONS: 'questions:preset',
  GENERATED_QUESTIONS: (profileHash: string) => `questions:generated:${profileHash}`,
  
  // 分析结果缓存
  ANALYSIS_RESULT: (assessmentId: string) => `analysis:${assessmentId}`,
  
  // 用户会话缓存
  USER_SESSION: (userId: string) => `session:${userId}`,
};

/**
 * 缓存过期时间配置
 */
const CACHE_TTL = {
  AGENT_CONFIG: 3600,      // 1小时
  PRESET_QUESTIONS: 86400,  // 24小时
  GENERATED_QUESTIONS: 1800, // 30分钟
  ANALYSIS_RESULT: 7200,    // 2小时
  USER_SESSION: 1800,       // 30分钟
};
```

#### 3.2.2 缓存更新策略
```mermaid
graph TD
    A["配置更新"] --> B{"版本检查"}
    B -->|新版本| C["清除旧缓存"]
    B -->|相同版本| D["保持缓存"]
    C --> E["加载新配置"]
    E --> F["更新缓存"]
    F --> G["通知服务重载"]
```

---

## 4. API架构设计

### 4.1 RESTful API设计

#### 4.1.1 API路由规范
```typescript
/**
 * API路由定义
 */
const API_ROUTES = {
  // 评估管理
  ASSESSMENTS: {
    CREATE: 'POST /api/v1/assessments',
    GET_BY_ID: 'GET /api/v1/assessments/:id',
    LIST: 'GET /api/v1/assessments',
    UPDATE: 'PUT /api/v1/assessments/:id',
    DELETE: 'DELETE /api/v1/assessments/:id',
  },
  
  // 智能体服务
  AGENTS: {
    QUESTION_DESIGNER: 'POST /api/v1/agents/question-designer',
    ORGANIZATION_TUTOR: 'POST /api/v1/agents/organization-tutor',
    CONFIG_UPDATE: 'PUT /api/v1/agents/config/:type',
  },
  
  // 组织管理
  ORGANIZATIONS: {
    CREATE: 'POST /api/v1/organizations',
    GET_PROFILE: 'GET /api/v1/organizations/:id/profile',
    UPDATE_PROFILE: 'PUT /api/v1/organizations/:id/profile',
  },
  
  // 报告生成
  REPORTS: {
    GENERATE: 'POST /api/v1/reports/generate',
    DOWNLOAD: 'GET /api/v1/reports/:id/download',
    PREVIEW: 'GET /api/v1/reports/:id/preview',
  },
};
```

#### 4.1.2 API响应格式标准
```typescript
/**
 * 统一API响应格式
 */
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

/**
 * 分页响应格式
 */
interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 4.2 中间件架构

#### 4.2.1 API网关中间件
```typescript
/**
 * API网关中间件栈
 */
const MIDDLEWARE_STACK = [
  'cors',           // 跨域处理
  'helmet',         // 安全头设置
  'rateLimit',      // 请求限流
  'auth',           // 身份认证
  'validation',     // 请求验证
  'logging',        // 请求日志
  'cache',          // 响应缓存
  'errorHandler',   // 错误处理
];

/**
 * 限流配置
 */
const RATE_LIMIT_CONFIG = {
  // LLM API调用限流
  LLM_CALLS: {
    windowMs: 60 * 1000,  // 1分钟
    max: 10,              // 最多10次调用
    message: 'LLM API调用频率过高，请稍后再试',
  },
  
  // 评估创建限流
  ASSESSMENT_CREATION: {
    windowMs: 60 * 60 * 1000, // 1小时
    max: 5,                   // 最多5次评估
    message: '评估创建频率过高，请稍后再试',
  },
};
```

---

## 5. 前端架构设计

### 5.1 组件架构

#### 5.1.1 组件层级结构
```
src/components/
├── ui/                 # 基础UI组件（shadcn/ui）
│   ├── button.tsx
│   ├── input.tsx
│   ├── dialog.tsx
│   └── chart.tsx
├── forms/              # 表单组件
│   ├── OrganizationProfileForm.tsx
│   ├── QuestionnaireForm.tsx
│   └── AssessmentForm.tsx
├── questionnaire/      # 问卷相关组件
│   ├── QuestionRenderer.tsx
│   ├── ProgressIndicator.tsx
│   └── QuestionnaireWizard.tsx
├── reports/            # 报告组件
│   ├── ReportViewer.tsx
│   ├── RadarChart.tsx
│   └── AnalysisSection.tsx
├── charts/             # 图表组件
│   ├── OCTIRadarChart.tsx
│   ├── DimensionChart.tsx
│   └── ComparisonChart.tsx
└── layout/             # 布局组件
    ├── Header.tsx
    ├── Sidebar.tsx
    └── Footer.tsx
```

#### 5.1.2 状态管理架构
```typescript
/**
 * Zustand状态管理设计
 */
interface AppState {
  // 用户状态
  user: {
    profile: UserProfile | null;
    isAuthenticated: boolean;
    preferences: UserPreferences;
  };
  
  // 评估状态
  assessment: {
    current: Assessment | null;
    progress: number;
    responses: QuestionnaireResponse[];
    isSubmitting: boolean;
  };
  
  // 组织状态
  organization: {
    profile: OrganizationProfile | null;
    assessments: Assessment[];
    isLoading: boolean;
  };
  
  // UI状态
  ui: {
    theme: 'light' | 'dark';
    sidebarOpen: boolean;
    notifications: Notification[];
  };
}

/**
 * 状态更新动作
 */
interface AppActions {
  // 用户动作
  setUser: (user: UserProfile) => void;
  logout: () => void;
  
  // 评估动作
  startAssessment: (type: AssessmentType) => void;
  updateResponse: (questionId: string, answer: any) => void;
  submitAssessment: () => Promise<void>;
  
  // 组织动作
  setOrganizationProfile: (profile: OrganizationProfile) => void;
  loadAssessments: () => Promise<void>;
}
```

### 5.2 页面路由架构

#### 5.2.1 Next.js App Router结构
```
src/app/
├── layout.tsx                    # 根布局
├── page.tsx                      # 首页
├── globals.css                   # 全局样式
├── assessment/                   # 评估相关页面
│   ├── page.tsx                 # 评估首页
│   ├── [id]/                    # 动态评估页面
│   │   ├── page.tsx            # 评估详情
│   │   └── report/             # 报告页面
│   │       └── page.tsx
│   └── create/                  # 创建评估
│       └── page.tsx
├── questionnaire/               # 问卷页面
│   ├── profile/                # 组织画像收集
│   │   └── page.tsx
│   └── [assessmentId]/         # 问卷答题
│       └── page.tsx
├── dashboard/                   # 仪表板
│   ├── page.tsx
│   └── organizations/
│       └── page.tsx
└── api/                        # API路由
    └── v1/
        ├── assessments/
        ├── agents/
        ├── organizations/
        └── reports/
```

---

## 6. 安全架构设计

### 6.1 API安全策略

#### 6.1.1 LLM API安全
```typescript
/**
 * LLM API代理服务
 */
class LLMApiProxy {
  private apiKeys: Map<string, string>;
  
  constructor() {
    this.apiKeys = new Map([
      ['minimax', process.env.MINIMAX_API_KEY!],
      ['deepseek', process.env.DEEPSEEK_API_KEY!],
    ]);
  }
  
  /**
   * 安全的API调用
   */
  async callLLM(provider: string, payload: any): Promise<any> {
    const apiKey = this.apiKeys.get(provider);
    if (!apiKey) {
      throw new Error(`API key not found for provider: ${provider}`);
    }
    
    // 请求验证
    this.validatePayload(payload);
    
    // 调用外部API
    const response = await this.makeSecureRequest(provider, payload, apiKey);
    
    // 响应过滤
    return this.sanitizeResponse(response);
  }
  
  private validatePayload(payload: any): void {
    // 输入验证逻辑
  }
  
  private sanitizeResponse(response: any): any {
    // 响应清理逻辑
  }
}
```

#### 6.1.2 数据安全策略
```typescript
/**
 * 数据加密服务
 */
class DataEncryptionService {
  private encryptionKey: string;
  
  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY!;
  }
  
  /**
   * 敏感数据加密
   */
  encryptSensitiveData(data: any): string {
    // 使用AES-256-GCM加密
    return encrypt(JSON.stringify(data), this.encryptionKey);
  }
  
  /**
   * 敏感数据解密
   */
  decryptSensitiveData(encryptedData: string): any {
    const decrypted = decrypt(encryptedData, this.encryptionKey);
    return JSON.parse(decrypted);
  }
}
```

### 6.2 认证授权架构

#### 6.2.1 JWT认证流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant A as 认证服务
    participant API as API服务
    participant DB as 数据库
    
    C->>A: 登录请求
    A->>DB: 验证用户凭据
    DB->>A: 返回用户信息
    A->>A: 生成JWT Token
    A->>C: 返回Token
    
    C->>API: 携带Token的API请求
    API->>API: 验证Token
    API->>DB: 查询数据
    DB->>API: 返回数据
    API->>C: 返回响应
```

---

## 7. 性能优化架构

### 7.1 前端性能优化

#### 7.1.1 Next.js优化策略
```typescript
/**
 * 页面级优化配置
 */
export const metadata = {
  title: 'OCTI智能评估系统',
  description: '基于AI的组织能力评估平台',
};

// 静态生成配置
export const revalidate = 3600; // 1小时重新验证

// 动态导入优化
const ReportChart = dynamic(() => import('./ReportChart'), {
  loading: () => <ChartSkeleton />,
  ssr: false, // 图表组件客户端渲染
});

// 图片优化
import Image from 'next/image';

const OptimizedImage = () => (
  <Image
    src="/logo.png"
    alt="OCTI Logo"
    width={200}
    height={100}
    priority // 关键图片预加载
  />
);
```

#### 7.1.2 缓存优化策略
```typescript
/**
 * React Query缓存配置
 */
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,     // 5分钟
      cacheTime: 10 * 60 * 1000,    // 10分钟
      refetchOnWindowFocus: false,
    },
  },
});

/**
 * 智能体调用缓存
 */
const useQuestionGeneration = (profile: OrganizationProfile) => {
  return useQuery({
    queryKey: ['questions', profile],
    queryFn: () => generateQuestions(profile),
    staleTime: 30 * 60 * 1000, // 30分钟缓存
    enabled: !!profile,
  });
};
```

### 7.2 后端性能优化

#### 7.2.1 数据库优化
```sql
-- 索引优化
CREATE INDEX idx_assessments_user_id ON assessments(user_id);
CREATE INDEX idx_assessments_status ON assessments(status);
CREATE INDEX idx_assessments_created_at ON assessments(created_at);

-- 复合索引
CREATE INDEX idx_assessments_user_status ON assessments(user_id, status);

-- 分区表（按时间分区）
CREATE TABLE assessments_2025 PARTITION OF assessments
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

#### 7.2.2 API性能优化
```typescript
/**
 * API响应缓存
 */
const apiCache = new Map<string, { data: any; timestamp: number }>();

const getCachedResponse = (key: string, ttl: number) => {
  const cached = apiCache.get(key);
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }
  return null;
};

/**
 * 批量数据加载
 */
const batchLoader = new DataLoader(async (ids: string[]) => {
  const assessments = await prisma.assessment.findMany({
    where: { id: { in: ids } },
  });
  return ids.map(id => assessments.find(a => a.id === id));
});
```

---

## 8. 监控与运维架构

### 8.1 监控体系

#### 8.1.1 应用监控
```typescript
/**
 * 性能监控配置
 */
const monitoring = {
  // API响应时间监控
  apiResponseTime: {
    threshold: 2000, // 2秒
    alerting: true,
  },
  
  // LLM调用监控
  llmCallMonitoring: {
    successRate: 0.95,  // 95%成功率
    responseTime: 10000, // 10秒超时
  },
  
  // 数据库连接监控
  databaseMonitoring: {
    connectionPool: 20,
    queryTimeout: 5000,
  },
};
```

#### 8.1.2 日志架构
```typescript
/**
 * 结构化日志
 */
interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error';
  service: string;
  traceId: string;
  message: string;
  metadata?: any;
}

/**
 * 日志记录器
 */
class Logger {
  static info(message: string, metadata?: any) {
    this.log('info', message, metadata);
  }
  
  static error(message: string, error?: Error, metadata?: any) {
    this.log('error', message, { error: error?.stack, ...metadata });
  }
  
  private static log(level: string, message: string, metadata?: any) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: level as any,
      service: 'octi-api',
      traceId: this.generateTraceId(),
      message,
      metadata,
    };
    
    console.log(JSON.stringify(entry));
  }
}
```

### 8.2 部署架构

#### 8.2.1 Docker容器化
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

#### 8.2.2 环境配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=octi
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

---

## 9. 扩展性架构

### 9.1 微服务化准备

#### 9.1.1 服务拆分策略
```mermaid
graph TB
    subgraph "当前单体架构"
        A["Next.js应用"]
    end
    
    subgraph "未来微服务架构"
        B["用户服务"]
        C["评估服务"]
        D["智能体服务"]
        E["报告服务"]
        F["通知服务"]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
```

#### 9.1.2 API网关设计
```typescript
/**
 * API网关路由配置
 */
const gatewayConfig = {
  routes: [
    {
      path: '/api/v1/users/*',
      target: 'http://user-service:3001',
      rateLimit: { max: 100, windowMs: 60000 },
    },
    {
      path: '/api/v1/assessments/*',
      target: 'http://assessment-service:3002',
      rateLimit: { max: 50, windowMs: 60000 },
    },
    {
      path: '/api/v1/agents/*',
      target: 'http://agent-service:3003',
      rateLimit: { max: 20, windowMs: 60000 },
    },
  ],
};
```

### 9.2 国际化架构

#### 9.2.1 多语言支持
```typescript
/**
 * 国际化配置
 */
const i18nConfig = {
  locales: ['zh-CN', 'en-US', 'ja-JP'],
  defaultLocale: 'zh-CN',
  domains: [
    {
      domain: 'octi.cn',
      defaultLocale: 'zh-CN',
    },
    {
      domain: 'octi.com',
      defaultLocale: 'en-US',
    },
  ],
};

/**
 * 智能体多语言配置
 */
interface MultiLanguageAgentConfig {
  [locale: string]: {
    questionDesigner: AgentConfig;
    organizationTutor: AgentConfig;
  };
}
```

---

## 10. 总结

### 10.1 架构优势

1. **配置驱动**: 业务逻辑与配置解耦，支持快速迭代
2. **智能体模块化**: 独立的问卷生成和分析服务，易于优化
3. **技术栈现代化**: Next.js 14 + TypeScript，开发效率高
4. **安全性**: LLM API代理，数据加密，认证授权完善
5. **可扩展性**: 微服务化准备，支持水平扩展
6. **性能优化**: 多层缓存，数据库优化，前端优化

### 10.2 技术债务管理

1. **配置版本管理**: 建立完善的配置版本控制机制
2. **监控完善**: 补充业务指标监控和告警
3. **测试覆盖**: 提升单元测试和集成测试覆盖率
4. **文档维护**: 保持架构文档与代码同步更新

### 10.3 未来演进方向

1. **AI能力增强**: 集成更多AI模型，提升分析深度
2. **数据分析**: 构建用户行为分析和业务洞察能力
3. **生态集成**: 与第三方系统集成，扩展应用场景
4. **移动端支持**: 开发移动应用，提升用户体验

---

**文档维护**: 本架构文档应随系统演进持续更新，确保架构设计与实际实现保持一致。