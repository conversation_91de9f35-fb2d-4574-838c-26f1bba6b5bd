'use client';

import { useState } from 'react';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

export default function TestSimplePage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testData = {
    profile: {
      organizationType: '公益组织',
      serviceArea: '教育',
      resourceStructure: '基金会支持型',
      developmentStage: '成长期',
      teamSize: '中型（21-50人）',
      operatingModel: '直接服务型',
      impactScope: '区域影响',
      organizationCulture: '使命驱动',
      challengesPriorities: '资金筹集和团队建设',
      futureVision: '成为区域内教育公益的领导者'
    },
    responses: [
      { questionId: 'SF_P001', answer: 'very_clear' },
      { questionId: 'SF_P002', answer: 'good' },
      { questionId: 'IT_P001', answer: 4 },
      { questionId: 'MV_P001', answer: ['mission_training', 'value_sharing'] },
      { questionId: 'AD_P001', answer: 'excellent' }
    ],
    version: 'standard'
  };

  const handleTest = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🚀 开始测试简化分析API...');
      
      const response = await fetch('/api/assessment/simple-analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData),
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ 测试成功:', data);
        setResult(data);
      } else {
        console.error('❌ 测试失败:', data);
        setResult({ error: data.error || '请求失败' });
      }
    } catch (error) {
      console.error('❌ 网络错误:', error);
      setResult({ error: '网络请求失败' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">OCTI简化分析测试</h1>
      
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">新的简化方案</h2>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>第一部分：快速组织类型判断（无需LLM）</li>
          <li>第二部分：LLM自然文本分析（不强制JSON格式）</li>
          <li>学习成功案例的简单直接模式</li>
          <li>避免复杂的JSON解析和格式要求</li>
        </ul>
      </div>

      <div className="space-y-4">
        <button
          onClick={handleTest}
          disabled={loading}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? '分析中...' : '开始测试简化分析'}
        </button>

        {loading && (
          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="text-yellow-800">正在执行分析，请稍候...</p>
          </div>
        )}

        {result && (
          <div className="space-y-4">
            {result.success ? (
              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-800 mb-4">✅ 分析成功</h3>
                
                {/* 第一部分：组织类型 */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-2">📊 组织类型判断</h4>
                  <div className="bg-white p-4 rounded border">
                    <p><strong>类型代码：</strong>{result.data.organizationType.code}</p>
                    <p><strong>类型名称：</strong>{result.data.organizationType.name}</p>
                    <p><strong>类型描述：</strong>{result.data.organizationType.description}</p>
                    
                    <div className="mt-3">
                      <p><strong>主要特征：</strong></p>
                      <ul className="list-disc list-inside ml-4">
                        {result.data.organizationType.characteristics.map((char: string, index: number) => (
                          <li key={index}>{char}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="mt-3">
                      <p><strong>核心优势：</strong></p>
                      <ul className="list-disc list-inside ml-4">
                        {result.data.organizationType.strengths.map((strength: string, index: number) => (
                          <li key={index}>{strength}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="mt-3">
                      <p><strong>发展挑战：</strong></p>
                      <ul className="list-disc list-inside ml-4">
                        {result.data.organizationType.challenges.map((challenge: string, index: number) => (
                          <li key={index}>{challenge}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 第二部分：详细分析 */}
                <div>
                  <h4 className="font-semibold mb-2">🤖 LLM详细分析</h4>
                  <div className="bg-white p-4 rounded border">
                    <MarkdownRenderer
                      content={result.data.detailedAnalysis}
                      className="text-sm"
                    />
                  </div>
                </div>

                <div className="mt-4 text-sm text-gray-600">
                  <p>分析时间: {new Date(result.data.timestamp).toLocaleString()}</p>
                  <p>分析版本: {result.data.version}</p>
                </div>
              </div>
            ) : (
              <div className="bg-red-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-red-800 mb-2">❌ 分析失败</h3>
                <p className="text-red-700">{result.error}</p>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="mt-8 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">测试数据</h3>
        <pre className="text-xs overflow-auto">{JSON.stringify(testData, null, 2)}</pre>
      </div>
    </div>
  );
}
