/**
 * OCTI智能评估系统 - LLM客户端服务
 * 
 * 支持MiniMax和DeepSeek双模型调用
 * 提供统一的API接口和错误处理
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { getEnvVar, sleep } from '@/lib/utils';
import { LLM_CONFIG } from '@/constants';

// ============================================================================
// 类型定义
// ============================================================================

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMRequest {
  messages: LLMMessage[];
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  stream?: boolean;
}

export interface LLMResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
}

export interface LLMError {
  code: string;
  message: string;
  type: 'rate_limit' | 'quota_exceeded' | 'invalid_request' | 'server_error' | 'network_error';
}

export type LLMProvider = 'minimax' | 'deepseek';

// ============================================================================
// LLM客户端基类
// ============================================================================

abstract class BaseLLMClient {
  protected client: AxiosInstance;
  protected provider: LLMProvider;
  protected retryAttempts: number = LLM_CONFIG.RETRY_ATTEMPTS;
  protected retryDelay: number = LLM_CONFIG.RETRY_DELAY;

  constructor(provider: LLMProvider, baseURL: string, apiKey: string) {
    this.provider = provider;
    this.client = axios.create({
      baseURL,
      timeout: LLM_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[${this.provider.toUpperCase()}] Request:`, {
          url: config.url,
          method: config.method,
          data: config.data ? JSON.stringify(config.data).substring(0, 200) + '...' : undefined,
        });
        return config;
      },
      (error) => {
        console.error(`[${this.provider.toUpperCase()}] Request error:`, error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[${this.provider.toUpperCase()}] Response:`, {
          status: response.status,
          data: response.data ? JSON.stringify(response.data).substring(0, 200) + '...' : undefined,
        });
        return response;
      },
      (error) => {
        console.error(`[${this.provider.toUpperCase()}] Response error:`, error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * 发送聊天请求
   * 
   * @param request - LLM请求
   * @returns LLM响应
   */
  async chat(request: LLMRequest): Promise<LLMResponse> {
    let lastError: LLMError | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        console.log(`[${this.provider.toUpperCase()}] Chat attempt ${attempt}/${this.retryAttempts}`);
        return await this.sendChatRequest(request);
      } catch (error) {
        lastError = error as LLMError;
        
        // 如果是速率限制或服务器错误，进行重试
        if (
          (lastError.type === 'rate_limit' || lastError.type === 'server_error') &&
          attempt < this.retryAttempts
        ) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1); // 指数退避
          console.log(`[${this.provider.toUpperCase()}] Retrying in ${delay}ms...`);
          await sleep(delay);
          continue;
        }
        
        // 其他错误直接抛出
        break;
      }
    }

    throw lastError;
  }

  /**
   * 发送聊天请求（子类实现）
   * 
   * @param request - LLM请求
   * @returns LLM响应
   */
  protected abstract sendChatRequest(request: LLMRequest): Promise<LLMResponse>;

  /**
   * 处理错误
   * 
   * @param error - 原始错误
   * @returns 标准化错误
   */
  protected handleError(error: any): LLMError {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      if (status === 429) {
        return {
          code: 'RATE_LIMIT_EXCEEDED',
          message: '请求频率过高，请稍后重试',
          type: 'rate_limit',
        };
      } else if (status === 402 || status === 403) {
        return {
          code: 'QUOTA_EXCEEDED',
          message: 'API配额已用完',
          type: 'quota_exceeded',
        };
      } else if (status >= 400 && status < 500) {
        return {
          code: 'INVALID_REQUEST',
          message: data?.message || '请求参数错误',
          type: 'invalid_request',
        };
      } else if (status >= 500) {
        return {
          code: 'SERVER_ERROR',
          message: '服务器内部错误',
          type: 'server_error',
        };
      }
    }

    if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      return {
        code: 'TIMEOUT',
        message: '请求超时',
        type: 'network_error',
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || '未知错误',
      type: 'network_error',
    };
  }
}

// ============================================================================
// MiniMax客户端
// ============================================================================

export class MinimaxClient extends BaseLLMClient {
  constructor() {
    const apiKey = getEnvVar('MINIMAX_API_KEY');
    const baseURL = getEnvVar('MINIMAX_BASE_URL', 'https://api.minimax.chat/v1');
    super('minimax', baseURL, apiKey);
  }

  protected async sendChatRequest(request: LLMRequest): Promise<LLMResponse> {
    const response = await this.client.post('/text/chatcompletion_v2', {
      model: LLM_CONFIG.MINIMAX.MODEL,
      messages: request.messages,
      temperature: request.temperature ?? LLM_CONFIG.MINIMAX.TEMPERATURE,
      max_tokens: request.maxTokens ?? LLM_CONFIG.MINIMAX.MAX_TOKENS,
      top_p: request.topP ?? LLM_CONFIG.MINIMAX.TOP_P,
      stream: request.stream ?? false,
    });

    const choice = response.data.choices[0];
    return {
      content: choice.message.content,
      usage: {
        promptTokens: response.data.usage.prompt_tokens,
        completionTokens: response.data.usage.completion_tokens,
        totalTokens: response.data.usage.total_tokens,
      },
      model: response.data.model,
      finishReason: choice.finish_reason,
    };
  }
}

// ============================================================================
// DeepSeek客户端
// ============================================================================

export class DeepSeekClient extends BaseLLMClient {
  constructor() {
    const apiKey = getEnvVar('DEEPSEEK_API_KEY');
    const baseURL = getEnvVar('DEEPSEEK_BASE_URL', 'https://api.deepseek.com/v1');
    super('deepseek', baseURL, apiKey);
  }

  protected async sendChatRequest(request: LLMRequest): Promise<LLMResponse> {
    const response = await this.client.post('/chat/completions', {
      model: LLM_CONFIG.DEEPSEEK.MODEL,
      messages: request.messages,
      temperature: request.temperature ?? LLM_CONFIG.DEEPSEEK.TEMPERATURE,
      max_tokens: request.maxTokens ?? LLM_CONFIG.DEEPSEEK.MAX_TOKENS,
      // DeepSeek API不支持top_p参数，移除它
      stream: request.stream ?? false,
    });

    const choice = response.data.choices[0];
    return {
      content: choice.message.content,
      usage: {
        promptTokens: response.data.usage.prompt_tokens,
        completionTokens: response.data.usage.completion_tokens,
        totalTokens: response.data.usage.total_tokens,
      },
      model: response.data.model,
      finishReason: choice.finish_reason,
    };
  }
}

// ============================================================================
// LLM客户端工厂
// ============================================================================

export class LLMClientFactory {
  private static clients = new Map<LLMProvider, BaseLLMClient>();

  /**
   * 获取LLM客户端
   * 
   * @param provider - LLM提供商
   * @returns LLM客户端实例
   */
  static getClient(provider: LLMProvider): BaseLLMClient {
    if (!this.clients.has(provider)) {
      switch (provider) {
        case 'minimax':
          this.clients.set(provider, new MinimaxClient());
          break;
        case 'deepseek':
          this.clients.set(provider, new DeepSeekClient());
          break;
        default:
          throw new Error(`Unsupported LLM provider: ${provider}`);
      }
    }

    return this.clients.get(provider)!;
  }

  /**
   * 测试LLM客户端连接
   * 
   * @param provider - LLM提供商
   * @returns 是否连接成功
   */
  static async testConnection(provider: LLMProvider): Promise<boolean> {
    try {
      const client = this.getClient(provider);
      const response = await client.chat({
        messages: [
          { role: 'user', content: 'Hello, this is a connection test.' }
        ],
        maxTokens: 10,
      });
      
      return response.content.length > 0;
    } catch (error) {
      console.error(`[${provider.toUpperCase()}] Connection test failed:`, error);
      return false;
    }
  }
}

// ============================================================================
// 导出便捷函数
// ============================================================================

/**
 * 发送MiniMax聊天请求
 * 
 * @param request - LLM请求
 * @returns LLM响应
 */
export async function chatWithMinimax(request: LLMRequest): Promise<LLMResponse> {
  const client = LLMClientFactory.getClient('minimax');
  return await client.chat(request);
}

/**
 * 发送DeepSeek聊天请求
 * 
 * @param request - LLM请求
 * @returns LLM响应
 */
export async function chatWithDeepSeek(request: LLMRequest): Promise<LLMResponse> {
  const client = LLMClientFactory.getClient('deepseek');
  return await client.chat(request);
}
