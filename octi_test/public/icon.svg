<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="256" fill="url(#gradient)"/>
  
  <!-- OCTI字母O -->
  <circle cx="256" cy="256" r="120" fill="none" stroke="white" stroke-width="24"/>
  
  <!-- 内部装饰 -->
  <circle cx="256" cy="256" r="60" fill="white" opacity="0.2"/>
  
  <!-- 四个方向的指示器，代表四个维度 -->
  <rect x="246" y="96" width="20" height="40" rx="10" fill="white"/>
  <rect x="246" y="376" width="20" height="40" rx="10" fill="white"/>
  <rect x="96" y="246" width="40" height="20" rx="10" fill="white"/>
  <rect x="376" y="246" width="40" height="20" rx="10" fill="white"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
