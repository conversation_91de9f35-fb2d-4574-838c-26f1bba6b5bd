# OCTI智能评估系统

<div align="center">

![OCTI Logo](https://img.shields.io/badge/OCTI-智能评估系统-blue?style=for-the-badge)
![Version](https://img.shields.io/badge/version-4.0-green?style=for-the-badge)
![License](https://img.shields.io/badge/license-MIT-orange?style=for-the-badge)

**基于配置驱动和智能体模块化的组织能力评估平台**

[功能特性](#功能特性) • [技术架构](#技术架构) • [快速开始](#快速开始) • [API文档](#api文档) • [贡献指南](#贡献指南)

</div>

---

## 📋 项目概述

OCTI（Organizational Capacity Type Indicator）智能评估系统是一个专为公益机构设计的组织能力评估平台。通过配置驱动的智能体架构，为组织提供科学、专业的能力诊断和发展建议。

### 🎯 核心价值

- **科学评估**: 基于OCTI理论框架的四维能力评估模型
- **智能分析**: 双AI模型协作，提供深度洞察和发展建议  
- **个性化服务**: 基于组织画像的定制化问卷和分析报告
- **快速迭代**: 配置驱动架构支持业务逻辑快速调整

### 🏆 产品特色

- **配置驱动架构**: 问卷生成和分析逻辑完全配置化
- **智能体模块化**: 问卷设计师 + 组织评估导师双智能体协作
- **双版本策略**: 标准版(¥99) + 专业版(¥199)差异化服务
- **AI模型融合**: MiniMax + DeepSeek双模型支持

---

## 🚀 功能特性

### 核心功能

- ✅ **智能问卷生成**: 基于组织画像的个性化问卷设计
- ✅ **多维能力评估**: OCTI四维能力框架全面评估
- ✅ **AI深度分析**: 双模型协作提供专业分析报告
- ✅ **配置热更新**: 无需重启即可更新业务逻辑
- ✅ **报告可视化**: 专业图表和PDF报告导出

### 智能体服务

- **问卷设计师智能体**: 32道预设 + 28道AI生成的混合问卷
- **组织评估导师智能体**: 标准版和专业版差异化分析
- **配置管理引擎**: 热更新、版本控制、回滚机制

### 用户体验

- **响应式设计**: 支持桌面端和移动端访问
- **进度保存**: 评估过程自动保存，支持分段完成
- **多格式导出**: PDF报告、Excel数据导出
- **用户认证**: 安全的登录注册和权限管理

---

## 🏗️ 技术架构

### 技术栈

```
前端: Next.js 14 + TypeScript + Tailwind CSS + shadcn/ui
后端: Next.js API Routes + Prisma ORM
数据库: PostgreSQL + Redis
AI服务: MiniMax API + DeepSeek API
部署: Docker + 单实例部署
```

### 架构设计

```mermaid
graph TB
    subgraph "前端层"
        A[React组件] --> B[状态管理 Zustand]
        B --> C[UI组件库 shadcn/ui]
    end
    
    subgraph "API网关层"
        D[Next.js API Routes] --> E[中间件]
        E --> F[API缓存]
    end
    
    subgraph "智能体服务层"
        G[问卷设计师] --> H[组织评估导师]
        H --> I[配置管理器]
    end
    
    subgraph "数据层"
        J[PostgreSQL] --> K[Redis缓存]
        K --> L[文件存储]
    end
    
    A --> D
    D --> G
    G --> J
```

### 项目结构

```
octi_test/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── api/               # API路由
│   │   ├── assessment/        # 评估相关页面
│   │   └── dashboard/         # 用户仪表板
│   ├── components/            # React组件
│   │   ├── ui/               # 基础UI组件
│   │   ├── charts/           # 图表组件
│   │   └── questionnaire/    # 问卷组件
│   ├── services/              # 业务逻辑服务
│   │   ├── agents/           # 智能体服务
│   │   ├── llm/              # LLM服务
│   │   └── analysis/         # 分析服务
│   ├── lib/                   # 工具库
│   ├── types/                 # TypeScript类型
│   └── constants/             # 常量定义
├── configs/                   # 智能体配置文件
├── prisma/                    # 数据库Schema
├── docs/                      # 项目文档
└── docker/                    # Docker配置
```

---

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- PostgreSQL >= 14
- Redis >= 6.0

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/octi-team/octi-assessment-system.git
cd octi-assessment-system
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

4. **数据库设置**
```bash
# 生成Prisma客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 填充初始数据
npm run db:seed
```

5. **启动开发服务器**
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### Docker部署

```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

---

## 📚 API文档

### 核心API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/questionnaire/generate` | POST | 生成个性化问卷 |
| `/api/assessment/analyze` | POST | 分析评估结果 |
| `/api/reports/export` | GET | 导出评估报告 |
| `/api/config/update` | PUT | 更新配置文件 |

### 请求示例

```javascript
// 生成问卷
const response = await fetch('/api/questionnaire/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    organizationProfile: {
      type: 'NGO',
      scale: 'MEDIUM',
      focusArea: 'EDUCATION'
    }
  })
});
```

详细API文档请参考: [docs/8_API_documentation.md](docs/8_API_documentation.md)

---

## 🧪 测试

### 运行测试

```bash
# 单元测试
npm run test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage

# E2E测试
npm run test:e2e
```

### 测试策略

- **单元测试**: Jest + Testing Library
- **集成测试**: API端点测试
- **E2E测试**: Playwright自动化测试
- **性能测试**: 负载和压力测试

---

## 📊 监控和日志

### 性能监控

- API响应时间监控
- 数据库查询性能
- 前端页面加载速度
- 用户行为分析

### 日志系统

- 结构化日志记录
- 错误追踪和告警
- 用户操作审计
- 系统性能指标

---

## 🤝 贡献指南

### 开发流程

1. Fork项目到个人仓库
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建Pull Request

### 代码规范

- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier代码规范
- 编写单元测试覆盖新功能
- 更新相关文档

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

---

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

## 📞 联系我们

- **项目主页**: https://github.com/octi-team/octi-assessment-system
- **问题反馈**: https://github.com/octi-team/octi-assessment-system/issues
- **邮箱**: <EMAIL>
- **文档**: [项目文档](docs/)

---

## 🙏 致谢

感谢所有为OCTI智能评估系统做出贡献的开发者和用户！

---

<div align="center">

**[⬆ 回到顶部](#octi智能评估系统)**

Made with ❤️ by OCTI Team

</div>
