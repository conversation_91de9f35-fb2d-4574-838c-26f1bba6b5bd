# OCTI智能评估系统 - 依赖说明

## 项目技术栈

本项目是一个基于Node.js/TypeScript的全栈Web应用，**不包含Python依赖**。

### 主要技术栈

- **前端**: Next.js 14 + TypeScript + React 18
- **后端**: Next.js API Routes + Prisma ORM
- **数据库**: PostgreSQL + Redis
- **AI服务**: MiniMax API + DeepSeek API (通过HTTP调用)
- **部署**: Docker + Node.js环境

### 依赖管理

项目使用npm作为包管理器，所有依赖都在`package.json`中定义：

```json
{
  "dependencies": {
    "next": "14.2.5",
    "react": "^18.3.1",
    "typescript": "^5.5.4",
    "@prisma/client": "^5.17.0",
    "axios": "^1.7.2",
    "zod": "^3.23.8",
    "zustand": "^4.5.4"
  }
}
```

### 安装依赖

```bash
# 安装所有依赖
npm install

# 或使用yarn
yarn install

# 或使用pnpm
pnpm install
```

### 系统要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **PostgreSQL**: >= 14
- **Redis**: >= 6.0

### 为什么不需要requirements.txt

1. **纯JavaScript/TypeScript项目**: 所有业务逻辑都用TypeScript实现
2. **AI服务通过API调用**: 不需要本地Python AI库
3. **数据处理使用JavaScript**: 所有数据处理都在Node.js环境中完成
4. **部署环境统一**: 使用Node.js运行时，无需Python环境

### 外部服务依赖

项目依赖以下外部服务，通过HTTP API调用：

- **MiniMax API**: 主要AI分析模型
- **DeepSeek API**: 专业版深度推理模型
- **PostgreSQL**: 关系数据库
- **Redis**: 缓存服务

这些服务都通过网络调用，不需要本地安装Python包。

### 开发环境设置

详细的环境设置请参考项目根目录的`README.md`文件。
