'use client';

import { useState } from 'react';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

export default function TestProfessionalPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testData = {
    profile: {
      organizationType: '成熟期公益组织',
      serviceArea: '环境保护',
      resourceStructure: '多元化资金来源',
      developmentStage: '成熟期',
      teamSize: '大型（50+人）',
      operatingModel: '综合服务型',
      impactScope: '全国影响',
      organizationCulture: '创新驱动',
      challengesPriorities: '数字化转型和影响力扩大',
      futureVision: '成为国内环保公益的领军组织'
    },
    responses: [
      { questionId: 'SF_P001', answer: 'very_clear' },
      { questionId: 'SF_P002', answer: 'excellent' },
      { questionId: 'IT_P001', answer: 5 },
      { questionId: 'MV_P001', answer: ['mission_training', 'value_sharing', 'impact_measurement'] },
      { questionId: 'AD_P001', answer: 'excellent' },
      { questionId: 'SF_P003', answer: 'good' },
      { questionId: 'IT_P002', answer: 4 },
      { questionId: 'MV_P002', answer: 'very_strong' }
    ],
    version: 'professional'
  };

  const handleTest = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🚀 开始测试专业版双模型分析API...');
      
      const response = await fetch('/api/assessment/professional-analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData),
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ 专业版测试成功:', data);
        setResult(data);
      } else {
        console.error('❌ 专业版测试失败:', data);
        setResult({ error: data.error || '请求失败' });
      }
    } catch (error) {
      console.error('❌ 网络错误:', error);
      setResult({ error: '网络请求失败' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">OCTI专业版双模型分析测试</h1>
      
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-3">🚀 专业版双模型方案</h2>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-blue-600 mb-2">第一步：组织类型判断</h3>
            <p className="text-sm text-gray-600">快速分类，无需LLM</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-green-600 mb-2">第二步：MiniMax基础分析</h3>
            <p className="text-sm text-gray-600">清晰实用的基础分析</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="font-semibold text-purple-600 mb-2">第三步：DeepSeek深度升华</h3>
            <p className="text-sm text-gray-600">战略指导和深度洞察</p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <button
          onClick={handleTest}
          disabled={loading}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed font-semibold"
        >
          {loading ? '分析中...' : '开始专业版双模型分析'}
        </button>

        {loading && (
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-600"></div>
              <div>
                <p className="text-yellow-800 font-semibold">正在执行专业版分析...</p>
                <p className="text-yellow-600 text-sm">这可能需要1-2分钟，请耐心等待</p>
              </div>
            </div>
          </div>
        )}

        {result && (
          <div className="space-y-6">
            {result.success ? (
              <div className="bg-green-50 border border-green-200 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-800 mb-4">✅ 专业版分析成功</h3>
                
                {/* 处理步骤 */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-2">📋 处理步骤</h4>
                  <div className="bg-white p-3 rounded border">
                    <ol className="list-decimal list-inside space-y-1">
                      {result.data.processingSteps.map((step: string, index: number) => (
                        <li key={index} className="text-sm">{step}</li>
                      ))}
                    </ol>
                  </div>
                </div>

                {/* 组织类型判断 */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-2">📊 第一步：组织类型判断</h4>
                  <div className="bg-white p-4 rounded border">
                    <p><strong>类型代码：</strong>{result.data.organizationType.code}</p>
                    <p><strong>类型名称：</strong>{result.data.organizationType.name}</p>
                    <p><strong>类型描述：</strong>{result.data.organizationType.description}</p>
                  </div>
                </div>

                {/* MiniMax基础分析 */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-2">🤖 第二步：MiniMax基础分析</h4>
                  <div className="bg-white p-4 rounded border max-h-96 overflow-y-auto">
                    <MarkdownRenderer
                      content={result.data.basicAnalysis}
                      className="text-sm"
                    />
                  </div>
                </div>

                {/* DeepSeek深度升华 */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-2">🧠 第三步：DeepSeek深度升华分析</h4>
                  <div className="bg-white p-4 rounded border max-h-96 overflow-y-auto">
                    <MarkdownRenderer
                      content={result.data.enhancedAnalysis}
                      className="text-sm"
                    />
                  </div>
                </div>

                <div className="mt-4 text-sm text-gray-600">
                  <p>分析时间: {new Date(result.data.timestamp).toLocaleString()}</p>
                  <p>分析版本: {result.data.version}</p>
                </div>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-red-800 mb-2">❌ 专业版分析失败</h3>
                <p className="text-red-700">{result.error}</p>
                
                {result.data && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">部分结果：</h4>
                    <div className="bg-white p-3 rounded border">
                      <p><strong>组织类型：</strong>{result.data.organizationType.name}</p>
                      <p><strong>处理步骤：</strong>{result.data.processingSteps.join(' -> ')}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      <div className="mt-8 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">测试数据</h3>
        <details className="cursor-pointer">
          <summary className="text-sm text-gray-600 hover:text-gray-800">点击查看测试数据</summary>
          <pre className="text-xs overflow-auto mt-2 bg-white p-3 rounded border">{JSON.stringify(testData, null, 2)}</pre>
        </details>
      </div>
    </div>
  );
}
