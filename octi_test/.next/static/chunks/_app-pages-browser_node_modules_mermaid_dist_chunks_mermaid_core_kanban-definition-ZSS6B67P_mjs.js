"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_kanban-definition-ZSS6B67P_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIconStyles: function() { return /* binding */ getIconStyles; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n\n\n// src/diagrams/globalStyles.ts\nvar getIconStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => `\n  /* Font Awesome icon styling - consolidated */\n  .label-icon {\n    display: inline-block;\n    height: 1em;\n    overflow: visible;\n    vertical-align: -0.125em;\n  }\n  \n  .node .label-icon path {\n    fill: currentColor;\n    stroke: revert;\n    stroke-width: revert;\n  }\n`, \"getIconStyles\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1FMkdZSVNGSS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFFOEI7O0FBRTlCO0FBQ0Esb0NBQW9DLDJEQUFNO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21lcm1haWQvZGlzdC9jaHVua3MvbWVybWFpZC5jb3JlL2NodW5rLUUyR1lJU0ZJLm1qcz81ZGRlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIF9fbmFtZVxufSBmcm9tIFwiLi9jaHVuay0zWFlSSDVBUC5tanNcIjtcblxuLy8gc3JjL2RpYWdyYW1zL2dsb2JhbFN0eWxlcy50c1xudmFyIGdldEljb25TdHlsZXMgPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKCgpID0+IGBcbiAgLyogRm9udCBBd2Vzb21lIGljb24gc3R5bGluZyAtIGNvbnNvbGlkYXRlZCAqL1xuICAubGFiZWwtaWNvbiB7XG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICAgIGhlaWdodDogMWVtO1xuICAgIG92ZXJmbG93OiB2aXNpYmxlO1xuICAgIHZlcnRpY2FsLWFsaWduOiAtMC4xMjVlbTtcbiAgfVxuICBcbiAgLm5vZGUgLmxhYmVsLWljb24gcGF0aCB7XG4gICAgZmlsbDogY3VycmVudENvbG9yO1xuICAgIHN0cm9rZTogcmV2ZXJ0O1xuICAgIHN0cm9rZS13aWR0aDogcmV2ZXJ0O1xuICB9XG5gLCBcImdldEljb25TdHlsZXNcIik7XG5cbmV4cG9ydCB7XG4gIGdldEljb25TdHlsZXNcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-ZSS6B67P.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-ZSS6B67P.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_E2GYISFI_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-E2GYISFI.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs\");\n/* harmony import */ var _chunk_L5ZGVLVO_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-L5ZGVLVO.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-L5ZGVLVO.mjs\");\n/* harmony import */ var _chunk_JW4RIYDF_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-JW4RIYDF.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-JW4RIYDF.mjs\");\n/* harmony import */ var _chunk_AC5SNWB5_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-AC5SNWB5.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-AC5SNWB5.mjs\");\n/* harmony import */ var _chunk_UWXLY5YG_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-UWXLY5YG.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-UWXLY5YG.mjs\");\n/* harmony import */ var _chunk_QESNASVV_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-QESNASVV.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-QESNASVV.mjs\");\n/* harmony import */ var _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-55PJQP7W.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs\");\n/* harmony import */ var _chunk_P3VETL53_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-P3VETL53.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-P3VETL53.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/is_dark.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/lighten.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/darken.js\");\n\n\n\n\n\n\n\n\n\n\n// src/diagrams/kanban/parser/kanban.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 4], $V1 = [1, 13], $V2 = [1, 12], $V3 = [1, 15], $V4 = [1, 16], $V5 = [1, 20], $V6 = [1, 19], $V7 = [6, 7, 8], $V8 = [1, 26], $V9 = [1, 24], $Va = [1, 25], $Vb = [6, 7, 11], $Vc = [1, 31], $Vd = [6, 7, 11, 24], $Ve = [1, 6, 13, 16, 17, 20, 23], $Vf = [1, 35], $Vg = [1, 36], $Vh = [1, 6, 7, 11, 13, 16, 17, 20, 23], $Vi = [1, 38];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mindMap\": 4, \"spaceLines\": 5, \"SPACELINE\": 6, \"NL\": 7, \"KANBAN\": 8, \"document\": 9, \"stop\": 10, \"EOF\": 11, \"statement\": 12, \"SPACELIST\": 13, \"node\": 14, \"shapeData\": 15, \"ICON\": 16, \"CLASS\": 17, \"nodeWithId\": 18, \"nodeWithoutId\": 19, \"NODE_DSTART\": 20, \"NODE_DESCR\": 21, \"NODE_DEND\": 22, \"NODE_ID\": 23, \"SHAPE_DATA\": 24, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"SPACELINE\", 7: \"NL\", 8: \"KANBAN\", 11: \"EOF\", 13: \"SPACELIST\", 16: \"ICON\", 17: \"CLASS\", 20: \"NODE_DSTART\", 21: \"NODE_DESCR\", 22: \"NODE_DEND\", 23: \"NODE_ID\", 24: \"SHAPE_DATA\" },\n    productions_: [0, [3, 1], [3, 2], [5, 1], [5, 2], [5, 2], [4, 2], [4, 3], [10, 1], [10, 1], [10, 1], [10, 2], [10, 2], [9, 3], [9, 2], [12, 3], [12, 2], [12, 2], [12, 2], [12, 1], [12, 2], [12, 1], [12, 1], [12, 1], [12, 1], [14, 1], [14, 1], [19, 3], [18, 1], [18, 4], [15, 2], [15, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 6:\n        case 7:\n          return yy;\n          break;\n        case 8:\n          yy.getLogger().trace(\"Stop NL \");\n          break;\n        case 9:\n          yy.getLogger().trace(\"Stop EOF \");\n          break;\n        case 11:\n          yy.getLogger().trace(\"Stop NL2 \");\n          break;\n        case 12:\n          yy.getLogger().trace(\"Stop EOF2 \");\n          break;\n        case 15:\n          yy.getLogger().info(\"Node: \", $$[$0 - 1].id);\n          yy.addNode($$[$0 - 2].length, $$[$0 - 1].id, $$[$0 - 1].descr, $$[$0 - 1].type, $$[$0]);\n          break;\n        case 16:\n          yy.getLogger().info(\"Node: \", $$[$0].id);\n          yy.addNode($$[$0 - 1].length, $$[$0].id, $$[$0].descr, $$[$0].type);\n          break;\n        case 17:\n          yy.getLogger().trace(\"Icon: \", $$[$0]);\n          yy.decorateNode({ icon: $$[$0] });\n          break;\n        case 18:\n        case 23:\n          yy.decorateNode({ class: $$[$0] });\n          break;\n        case 19:\n          yy.getLogger().trace(\"SPACELIST\");\n          break;\n        case 20:\n          yy.getLogger().trace(\"Node: \", $$[$0 - 1].id);\n          yy.addNode(0, $$[$0 - 1].id, $$[$0 - 1].descr, $$[$0 - 1].type, $$[$0]);\n          break;\n        case 21:\n          yy.getLogger().trace(\"Node: \", $$[$0].id);\n          yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);\n          break;\n        case 22:\n          yy.decorateNode({ icon: $$[$0] });\n          break;\n        case 27:\n          yy.getLogger().trace(\"node found ..\", $$[$0 - 2]);\n          this.$ = { id: $$[$0 - 1], descr: $$[$0 - 1], type: yy.getType($$[$0 - 2], $$[$0]) };\n          break;\n        case 28:\n          this.$ = { id: $$[$0], descr: $$[$0], type: 0 };\n          break;\n        case 29:\n          yy.getLogger().trace(\"node found ..\", $$[$0 - 3]);\n          this.$ = { id: $$[$0 - 3], descr: $$[$0 - 1], type: yy.getType($$[$0 - 2], $$[$0]) };\n          break;\n        case 30:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 31:\n          this.$ = $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 8: $V0 }, { 1: [3] }, { 1: [2, 1] }, { 4: 6, 6: [1, 7], 7: [1, 8], 8: $V0 }, { 6: $V1, 7: [1, 10], 9: 9, 12: 11, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, o($V7, [2, 3]), { 1: [2, 2] }, o($V7, [2, 4]), o($V7, [2, 5]), { 1: [2, 6], 6: $V1, 12: 21, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, { 6: $V1, 9: 22, 12: 11, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, { 6: $V8, 7: $V9, 10: 23, 11: $Va }, o($Vb, [2, 24], { 18: 17, 19: 18, 14: 27, 16: [1, 28], 17: [1, 29], 20: $V5, 23: $V6 }), o($Vb, [2, 19]), o($Vb, [2, 21], { 15: 30, 24: $Vc }), o($Vb, [2, 22]), o($Vb, [2, 23]), o($Vd, [2, 25]), o($Vd, [2, 26]), o($Vd, [2, 28], { 20: [1, 32] }), { 21: [1, 33] }, { 6: $V8, 7: $V9, 10: 34, 11: $Va }, { 1: [2, 7], 6: $V1, 12: 21, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, o($Ve, [2, 14], { 7: $Vf, 11: $Vg }), o($Vh, [2, 8]), o($Vh, [2, 9]), o($Vh, [2, 10]), o($Vb, [2, 16], { 15: 37, 24: $Vc }), o($Vb, [2, 17]), o($Vb, [2, 18]), o($Vb, [2, 20], { 24: $Vi }), o($Vd, [2, 31]), { 21: [1, 39] }, { 22: [1, 40] }, o($Ve, [2, 13], { 7: $Vf, 11: $Vg }), o($Vh, [2, 11]), o($Vh, [2, 12]), o($Vb, [2, 15], { 24: $Vi }), o($Vd, [2, 30]), { 22: [1, 41] }, o($Vd, [2, 27]), o($Vd, [2, 29])],\n    defaultActions: { 2: [2, 1], 6: [2, 2] },\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"shapeData\");\n            yy_.yytext = \"\";\n            return 24;\n            break;\n          case 1:\n            this.pushState(\"shapeDataStr\");\n            return 24;\n            break;\n          case 2:\n            this.popState();\n            return 24;\n            break;\n          case 3:\n            const re = /\\n\\s*/g;\n            yy_.yytext = yy_.yytext.replace(re, \"<br/>\");\n            return 24;\n            break;\n          case 4:\n            return 24;\n            break;\n          case 5:\n            this.popState();\n            break;\n          case 6:\n            yy.getLogger().trace(\"Found comment\", yy_.yytext);\n            return 6;\n            break;\n          case 7:\n            return 8;\n            break;\n          case 8:\n            this.begin(\"CLASS\");\n            break;\n          case 9:\n            this.popState();\n            return 17;\n            break;\n          case 10:\n            this.popState();\n            break;\n          case 11:\n            yy.getLogger().trace(\"Begin icon\");\n            this.begin(\"ICON\");\n            break;\n          case 12:\n            yy.getLogger().trace(\"SPACELINE\");\n            return 6;\n            break;\n          case 13:\n            return 7;\n            break;\n          case 14:\n            return 16;\n            break;\n          case 15:\n            yy.getLogger().trace(\"end icon\");\n            this.popState();\n            break;\n          case 16:\n            yy.getLogger().trace(\"Exploding node\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 17:\n            yy.getLogger().trace(\"Cloud\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 18:\n            yy.getLogger().trace(\"Explosion Bang\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 19:\n            yy.getLogger().trace(\"Cloud Bang\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 20:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 21:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 22:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 23:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 24:\n            return 13;\n            break;\n          case 25:\n            return 23;\n            break;\n          case 26:\n            return 11;\n            break;\n          case 27:\n            this.begin(\"NSTR2\");\n            break;\n          case 28:\n            return \"NODE_DESCR\";\n            break;\n          case 29:\n            this.popState();\n            break;\n          case 30:\n            yy.getLogger().trace(\"Starting NSTR\");\n            this.begin(\"NSTR\");\n            break;\n          case 31:\n            yy.getLogger().trace(\"description:\", yy_.yytext);\n            return \"NODE_DESCR\";\n            break;\n          case 32:\n            this.popState();\n            break;\n          case 33:\n            this.popState();\n            yy.getLogger().trace(\"node end ))\");\n            return \"NODE_DEND\";\n            break;\n          case 34:\n            this.popState();\n            yy.getLogger().trace(\"node end )\");\n            return \"NODE_DEND\";\n            break;\n          case 35:\n            this.popState();\n            yy.getLogger().trace(\"node end ...\", yy_.yytext);\n            return \"NODE_DEND\";\n            break;\n          case 36:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 37:\n            this.popState();\n            yy.getLogger().trace(\"node end (-\");\n            return \"NODE_DEND\";\n            break;\n          case 38:\n            this.popState();\n            yy.getLogger().trace(\"node end (-\");\n            return \"NODE_DEND\";\n            break;\n          case 39:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 40:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 41:\n            yy.getLogger().trace(\"Long description:\", yy_.yytext);\n            return 21;\n            break;\n          case 42:\n            yy.getLogger().trace(\"Long description:\", yy_.yytext);\n            return 21;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:@\\{)/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\\\"]+)/i, /^(?:[^}^\"]+)/i, /^(?:\\})/i, /^(?:\\s*%%.*)/i, /^(?:kanban\\b)/i, /^(?::::)/i, /^(?:.+)/i, /^(?:\\n)/i, /^(?:::icon\\()/i, /^(?:[\\s]+[\\n])/i, /^(?:[\\n]+)/i, /^(?:[^\\)]+)/i, /^(?:\\))/i, /^(?:-\\))/i, /^(?:\\(-)/i, /^(?:\\)\\))/i, /^(?:\\))/i, /^(?:\\(\\()/i, /^(?:\\{\\{)/i, /^(?:\\()/i, /^(?:\\[)/i, /^(?:[\\s]+)/i, /^(?:[^\\(\\[\\n\\)\\{\\}@]+)/i, /^(?:$)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[^\"]+)/i, /^(?:[\"])/i, /^(?:[\\)]\\))/i, /^(?:[\\)])/i, /^(?:[\\]])/i, /^(?:\\}\\})/i, /^(?:\\(-)/i, /^(?:-\\))/i, /^(?:\\(\\()/i, /^(?:\\()/i, /^(?:[^\\)\\]\\(\\}]+)/i, /^(?:.+(?!\\(\\())/i],\n      conditions: { \"shapeDataEndBracket\": { \"rules\": [], \"inclusive\": false }, \"shapeDataStr\": { \"rules\": [2, 3], \"inclusive\": false }, \"shapeData\": { \"rules\": [1, 4, 5], \"inclusive\": false }, \"CLASS\": { \"rules\": [9, 10], \"inclusive\": false }, \"ICON\": { \"rules\": [14, 15], \"inclusive\": false }, \"NSTR2\": { \"rules\": [28, 29], \"inclusive\": false }, \"NSTR\": { \"rules\": [31, 32], \"inclusive\": false }, \"NODE\": { \"rules\": [27, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 6, 7, 8, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar kanban_default = parser;\n\n// src/diagrams/kanban/kanbanDb.ts\nvar nodes = [];\nvar sections = [];\nvar cnt = 0;\nvar elements = {};\nvar clear = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(() => {\n  nodes = [];\n  sections = [];\n  cnt = 0;\n  elements = {};\n}, \"clear\");\nvar getSection = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((level) => {\n  if (nodes.length === 0) {\n    return null;\n  }\n  const sectionLevel = nodes[0].level;\n  let lastSection = null;\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    if (nodes[i].level === sectionLevel && !lastSection) {\n      lastSection = nodes[i];\n    }\n    if (nodes[i].level < sectionLevel) {\n      throw new Error('Items without section detected, found section (\"' + nodes[i].label + '\")');\n    }\n  }\n  if (level === lastSection?.level) {\n    return null;\n  }\n  return lastSection;\n}, \"getSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getData = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(function() {\n  const edges = [];\n  const _nodes = [];\n  const sections2 = getSections();\n  const conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.getConfig2)();\n  for (const section of sections2) {\n    const node = {\n      id: section.id,\n      label: (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(section.label ?? \"\", conf),\n      isGroup: true,\n      ticket: section.ticket,\n      shape: \"kanbanSection\",\n      level: section.level,\n      look: conf.look\n    };\n    _nodes.push(node);\n    const children = nodes.filter((n) => n.parentId === section.id);\n    for (const item of children) {\n      const childNode = {\n        id: item.id,\n        parentId: section.id,\n        label: (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(item.label ?? \"\", conf),\n        isGroup: false,\n        ticket: item?.ticket,\n        priority: item?.priority,\n        assigned: item?.assigned,\n        icon: item?.icon,\n        shape: \"kanbanItem\",\n        level: item.level,\n        rx: 5,\n        ry: 5,\n        cssStyles: [\"text-align: left\"]\n      };\n      _nodes.push(childNode);\n    }\n  }\n  return { nodes: _nodes, edges, other: {}, config: (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.getConfig2)() };\n}, \"getData\");\nvar addNode = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((level, id, descr, type, shapeData) => {\n  const conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.getConfig2)();\n  let padding = conf.mindmap?.padding ?? _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.defaultConfig_default.mindmap.padding;\n  switch (type) {\n    case nodeType.ROUNDED_RECT:\n    case nodeType.RECT:\n    case nodeType.HEXAGON:\n      padding *= 2;\n  }\n  const node = {\n    id: (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(id, conf) || \"kbn\" + cnt++,\n    level,\n    label: (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(descr, conf),\n    width: conf.mindmap?.maxNodeWidth ?? _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.defaultConfig_default.mindmap.maxNodeWidth,\n    padding,\n    isGroup: false\n  };\n  if (shapeData !== void 0) {\n    let yamlData;\n    if (!shapeData.includes(\"\\n\")) {\n      yamlData = \"{\\n\" + shapeData + \"\\n}\";\n    } else {\n      yamlData = shapeData + \"\\n\";\n    }\n    const doc = (0,_chunk_L5ZGVLVO_mjs__WEBPACK_IMPORTED_MODULE_1__.load)(yamlData, { schema: _chunk_L5ZGVLVO_mjs__WEBPACK_IMPORTED_MODULE_1__.JSON_SCHEMA });\n    if (doc.shape && (doc.shape !== doc.shape.toLowerCase() || doc.shape.includes(\"_\"))) {\n      throw new Error(`No such shape: ${doc.shape}. Shape names should be lowercase.`);\n    }\n    if (doc?.shape && doc.shape === \"kanbanItem\") {\n      node.shape = doc?.shape;\n    }\n    if (doc?.label) {\n      node.label = doc?.label;\n    }\n    if (doc?.icon) {\n      node.icon = doc?.icon.toString();\n    }\n    if (doc?.assigned) {\n      node.assigned = doc?.assigned.toString();\n    }\n    if (doc?.ticket) {\n      node.ticket = doc?.ticket.toString();\n    }\n    if (doc?.priority) {\n      node.priority = doc?.priority;\n    }\n  }\n  const section = getSection(level);\n  if (section) {\n    node.parentId = section.id || \"kbn\" + cnt++;\n  } else {\n    sections.push(node);\n  }\n  nodes.push(node);\n}, \"addNode\");\nvar nodeType = {\n  DEFAULT: 0,\n  NO_BORDER: 0,\n  ROUNDED_RECT: 1,\n  RECT: 2,\n  CIRCLE: 3,\n  CLOUD: 4,\n  BANG: 5,\n  HEXAGON: 6\n};\nvar getType = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((startStr, endStr) => {\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.log.debug(\"In get type\", startStr, endStr);\n  switch (startStr) {\n    case \"[\":\n      return nodeType.RECT;\n    case \"(\":\n      return endStr === \")\" ? nodeType.ROUNDED_RECT : nodeType.CLOUD;\n    case \"((\":\n      return nodeType.CIRCLE;\n    case \")\":\n      return nodeType.CLOUD;\n    case \"))\":\n      return nodeType.BANG;\n    case \"{{\":\n      return nodeType.HEXAGON;\n    default:\n      return nodeType.DEFAULT;\n  }\n}, \"getType\");\nvar setElementForId = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((id, element) => {\n  elements[id] = element;\n}, \"setElementForId\");\nvar decorateNode = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((decoration) => {\n  if (!decoration) {\n    return;\n  }\n  const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.getConfig2)();\n  const node = nodes[nodes.length - 1];\n  if (decoration.icon) {\n    node.icon = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(decoration.icon, config);\n  }\n  if (decoration.class) {\n    node.cssClasses = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(decoration.class, config);\n  }\n}, \"decorateNode\");\nvar type2Str = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((type) => {\n  switch (type) {\n    case nodeType.DEFAULT:\n      return \"no-border\";\n    case nodeType.RECT:\n      return \"rect\";\n    case nodeType.ROUNDED_RECT:\n      return \"rounded-rect\";\n    case nodeType.CIRCLE:\n      return \"circle\";\n    case nodeType.CLOUD:\n      return \"cloud\";\n    case nodeType.BANG:\n      return \"bang\";\n    case nodeType.HEXAGON:\n      return \"hexgon\";\n    // cspell: disable-line\n    default:\n      return \"no-border\";\n  }\n}, \"type2Str\");\nvar getLogger = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(() => _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.log, \"getLogger\");\nvar getElementById = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((id) => elements[id], \"getElementById\");\nvar db = {\n  clear,\n  addNode,\n  getSections,\n  getData,\n  nodeType,\n  getType,\n  setElementForId,\n  decorateNode,\n  type2Str,\n  getLogger,\n  getElementById\n};\nvar kanbanDb_default = db;\n\n// src/diagrams/kanban/kanbanRenderer.ts\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)(async (text, id, _version, diagObj) => {\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.log.debug(\"Rendering kanban diagram\\n\" + text);\n  const db2 = diagObj.db;\n  const data4Layout = db2.getData();\n  const conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.getConfig2)();\n  conf.htmlLabels = false;\n  const svg = (0,_chunk_P3VETL53_mjs__WEBPACK_IMPORTED_MODULE_7__.selectSvgElement)(id);\n  const sectionsElem = svg.append(\"g\");\n  sectionsElem.attr(\"class\", \"sections\");\n  const nodesElem = svg.append(\"g\");\n  nodesElem.attr(\"class\", \"items\");\n  const sections2 = data4Layout.nodes.filter(\n    // TODO: TypeScript 5.5 will infer this predicate automatically\n    (node) => node.isGroup\n  );\n  let cnt2 = 0;\n  const padding = 10;\n  const sectionObjects = [];\n  let maxLabelHeight = 25;\n  for (const section of sections2) {\n    const WIDTH = conf?.kanban?.sectionWidth || 200;\n    cnt2 = cnt2 + 1;\n    section.x = WIDTH * cnt2 + (cnt2 - 1) * padding / 2;\n    section.width = WIDTH;\n    section.y = 0;\n    section.height = WIDTH * 3;\n    section.rx = 5;\n    section.ry = 5;\n    section.cssClasses = section.cssClasses + \" section-\" + cnt2;\n    const sectionObj = await (0,_chunk_JW4RIYDF_mjs__WEBPACK_IMPORTED_MODULE_2__.insertCluster)(sectionsElem, section);\n    maxLabelHeight = Math.max(maxLabelHeight, sectionObj?.labelBBox?.height);\n    sectionObjects.push(sectionObj);\n  }\n  let i = 0;\n  for (const section of sections2) {\n    const sectionObj = sectionObjects[i];\n    i = i + 1;\n    const WIDTH = conf?.kanban?.sectionWidth || 200;\n    const top = -WIDTH * 3 / 2 + maxLabelHeight;\n    let y = top;\n    const sectionItems = data4Layout.nodes.filter((node) => node.parentId === section.id);\n    for (const item of sectionItems) {\n      if (item.isGroup) {\n        throw new Error(\"Groups within groups are not allowed in Kanban diagrams\");\n      }\n      item.x = section.x;\n      item.width = WIDTH - 1.5 * padding;\n      const nodeEl = await (0,_chunk_JW4RIYDF_mjs__WEBPACK_IMPORTED_MODULE_2__.insertNode)(nodesElem, item, { config: conf });\n      const bbox = nodeEl.node().getBBox();\n      item.y = y + bbox.height / 2;\n      await (0,_chunk_JW4RIYDF_mjs__WEBPACK_IMPORTED_MODULE_2__.positionNode)(item);\n      y = item.y + bbox.height / 2 + padding / 2;\n    }\n    const rect = sectionObj.cluster.select(\"rect\");\n    const height = Math.max(y - top + 3 * padding, 50) + (maxLabelHeight - 25);\n    rect.attr(\"height\", height);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.setupGraphViewbox)(\n    void 0,\n    svg,\n    conf.mindmap?.padding ?? _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.defaultConfig_default.kanban.padding,\n    conf.mindmap?.useMaxWidth ?? _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.defaultConfig_default.kanban.useMaxWidth\n  );\n}, \"draw\");\nvar kanbanRenderer_default = {\n  draw\n};\n\n// src/diagrams/kanban/styles.ts\n\nvar genSections = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if ((0,khroma__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(options[\"lineColor\" + i], 20);\n    }\n  }\n  const adjuster = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((color, level) => options.darkMode ? (0,khroma__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(color, level) : (0,khroma__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(color, level), \"adjuster\");\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} polygon, .section-${i - 1} path  {\n      fill: ${adjuster(options[\"cScale\" + i], 10)};\n      stroke: ${adjuster(options[\"cScale\" + i], 10)};\n\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.background};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .kanban-ticket-link {\n    fill: ${options.background};\n    stroke: ${options.nodeBorder};\n    text-decoration: underline;\n  }\n    `;\n  }\n  return sections2;\n}, \"genSections\");\nvar getStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_8__.__name)((options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .cluster-label, .label {\n    color: ${options.textColor};\n    fill: ${options.textColor};\n    }\n  .kanban-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n    ${(0,_chunk_E2GYISFI_mjs__WEBPACK_IMPORTED_MODULE_0__.getIconStyles)()}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/kanban/kanban-definition.ts\nvar diagram = {\n  db: kanbanDb_default,\n  renderer: kanbanRenderer_default,\n  parser: kanban_default,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-ZSS6B67P.mjs\n"));

/***/ })

}]);