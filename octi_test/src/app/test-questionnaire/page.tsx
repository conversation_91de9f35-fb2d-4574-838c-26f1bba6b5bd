/**
 * 问卷测试页面
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getAllQuestions } from '@/data/questionnaire-bank';
import { QuestionnaireWizard } from '@/components/questionnaire/questionnaire-wizard';
import { QuestionResponse } from '@/components/questionnaire/question-renderer';

export default function TestQuestionnairePage() {
  const [showWizard, setShowWizard] = useState(false);
  const [questions] = useState(() => getAllQuestions());

  const mockQuestionnaire = {
    id: 'test-questionnaire',
    title: '测试问卷',
    description: '用于测试问卷功能的简化版本',
    totalQuestions: questions.length,
    estimatedDuration: Math.ceil(questions.length * 1.5),
  };

  const handleSave = async (responses: QuestionResponse[]) => {
    console.log('💾 保存回答:', responses.length);
  };

  const handleSubmit = async (responses: QuestionResponse[]) => {
    console.log('🚀 提交问卷:', responses.length);
    console.log('📝 详细回答:', responses);
    alert(`问卷提交成功！共回答了 ${responses.length} 道题目。`);
  };

  const handleExit = () => {
    setShowWizard(false);
  };

  if (showWizard) {
    return (
      <div className="container mx-auto p-6">
        <QuestionnaireWizard
          questionnaire={mockQuestionnaire}
          questions={questions}
          onSave={handleSave}
          onSubmit={handleSubmit}
          onExit={handleExit}
          autoSave={false}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>问卷测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>总题目数: {questions.length}</div>
            <div>预设题目: {questions.filter(q => q.source === 'PRESET').length}</div>
            <div>智能生成: {questions.filter(q => q.source === 'AI_GENERATED').length}</div>
            <div>必答题目: {questions.filter(q => q.required).length}</div>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-semibold">维度分布:</h3>
            <div className="grid grid-cols-4 gap-2 text-sm">
              <div>SF: {questions.filter(q => q.category.startsWith('SF')).length}题</div>
              <div>IT: {questions.filter(q => q.category.startsWith('IT')).length}题</div>
              <div>MV: {questions.filter(q => q.category.startsWith('MV')).length}题</div>
              <div>AD: {questions.filter(q => q.category.startsWith('AD')).length}题</div>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">Order范围:</h3>
            <div className="text-sm">
              最小: {Math.min(...questions.map(q => q.order))} - 
              最大: {Math.max(...questions.map(q => q.order))}
            </div>
          </div>

          <Button onClick={() => setShowWizard(true)} className="w-full">
            开始测试问卷
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
