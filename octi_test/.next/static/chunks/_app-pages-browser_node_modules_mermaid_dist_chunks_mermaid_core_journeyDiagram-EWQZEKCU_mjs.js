"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_journeyDiagram-EWQZEKCU_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawBackgroundRect: function() { return /* binding */ drawBackgroundRect; },\n/* harmony export */   drawEmbeddedImage: function() { return /* binding */ drawEmbeddedImage; },\n/* harmony export */   drawImage: function() { return /* binding */ drawImage; },\n/* harmony export */   drawRect: function() { return /* binding */ drawRect; },\n/* harmony export */   drawText: function() { return /* binding */ drawText; },\n/* harmony export */   getNoteRect: function() { return /* binding */ getNoteRect; },\n/* harmony export */   getTextObj: function() { return /* binding */ getTextObj; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n// src/diagrams/common/svgDrawCommon.ts\n\nvar drawRect = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, textData) => {\n  const nText = textData.text.replace(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIconStyles: function() { return /* binding */ getIconStyles; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n\n\n// src/diagrams/globalStyles.ts\nvar getIconStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => `\n  /* Font Awesome icon styling - consolidated */\n  .label-icon {\n    display: inline-block;\n    height: 1em;\n    overflow: visible;\n    vertical-align: -0.125em;\n  }\n  \n  .node .label-icon path {\n    fill: currentColor;\n    stroke: revert;\n    stroke-width: revert;\n  }\n`, \"getIconStyles\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1FMkdZSVNGSS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFFOEI7O0FBRTlCO0FBQ0Esb0NBQW9DLDJEQUFNO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21lcm1haWQvZGlzdC9jaHVua3MvbWVybWFpZC5jb3JlL2NodW5rLUUyR1lJU0ZJLm1qcz81ZGRlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIF9fbmFtZVxufSBmcm9tIFwiLi9jaHVuay0zWFlSSDVBUC5tanNcIjtcblxuLy8gc3JjL2RpYWdyYW1zL2dsb2JhbFN0eWxlcy50c1xudmFyIGdldEljb25TdHlsZXMgPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKCgpID0+IGBcbiAgLyogRm9udCBBd2Vzb21lIGljb24gc3R5bGluZyAtIGNvbnNvbGlkYXRlZCAqL1xuICAubGFiZWwtaWNvbiB7XG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICAgIGhlaWdodDogMWVtO1xuICAgIG92ZXJmbG93OiB2aXNpYmxlO1xuICAgIHZlcnRpY2FsLWFsaWduOiAtMC4xMjVlbTtcbiAgfVxuICBcbiAgLm5vZGUgLmxhYmVsLWljb24gcGF0aCB7XG4gICAgZmlsbDogY3VycmVudENvbG9yO1xuICAgIHN0cm9rZTogcmV2ZXJ0O1xuICAgIHN0cm9rZS13aWR0aDogcmV2ZXJ0O1xuICB9XG5gLCBcImdldEljb25TdHlsZXNcIik7XG5cbmV4cG9ydCB7XG4gIGdldEljb25TdHlsZXNcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-EWQZEKCU.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-EWQZEKCU.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-67H74DCK.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs\");\n/* harmony import */ var _chunk_E2GYISFI_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-E2GYISFI.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-E2GYISFI.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n\n\n// src/diagrams/user-journey/parser/journey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 18], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 14];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"journey\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"taskName\": 18, \"taskData\": 19, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"journey\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 18: \"taskName\", 19: \"taskData\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 13:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 15, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 16] }, { 15: [1, 17] }, o($V0, [2, 11]), o($V0, [2, 12]), { 19: [1, 18] }, o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 13])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n            break;\n          case 6:\n            return 11;\n            break;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 17;\n            break;\n          case 15:\n            return 18;\n            break;\n          case 16:\n            return 19;\n            break;\n          case 17:\n            return \":\";\n            break;\n          case 18:\n            return 6;\n            break;\n          case 19:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:journey\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar journey_default = parser;\n\n// src/diagrams/user-journey/journeyDb.js\nvar currentSection = \"\";\nvar sections = [];\nvar tasks = [];\nvar rawTasks = [];\nvar clear2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.clear)();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n}, \"getTasks\");\nvar updateActors = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n}, \"updateActors\");\nvar addTask = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(descr, taskData) {\n  const pieces = taskData.substr(1).split(\":\");\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(\",\");\n  }\n  const peopleList = peeps.map((s) => s.trim());\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score\n  };\n  rawTasks.push(rawTask);\n}, \"addTask\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(pos) {\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar getActors = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return updateActors();\n}, \"getActors\");\nvar journeyDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(() => (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().journey, \"getConfig\"),\n  clear: clear2,\n  setDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.setDiagramTitle,\n  getDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getDiagramTitle,\n  setAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.setAccTitle,\n  getAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getAccTitle,\n  setAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.setAccDescription,\n  getAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors\n};\n\n// src/diagrams/user-journey/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((options) => `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n    font-family: ${options.fontFamily};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : \"fill: #FFF8DC\"};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : \"\"};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : \"\"};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : \"\"};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : \"\"};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : \"\"};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : \"\"};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : \"\"};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : \"\"};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : \"\"};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : \"\"};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : \"\"};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : \"\"};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : \"\"};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : \"\"};\n  }\n  ${(0,_chunk_E2GYISFI_mjs__WEBPACK_IMPORTED_MODULE_1__.getIconStyles)()}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/user-journey/journeyRenderer.ts\n\n\n// src/diagrams/user-journey/svgDraw.js\n\nvar drawRect2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, rectData) {\n  return (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.drawRect)(elem, rectData);\n}, \"drawRect\");\nvar drawFace = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_3__.arc)().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(smile, \"smile\");\n  function sad(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_3__.arc)().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(sad, \"sad\");\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(ambivalent, \"ambivalent\");\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n}, \"drawFace\");\nvar drawCircle = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n}, \"drawCircle\");\nvar drawText2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, textData) {\n  return (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.drawText)(elem, textData);\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText2(elem, txtObject);\n}, \"drawLabel\");\nvar drawSection = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, section, conf2) {\n  const g = elem.append(\"g\");\n  const rect = (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf2.width * section.taskCount + // width of the tasks\n  conf2.diagramMarginX * (section.taskCount - 1);\n  rect.height = conf2.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  _drawTextCandidateFunc(conf2)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf2,\n    section.colour\n  );\n}, \"drawSection\");\nvar taskCount = -1;\nvar drawTask = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, task, conf2) {\n  const center = task.x + conf2.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf2.width;\n  rect.height = conf2.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect2(g, rect);\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      title: person,\n      pos: task.actors[person].position\n    };\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n  _drawTextCandidateFunc(conf2)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf2,\n    task.colour\n  );\n}, \"drawTask\");\nvar drawBackgroundRect2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, bounds2) {\n  (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.drawBackgroundRect)(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2, colour) {\n    const { taskFontSize, taskFontFamily } = conf2;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar initGraphics = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n}, \"initGraphics\");\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawCircle,\n  drawSection,\n  drawText: drawText2,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect: drawBackgroundRect2,\n  initGraphics\n};\n\n// src/diagrams/user-journey/journeyRenderer.ts\nvar setConf = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(cnf) {\n  const keys = Object.keys(cnf);\n  keys.forEach(function(key) {\n    conf[key] = cnf[key];\n  });\n}, \"setConf\");\nvar actors = {};\nvar maxWidth = 0;\nfunction drawActorLegend(diagram2) {\n  const conf2 = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().journey;\n  const maxLabelWidth = conf2.maxLabelWidth;\n  maxWidth = 0;\n  let yPos = 60;\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      pos: actors[person].position\n    };\n    svgDraw_default.drawCircle(diagram2, circleData);\n    let measureText = diagram2.append(\"text\").attr(\"visibility\", \"hidden\").text(person);\n    const fullTextWidth = measureText.node().getBoundingClientRect().width;\n    measureText.remove();\n    let lines = [];\n    if (fullTextWidth <= maxLabelWidth) {\n      lines = [person];\n    } else {\n      const words = person.split(\" \");\n      let currentLine = \"\";\n      measureText = diagram2.append(\"text\").attr(\"visibility\", \"hidden\");\n      words.forEach((word) => {\n        const testLine = currentLine ? `${currentLine} ${word}` : word;\n        measureText.text(testLine);\n        const textWidth = measureText.node().getBoundingClientRect().width;\n        if (textWidth > maxLabelWidth) {\n          if (currentLine) {\n            lines.push(currentLine);\n          }\n          currentLine = word;\n          measureText.text(word);\n          if (measureText.node().getBoundingClientRect().width > maxLabelWidth) {\n            let brokenWord = \"\";\n            for (const char of word) {\n              brokenWord += char;\n              measureText.text(brokenWord + \"-\");\n              if (measureText.node().getBoundingClientRect().width > maxLabelWidth) {\n                lines.push(brokenWord.slice(0, -1) + \"-\");\n                brokenWord = char;\n              }\n            }\n            currentLine = brokenWord;\n          }\n        } else {\n          currentLine = testLine;\n        }\n      });\n      if (currentLine) {\n        lines.push(currentLine);\n      }\n      measureText.remove();\n    }\n    lines.forEach((line, index) => {\n      const labelData = {\n        x: 40,\n        y: yPos + 7 + index * 20,\n        fill: \"#666\",\n        text: line,\n        textMargin: conf2.boxTextMargin ?? 5\n      };\n      const textElement = svgDraw_default.drawText(diagram2, labelData);\n      const lineWidth = textElement.node().getBoundingClientRect().width;\n      if (lineWidth > maxWidth && lineWidth > conf2.leftMargin - lineWidth) {\n        maxWidth = lineWidth;\n      }\n    });\n    yPos += Math.max(20, lines.length * 20);\n  });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(drawActorLegend, \"drawActorLegend\");\nvar conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().journey;\nvar leftMargin = 0;\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(text, id, version, diagObj) {\n  const configObject = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)();\n  const titleColor = configObject.journey.titleColor;\n  const titleFontSize = configObject.journey.titleFontSize;\n  const titleFontFamily = configObject.journey.titleFontFamily;\n  const securityLevel = configObject.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(\"body\");\n  bounds.init();\n  const diagram2 = root.select(\"#\" + id);\n  svgDraw_default.initGraphics(diagram2);\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf.actorColours[actorPos % conf.actorColours.length],\n      position: actorPos\n    };\n    actorPos++;\n  });\n  drawActorLegend(diagram2);\n  leftMargin = conf.leftMargin + maxWidth;\n  bounds.insert(0, 0, leftMargin, Object.keys(actors).length * 50);\n  drawTasks(diagram2, tasks2, 0);\n  const box = bounds.getBounds();\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", leftMargin).attr(\"font-size\", titleFontSize).attr(\"font-weight\", \"bold\").attr(\"y\", 25).attr(\"fill\", titleColor).attr(\"font-family\", titleFontFamily);\n  }\n  const height = box.stopy - box.starty + 2 * conf.diagramMarginY;\n  const width = leftMargin + box.stopx + 2 * conf.diagramMarginX;\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.configureSvgSize)(diagram2, height, width, conf.useMaxWidth);\n  diagram2.append(\"line\").attr(\"x1\", leftMargin).attr(\"y1\", conf.height * 4).attr(\"x2\", width - leftMargin - 4).attr(\"y2\", conf.height * 4).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  const extraVertForTitle = title ? 70 : 0;\n  diagram2.attr(\"viewBox\", `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram2.attr(\"preserveAspectRatio\", \"xMinYMin meet\");\n  diagram2.attr(\"height\", height + extraVertForTitle + 25);\n}, \"draw\");\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  init: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n    this.sequenceItems = [];\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(startx, starty, stopx, stopy) {\n    const conf2 = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().journey;\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf2.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf2.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf2.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf2.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  bumpVerticalPos: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n    return this.data;\n  }, \"getBounds\")\n};\nvar fills = conf.sectionFills;\nvar textColours = conf.sectionColours;\nvar drawTasks = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(diagram2, tasks2, verticalPos) {\n  const conf2 = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().journey;\n  let lastSection = \"\";\n  const sectionVHeight = conf2.height * 2 + conf2.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n  let sectionNumber = 0;\n  let fill = \"#CCC\";\n  let colour = \"black\";\n  let num = 0;\n  for (const [i, task] of tasks2.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n      let taskInSectionCount = 0;\n      const currentSection2 = task.section;\n      for (let taskIndex = i; taskIndex < tasks2.length; taskIndex++) {\n        if (tasks2[taskIndex].section == currentSection2) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n      const section = {\n        x: i * conf2.taskMargin + i * conf2.width + leftMargin,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount\n      };\n      svgDraw_default.drawSection(diagram2, section, conf2);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n      return acc;\n    }, {});\n    task.x = i * conf2.taskMargin + i * conf2.width + leftMargin;\n    task.y = taskPos;\n    task.width = conf2.diagramMarginX;\n    task.height = conf2.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n    svgDraw_default.drawTask(diagram2, task, conf2);\n    bounds.insert(task.x, task.y, task.x + task.width + conf2.taskMargin, 300 + 5 * 30);\n  }\n}, \"drawTasks\");\nvar journeyRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/user-journey/journeyDiagram.ts\nvar diagram = {\n  parser: journey_default,\n  db: journeyDb_default,\n  renderer: journeyRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf) => {\n    journeyRenderer_default.setConf(cnf.journey);\n    journeyDb_default.clear();\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-EWQZEKCU.mjs\n"));

/***/ })

}]);