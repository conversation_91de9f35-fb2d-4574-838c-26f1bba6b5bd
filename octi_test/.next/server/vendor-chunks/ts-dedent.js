"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ts-dedent";
exports.ids = ["vendor-chunks/ts-dedent"];
exports.modules = {

/***/ "(ssr)/./node_modules/ts-dedent/esm/index.js":
/*!*********************************************!*\
  !*** ./node_modules/ts-dedent/esm/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dedent: () => (/* binding */ dedent),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction dedent(templ) {\n    var values = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        values[_i - 1] = arguments[_i];\n    }\n    var strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n    strings[strings.length - 1] = strings[strings.length - 1].replace(/\\r?\\n([\\t ]*)$/, '');\n    var indentLengths = strings.reduce(function (arr, str) {\n        var matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n        if (matches) {\n            return arr.concat(matches.map(function (match) { var _a, _b; return (_b = (_a = match.match(/[\\t ]/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0; }));\n        }\n        return arr;\n    }, []);\n    if (indentLengths.length) {\n        var pattern_1 = new RegExp(\"\\n[\\t ]{\" + Math.min.apply(Math, indentLengths) + \"}\", 'g');\n        strings = strings.map(function (str) { return str.replace(pattern_1, '\\n'); });\n    }\n    strings[0] = strings[0].replace(/^\\r?\\n/, '');\n    var string = strings[0];\n    values.forEach(function (value, i) {\n        var endentations = string.match(/(?:^|\\n)( *)$/);\n        var endentation = endentations ? endentations[1] : '';\n        var indentedValue = value;\n        if (typeof value === 'string' && value.includes('\\n')) {\n            indentedValue = String(value)\n                .split('\\n')\n                .map(function (str, i) {\n                return i === 0 ? str : \"\" + endentation + str;\n            })\n                .join('\\n');\n        }\n        string += indentedValue + strings[i + 1];\n    });\n    return string;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dedent);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ts-dedent/esm/index.js\n");

/***/ })

};
;