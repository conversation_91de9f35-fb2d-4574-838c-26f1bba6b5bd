"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-polygon";
exports.ids = ["vendor-chunks/d3-polygon"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-polygon/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-polygon/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n  var i = -1,\n      n = polygon.length,\n      a,\n      b = polygon[n - 1],\n      area = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    area += a[1] * b[0] - a[0] * b[1];\n  }\n\n  return area / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2FyZWEuanM/MzA2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2x5Z29uKSB7XG4gIHZhciBpID0gLTEsXG4gICAgICBuID0gcG9seWdvbi5sZW5ndGgsXG4gICAgICBhLFxuICAgICAgYiA9IHBvbHlnb25bbiAtIDFdLFxuICAgICAgYXJlYSA9IDA7XG5cbiAgd2hpbGUgKCsraSA8IG4pIHtcbiAgICBhID0gYjtcbiAgICBiID0gcG9seWdvbltpXTtcbiAgICBhcmVhICs9IGFbMV0gKiBiWzBdIC0gYVswXSAqIGJbMV07XG4gIH1cblxuICByZXR1cm4gYXJlYSAvIDI7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/centroid.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-polygon/src/centroid.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n  var i = -1,\n      n = polygon.length,\n      x = 0,\n      y = 0,\n      a,\n      b = polygon[n - 1],\n      c,\n      k = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    k += c = a[0] * b[1] - b[0] * a[1];\n    x += (a[0] + b[0]) * c;\n    y += (a[1] + b[1]) * c;\n  }\n\n  return k *= 3, [x / k, y / k];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY2VudHJvaWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY2VudHJvaWQuanM/ZmVhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2x5Z29uKSB7XG4gIHZhciBpID0gLTEsXG4gICAgICBuID0gcG9seWdvbi5sZW5ndGgsXG4gICAgICB4ID0gMCxcbiAgICAgIHkgPSAwLFxuICAgICAgYSxcbiAgICAgIGIgPSBwb2x5Z29uW24gLSAxXSxcbiAgICAgIGMsXG4gICAgICBrID0gMDtcblxuICB3aGlsZSAoKytpIDwgbikge1xuICAgIGEgPSBiO1xuICAgIGIgPSBwb2x5Z29uW2ldO1xuICAgIGsgKz0gYyA9IGFbMF0gKiBiWzFdIC0gYlswXSAqIGFbMV07XG4gICAgeCArPSAoYVswXSArIGJbMF0pICogYztcbiAgICB5ICs9IChhWzFdICsgYlsxXSkgKiBjO1xuICB9XG5cbiAgcmV0dXJuIGsgKj0gMywgW3ggLyBrLCB5IC8ga107XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-polygon/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n  var n = polygon.length,\n      p = polygon[n - 1],\n      x = point[0], y = point[1],\n      x0 = p[0], y0 = p[1],\n      x1, y1,\n      inside = false;\n\n  for (var i = 0; i < n; ++i) {\n    p = polygon[i], x1 = p[0], y1 = p[1];\n    if (((y1 > y) !== (y0 > y)) && (x < (x0 - x1) * (y - y1) / (y0 - y1) + x1)) inside = !inside;\n    x0 = x1, y0 = y1;\n  }\n\n  return inside;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY29udGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0IsT0FBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9jb250YWlucy5qcz83Y2FkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHBvbHlnb24sIHBvaW50KSB7XG4gIHZhciBuID0gcG9seWdvbi5sZW5ndGgsXG4gICAgICBwID0gcG9seWdvbltuIC0gMV0sXG4gICAgICB4ID0gcG9pbnRbMF0sIHkgPSBwb2ludFsxXSxcbiAgICAgIHgwID0gcFswXSwgeTAgPSBwWzFdLFxuICAgICAgeDEsIHkxLFxuICAgICAgaW5zaWRlID0gZmFsc2U7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBuOyArK2kpIHtcbiAgICBwID0gcG9seWdvbltpXSwgeDEgPSBwWzBdLCB5MSA9IHBbMV07XG4gICAgaWYgKCgoeTEgPiB5KSAhPT0gKHkwID4geSkpICYmICh4IDwgKHgwIC0geDEpICogKHkgLSB5MSkgLyAoeTAgLSB5MSkgKyB4MSkpIGluc2lkZSA9ICFpbnNpZGU7XG4gICAgeDAgPSB4MSwgeTAgPSB5MTtcbiAgfVxuXG4gIHJldHVybiBpbnNpZGU7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/cross.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-polygon/src/cross.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY3Jvc3MuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY3Jvc3MuanM/N2UwYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZXR1cm5zIHRoZSAyRCBjcm9zcyBwcm9kdWN0IG9mIEFCIGFuZCBBQyB2ZWN0b3JzLCBpLmUuLCB0aGUgei1jb21wb25lbnQgb2Zcbi8vIHRoZSAzRCBjcm9zcyBwcm9kdWN0IGluIGEgcXVhZHJhbnQgSSBDYXJ0ZXNpYW4gY29vcmRpbmF0ZSBzeXN0ZW0gKCt4IGlzXG4vLyByaWdodCwgK3kgaXMgdXApLiBSZXR1cm5zIGEgcG9zaXRpdmUgdmFsdWUgaWYgQUJDIGlzIGNvdW50ZXItY2xvY2t3aXNlLFxuLy8gbmVnYXRpdmUgaWYgY2xvY2t3aXNlLCBhbmQgemVybyBpZiB0aGUgcG9pbnRzIGFyZSBjb2xsaW5lYXIuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiLCBjKSB7XG4gIHJldHVybiAoYlswXSAtIGFbMF0pICogKGNbMV0gLSBhWzFdKSAtIChiWzFdIC0gYVsxXSkgKiAoY1swXSAtIGFbMF0pO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/cross.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/hull.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-polygon/src/hull.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/./node_modules/d3-polygon/src/cross.js\");\n\n\nfunction lexicographicOrder(a, b) {\n  return a[0] - b[0] || a[1] - b[1];\n}\n\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n  const n = points.length,\n      indexes = [0, 1];\n  let size = 2, i;\n\n  for (i = 2; i < n; ++i) {\n    while (size > 1 && (0,_cross_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0) --size;\n    indexes[size++] = i;\n  }\n\n  return indexes.slice(0, size); // remove popped points\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(points) {\n  if ((n = points.length) < 3) return null;\n\n  var i,\n      n,\n      sortedPoints = new Array(n),\n      flippedPoints = new Array(n);\n\n  for (i = 0; i < n; ++i) sortedPoints[i] = [+points[i][0], +points[i][1], i];\n  sortedPoints.sort(lexicographicOrder);\n  for (i = 0; i < n; ++i) flippedPoints[i] = [sortedPoints[i][0], -sortedPoints[i][1]];\n\n  var upperIndexes = computeUpperHullIndexes(sortedPoints),\n      lowerIndexes = computeUpperHullIndexes(flippedPoints);\n\n  // Construct the hull polygon, removing possible duplicate endpoints.\n  var skipLeft = lowerIndexes[0] === upperIndexes[0],\n      skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1],\n      hull = [];\n\n  // Add upper hull in right-to-l order.\n  // Then add lower hull in left-to-right order.\n  for (i = upperIndexes.length - 1; i >= 0; --i) hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n  for (i = +skipLeft; i < lowerIndexes.length - skipRight; ++i) hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n\n  return hull;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/hull.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-polygon/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polygonArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   polygonCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   polygonContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   polygonHull: () => (/* reexport safe */ _hull_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   polygonLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-polygon/src/area.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-polygon/src/centroid.js\");\n/* harmony import */ var _hull_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hull.js */ \"(ssr)/./node_modules/d3-polygon/src/hull.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-polygon/src/contains.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-polygon/src/length.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpRDtBQUNRO0FBQ1I7QUFDUTtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1wb2x5Z29uL3NyYy9pbmRleC5qcz9lODhmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uQXJlYX0gZnJvbSBcIi4vYXJlYS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHBvbHlnb25DZW50cm9pZH0gZnJvbSBcIi4vY2VudHJvaWQuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uSHVsbH0gZnJvbSBcIi4vaHVsbC5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHBvbHlnb25Db250YWluc30gZnJvbSBcIi4vY29udGFpbnMuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uTGVuZ3RofSBmcm9tIFwiLi9sZW5ndGguanNcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/length.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-polygon/src/length.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n  var i = -1,\n      n = polygon.length,\n      b = polygon[n - 1],\n      xa,\n      ya,\n      xb = b[0],\n      yb = b[1],\n      perimeter = 0;\n\n  while (++i < n) {\n    xa = xb;\n    ya = yb;\n    b = polygon[i];\n    xb = b[0];\n    yb = b[1];\n    xa -= xb;\n    ya -= yb;\n    perimeter += Math.hypot(xa, ya);\n  }\n\n  return perimeter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLXBvbHlnb24vc3JjL2xlbmd0aC5qcz8zOTc2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHBvbHlnb24pIHtcbiAgdmFyIGkgPSAtMSxcbiAgICAgIG4gPSBwb2x5Z29uLmxlbmd0aCxcbiAgICAgIGIgPSBwb2x5Z29uW24gLSAxXSxcbiAgICAgIHhhLFxuICAgICAgeWEsXG4gICAgICB4YiA9IGJbMF0sXG4gICAgICB5YiA9IGJbMV0sXG4gICAgICBwZXJpbWV0ZXIgPSAwO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgeGEgPSB4YjtcbiAgICB5YSA9IHliO1xuICAgIGIgPSBwb2x5Z29uW2ldO1xuICAgIHhiID0gYlswXTtcbiAgICB5YiA9IGJbMV07XG4gICAgeGEgLT0geGI7XG4gICAgeWEgLT0geWI7XG4gICAgcGVyaW1ldGVyICs9IE1hdGguaHlwb3QoeGEsIHlhKTtcbiAgfVxuXG4gIHJldHVybiBwZXJpbWV0ZXI7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/length.js\n");

/***/ })

};
;