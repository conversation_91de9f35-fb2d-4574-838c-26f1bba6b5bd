/**
 * OCTI智能评估系统 - 评估服务
 * 
 * 整合所有核心业务逻辑，提供完整的评估流程
 * 包括对话收集、问卷生成、评估分析、报告生成
 */

import { questionDesignerAgent } from './agents/question-designer';
import { organizationMentorAgent } from './agents/organization-mentor';
import { dialogueManager } from './dialogue-manager';
import { prisma } from '@/lib/prisma';
import { generateUUID } from '@/lib/utils';
import type {
  Assessment,
  AssessmentType,
  AssessmentStatus,
  Questionnaire,
  Question,
  Response,
  AnalysisResult,
  Report,
  NonprofitProfile,
  Organization,
} from '@prisma/client';

// ============================================================================
// 类型定义
// ============================================================================

export interface AssessmentCreationRequest {
  organizationId: string;
  title?: string;
  description?: string;
  type: AssessmentType;
}

export interface AssessmentFlowResult {
  assessment: Assessment;
  questionnaire?: Questionnaire;
  questions?: Question[];
  analysisResult?: AnalysisResult;
  report?: Report;
}

export interface QuestionnaireSubmission {
  assessmentId: string;
  responses: Array<{
    questionId: string;
    answer: any;
    confidence?: number;
    timeSpent?: number;
  }>;
}

export interface AssessmentProgress {
  assessmentId: string;
  status: AssessmentStatus;
  progress: number; // 0-100
  currentStep: string;
  nextStep?: string;
  estimatedTimeRemaining?: number; // 分钟
}

// ============================================================================
// 评估服务类
// ============================================================================

export class AssessmentService {
  /**
   * 创建新评估
   * 
   * @param request - 评估创建请求
   * @returns 评估对象
   */
  async createAssessment(request: AssessmentCreationRequest): Promise<Assessment> {
    try {
      // 验证组织存在
      const organization = await prisma.organization.findUnique({
        where: { id: request.organizationId },
        include: { nonprofitProfile: true },
      });

      if (!organization) {
        throw new Error('组织不存在');
      }

      // 创建评估记录
      const assessment = await prisma.assessment.create({
        data: {
          id: generateUUID(),
          title: request.title || `${organization.name} - OCTI能力评估`,
          description: request.description || '基于OCTI框架的组织能力评估',
          type: request.type,
          status: organization.nonprofitProfile ? 'QUESTIONNAIRE_GENERATION' : 'PROFILE_COLLECTION',
          organizationId: request.organizationId,
          userId: organization.userId,
          nonprofitProfileId: organization.nonprofitProfile?.id,
        },
      });

      return assessment;
    } catch (error) {
      console.error('Failed to create assessment:', error);
      throw new Error('评估创建失败');
    }
  }

  /**
   * 开始组织画像收集对话
   * 
   * @param assessmentId - 评估ID
   * @returns 对话响应
   */
  async startProfileCollection(assessmentId: string) {
    try {
      const assessment = await prisma.assessment.findUnique({
        where: { id: assessmentId },
      });

      if (!assessment) {
        throw new Error('评估不存在');
      }

      // 更新评估状态
      await prisma.assessment.update({
        where: { id: assessmentId },
        data: { 
          status: 'PROFILE_COLLECTION',
          startedAt: new Date(),
        },
      });

      // 开始对话
      const dialogueResponse = await dialogueManager.startDialogue(assessment.organizationId);

      return {
        assessmentId,
        ...dialogueResponse,
      };
    } catch (error) {
      console.error('Failed to start profile collection:', error);
      throw new Error('画像收集启动失败');
    }
  }

  /**
   * 处理对话消息
   * 
   * @param sessionId - 会话ID
   * @param message - 用户消息
   * @returns 对话响应
   */
  async processDialogueMessage(sessionId: string, message: string) {
    try {
      const response = await dialogueManager.processUserMessage(sessionId, message);

      // 如果对话完成，更新评估状态
      if (response.isComplete) {
        const session = await prisma.dialogueSession.findUnique({
          where: { id: sessionId },
        });

        if (session) {
          const assessment = await prisma.assessment.findFirst({
            where: { organizationId: session.organizationId },
            orderBy: { createdAt: 'desc' },
          });

          if (assessment) {
            await prisma.assessment.update({
              where: { id: assessment.id },
              data: { status: 'QUESTIONNAIRE_GENERATION' },
            });
          }
        }
      }

      return response;
    } catch (error) {
      console.error('Failed to process dialogue message:', error);
      throw new Error('对话消息处理失败');
    }
  }

  /**
   * 生成个性化问卷
   * 
   * @param assessmentId - 评估ID
   * @returns 问卷生成结果
   */
  async generateQuestionnaire(assessmentId: string) {
    try {
      const assessment = await prisma.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          organization: {
            include: { nonprofitProfile: true },
          },
        },
      });

      if (!assessment || !assessment.organization.nonprofitProfile) {
        throw new Error('评估或组织画像不存在');
      }

      // 更新评估状态
      await prisma.assessment.update({
        where: { id: assessmentId },
        data: { status: 'QUESTIONNAIRE_GENERATION' },
      });

      // 生成问卷
      const generationResult = await questionDesignerAgent.generateHybridQuestionnaire({
        organizationProfile: assessment.organization.nonprofitProfile,
        targetDimensions: ['governance', 'strategy', 'operations', 'finance'],
      });

      // 关联问卷到评估
      await prisma.assessment.update({
        where: { id: assessmentId },
        data: {
          questionnaireId: generationResult.questionnaire.id,
          status: 'IN_PROGRESS',
        },
      });

      return {
        assessmentId,
        questionnaire: generationResult.questionnaire,
        questions: generationResult.questions,
        metadata: generationResult.generationMetadata,
      };
    } catch (error) {
      console.error('Failed to generate questionnaire:', error);
      throw new Error('问卷生成失败');
    }
  }

  /**
   * 提交问卷回答
   * 
   * @param submission - 问卷提交数据
   * @returns 提交结果
   */
  async submitQuestionnaire(submission: QuestionnaireSubmission) {
    try {
      const assessment = await prisma.assessment.findUnique({
        where: { id: submission.assessmentId },
        include: {
          questionnaire: {
            include: { questions: true },
          },
        },
      });

      if (!assessment || !assessment.questionnaire) {
        throw new Error('评估或问卷不存在');
      }

      // 验证回答完整性
      const questionIds = new Set(assessment.questionnaire.questions.map(q => q.id));
      const responseQuestionIds = new Set(submission.responses.map(r => r.questionId));
      
      const missingQuestions = [...questionIds].filter(id => !responseQuestionIds.has(id));
      if (missingQuestions.length > 0) {
        throw new Error(`缺少${missingQuestions.length}道题目的回答`);
      }

      // 保存回答
      const responseData = submission.responses.map(r => ({
        id: generateUUID(),
        questionId: r.questionId,
        questionnaireId: assessment.questionnaireId!,
        assessmentId: submission.assessmentId,
        answer: r.answer,
        confidence: r.confidence,
        timeSpent: r.timeSpent,
      }));

      await prisma.response.createMany({
        data: responseData,
      });

      // 更新评估状态
      await prisma.assessment.update({
        where: { id: submission.assessmentId },
        data: { status: 'ANALYSIS' },
      });

      return {
        assessmentId: submission.assessmentId,
        responsesCount: responseData.length,
        status: 'submitted',
      };
    } catch (error) {
      console.error('Failed to submit questionnaire:', error);
      throw new Error('问卷提交失败');
    }
  }

  /**
   * 执行评估分析
   * 
   * @param assessmentId - 评估ID
   * @param useDualModel - 是否使用双模型分析
   * @returns 分析结果
   */
  async performAnalysis(assessmentId: string, useDualModel: boolean = false) {
    try {
      const assessment = await prisma.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          questionnaire: {
            include: { questions: true },
          },
          responses: true,
          organization: {
            include: { nonprofitProfile: true },
          },
        },
      });

      if (!assessment || !assessment.questionnaire || !assessment.organization.nonprofitProfile) {
        throw new Error('评估数据不完整');
      }

      // 构建分析上下文
      const analysisContext = {
        assessment,
        questionnaire: assessment.questionnaire,
        questions: assessment.questionnaire.questions,
        responses: assessment.responses,
        organizationProfile: assessment.organization.nonprofitProfile,
      };

      // 执行分析
      const analysisOutput = await organizationMentorAgent.analyzeAssessment(
        analysisContext,
        useDualModel || assessment.type === 'PROFESSIONAL'
      );

      // 保存分析结果
      const analysisResult = await organizationMentorAgent.saveAnalysisResult(assessmentId, analysisOutput);

      // 更新评估状态
      await prisma.assessment.update({
        where: { id: assessmentId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
        },
      });

      return {
        assessmentId,
        analysisResult,
        analysisOutput,
      };
    } catch (error) {
      console.error('Failed to perform analysis:', error);
      throw new Error('评估分析失败');
    }
  }

  /**
   * 生成评估报告
   * 
   * @param assessmentId - 评估ID
   * @param reportType - 报告类型
   * @returns 报告对象
   */
  async generateReport(assessmentId: string, reportType: 'SUMMARY' | 'DETAILED' = 'SUMMARY') {
    try {
      const assessment = await prisma.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          analysisResults: { orderBy: { createdAt: 'desc' }, take: 1 },
          organization: true,
        },
      });

      if (!assessment || assessment.analysisResults.length === 0) {
        throw new Error('评估或分析结果不存在');
      }

      const analysisResult = assessment.analysisResults[0];

      // 构建报告内容
      const reportContent = {
        executiveSummary: this.generateExecutiveSummary(analysisResult),
        organizationOverview: {
          name: assessment.organization.name,
          type: assessment.type,
          assessmentDate: assessment.completedAt,
        },
        capabilityAnalysis: analysisResult.capabilityScores,
        keyFindings: analysisResult.keyFindings,
        recommendations: analysisResult.recommendations,
        developmentPriorities: analysisResult.developmentPriorities,
        nextSteps: analysisResult.nextSteps,
        methodology: {
          framework: 'OCTI (Organization Capability Type Indicator)',
          analysisModel: analysisResult.analysisModel,
          confidence: analysisResult.confidence,
        },
      };

      // 创建报告记录
      const report = await prisma.report.create({
        data: {
          id: generateUUID(),
          title: `${assessment.organization.name} - OCTI评估报告`,
          type: reportType,
          format: 'JSON',
          content: reportContent,
          assessmentId,
          analysisResultId: analysisResult.id,
        },
      });

      return report;
    } catch (error) {
      console.error('Failed to generate report:', error);
      throw new Error('报告生成失败');
    }
  }

  /**
   * 获取评估进度
   * 
   * @param assessmentId - 评估ID
   * @returns 评估进度
   */
  async getAssessmentProgress(assessmentId: string): Promise<AssessmentProgress> {
    try {
      const assessment = await prisma.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          questionnaire: true,
          responses: true,
        },
      });

      if (!assessment) {
        throw new Error('评估不存在');
      }

      let progress = 0;
      let currentStep = '';
      let nextStep = '';
      let estimatedTimeRemaining = 0;

      switch (assessment.status) {
        case 'DRAFT':
          progress = 0;
          currentStep = '准备阶段';
          nextStep = '开始组织画像收集';
          estimatedTimeRemaining = 30;
          break;
        case 'PROFILE_COLLECTION':
          progress = 10;
          currentStep = '组织画像收集中';
          nextStep = '生成个性化问卷';
          estimatedTimeRemaining = 25;
          break;
        case 'QUESTIONNAIRE_GENERATION':
          progress = 20;
          currentStep = '生成个性化问卷';
          nextStep = '填写问卷';
          estimatedTimeRemaining = 20;
          break;
        case 'IN_PROGRESS':
          const totalQuestions = assessment.questionnaire?.totalQuestions || 60;
          const answeredQuestions = assessment.responses.length;
          progress = 20 + (answeredQuestions / totalQuestions) * 60;
          currentStep = `问卷填写中 (${answeredQuestions}/${totalQuestions})`;
          nextStep = answeredQuestions === totalQuestions ? '开始分析' : '继续填写问卷';
          estimatedTimeRemaining = Math.ceil((totalQuestions - answeredQuestions) * 1.5);
          break;
        case 'ANALYSIS':
          progress = 85;
          currentStep = '智能分析中';
          nextStep = '生成报告';
          estimatedTimeRemaining = 3;
          break;
        case 'COMPLETED':
          progress = 100;
          currentStep = '评估完成';
          estimatedTimeRemaining = 0;
          break;
        default:
          progress = 0;
          currentStep = '未知状态';
      }

      return {
        assessmentId,
        status: assessment.status,
        progress: Math.round(progress),
        currentStep,
        nextStep,
        estimatedTimeRemaining,
      };
    } catch (error) {
      console.error('Failed to get assessment progress:', error);
      throw new Error('获取评估进度失败');
    }
  }

  /**
   * 生成执行摘要
   */
  private generateExecutiveSummary(analysisResult: AnalysisResult): string {
    const overallLevel = this.getScoreLevel(analysisResult.overallScore);
    const topCapabilities = (analysisResult.capabilityScores as any[])
      .sort((a, b) => b.score - a.score)
      .slice(0, 2)
      .map(c => c.dimension);

    return `该组织的整体能力水平为${overallLevel}（${analysisResult.overallScore.toFixed(1)}分），在${topCapabilities.join('和')}方面表现突出。${analysisResult.keyFindings[0] || ''}`;
  }

  /**
   * 获取分数等级
   */
  private getScoreLevel(score: number): string {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    if (score >= 40) return '一般';
    if (score >= 20) return '待提升';
    return '需要重点关注';
  }
}

// ============================================================================
// 导出实例
// ============================================================================

export const assessmentService = new AssessmentService();
