"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_c4Diagram-6F6E4RAY_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F6E4RAY.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F6E4RAY.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-67H74DCK.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs\");\n/* harmony import */ var _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-55PJQP7W.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n\n\n// src/diagrams/c4/parser/c4Diagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 29:\n          $$[$0].splice(2, 0, \"SYSTEM\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n            break;\n          case 1:\n            return 7;\n            break;\n          case 2:\n            return 8;\n            break;\n          case 3:\n            return 9;\n            break;\n          case 4:\n            return 22;\n            break;\n          case 5:\n            return 23;\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n            break;\n          case 16:\n            break;\n          case 17:\n            return 11;\n            break;\n          case 18:\n            return 15;\n            break;\n          case 19:\n            return 16;\n            break;\n          case 20:\n            return 17;\n            break;\n          case 21:\n            return 18;\n            break;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n            break;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n            break;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n            break;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n            break;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n            break;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n            break;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n            break;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n            break;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n            break;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n            break;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n            break;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n            break;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n            break;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n            break;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n            break;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n            break;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n            break;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n            break;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n            break;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n            break;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n            break;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n            break;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n            break;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n            break;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n            break;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n            break;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n            break;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n            break;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n            break;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n            break;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n            break;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n            break;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n            break;\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            break;\n          case 71:\n            return 80;\n            break;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n            break;\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n            break;\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n            break;\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n            break;\n          case 81:\n            return \"LBRACE\";\n            break;\n          case 82:\n            return \"RBRACE\";\n            break;\n          case 83:\n            return \"SPACE\";\n            break;\n          case 84:\n            return \"EOL\";\n            break;\n          case 85:\n            return 14;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar c4Diagram_default = parser;\n\n// src/diagrams/c4/c4Db.js\nvar c4ShapeArray = [];\nvar boundaryParseStack = [\"\"];\nvar currentBoundaryParse = \"global\";\nvar parentBoundaryParse = \"\";\nvar boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nvar rels = [];\nvar title = \"\";\nvar wrapEnabled = false;\nvar c4ShapeInRow = 4;\nvar c4BoundaryInRow = 2;\nvar c4Type;\nvar getC4Type = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return c4Type;\n}, \"getC4Type\");\nvar setC4Type = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(c4TypeParam) {\n  let sanitizedText = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitizeText)(c4TypeParam, (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)());\n  c4Type = sanitizedText;\n}, \"setC4Type\");\nvar addRel = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n}, \"addRel\");\nvar addPersonOrSystem = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n}, \"addPersonOrSystem\");\nvar addContainer = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n}, \"addContainer\");\nvar addComponent = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n}, \"addComponent\");\nvar addPersonOrSystemBoundary = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addPersonOrSystemBoundary\");\nvar addContainerBoundary = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addContainerBoundary\");\nvar addDeploymentNode = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addDeploymentNode\");\nvar popBoundaryParseStack = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"popBoundaryParseStack\");\nvar updateElStyle = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n}, \"updateElStyle\");\nvar updateRelStyle = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n}, \"updateRelStyle\");\nvar updateLayoutConfig = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n}, \"updateLayoutConfig\");\nvar getC4ShapeInRow = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return c4ShapeInRow;\n}, \"getC4ShapeInRow\");\nvar getC4BoundaryInRow = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return c4BoundaryInRow;\n}, \"getC4BoundaryInRow\");\nvar getCurrentBoundaryParse = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return currentBoundaryParse;\n}, \"getCurrentBoundaryParse\");\nvar getParentBoundaryParse = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return parentBoundaryParse;\n}, \"getParentBoundaryParse\");\nvar getC4ShapeArray = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n}, \"getC4ShapeArray\");\nvar getC4Shape = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n}, \"getC4Shape\");\nvar getC4ShapeKeys = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n}, \"getC4ShapeKeys\");\nvar getBoundaries = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n}, \"getBoundaries\");\nvar getBoundarys = getBoundaries;\nvar getRels = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return rels;\n}, \"getRels\");\nvar getTitle = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return title;\n}, \"getTitle\");\nvar setWrap = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n}, \"setWrap\");\nvar autoWrap = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  return wrapEnabled;\n}, \"autoWrap\");\nvar clear = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n}, \"clear\");\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar setTitle = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(txt) {\n  let sanitizedText = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitizeText)(txt, (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)());\n  title = sanitizedText;\n}, \"setTitle\");\nvar c4Db_default = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.setAccTitle,\n  getAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getAccTitle,\n  getAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getAccDescription,\n  setAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.setAccDescription,\n  getConfig: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(() => (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().c4, \"getConfig\"),\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\n\n// src/diagrams/c4/c4Renderer.js\n\n\n// src/diagrams/c4/svgDraw.js\n\nvar drawRect2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, rectData) {\n  return (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.drawRect)(elem, rectData);\n}, \"drawRect\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_4__.sanitizeUrl)(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawRels = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n}, \"drawRels\");\nvar drawBoundary = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect2(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n}, \"drawBoundary\");\nvar drawC4Shape = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem, c4Shape, conf2) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = (0,_chunk_67H74DCK_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect2(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && c4Shape.techn?.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n}, \"drawC4Shape\");\nvar insertDatabaseIcon = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowEnd = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n}, \"insertArrowEnd\");\nvar insertArrowFilledHead = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertDynamicNumber = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertDynamicNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n}, \"insertArrowCrossHead\");\nvar getC4ShapeFont = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"getC4ShapeFont\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\n\n// src/diagrams/c4/c4Renderer.js\nvar globalBoundaryMaxX = 0;\nvar globalBoundaryMaxY = 0;\nvar c4ShapeInRow2 = 4;\nvar c4BoundaryInRow2 = 2;\nparser.yy = c4Db_default;\nvar conf = {};\nvar Bounds = class {\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(this, \"Bounds\");\n  }\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow2) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n};\nvar setConf = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(cnf) {\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.assignWithDepth_default)(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar c4ShapeFont = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"c4ShapeFont\");\nvar boundaryFont = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n}, \"boundaryFont\");\nvar messageFont = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.wrapLabel)(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.common_default.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextHeight)(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.common_default.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextHeight)(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(calcC4ShapeTextWH, \"calcC4ShapeTextWH\");\nvar drawBoundary2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw_default.drawBoundary(diagram2, boundary, conf);\n}, \"drawBoundary\");\nvar drawC4ShapeArray = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(\n      \"\\xAB\" + c4Shape.typeC4Shape.text + \"\\xBB\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw_default.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n}, \"drawC4ShapeArray\");\nvar Point = class {\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(this, \"Point\");\n  }\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n};\nvar getIntersectPoint = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n}, \"getIntersectPoint\");\nvar getIntersectPoints = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n}, \"getIntersectPoints\");\nvar drawRels2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.calculateTextWidth)(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw_default.drawRels(diagram2, rels2, conf);\n}, \"drawRels\");\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow2, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n    if (i == 0 || i % c4BoundaryInRow2 === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundaries(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary2(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(drawInsideBoundary, \"drawInsideBoundary\");\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(function(_text, id, _version, diagObj) {\n  conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().c4;\n  const securityLevel = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(\"body\");\n  let db = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow2 = db.getC4ShapeInRow();\n  c4BoundaryInRow2 = db.getC4BoundaryInRow();\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : (0,d3__WEBPACK_IMPORTED_MODULE_3__.select)(`[id=\"${id}\"]`);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundaries(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowEnd(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  drawRels2(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.configureSvgSize)(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.log.debug(`models:`, box);\n}, \"draw\");\nvar c4Renderer_default = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary: drawBoundary2,\n  setConf,\n  draw\n};\n\n// src/diagrams/c4/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)((options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/c4/c4Diagram.ts\nvar diagram = {\n  parser: c4Diagram_default,\n  db: c4Db_default,\n  renderer: c4Renderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_2__.__name)(({ c4, wrap }) => {\n    c4Renderer_default.setConf(c4);\n    c4Db_default.setWrap(wrap);\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-6F6E4RAY.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawBackgroundRect: function() { return /* binding */ drawBackgroundRect; },\n/* harmony export */   drawEmbeddedImage: function() { return /* binding */ drawEmbeddedImage; },\n/* harmony export */   drawImage: function() { return /* binding */ drawImage; },\n/* harmony export */   drawRect: function() { return /* binding */ drawRect; },\n/* harmony export */   drawText: function() { return /* binding */ drawText; },\n/* harmony export */   getNoteRect: function() { return /* binding */ getNoteRect; },\n/* harmony export */   getTextObj: function() { return /* binding */ getTextObj; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n// src/diagrams/common/svgDrawCommon.ts\n\nvar drawRect = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, textData) => {\n  const nText = textData.text.replace(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-67H74DCK.mjs\n"));

/***/ })

}]);