"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-delaunay";
exports.ids = ["vendor-chunks/d3-delaunay"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-delaunay/src/delaunay.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-delaunay/src/delaunay.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunay)\n/* harmony export */ });\n/* harmony import */ var delaunator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! delaunator */ \"(ssr)/./node_modules/delaunator/index.js\");\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./path.js */ \"(ssr)/./node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/./node_modules/d3-delaunay/src/polygon.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/./node_modules/d3-delaunay/src/voronoi.js\");\n\n\n\n\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nclass Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) {\n        inedges[hull[1]] = 0;\n        this.triangles[1] = hull[1];\n        this.triangles[2] = hull[1];\n      }\n    }\n  }\n  voronoi(bounds) {\n    return new _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r) {\n    if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n    r = r == undefined ? 2 : +r;\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/delaunay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-delaunay/src/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Delaunay: () => (/* reexport safe */ _delaunay_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Voronoi: () => (/* reexport safe */ _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _delaunay_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delaunay.js */ \"(ssr)/./node_modules/d3-delaunay/src/delaunay.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/./node_modules/d3-delaunay/src/voronoi.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL2luZGV4LmpzP2UyOGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0IGFzIERlbGF1bmF5fSBmcm9tIFwiLi9kZWxhdW5heS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIFZvcm9ub2l9IGZyb20gXCIuL3Zvcm9ub2kuanNcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/path.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-delaunay/src/path.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Path)\n/* harmony export */ });\nconst epsilon = 1e-6;\n\nclass Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/path.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/polygon.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-delaunay/src/polygon.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Polygon)\n/* harmony export */ });\nclass Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL3BvbHlnb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWRlbGF1bmF5L3NyYy9wb2x5Z29uLmpzPzkyNjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgY2xhc3MgUG9seWdvbiB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuXyA9IFtdO1xuICB9XG4gIG1vdmVUbyh4LCB5KSB7XG4gICAgdGhpcy5fLnB1c2goW3gsIHldKTtcbiAgfVxuICBjbG9zZVBhdGgoKSB7XG4gICAgdGhpcy5fLnB1c2godGhpcy5fWzBdLnNsaWNlKCkpO1xuICB9XG4gIGxpbmVUbyh4LCB5KSB7XG4gICAgdGhpcy5fLnB1c2goW3gsIHldKTtcbiAgfVxuICB2YWx1ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fLmxlbmd0aCA/IHRoaXMuXyA6IG51bGw7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/polygon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/voronoi.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-delaunay/src/voronoi.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Voronoi)\n/* harmony export */ });\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./path.js */ \"(ssr)/./node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/./node_modules/d3-delaunay/src/polygon.js\");\n\n\n\nclass Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n    let bx, by; // lazily computed barycenter of the hull\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (Math.abs(ab) < 1e-9) {\n        // For a degenerate triangle, the circumcenter is at the infinity, in a\n        // direction orthogonal to the halfedge and away from the “center” of\n        // the diagram <bx, by>, defined as the hull’s barycenter.\n        if (bx === undefined) {\n          bx = by = 0;\n          for (const i of hull) bx += points[i * 2], by += points[i * 2 + 1];\n          bx /= hull.length, by /= hull.length;\n        }\n        const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n        x = (x1 + x3) / 2 - a * ey;\n        y = (y1 + y3) / 2 + a * ex;\n      } else {\n        const d = 1 / ab;\n        const bl = dx * dx + dy * dy;\n        const cl = ex * ex + ey * ey;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] === cj[aj]\n              && ci[ai + 1] === cj[aj + 1]\n              && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj]\n              && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return this._simplify(V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points));\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1 = 0;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    // for more robustness, always consider the segment in the same order\n    const flip = c0 < c1;\n    if (flip) [x0, y0, x1, y1, c0, c1] = [x1, y1, x0, y0, c1, c0];\n    while (true) {\n      if (c0 === 0 && c1 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n      // undefined, the conditional statement will be executed.\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n  _simplify(P) {\n    if (P && P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n          P.splice(j, 2), i -= 2;\n        }\n      }\n      if (!P.length) P = null;\n    }\n    return P;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/voronoi.js\n");

/***/ })

};
;