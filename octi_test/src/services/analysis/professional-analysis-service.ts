/**
 * 专业版双模型OCTI分析服务
 * 第一步：MiniMax基础分析 -> 第二步：DeepSeek-Reasoner深度升华
 */

import fs from 'fs';
import path from 'path';
import { OCTITypeClassifier, OCTITypeResult } from './octi-type-classifier';
import { LLMClient } from '../llm/llm-client';

interface ProfessionalAnalysisRequest {
  profile: any;
  responses: any[];
  version: 'professional';
}

interface ProfessionalAnalysisResult {
  success: boolean;
  data: {
    // 第一部分：组织类型判断
    organizationType: OCTITypeResult;
    
    // 第二部分：MiniMax基础分析
    basicAnalysis: string;
    
    // 第三部分：DeepSeek深度升华分析
    enhancedAnalysis: string;
    
    // 元数据
    timestamp: string;
    version: string;
    processingSteps: string[];
  };
  error?: string;
}

/**
 * 专业版双模型OCTI分析服务
 */
export class ProfessionalAnalysisService {
  private minimaxClient: LLMClient;
  private deepseekClient: LLMClient;
  private basicPromptConfig: any;
  private enhancementPromptConfig: any;

  constructor() {
    this.minimaxClient = new LLMClient('minimax');
    this.deepseekClient = new LLMClient('deepseek');
    this.loadPromptConfigs();
  }

  /**
   * 加载提示词配置
   */
  private loadPromptConfigs(): void {
    try {
      // 基础分析使用精简配置
      const basicConfigPath = path.join(process.cwd(), 'configs', 'octi_analysis_prompt.json');
      const basicConfigContent = fs.readFileSync(basicConfigPath, 'utf-8');
      this.basicPromptConfig = JSON.parse(basicConfigContent);
      
      // 深度分析使用完整配置
      const enhancementConfigPath = path.join(process.cwd(), 'configs', 'organization_tutor_prompt.json');
      const enhancementConfigContent = fs.readFileSync(enhancementConfigPath, 'utf-8');
      this.enhancementPromptConfig = JSON.parse(enhancementConfigContent);
      
      console.log('✅ 专业版双模型提示词配置加载成功');
      console.log(`📏 基础配置长度: ${basicConfigContent.length}字符`);
      console.log(`📏 增强配置长度: ${enhancementConfigContent.length}字符`);
    } catch (error) {
      console.error('❌ 加载专业版提示词配置失败:', error);
      throw new Error('无法加载专业版分析配置');
    }
  }

  /**
   * 执行专业版双模型分析
   */
  async analyzeProfessional(request: ProfessionalAnalysisRequest): Promise<ProfessionalAnalysisResult> {
    const processingSteps: string[] = [];
    
    try {
      console.log('🚀 开始专业版双模型OCTI分析...');
      processingSteps.push('开始专业版分析');
      
      // 第一步：快速组织类型判断
      console.log('📊 步骤1: 组织类型判断...');
      const organizationType = OCTITypeClassifier.classifyOrganizationType(request.responses);
      console.log(`✅ 组织类型: ${organizationType.code} - ${organizationType.name}`);
      processingSteps.push(`组织类型判断完成: ${organizationType.name}`);
      
      // 第二步：MiniMax基础分析
      console.log('🤖 步骤2: MiniMax基础分析...');
      const basicAnalysis = await this.generateBasicAnalysis(request);
      console.log('✅ MiniMax基础分析完成');
      processingSteps.push('MiniMax基础分析完成');
      
      // 第三步：DeepSeek深度升华
      console.log('🧠 步骤3: DeepSeek深度升华...');
      const enhancedAnalysis = await this.generateEnhancedAnalysis(request, basicAnalysis, organizationType);
      console.log('✅ DeepSeek深度升华完成');
      processingSteps.push('DeepSeek深度升华完成');
      
      return {
        success: true,
        data: {
          organizationType,
          basicAnalysis,
          enhancedAnalysis,
          timestamp: new Date().toISOString(),
          version: request.version,
          processingSteps
        }
      };
      
    } catch (error) {
      console.error('❌ 专业版分析失败:', error);
      processingSteps.push(`分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
      
      // 容错处理：至少返回组织类型判断
      try {
        const organizationType = OCTITypeClassifier.classifyOrganizationType(request.responses);
        return {
          success: false,
          error: `专业版分析失败: ${error instanceof Error ? error.message : '未知错误'}`,
          data: {
            organizationType,
            basicAnalysis: '基础分析失败，请稍后重试。',
            enhancedAnalysis: '深度分析失败，请稍后重试。',
            timestamp: new Date().toISOString(),
            version: request.version,
            processingSteps
          }
        };
      } catch (fallbackError) {
        return {
          success: false,
          error: `完全分析失败: ${error instanceof Error ? error.message : '未知错误'}`,
          data: {
            organizationType: {
              code: 'ERROR',
              name: '分析失败',
              description: '分析过程中出现错误',
              characteristics: [],
              strengths: [],
              challenges: []
            },
            basicAnalysis: '分析失败，请检查系统配置。',
            enhancedAnalysis: '分析失败，请检查系统配置。',
            timestamp: new Date().toISOString(),
            version: request.version,
            processingSteps
          }
        };
      }
    }
  }

  /**
   * 第二步：MiniMax基础分析
   */
  private async generateBasicAnalysis(request: ProfessionalAnalysisRequest): Promise<string> {
    // 构建基础分析提示词
    const systemPrompt = this.buildBasicSystemPrompt();
    const userPrompt = this.buildBasicUserPrompt(request);
    
    console.log('📏 MiniMax提示词长度:', systemPrompt.length + userPrompt.length, '字符');
    
    // 调用MiniMax
    const rawResponse = await this.minimaxClient.call({
      model: 'minimax-M1',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 3000
    });

    // 提取文本内容
    let responseText = '';
    if (rawResponse?.choices?.[0]?.message?.content) {
      responseText = rawResponse.choices[0].message.content;
    } else if (typeof rawResponse === 'string') {
      responseText = rawResponse;
    } else {
      console.warn('⚠️ MiniMax响应格式异常:', rawResponse);
      responseText = '';
    }

    // 确保是字符串类型
    if (typeof responseText !== 'string') {
      console.warn('⚠️ MiniMax响应内容不是字符串:', typeof responseText, responseText);
      responseText = String(responseText || '');
    }

    // 清理文本格式
    return this.cleanAnalysisText(responseText);
  }

  /**
   * 第三步：DeepSeek深度升华分析
   */
  private async generateEnhancedAnalysis(
    request: ProfessionalAnalysisRequest, 
    basicAnalysis: string, 
    organizationType: OCTITypeResult
  ): Promise<string> {
    // 构建深度分析提示词
    const systemPrompt = this.buildEnhancementSystemPrompt();
    const userPrompt = this.buildEnhancementUserPrompt(request, basicAnalysis, organizationType);
    
    console.log('📏 DeepSeek提示词长度:', systemPrompt.length + userPrompt.length, '字符');
    
    // 调用DeepSeek-Reasoner
    const rawResponse = await this.deepseekClient.call({
      model: 'deepseek-reasoner',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 4000
    });

    // 提取文本内容
    let responseText = '';
    if (rawResponse?.choices?.[0]?.message?.content) {
      responseText = rawResponse.choices[0].message.content;
    } else if (typeof rawResponse === 'string') {
      responseText = rawResponse;
    } else {
      console.warn('⚠️ DeepSeek响应格式异常:', rawResponse);
      responseText = '';
    }

    // 确保是字符串类型
    if (typeof responseText !== 'string') {
      console.warn('⚠️ DeepSeek响应内容不是字符串:', typeof responseText, responseText);
      responseText = String(responseText || '');
    }

    // 清理文本格式
    return this.cleanAnalysisText(responseText);
  }

  /**
   * 构建MiniMax基础分析系统提示词
   */
  private buildBasicSystemPrompt(): string {
    return `你是OCTI公益组织能力评估专家，负责进行基础的组织能力分析。

你的任务是基于组织画像和问卷结果，提供清晰、实用的基础分析，为后续的深度分析提供基础。

分析要求：
1. 语言通俗易懂，避免过于专业的术语
2. 重点关注组织的实际能力表现
3. 识别明显的优势和挑战
4. 保持客观中肯的分析态度
5. 为深度分析提供扎实的基础

输出格式：自然文本，结构清晰，不使用markdown符号。`;
  }

  /**
   * 构建MiniMax基础分析用户提示词
   */
  private buildBasicUserPrompt(request: ProfessionalAnalysisRequest): string {
    const { profile, responses } = request;
    
    const organizationProfile = this.formatOrganizationProfile(profile);
    const questionnaireResults = this.formatQuestionnaireResults(responses);
    
    return `请对以下公益组织进行基础能力分析：

【组织基本情况】
${organizationProfile}

【问卷评估结果】
${questionnaireResults}

请按照OCTI四维模型进行基础分析，严格采用以下格式：

一、组织能力概览
简要总结组织的整体能力水平和发展阶段

二、四维能力分析

1. 战略聚焦度
当前表现：
- [具体表现点1]
- [具体表现点2]
- [具体表现点3]

主要优势：
- [优势点1]
- [优势点2]
- [优势点3]

改进空间：
- [改进建议1]
- [改进建议2]

2. 团队协同度
当前表现：
- [具体表现点1]
- [具体表现点2]
- [具体表现点3]

主要优势：
- [优势点1]
- [优势点2]
- [优势点3]

改进空间：
- [改进建议1]
- [改进建议2]

3. 价值导向度
当前表现：
- [具体表现点1]
- [具体表现点2]
- [具体表现点3]

主要优势：
- [优势点1]
- [优势点2]
- [优势点3]

改进空间：
- [改进建议1]
- [改进建议2]

4. 能力发展度
当前表现：
- [具体表现点1]
- [具体表现点2]
- [具体表现点3]

主要优势：
- [优势点1]
- [优势点2]
- [优势点3]

改进空间：
- [改进建议1]
- [改进建议2]

三、核心建议
1. [具体建议1]
2. [具体建议2]
3. [具体建议3]

**重要要求：**
- 严格按照上述格式输出，保持层次清晰
- 全部使用中文，绝对不要出现任何英文单词（如infrastructure等）
- 不要出现任何问题ID、调试信息或代码（如MV_P001、SF_P003等）
- 不要使用括号，特别是连续括号（（或空括号（）
- 每个要点独占一行，便于阅读
- 用自然流畅的中文描述，避免技术术语
- 如果需要表达技术概念，请用中文词汇替代英文`;
  }

  /**
   * 构建DeepSeek深度分析系统提示词
   */
  private buildEnhancementSystemPrompt(): string {
    // 使用完整的organization_tutor_prompt.json配置
    const fullPrompt = this.enhancementPromptConfig.prompt_template?.system_message || 
                      this.enhancementPromptConfig.systemPrompt?.role || 
                      '你是资深的公益组织发展顾问和OCTI评估专家';
    
    return `${fullPrompt}

你的特殊任务是基于已有的基础分析，进行深度升华和专业指导：

1. 深化分析深度：从基础分析中提炼更深层的洞察
2. 提供战略指导：结合行业最佳实践，给出战略性建议
3. 系统性思考：将各个维度整合为系统性的发展方案
4. 前瞻性规划：考虑组织的长期发展和行业趋势
5. 可操作性：确保建议具体可行，有明确的实施路径

你要在基础分析的基础上，提供更有价值、更具指导性的专业分析。`;
  }

  /**
   * 构建DeepSeek深度分析用户提示词
   */
  private buildEnhancementUserPrompt(
    request: ProfessionalAnalysisRequest, 
    basicAnalysis: string, 
    organizationType: OCTITypeResult
  ): string {
    const { profile } = request;
    
    return `请基于以下信息，对这个公益组织进行深度升华分析：

【组织类型】
${organizationType.code} - ${organizationType.name}
${organizationType.description}

【组织基本情况】
${this.formatOrganizationProfile(profile)}

【基础分析结果】
${basicAnalysis}

请在基础分析的基础上，进行深度升华，提供：

1. 深层能力洞察：挖掘基础分析中的深层含义和潜在机会
2. 战略发展建议：结合组织类型特征，提供战略性发展方向
3. 系统性改进方案：整合各维度，形成系统性的能力提升方案
4. 行业对标分析：与同类型优秀组织对比，找出差距和机会
5. 长期发展规划：考虑3-5年的发展路径和里程碑
6. 风险预警和应对：识别潜在风险并提供应对策略

**重要要求：**
- 请提供更有深度、更具指导价值的专业分析，帮助组织实现跨越式发展
- 全部使用中文，绝对不要出现任何英文单词（如infrastructure、framework等）
- 不要在分析结果中包含任何调试信息、问题ID或评分代码（如IT_P002=4、MV_P001等）
- 不要使用括号，特别是连续括号（（或空括号（）
- 确保输出内容完全面向最终用户，不包含后台运算过程
- 如果需要表达技术概念，请用中文词汇替代英文
- 如果需要使用表格，请使用标准的Markdown表格格式，确保前端能正确渲染`;
  }

  /**
   * 格式化组织画像
   */
  private formatOrganizationProfile(profile: any): string {
    return `
组织类型: ${profile.organizationType || '未知'}
服务领域: ${profile.serviceArea || '未知'}
发展阶段: ${profile.developmentStage || '未知'}
团队规模: ${profile.teamSize || '未知'}
运营模式: ${profile.operatingModel || '未知'}
影响范围: ${profile.impactScope || '未知'}
组织文化: ${profile.organizationCulture || '未知'}
主要挑战: ${profile.challengesPriorities || '未知'}
未来愿景: ${profile.futureVision || '未知'}
    `.trim();
  }

  /**
   * 格式化问卷结果
   */
  private formatQuestionnaireResults(responses: any[]): string {
    return responses.map((response, index) => {
      const answer = Array.isArray(response.answer) 
        ? response.answer.join(', ')
        : response.answer;
      return `${index + 1}. ${response.questionId}: ${answer}`;
    }).join('\n');
  }

  /**
   * 清理分析文本（保留格式结构）
   */
  private cleanAnalysisText(text: string): string {
    // 确保输入是字符串
    if (typeof text !== 'string') {
      console.warn('⚠️ cleanAnalysisText 接收到非字符串类型:', typeof text);
      return '';
    }

    // 先保存原始的换行结构
    let cleanedText = text;

    // 第一步：移除调试信息（保留换行）
    cleanedText = cleanedText
      // 移除各种格式的调试代码
      .replace(/\([A-Z]{2,}_[PI]\d+\s*[=:]\s*[^)]+\)/g, '') // 移除 (IT_P002=4) 或 (IT_I002: 2分低) 类型
      .replace(/\([A-Z]{2,}_\w+\s*[=:]\s*[^)]+\)/g, '') // 移除其他类似的调试信息
      .replace(/[A-Z]{2,}_[PI]\d+\s*[=:]\s*[^,，\s\n)]+/g, '') // 移除 SF_P001: very_clear 类型
      .replace(/（[A-Z]{2,}_[PI]\d+\s*[=:]\s*[^）]+）/g, '') // 移除中文括号的调试信息
      .replace(/[A-Z]{2,}_[PI]\d+\s*:\s*\d+分[低中高]/g, '') // 移除 "IT_I002: 2分低" 类型
      .replace(/[A-Z]{2,}_[PI]\d+/g, '') // 移除单独的调试代码如MV_P001
      .replace(/（[A-Z]{2,}_[PI]\d+）/g, '') // 移除括号中的调试代码
      .replace(/数据提示：[^\n]*/g, '') // 移除"数据提示："开头的调试行
      .replace(/但类似于[^\n]*不应该出现[^\n]*/g, '') // 移除特定的调试说明
      // 新增：移除更多调试信息格式
      .replace(/\d+分[低中高等]/g, '') // 移除 "2分低"、"4分中等" 类型
      .replace(/[A-Z]{2,}_[A-Z]\d+\s*:\s*\d+分[低中高等]/g, '') // 移除完整的评分调试信息
      .replace(/评分[：:]\s*\d+分/g, '') // 移除 "评分：4分" 类型
      .replace(/得分[：:]\s*\d+/g, ''); // 移除 "得分：4" 类型

    // 第二步：移除英文内容（保留换行）
    cleanedText = cleanedText
      .replace(/（问卷显示"[^"]*"）/g, '') // 移除（问卷显示"very_clear"）类型的调试信息
      .replace(/（"[^"]*"）/g, '') // 移除（"excellent"）类型的调试信息
      .replace(/问卷显示"[^"]*"/g, '') // 移除问卷显示"very_clear"
      .replace(/"[a-zA-Z_]+"/g, '') // 移除英文引号内容
      .replace(/\([a-zA-Z_]+\)/g, '') // 移除英文括号内容
      .replace(/\b[a-zA-Z]{4,}\b/g, '') // 移除4个字母以上的英文单词
      .replace(/\([^)]*[a-zA-Z]{3,}[^)]*\)/g, ''); // 移除包含英文的括号内容

    // 第三步：修复括号问题（保留换行）
    cleanedText = cleanedText
      .replace(/（+/g, '') // 清理连续的左括号
      .replace(/）+/g, '') // 清理连续的右括号
      .replace(/（\s*）/g, '') // 清理空括号
      .replace(/\(\s*\)/g, ''); // 清理英文空括号

    // 第四步：清理多余的空格和空行，但保留段落结构
    cleanedText = cleanedText
      .replace(/[ \t]+/g, ' ') // 合并连续的空格和制表符
      .replace(/\n[ \t]+/g, '\n') // 清理行首的空格
      .replace(/[ \t]+\n/g, '\n') // 清理行尾的空格
      .replace(/\n{3,}/g, '\n\n') // 合并多余的空行，但保留段落分隔
      .trim();

    return cleanedText;
  }
}
