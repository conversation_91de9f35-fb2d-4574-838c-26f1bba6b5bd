'use client';

import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

export default function TestFormatPage() {
  // 模拟 MiniMax 分析结果（包含您提到的格式问题）
  const originalText = `一、组织能力概览 该环保公益组织处于成熟发展阶段，团队规模大50+人，运营模式为综合服务型，影响范围覆盖全国。组织以创新驱动为核心文化，战略目标清晰且执行效率较高，但在数字化转型和影响力扩大方面面临挑战。整体能力表现良好，具备成为行业领军组织的潜力，需在技术应用和协同机制上进一步优化。
---

二、四维能力分析
1. **战略聚焦度** **当前表现**： - 战略目标明确，方向清晰 - 战略规划系统性强，执行效果优秀 - 战略执行细节仍有优化空间
**主要优势**： - 目标设定与组织愿景高度一致，方向感强 - 战略规划逻辑严谨，资源分配合理 - 团队对战略的认同度高，执行力突出

**改进空间**： - 细化数字化转型路径，明确阶段性目标 - 加强战略执行中的数据反馈机制，动态调整策略

---

2. **团队协同度** **当前表现**： - 团队结构合理，职责分工明确 - 跨部门协作流畅，沟通机制完善 - 团队规模较大，但整体协同效率保持良好
**主要优势**： - 组织架构清晰，管理层与执行层衔接紧密 - 内部沟通渠道多元，协作文化浓厚 - 团队成员对组织使命的认同感强

**改进空间**： - 引入数字化协作工具，提升远程办公效率 - 加强跨部门项目制管理，避免职能壁垒

---

3. **价值导向度** **当前表现**： - 使命培训体系完善 - 价值观共享机制有效 - 影响力测量方法科学 - 团队价值认同感极强
**主要优势**： - 使命驱动型文化渗透到日常运营 - 价值观传播与共享机制成熟 - 影响力评估体系完善，结果应用性强

**改进空间**： - 扩大价值传播的受众范围如公众、合作伙伴 - 量化价值输出的社会影响力，增强数据可视化

---

4. **能力发展度** **当前表现**： - 技术应用水平较高 - 数字化工具使用率良好 - 团队学习与创新能力突出组织文化: 创新驱动
**主要优势**： - 技术应用覆盖核心业务环节 - 团队具备较强的创新意识和学习能力 - 数字化基础扎实，具备转型潜力

**改进空间**： - 提升数据管理能力如大数据分析、数字化决策 - 引入更先进的环保技术工具如智能监测系统 - 加强员工数字化技能培训

---

三、核心建议 1. **加速数字化转型**：制定分阶段数字化战略，优先投入数据管理系统和智能工具，提升运营效率与影响力量化能力。 2. **优化协同机制**：推广数字化协作平台，打破部门壁垒，强化跨团队项目管理能力。 3. **强化价值传播**：通过公众报告、案例分享等方式扩大社会影响力，同时完善影响力数据的可视化呈现。
--- **分析说明**：本评估基于问卷结果与组织画像，结合四维模型战略聚焦、团队协同、价值导向、能力发展展开，聚焦实际能力表现与改进方向。`;

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">分析结果格式化测试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 原始格式 */}
        <div className="bg-white border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4 text-red-600">原始格式（问题版本）</h2>
          <div className="bg-gray-50 p-4 rounded border max-h-96 overflow-y-auto">
            <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono">
              {originalText}
            </pre>
          </div>
        </div>

        {/* 优化后格式 */}
        <div className="bg-white border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4 text-green-600">优化后格式</h2>
          <div className="bg-white p-4 rounded border max-h-96 overflow-y-auto">
            <MarkdownRenderer
              content={originalText}
              className="text-sm"
            />
          </div>
        </div>
      </div>

      <div className="mt-8 bg-blue-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">格式化改进说明</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ 去除了 ** # * 等 Markdown 格式符号</li>
          <li>✅ 为主标题（一、二、三、）添加了醒目的样式</li>
          <li>✅ 为子标题（1. 2. 3.）添加了背景色和边框</li>
          <li>✅ 为重要标题（当前表现、主要优势、改进空间）添加了特殊样式</li>
          <li>✅ 优化了列表项的显示，使用自定义符号</li>
          <li>✅ 为分隔线添加了合适的间距</li>
          <li>✅ 为带冒号的描述性内容添加了强调样式</li>
        </ul>
      </div>
    </div>
  );
}
