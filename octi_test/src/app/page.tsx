import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Brain, Clock, FileText, CheckCircle, Zap, Star } from 'lucide-react';
import { UserLayout, UserPageContainer } from '@/components/layouts/user-layout';

export const metadata: Metadata = {
  title: 'OCTI组织能力评估',
  description: 'OCTI智能评估系统 - 为公益机构提供专业的组织能力评估服务',
};

/**
 * OCTI智能评估系统 - 用户端首页
 *
 * 专注于评估的简洁首页，为用户提供直观的评估入口
 */
export default function HomePage() {
  return (
    <UserLayout>
      <UserPageContainer className="text-center py-12">

        {/* 主标题区域 */}
        <div className="mb-12">
          <div className="mb-6">
            <Brain className="h-16 w-16 text-primary mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              OCTI组织能力评估
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              基于四维八极模型的组织能力评估工具，帮助公益组织、社会企业和基金会了解自身能力特点，找到适合的发展路径
            </p>
          </div>
        </div>

        {/* 评估信息概览 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8 max-w-4xl mx-auto">
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <Clock className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">30-45分钟</div>
              <div className="text-sm text-gray-600">完成时间</div>
            </div>
            <div className="text-center">
              <FileText className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">60道题目</div>
              <div className="text-sm text-gray-600">评估问题</div>
            </div>
            <div className="text-center">
              <CheckCircle className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">专业报告</div>
              <div className="text-sm text-gray-600">分析结果</div>
            </div>
          </div>

          {/* 版本选择 */}
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">选择评估版本</h2>
            <p className="text-gray-600">根据您的需求选择合适的分析深度</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* 标准版 */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-6 hover:border-blue-300 transition-colors">
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                  <Brain className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">标准版分析</h3>
                <p className="text-sm text-gray-600 mt-1">专业AI分析，全面评估</p>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>组织能力评估师智能体</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>OCTI四维能力评估</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>基础发展建议报告</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>核心能力优化方案</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">¥99</div>
                <Link
                  href="/assessment/start?version=standard"
                  className="inline-flex items-center justify-center w-full gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  选择标准版
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* 专业版 */}
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 border-2 border-purple-300 rounded-xl p-6 hover:border-purple-400 transition-colors relative">
              {/* 推荐标签 */}
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-semibold flex items-center">
                  <Star className="h-3 w-3 mr-1" />
                  推荐
                </div>
              </div>

              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">专业版分析</h3>
                <p className="text-sm text-gray-600 mt-1">双模型深度分析，战略洞察</p>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>双智能体协作分析</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>深度推理与战略洞察</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>增强版分析报告</span>
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span>系统性发展规划</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">¥199</div>
                <Link
                  href="/assessment/start?version=professional"
                  className="inline-flex items-center justify-center w-full gap-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg text-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors"
                >
                  选择专业版
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* 评估流程说明 */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">评估流程</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  1
                </div>
                <h3 className="text-lg font-semibold text-gray-900">组织信息收集</h3>
              </div>
              <p className="text-gray-600">
                填写您的组织基本信息，包括组织类型、规模、主要业务领域等，帮助我们为您定制评估内容。
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  2
                </div>
                <h3 className="text-lg font-semibold text-gray-900">能力评估问卷</h3>
              </div>
              <p className="text-gray-600">
                回答60道专业设计的问题，涵盖组织治理、项目管理、资源筹集、影响力评估等核心能力领域。
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  3
                </div>
                <h3 className="text-lg font-semibold text-gray-900">智能分析</h3>
              </div>
              <p className="text-gray-600">
                AI系统将分析您的回答，识别组织优势和改进空间，生成个性化的能力评估报告。
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                  4
                </div>
                <h3 className="text-lg font-semibold text-gray-900">发展建议</h3>
              </div>
              <p className="text-gray-600">
                获得针对性的组织发展建议和具体的行动计划，助力您的组织持续成长。
              </p>
            </div>
          </div>
        </div>

        {/* 什么是OCTI */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">什么是OCTI?</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              OCTI（组织能力类型指标）是一套基于四维八极模型的组织能力评估工具，
              通过评估，您将了解组织在战略聚焦度(S/F)、团队协同度(I/T)、
              价值导向度(M/V)和能力发展度(A/D)四个维度的表现，
              并获得针对性的发展建议。
            </p>
          </div>

          {/* 四维八极理论框架 */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">四维八极理论框架</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 战略聚焦度 */}
              <div className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-bold text-lg">S/F</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">战略聚焦度</h4>
                    <p className="text-sm text-gray-500">战略模糊 vs 战略聚焦</p>
                  </div>
                </div>
                <p className="text-gray-600">
                  评估组织的战略定位清晰度、专业深度和资源聚焦程度。战略模糊(S)的组织倾向于多元尝试，边界不清；战略聚焦(F)的组织则定位明确，专业深耕。
                </p>
              </div>

              {/* 团队协同度 */}
              <div className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-green-600 font-bold text-lg">I/T</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">团队协同度</h4>
                    <p className="text-sm text-gray-500">个体驱动 vs 团队协同</p>
                  </div>
                </div>
                <p className="text-gray-600">
                  评估组织的核心团队能力结构和协作模式。个体驱动(I)的组织高度依赖核心人物；团队协同(T)的组织则建立了系统化的团队力量。
                </p>
              </div>

              {/* 价值导向度 */}
              <div className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-purple-600 font-bold text-lg">M/V</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">价值导向度</h4>
                    <p className="text-sm text-gray-500">使命导向 vs 价值导向</p>
                  </div>
                </div>
                <p className="text-gray-600">
                  评估组织的价值诉求表达方式。使命导向(M)的组织注重理想主义和内在驱动；价值导向(V)的组织则更务实，注重外在认可和实际成果。
                </p>
              </div>

              {/* 能力发展度 */}
              <div className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-orange-600 font-bold text-lg">A/D</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">能力发展度</h4>
                    <p className="text-sm text-gray-500">适应成长 vs 能力完善</p>
                  </div>
                </div>
                <p className="text-gray-600">
                  评估组织的能力盘点发展阶段。适应成长(A)的组织灵活应变，快速学习；能力完善(D)的组织则体系成熟，标准化程度高。
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 16种组织能力类型 */}
        <div className="mt-16 max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">16种组织能力类型</h3>
            <p className="text-lg text-gray-600">
              基于四维八极模型，OCTI可以识别16种不同的组织能力类型，分为三大类
            </p>
          </div>

          {/* 经典成功型组织 */}
          <div className="mb-12">
            <h4 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                ✓
              </span>
              经典成功型组织（4种）
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">🎯</span>
                  <div>
                    <h5 className="font-bold text-gray-900">FTMD</h5>
                    <p className="text-sm text-gray-600">战略大师型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略聚焦、团队协同、使命导向、能力完善</p>
                <p className="text-xs text-gray-500">如：壹基金、免费午餐等成熟机构</p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">🚀</span>
                  <div>
                    <h5 className="font-bold text-gray-900">FTMA</h5>
                    <p className="text-sm text-gray-600">创新先锋型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略聚焦、团队协同、使命导向、适应成长</p>
                <p className="text-xs text-gray-500">如：创新型环保组织、社会企业家项目</p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">🏥</span>
                  <div>
                    <h5 className="font-bold text-gray-900">FTVD</h5>
                    <p className="text-sm text-gray-600">专业服务型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略聚焦、团队协同、价值导向、能力完善</p>
                <p className="text-xs text-gray-500">如：成熟教育、医疗类公益组织</p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">💡</span>
                  <div>
                    <h5 className="font-bold text-gray-900">FTVA</h5>
                    <p className="text-sm text-gray-600">价值创新型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略聚焦、团队协同、价值导向、适应成长</p>
                <p className="text-xs text-gray-500">如：新兴社会企业、跨界创新组织</p>
              </div>
            </div>
          </div>

          {/* 风险挑战型组织 */}
          <div className="mb-12">
            <h4 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <span className="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                ⚠
              </span>
              风险挑战型组织（4种）
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">⚠️</span>
                  <div>
                    <h5 className="font-bold text-gray-900">SIMD</h5>
                    <p className="text-sm text-gray-600">个人英雄型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略模糊、个体驱动、使命导向、能力完善</p>
                <p className="text-xs text-red-500">风险：核心人员风险高</p>
              </div>

              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">📈</span>
                  <div>
                    <h5 className="font-bold text-gray-900">SIVD</h5>
                    <p className="text-sm text-gray-600">机会主义型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略模糊、个体驱动、价值导向、能力完善</p>
                <p className="text-xs text-red-500">风险：缺乏内在驱动力</p>
              </div>

              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">👤</span>
                  <div>
                    <h5 className="font-bold text-gray-900">SIMA</h5>
                    <p className="text-sm text-gray-600">创始人驱动型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略模糊、个体驱动、使命导向、适应成长</p>
                <p className="text-xs text-red-500">风险：过度依赖创始人</p>
              </div>

              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-3">
                  <span className="text-2xl mr-2">💰</span>
                  <div>
                    <h5 className="font-bold text-gray-900">SIVA</h5>
                    <p className="text-sm text-gray-600">资源导向型</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2">战略模糊、个体驱动、价值导向、适应成长</p>
                <p className="text-xs text-red-500">风险：缺乏战略方向</p>
              </div>
            </div>
          </div>

          {/* 成长潜力型组织 */}
          <div className="mb-12">
            <h4 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <span className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                ↗
              </span>
              成长潜力型组织（8种）
            </h4>
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
              <p className="text-gray-600 mb-4">
                各种过渡型和发展型组织类型，具有不同的成长潜力和发展方向：
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <span className="text-lg mr-2">🌱</span>
                  <div>
                    <h5 className="font-semibold text-gray-900">STMA - 创业萌芽型</h5>
                    <p className="text-sm text-gray-600">战略模糊但团队协作好，使命导向强，适应性高</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="text-lg mr-2">📈</span>
                  <div>
                    <h5 className="font-semibold text-gray-900">FTMD - 能力提升型</h5>
                    <p className="text-sm text-gray-600">战略聚焦、团队协同、使命导向，但能力有待完善</p>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                还有其他6种过渡型组织类型，通过评估可以获得具体的类型识别和发展建议
              </p>
            </div>
          </div>
        </div>

        {/* OCTI评估的三大价值 */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">OCTI评估的价值</h3>
            <p className="text-lg text-gray-600">
              通过科学的评估方法，为您的组织发展提供专业指导
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-8 shadow-sm border hover:shadow-md transition-shadow text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔍</span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">自我认知</h4>
              <p className="text-gray-600">
                帮助组织清晰认识自身能力特点，了解优势和挑战，建立客观的自我认知基础
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border hover:shadow-md transition-shadow text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📈</span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">发展指导</h4>
              <p className="text-gray-600">
                提供针对性的发展建议，指明能力提升方向，制定可行的改进计划
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border hover:shadow-md transition-shadow text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-3">战略规划</h4>
              <p className="text-gray-600">
                为组织战略规划提供科学依据，避免盲目发展，确保资源的有效配置
              </p>
            </div>
          </div>
        </div>

        {/* 核心优势 */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">为什么选择OCTI?</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-2">理论基础</h4>
              <p className="text-gray-600">基于四维八极模型，将经典MBTI理论应用于公益组织能力评估</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-2">AI驱动</h4>
              <p className="text-gray-600">结合AI技术实现智能化、标准化的组织诊断和个性化建议</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-2">专业服务</h4>
              <p className="text-gray-600">为公益组织提供战略管理的数字化入口工具和专业咨询</p>
            </div>
          </div>
        </div>

        {/* 立即开始 */}
        <div className="mt-16 text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
          <h3 className="text-2xl font-bold mb-4">开始您的组织能力评估</h3>
          <p className="text-lg mb-6 opacity-90">
            了解您的组织能力类型，获得专业的发展建议
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/assessment/start?version=standard"
              className="inline-flex items-center justify-center gap-2 bg-white text-blue-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              标准版评估 ¥99
              <ArrowRight className="h-4 w-4" />
            </Link>
            <Link
              href="/assessment/start?version=professional"
              className="inline-flex items-center justify-center gap-2 bg-purple-700 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-purple-800 transition-colors border border-purple-500"
            >
              专业版评估 ¥199
              <ArrowRight className="h-4 w-4" />
            </Link>
          </div>
        </div>

      </UserPageContainer>
    </UserLayout>
  );
}
