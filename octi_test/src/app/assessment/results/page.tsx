/**
 * OCTI智能评估系统 - 用户端评估结果页面
 * 
 * 用户查看自己的评估结果和发展建议
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { UserLayout, UserPageContainer, StepIndicator } from '@/components/layouts/user-layout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Share2, BarChart3, TrendingUp, Target, CheckCircle, Loader2 } from 'lucide-react';
import { AnalysisResult } from '@/services/analysis/analysis-service';

// ============================================================================
// 数据获取和状态管理
// ============================================================================

/**
 * 用户端评估结果页面
 */
export default function AssessmentResultsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'dimensions' | 'recommendations'>('overview');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取分析结果
  useEffect(() => {
    const fetchAnalysisResult = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 从localStorage获取数据
        const profileData = localStorage.getItem('profileAnswers');
        const responsesData = localStorage.getItem('assessmentResponses');

        if (!profileData || !responsesData) {
          throw new Error('缺少评估数据，请重新完成评估');
        }

        const profile = JSON.parse(profileData);
        const responses = JSON.parse(responsesData);

        console.log('📊 开始请求分析结果...');

        // 调用OCTI专业分析API
        const response = await fetch('/api/assessment/analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profile,
            responses,
            version: 'standard' // 默认使用标准版，可以后续添加版本选择
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '分析服务暂时不可用');
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || '分析失败');
        }

        setAnalysisResult(result.data);
        console.log('✅ 分析结果获取成功');

      } catch (err) {
        console.error('❌ 获取分析结果失败:', err);
        setError(err instanceof Error ? err.message : '获取分析结果失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalysisResult();
  }, []);

  const getLevelColor = (level: string) => {
    switch (level) {
      case '优秀': return 'bg-green-100 text-green-800';
      case '良好': return 'bg-blue-100 text-blue-800';
      case '一般': return 'bg-yellow-100 text-yellow-800';
      case '待改进': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 加载状态
  if (isLoading) {
    return (
      <UserLayout
        showBackButton
        backHref="/assessment/questionnaire/assessment-1"
        title="评估结果"
        subtitle="正在生成您的组织能力分析报告..."
      >
        <UserPageContainer>
          <div className="flex items-center justify-center py-20">
            <div className="text-center space-y-4">
              <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">AI正在分析您的数据</h3>
                <p className="text-muted-foreground">
                  这可能需要30-60秒，请耐心等待...
                </p>
              </div>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  // 错误状态
  if (error) {
    return (
      <UserLayout
        showBackButton
        backHref="/assessment/questionnaire/assessment-1"
        title="评估结果"
        subtitle="获取分析结果时出现问题"
      >
        <UserPageContainer>
          <div className="text-center py-20">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold mb-2">分析失败</h3>
            <p className="text-muted-foreground mb-6">{error}</p>
            <div className="space-x-4">
              <Button onClick={() => window.location.reload()}>
                重试
              </Button>
              <Link href="/assessment/start">
                <Button variant="outline">重新开始评估</Button>
              </Link>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  // 没有数据
  if (!analysisResult) {
    return (
      <UserLayout
        showBackButton
        backHref="/assessment/start"
        title="评估结果"
        subtitle="没有找到分析结果"
      >
        <UserPageContainer>
          <div className="text-center py-20">
            <div className="text-gray-400 text-6xl mb-4">📊</div>
            <h3 className="text-lg font-semibold mb-2">暂无分析结果</h3>
            <p className="text-muted-foreground mb-6">请先完成组织画像和能力评估问卷</p>
            <Link href="/assessment/start">
              <Button>开始评估</Button>
            </Link>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  return (
    <UserLayout 
      showBackButton 
      backHref="/assessment/questionnaire/assessment-1"
      title="评估结果"
      subtitle="恭喜您完成评估！以下是您的组织能力分析报告"
    >
      <UserPageContainer>
        {/* 步骤指示器 */}
        <StepIndicator 
          currentStep={4}
          totalSteps={4}
          steps={['组织信息', '能力评估', '结果分析', '发展建议']}
        />

        {/* 总体得分卡片 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8 text-center">
          <div className="mb-6">
            <div className="text-6xl font-bold text-primary mb-2">
              {analysisResult.overallScore}
            </div>
            <div className="text-xl text-gray-600">总体得分</div>
            <Badge className={`mt-2 ${getLevelColor(analysisResult.level)}`}>
              {analysisResult.level}
            </Badge>
          </div>

          <div className="flex justify-center space-x-4 mb-6">
            <Button className="flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>下载报告</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2">
              <Share2 className="h-4 w-4" />
              <span>分享结果</span>
            </Button>
          </div>

          <p className="text-gray-600">
            完成时间：{new Date(analysisResult.completedAt).toLocaleDateString('zh-CN')}
          </p>
        </div>

        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg w-fit mx-auto">
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            总体概览
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'dimensions'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            onClick={() => setActiveTab('dimensions')}
          >
            能力维度
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'recommendations'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
            onClick={() => setActiveTab('recommendations')}
          >
            发展建议
          </button>
        </div>

        {/* 标签页内容 */}
        {activeTab === 'overview' && (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {analysisResult.dimensions.map((dimension, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{dimension.name}</CardTitle>
                  <CardDescription>{dimension.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <div className="text-3xl font-bold text-primary mb-1">
                      {dimension.score}
                    </div>
                    <Badge className={getLevelColor(dimension.level)}>
                      {dimension.level}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'dimensions' && (
          <div className="space-y-6">
            {analysisResult.dimensions.map((dimension, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl">{dimension.name}</CardTitle>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">
                        {dimension.score}
                      </div>
                      <Badge className={getLevelColor(dimension.level)}>
                        {dimension.level}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription>{dimension.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-green-700 mb-2 flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        优势
                      </h4>
                      <ul className="space-y-1">
                        {dimension.strengths.map((strength, idx) => (
                          <li key={idx} className="text-sm text-gray-600">
                            • {strength}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-blue-700 mb-2 flex items-center">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        改进建议
                      </h4>
                      <ul className="space-y-1">
                        {dimension.improvements.map((improvement, idx) => (
                          <li key={idx} className="text-sm text-gray-600">
                            • {improvement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div className="space-y-6">
            {analysisResult.recommendations.map((rec, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{rec.title}</CardTitle>
                    <Badge className={getPriorityColor(rec.priority)}>
                      {rec.priority === 'high' ? '高优先级' :
                       rec.priority === 'medium' ? '中优先级' : '低优先级'}
                    </Badge>
                  </div>
                  <CardDescription>{rec.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <h4 className="font-semibold mb-2 flex items-center">
                    <Target className="h-4 w-4 mr-2" />
                    具体行动
                  </h4>
                  <ul className="space-y-1">
                    {rec.actions.map((action, idx) => (
                      <li key={idx} className="text-sm text-gray-600">
                        • {action}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* 底部操作 */}
        <div className="text-center mt-12 pt-8 border-t">
          <div className="space-y-4">
            <p className="text-gray-600">
              感谢您使用OCTI智能评估系统！如有疑问，请联系我们的专业团队。
            </p>
            <div className="flex justify-center space-x-4">
              <Link href="/">
                <Button variant="outline">返回首页</Button>
              </Link>
              <Link href="/assessment/start">
                <Button>开始新评估</Button>
              </Link>
            </div>
          </div>
        </div>
      </UserPageContainer>
    </UserLayout>
  );
}
