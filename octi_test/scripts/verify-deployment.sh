#!/bin/bash

# OCTI智能评估系统 - 部署验证脚本
# 验证Docker部署是否成功

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    local compose_file="docker-compose.yml"
    if [ "$1" = "dev" ]; then
        compose_file="docker-compose.dev.yml"
    fi
    
    # 检查必要的容器
    local containers=("app" "postgres" "redis")
    local all_running=true
    
    for container in "${containers[@]}"; do
        if docker-compose -f $compose_file ps | grep -q "${container}.*Up"; then
            log_success "${container} 容器运行正常"
        else
            log_error "${container} 容器未运行"
            all_running=false
        fi
    done
    
    if [ "$all_running" = true ]; then
        log_success "所有容器运行正常"
        return 0
    else
        log_error "部分容器未正常运行"
        return 1
    fi
}

# 检查网络连接
check_network() {
    log_info "检查容器网络连接..."
    
    # 检查应用到数据库的连接
    if docker-compose exec app ping -c 1 postgres > /dev/null 2>&1; then
        log_success "应用到数据库网络连接正常"
    else
        log_error "应用到数据库网络连接失败"
        return 1
    fi
    
    # 检查应用到Redis的连接
    if docker-compose exec app ping -c 1 redis > /dev/null 2>&1; then
        log_success "应用到Redis网络连接正常"
    else
        log_error "应用到Redis网络连接失败"
        return 1
    fi
    
    return 0
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if docker-compose exec postgres pg_isready -U octi_user -d octi_db > /dev/null 2>&1; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis连接正常"
        return 0
    else
        log_error "Redis连接失败"
        return 1
    fi
}

# 检查应用健康状态
check_app_health() {
    log_info "检查应用健康状态..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_info "等待应用启动... (${attempt}/${max_attempts})"
        sleep 2
        ((attempt++))
    done
    
    log_error "应用健康检查失败"
    return 1
}

# 检查API端点
check_api_endpoints() {
    log_info "检查API端点..."
    
    local endpoints=(
        "/api/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f "http://localhost:3000${endpoint}" > /dev/null 2>&1; then
            log_success "API端点 ${endpoint} 正常"
        else
            log_warning "API端点 ${endpoint} 可能未就绪"
        fi
    done
}

# 检查数据库表
check_database_tables() {
    log_info "检查数据库表结构..."
    
    local table_count=$(docker-compose exec postgres psql -U octi_user -d octi_db -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
    
    if [ "$table_count" -gt 0 ]; then
        log_success "数据库表结构正常 (${table_count} 个表)"
    else
        log_warning "数据库表未创建，可能需要运行迁移"
    fi
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量配置..."
    
    if [ ! -f .env ]; then
        log_error ".env文件不存在"
        return 1
    fi
    
    # 检查关键环境变量
    source .env
    
    local required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "NEXTAUTH_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 ${var} 未设置"
            return 1
        else
            log_success "环境变量 ${var} 已设置"
        fi
    done
    
    return 0
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    local available_space=$(df . | awk 'NR==2 {print $4}')
    local required_space=1048576  # 1GB in KB
    
    if [ "$available_space" -gt "$required_space" ]; then
        log_success "磁盘空间充足 ($(($available_space / 1024 / 1024))GB 可用)"
    else
        log_warning "磁盘空间不足，建议清理空间"
    fi
}

# 检查内存使用
check_memory() {
    log_info "检查内存使用情况..."
    
    # 检查Docker容器内存使用
    docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}" | grep -E "(octi-|postgres|redis)" || true
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    local report_file="deployment-verification-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "OCTI智能评估系统 - 部署验证报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "容器状态:"
        docker-compose ps
        echo ""
        
        echo "健康检查结果:"
        curl -s http://localhost:3000/api/health | jq '.' 2>/dev/null || echo "健康检查API不可用"
        echo ""
        
        echo "系统资源使用:"
        docker stats --no-stream
        echo ""
        
    } > "$report_file"
    
    log_success "验证报告已生成: $report_file"
}

# 主验证函数
main() {
    log_info "开始验证OCTI智能评估系统部署..."
    echo ""
    
    local failed_checks=0
    
    # 执行各项检查
    check_environment || ((failed_checks++))
    check_containers "$1" || ((failed_checks++))
    check_network || ((failed_checks++))
    check_database || ((failed_checks++))
    check_redis || ((failed_checks++))
    check_app_health || ((failed_checks++))
    check_api_endpoints
    check_database_tables
    check_disk_space
    check_memory
    
    echo ""
    
    if [ $failed_checks -eq 0 ]; then
        log_success "所有关键检查通过！部署验证成功 ✅"
        generate_report
        return 0
    else
        log_error "有 $failed_checks 项检查失败，请检查部署配置 ❌"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "OCTI智能评估系统 - 部署验证脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev     验证开发环境部署"
    echo "  prod    验证生产环境部署（默认）"
    echo "  help    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 验证开发环境"
    echo "  $0 prod     # 验证生产环境"
    echo "  $0          # 默认验证生产环境"
}

# 处理命令行参数
case "${1:-prod}" in
    "dev")
        main "dev"
        ;;
    "prod")
        main "prod"
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
