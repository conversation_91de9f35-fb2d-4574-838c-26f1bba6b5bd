"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/assessment/simple-analyze/route";
exports.ids = ["app/api/assessment/simple-analyze/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&page=%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fsimple-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&page=%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fsimple-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_assessment_simple_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/assessment/simple-analyze/route.ts */ \"(rsc)/./src/app/api/assessment/simple-analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/assessment/simple-analyze/route\",\n        pathname: \"/api/assessment/simple-analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/assessment/simple-analyze/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/simple-analyze/route.ts\",\n    nextConfigOutput,\n    userland: _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_assessment_simple_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/assessment/simple-analyze/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&page=%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fsimple-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/assessment/simple-analyze/route.ts":
/*!********************************************************!*\
  !*** ./src/app/api/assessment/simple-analyze/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_analysis_simple_analysis_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/analysis/simple-analysis-service */ \"(rsc)/./src/services/analysis/simple-analysis-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/**\n * 简化的OCTI分析API路由\n * 按照成功案例模式：简单直接，避免复杂处理\n */ \n\n\n// 请求数据验证schema\nconst AnalysisRequestSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    profile: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        organizationType: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        serviceArea: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        resourceStructure: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        developmentStage: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        teamSize: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        operatingModel: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        impactScope: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        organizationCulture: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        challengesPriorities: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        futureVision: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n    }),\n    responses: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        questionId: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        answer: zod__WEBPACK_IMPORTED_MODULE_2__.union([\n            zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n            zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n            zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string())\n        ])\n    })),\n    version: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n        \"standard\",\n        \"professional\"\n    ]).default(\"standard\")\n});\nasync function POST(request) {\n    try {\n        console.log(\"\\uD83D\\uDCE5 收到简化分析请求\");\n        // 解析请求数据\n        const body = await request.json();\n        // 验证数据格式\n        const validatedData = AnalysisRequestSchema.parse(body);\n        console.log(\"\\uD83D\\uDCCA 数据验证通过\");\n        console.log(\"\\uD83D\\uDC64 组织画像:\", validatedData.profile);\n        console.log(\"\\uD83D\\uDCDD 回答数量:\", validatedData.responses.length);\n        console.log(\"\\uD83C\\uDFAF 分析版本:\", validatedData.version);\n        // 执行分析\n        const analysisService = new _services_analysis_simple_analysis_service__WEBPACK_IMPORTED_MODULE_1__.SimpleAnalysisService();\n        const result = await analysisService.analyzeOrganization(validatedData);\n        if (result.success) {\n            console.log(\"✅ 简化分析完成\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n        } else {\n            console.error(\"❌ 分析失败:\", result.error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ API处理失败:\", error);\n        // 返回友好的错误信息\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"请求数据格式不正确\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"服务器内部错误\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"OCTI简化分析API\",\n        version: \"1.0.0\",\n        endpoints: {\n            POST: \"/api/assessment/simple-analyze - 执行OCTI组织分析\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/assessment/simple-analyze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/octi-type-classifier.ts":
/*!*******************************************************!*\
  !*** ./src/services/analysis/octi-type-classifier.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OCTITypeClassifier: () => (/* binding */ OCTITypeClassifier)\n/* harmony export */ });\n/**\n * OCTI四维八极组织类型分类器\n * 基于问卷结果快速判断组织类型，无需复杂的LLM调用\n */ /**\n * OCTI组织类型定义\n */ const OCTI_TYPES = {\n    \"SFMV\": {\n        code: \"SFMV\",\n        name: \"战略价值型\",\n        description: \"具有清晰战略定位和强烈价值导向的组织，在使命驱动和专业聚焦方面表现突出。\",\n        characteristics: [\n            \"使命陈述清晰明确\",\n            \"价值观深入人心\",\n            \"专业领域聚焦\",\n            \"社会影响力导向\"\n        ],\n        strengths: [\n            \"战略方向明确\",\n            \"团队价值认同度高\",\n            \"专业能力突出\",\n            \"社会公信力强\"\n        ],\n        challenges: [\n            \"团队协作机制需完善\",\n            \"适应性发展能力待提升\",\n            \"资源整合效率可优化\"\n        ]\n    },\n    \"ITAD\": {\n        code: \"ITAD\",\n        name: \"协同发展型\",\n        description: \"注重团队协作和能力建设的组织，在内部管理和学习发展方面具有优势。\",\n        characteristics: [\n            \"团队协作机制完善\",\n            \"学习发展氛围浓厚\",\n            \"内部沟通顺畅\",\n            \"适应变化能力强\"\n        ],\n        strengths: [\n            \"团队凝聚力强\",\n            \"组织学习能力突出\",\n            \"变革适应性好\",\n            \"内部管理规范\"\n        ],\n        challenges: [\n            \"战略聚焦度需加强\",\n            \"价值传播影响力待提升\",\n            \"外部资源整合能力需完善\"\n        ]\n    },\n    \"SFAD\": {\n        code: \"SFAD\",\n        name: \"战略发展型\",\n        description: \"战略清晰且具备强适应能力的组织，能够在变化中保持发展方向。\",\n        characteristics: [\n            \"战略规划完善\",\n            \"发展目标明确\",\n            \"创新能力较强\",\n            \"资源配置合理\"\n        ],\n        strengths: [\n            \"长期发展规划清晰\",\n            \"创新发展能力强\",\n            \"资源利用效率高\",\n            \"市场适应性好\"\n        ],\n        challenges: [\n            \"团队协同效率需提升\",\n            \"价值文化建设待加强\",\n            \"利益相关者参与度可提高\"\n        ]\n    },\n    \"MVIT\": {\n        code: \"MVIT\",\n        name: \"价值协同型\",\n        description: \"价值导向明确且团队协作良好的组织，注重文化建设和团队凝聚。\",\n        characteristics: [\n            \"组织文化浓厚\",\n            \"价值观统一\",\n            \"团队合作默契\",\n            \"志愿者参与度高\"\n        ],\n        strengths: [\n            \"组织文化凝聚力强\",\n            \"团队协作效率高\",\n            \"价值传播能力突出\",\n            \"社会认同度高\"\n        ],\n        challenges: [\n            \"战略执行力需加强\",\n            \"创新发展能力待提升\",\n            \"专业技能建设需完善\"\n        ]\n    },\n    \"BALANCED\": {\n        code: \"BALANCED\",\n        name: \"均衡发展型\",\n        description: \"四个维度发展相对均衡的组织，具有全面但不突出的特征。\",\n        characteristics: [\n            \"各维度发展均衡\",\n            \"综合能力较强\",\n            \"发展潜力较大\",\n            \"适应性较好\"\n        ],\n        strengths: [\n            \"综合发展能力强\",\n            \"各方面基础扎实\",\n            \"发展潜力大\",\n            \"风险抵御能力较强\"\n        ],\n        challenges: [\n            \"缺乏突出优势\",\n            \"特色不够鲜明\",\n            \"需要找到发展重点\",\n            \"资源分散风险\"\n        ]\n    }\n};\n/**\n * 基于问卷结果分类OCTI组织类型\n */ class OCTITypeClassifier {\n    /**\n   * 根据问卷结果判断组织类型\n   */ static classifyOrganizationType(responses) {\n        const scores = this.calculateDimensionScores(responses);\n        const typeCode = this.determineTypeCode(scores);\n        return OCTI_TYPES[typeCode] || OCTI_TYPES[\"BALANCED\"];\n    }\n    /**\n   * 计算四个维度的得分\n   */ static calculateDimensionScores(responses) {\n        const scores = {\n            SF: 0,\n            IT: 0,\n            MV: 0,\n            AD: 0\n        };\n        const counts = {\n            SF: 0,\n            IT: 0,\n            MV: 0,\n            AD: 0\n        };\n        responses.forEach((response)=>{\n            const dimension = this.getDimensionFromQuestionId(response.questionId);\n            if (dimension) {\n                const score = this.convertAnswerToScore(response.answer);\n                scores[dimension] += score;\n                counts[dimension]++;\n            }\n        });\n        // 计算平均分\n        Object.keys(scores).forEach((dim)=>{\n            const dimension = dim;\n            if (counts[dimension] > 0) {\n                scores[dimension] = scores[dimension] / counts[dimension];\n            } else {\n                scores[dimension] = 3; // 默认中等分数\n            }\n        });\n        return scores;\n    }\n    /**\n   * 从问题ID判断维度\n   */ static getDimensionFromQuestionId(questionId) {\n        if (questionId.startsWith(\"SF_\")) return \"SF\";\n        if (questionId.startsWith(\"IT_\")) return \"IT\";\n        if (questionId.startsWith(\"MV_\")) return \"MV\";\n        if (questionId.startsWith(\"AD_\")) return \"AD\";\n        return null;\n    }\n    /**\n   * 将答案转换为分数\n   */ static convertAnswerToScore(answer) {\n        if (typeof answer === \"number\") {\n            return Math.max(1, Math.min(5, answer));\n        }\n        if (typeof answer === \"string\") {\n            const lowerAnswer = answer.toLowerCase();\n            if (lowerAnswer.includes(\"excellent\") || lowerAnswer.includes(\"very_clear\")) return 5;\n            if (lowerAnswer.includes(\"good\") || lowerAnswer.includes(\"clear\")) return 4;\n            if (lowerAnswer.includes(\"average\") || lowerAnswer.includes(\"moderate\")) return 3;\n            if (lowerAnswer.includes(\"poor\") || lowerAnswer.includes(\"unclear\")) return 2;\n            if (lowerAnswer.includes(\"very_poor\") || lowerAnswer.includes(\"very_unclear\")) return 1;\n        }\n        if (Array.isArray(answer)) {\n            // 多选题按选择数量评分\n            return Math.min(5, Math.max(1, answer.length));\n        }\n        return 3; // 默认中等分数\n    }\n    /**\n   * 根据维度得分确定组织类型代码\n   */ static determineTypeCode(scores) {\n        const threshold = 3.5; // 高分阈值\n        const highDimensions = [];\n        if (scores.SF >= threshold) highDimensions.push(\"SF\");\n        if (scores.IT >= threshold) highDimensions.push(\"IT\");\n        if (scores.MV >= threshold) highDimensions.push(\"MV\");\n        if (scores.AD >= threshold) highDimensions.push(\"AD\");\n        // 根据高分维度组合确定类型\n        if (highDimensions.includes(\"SF\") && highDimensions.includes(\"MV\")) {\n            return \"SFMV\";\n        }\n        if (highDimensions.includes(\"IT\") && highDimensions.includes(\"AD\")) {\n            return \"ITAD\";\n        }\n        if (highDimensions.includes(\"SF\") && highDimensions.includes(\"AD\")) {\n            return \"SFAD\";\n        }\n        if (highDimensions.includes(\"MV\") && highDimensions.includes(\"IT\")) {\n            return \"MVIT\";\n        }\n        return \"BALANCED\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/octi-type-classifier.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/simple-analysis-service.ts":
/*!**********************************************************!*\
  !*** ./src/services/analysis/simple-analysis-service.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAnalysisService: () => (/* binding */ SimpleAnalysisService)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _octi_type_classifier__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./octi-type-classifier */ \"(rsc)/./src/services/analysis/octi-type-classifier.ts\");\n/* harmony import */ var _llm_llm_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../llm/llm-client */ \"(rsc)/./src/services/llm/llm-client.ts\");\n/**\n * 简化的OCTI分析服务\n * 按照成功案例的模式：简单直接，避免复杂的JSON解析\n */ \n\n\n\n/**\n * 简化的OCTI分析服务\n */ class SimpleAnalysisService {\n    constructor(){\n        this.llmClient = new _llm_llm_client__WEBPACK_IMPORTED_MODULE_3__.LLMClient();\n        this.loadPromptConfig();\n    }\n    /**\n   * 加载提示词配置 - 学习成功案例的模式\n   */ loadPromptConfig() {\n        try {\n            const configPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"configs\", \"octi_analysis_prompt.json\");\n            const configContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(configPath, \"utf-8\");\n            this.promptConfig = JSON.parse(configContent);\n            console.log(\"✅ OCTI分析提示词配置加载成功\");\n        } catch (error) {\n            console.error(\"❌ 加载OCTI分析提示词配置失败:\", error);\n            throw new Error(\"无法加载OCTI分析配置\");\n        }\n    }\n    /**\n   * 执行OCTI分析 - 分为两个部分\n   */ async analyzeOrganization(request) {\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始OCTI简化分析...\");\n            // 第一部分：快速组织类型判断\n            const organizationType = _octi_type_classifier__WEBPACK_IMPORTED_MODULE_2__.OCTITypeClassifier.classifyOrganizationType(request.responses);\n            console.log(`📊 组织类型判断完成: ${organizationType.code} - ${organizationType.name}`);\n            // 第二部分：LLM详细分析\n            const detailedAnalysis = await this.generateDetailedAnalysis(request);\n            console.log(\"\\uD83E\\uDD16 LLM详细分析完成\");\n            return {\n                success: true,\n                data: {\n                    organizationType,\n                    detailedAnalysis,\n                    timestamp: new Date().toISOString(),\n                    version: request.version\n                }\n            };\n        } catch (error) {\n            console.error(\"❌ OCTI分析失败:\", error);\n            // 容错处理：如果LLM失败，至少返回组织类型判断\n            try {\n                const organizationType = _octi_type_classifier__WEBPACK_IMPORTED_MODULE_2__.OCTITypeClassifier.classifyOrganizationType(request.responses);\n                return {\n                    success: true,\n                    data: {\n                        organizationType,\n                        detailedAnalysis: this.generateFallbackAnalysis(organizationType, request),\n                        timestamp: new Date().toISOString(),\n                        version: request.version\n                    }\n                };\n            } catch (fallbackError) {\n                return {\n                    success: false,\n                    error: `分析失败: ${error instanceof Error ? error.message : \"未知错误\"}`,\n                    data: {\n                        organizationType: {\n                            code: \"UNKNOWN\",\n                            name: \"未知类型\",\n                            description: \"分析过程中出现错误，无法确定组织类型\",\n                            characteristics: [],\n                            strengths: [],\n                            challenges: []\n                        },\n                        detailedAnalysis: \"抱歉，分析过程中出现错误，请稍后重试。\",\n                        timestamp: new Date().toISOString(),\n                        version: request.version\n                    }\n                };\n            }\n        }\n    }\n    /**\n   * 生成LLM详细分析 - 学习成功案例的简单模式\n   */ async generateDetailedAnalysis(request) {\n        // 构建系统提示词 - 直接使用JSON配置\n        const systemPrompt = JSON.stringify(this.promptConfig, null, 2);\n        // 构建用户提示词\n        const userPrompt = this.buildUserPrompt(request);\n        console.log(\"\\uD83D\\uDCCF 提示词长度:\", systemPrompt.length + userPrompt.length, \"字符\");\n        // 调用LLM - 简单直接，不强制JSON格式\n        const rawResponse = await this.llmClient.call({\n            model: \"minimax-M1\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7\n        });\n        // 简单的文本清理 - 移除markdown符号\n        return this.cleanAnalysisText(rawResponse);\n    }\n    /**\n   * 构建用户提示词\n   */ buildUserPrompt(request) {\n        const { profile, responses } = request;\n        // 格式化组织画像\n        const organizationProfile = this.formatOrganizationProfile(profile);\n        // 格式化问卷结果\n        const questionnaireResults = this.formatQuestionnaireResults(responses);\n        // 使用模板\n        return this.promptConfig.promptTemplate.replace(\"{organizationProfile}\", organizationProfile).replace(\"{questionnaireResults}\", questionnaireResults);\n    }\n    /**\n   * 格式化组织画像\n   */ formatOrganizationProfile(profile) {\n        return `\n组织类型: ${profile.organizationType || \"未知\"}\n服务领域: ${profile.serviceArea || \"未知\"}\n发展阶段: ${profile.developmentStage || \"未知\"}\n团队规模: ${profile.teamSize || \"未知\"}\n运营模式: ${profile.operatingModel || \"未知\"}\n影响范围: ${profile.impactScope || \"未知\"}\n组织文化: ${profile.organizationCulture || \"未知\"}\n主要挑战: ${profile.challengesPriorities || \"未知\"}\n未来愿景: ${profile.futureVision || \"未知\"}\n    `.trim();\n    }\n    /**\n   * 格式化问卷结果\n   */ formatQuestionnaireResults(responses) {\n        return responses.map((response, index)=>{\n            const answer = Array.isArray(response.answer) ? response.answer.join(\", \") : response.answer;\n            return `${index + 1}. ${response.questionId}: ${answer}`;\n        }).join(\"\\n\");\n    }\n    /**\n   * 清理分析文本 - 移除markdown符号和调试信息\n   */ cleanAnalysisText(text) {\n        return text// 移除调试信息\n        .replace(/\\([A-Z]{2,}_[PI]\\d+\\s*[=:]\\s*[^)]+\\)/g, \"\") // 移除 (IT_I002: 2分低) 类型\n        .replace(/[A-Z]{2,}_[PI]\\d+\\s*[=:]\\s*[^,，\\s\\n)]+/g, \"\") // 移除 IT_I002: 2分低 类型\n        .replace(/[A-Z]{2,}_[PI]\\d+\\s*:\\s*\\d+分[低中高等]/g, \"\") // 移除 \"IT_I002: 2分低\" 类型\n        .replace(/\\d+分[低中高等]/g, \"\") // 移除 \"2分低\"、\"4分中等\" 类型\n        .replace(/[A-Z]{2,}_[A-Z]\\d+\\s*:\\s*\\d+分[低中高等]/g, \"\") // 移除完整的评分调试信息\n        .replace(/评分[：:]\\s*\\d+分/g, \"\") // 移除 \"评分：4分\" 类型\n        .replace(/得分[：:]\\s*\\d+/g, \"\") // 移除 \"得分：4\" 类型\n        .replace(/[A-Z]{2,}_[PI]\\d+/g, \"\") // 移除单独的调试代码\n        // 移除markdown符号\n        .replace(/\\*\\*/g, \"\") // 移除粗体标记\n        .replace(/#{1,6}\\s*/g, \"\") // 移除标题标记\n        .replace(/\\*/g, \"\") // 移除斜体标记\n        .replace(/`{1,3}/g, \"\") // 移除代码标记\n        .replace(/^\\s*[-*+]\\s*/gm, \"\") // 移除列表标记\n        .replace(/\\n{3,}/g, \"\\n\\n\") // 合并多余的换行\n        .trim();\n    }\n    /**\n   * 生成备用分析 - 当LLM失败时使用\n   */ generateFallbackAnalysis(organizationType, request) {\n        return `\n1. 组织能力概览\n\n根据问卷评估结果，您的组织属于\"${organizationType.name}\"类型。${organizationType.description}\n\n2. 组织特征分析\n\n主要特征：\n${organizationType.characteristics.map((char, index)=>`${index + 1}. ${char}`).join(\"\\n\")}\n\n3. 核心优势\n\n${organizationType.strengths.map((strength, index)=>`${index + 1}. ${strength}`).join(\"\\n\")}\n\n4. 发展挑战\n\n${organizationType.challenges.map((challenge, index)=>`${index + 1}. ${challenge}`).join(\"\\n\")}\n\n5. 发展建议\n\n基于您组织的类型特征，建议重点关注以下方面的发展：\n\n1. 继续发挥现有优势，巩固核心竞争力\n2. 针对识别出的挑战制定具体的改进计划\n3. 加强团队能力建设，提升整体执行力\n4. 建立定期的组织能力评估机制，持续改进\n\n注：本分析基于基础评估模型生成，如需更详细的分析建议，请稍后重试或联系专业顾问。\n    `.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvYW5hbHlzaXMvc2ltcGxlLWFuYWx5c2lzLXNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVtQjtBQUNJO0FBQ29EO0FBQzlCO0FBd0I5Qzs7Q0FFQyxHQUNNLE1BQU1JO0lBSVhDLGFBQWM7UUFDWixJQUFJLENBQUNDLFNBQVMsR0FBRyxJQUFJSCxzREFBU0E7UUFDOUIsSUFBSSxDQUFDSSxnQkFBZ0I7SUFDdkI7SUFFQTs7R0FFQyxHQUNELG1CQUFpQztRQUMvQixJQUFJO1lBQ0YsTUFBTUMsYUFBYVAsZ0RBQVMsQ0FBQ1MsUUFBUUMsR0FBRyxJQUFJLFdBQVc7WUFDdkQsTUFBTUMsZ0JBQWdCWixzREFBZSxDQUFDUSxZQUFZO1lBQ2xELElBQUksQ0FBQ00sWUFBWSxHQUFHQyxLQUFLQyxLQUFLLENBQUNKO1lBQy9CSyxRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9DLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLHNCQUFzQkE7WUFDcEMsTUFBTSxJQUFJQyxNQUFNO1FBQ2xCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1DLG9CQUFvQkMsT0FBd0IsRUFBaUM7UUFDakYsSUFBSTtZQUNGTCxRQUFRQyxHQUFHLENBQUM7WUFFWixnQkFBZ0I7WUFDaEIsTUFBTUssbUJBQW1CckIscUVBQWtCQSxDQUFDc0Isd0JBQXdCLENBQUNGLFFBQVFHLFNBQVM7WUFDdEZSLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGFBQWEsRUFBRUssaUJBQWlCRyxJQUFJLENBQUMsR0FBRyxFQUFFSCxpQkFBaUJJLElBQUksQ0FBQyxDQUFDO1lBRTlFLGVBQWU7WUFDZixNQUFNQyxtQkFBbUIsTUFBTSxJQUFJLENBQUNDLHdCQUF3QixDQUFDUDtZQUM3REwsUUFBUUMsR0FBRyxDQUFDO1lBRVosT0FBTztnQkFDTFksU0FBUztnQkFDVEMsTUFBTTtvQkFDSlI7b0JBQ0FLO29CQUNBSSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7b0JBQ2pDQyxTQUFTYixRQUFRYSxPQUFPO2dCQUMxQjtZQUNGO1FBRUYsRUFBRSxPQUFPaEIsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsZUFBZUE7WUFFN0IsMEJBQTBCO1lBQzFCLElBQUk7Z0JBQ0YsTUFBTUksbUJBQW1CckIscUVBQWtCQSxDQUFDc0Isd0JBQXdCLENBQUNGLFFBQVFHLFNBQVM7Z0JBQ3RGLE9BQU87b0JBQ0xLLFNBQVM7b0JBQ1RDLE1BQU07d0JBQ0pSO3dCQUNBSyxrQkFBa0IsSUFBSSxDQUFDUSx3QkFBd0IsQ0FBQ2Isa0JBQWtCRDt3QkFDbEVVLFdBQVcsSUFBSUMsT0FBT0MsV0FBVzt3QkFDakNDLFNBQVNiLFFBQVFhLE9BQU87b0JBQzFCO2dCQUNGO1lBQ0YsRUFBRSxPQUFPRSxlQUFlO2dCQUN0QixPQUFPO29CQUNMUCxTQUFTO29CQUNUWCxPQUFPLENBQUMsTUFBTSxFQUFFQSxpQkFBaUJDLFFBQVFELE1BQU1tQixPQUFPLEdBQUcsT0FBTyxDQUFDO29CQUNqRVAsTUFBTTt3QkFDSlIsa0JBQWtCOzRCQUNoQkcsTUFBTTs0QkFDTkMsTUFBTTs0QkFDTlksYUFBYTs0QkFDYkMsaUJBQWlCLEVBQUU7NEJBQ25CQyxXQUFXLEVBQUU7NEJBQ2JDLFlBQVksRUFBRTt3QkFDaEI7d0JBQ0FkLGtCQUFrQjt3QkFDbEJJLFdBQVcsSUFBSUMsT0FBT0MsV0FBVzt3QkFDakNDLFNBQVNiLFFBQVFhLE9BQU87b0JBQzFCO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjTix5QkFBeUJQLE9BQXdCLEVBQW1CO1FBQ2hGLHVCQUF1QjtRQUN2QixNQUFNcUIsZUFBZTVCLEtBQUs2QixTQUFTLENBQUMsSUFBSSxDQUFDOUIsWUFBWSxFQUFFLE1BQU07UUFFN0QsVUFBVTtRQUNWLE1BQU0rQixhQUFhLElBQUksQ0FBQ0MsZUFBZSxDQUFDeEI7UUFFeENMLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYXlCLGFBQWFJLE1BQU0sR0FBR0YsV0FBV0UsTUFBTSxFQUFFO1FBRWxFLHlCQUF5QjtRQUN6QixNQUFNQyxjQUFjLE1BQU0sSUFBSSxDQUFDMUMsU0FBUyxDQUFDMkMsSUFBSSxDQUFDO1lBQzVDQyxPQUFPO1lBQ1BDLFVBQVU7Z0JBQ1I7b0JBQUVDLE1BQU07b0JBQVVDLFNBQVNWO2dCQUFhO2dCQUN4QztvQkFBRVMsTUFBTTtvQkFBUUMsU0FBU1I7Z0JBQVc7YUFDckM7WUFDRFMsYUFBYTtRQUNmO1FBRUEseUJBQXlCO1FBQ3pCLE9BQU8sSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ1A7SUFDaEM7SUFFQTs7R0FFQyxHQUNELGdCQUF3QjFCLE9BQXdCLEVBQVU7UUFDeEQsTUFBTSxFQUFFa0MsT0FBTyxFQUFFL0IsU0FBUyxFQUFFLEdBQUdIO1FBRS9CLFVBQVU7UUFDVixNQUFNbUMsc0JBQXNCLElBQUksQ0FBQ0MseUJBQXlCLENBQUNGO1FBRTNELFVBQVU7UUFDVixNQUFNRyx1QkFBdUIsSUFBSSxDQUFDQywwQkFBMEIsQ0FBQ25DO1FBRTdELE9BQU87UUFDUCxPQUFPLElBQUksQ0FBQ1gsWUFBWSxDQUFDK0MsY0FBYyxDQUNwQ0MsT0FBTyxDQUFDLHlCQUF5QkwscUJBQ2pDSyxPQUFPLENBQUMsMEJBQTBCSDtJQUN2QztJQUVBOztHQUVDLEdBQ0QsMEJBQWtDSCxPQUFZLEVBQVU7UUFDdEQsT0FBTyxDQUFDO01BQ04sRUFBRUEsUUFBUWpDLGdCQUFnQixJQUFJLEtBQUs7TUFDbkMsRUFBRWlDLFFBQVFPLFdBQVcsSUFBSSxLQUFLO01BQzlCLEVBQUVQLFFBQVFRLGdCQUFnQixJQUFJLEtBQUs7TUFDbkMsRUFBRVIsUUFBUVMsUUFBUSxJQUFJLEtBQUs7TUFDM0IsRUFBRVQsUUFBUVUsY0FBYyxJQUFJLEtBQUs7TUFDakMsRUFBRVYsUUFBUVcsV0FBVyxJQUFJLEtBQUs7TUFDOUIsRUFBRVgsUUFBUVksbUJBQW1CLElBQUksS0FBSztNQUN0QyxFQUFFWixRQUFRYSxvQkFBb0IsSUFBSSxLQUFLO01BQ3ZDLEVBQUViLFFBQVFjLFlBQVksSUFBSSxLQUFLO0lBQ2pDLENBQUMsQ0FBQ0MsSUFBSTtJQUNSO0lBRUE7O0dBRUMsR0FDRCwyQkFBbUM5QyxTQUFnQixFQUFVO1FBQzNELE9BQU9BLFVBQVUrQyxHQUFHLENBQUMsQ0FBQ0MsVUFBVUM7WUFDOUIsTUFBTUMsU0FBU0MsTUFBTUMsT0FBTyxDQUFDSixTQUFTRSxNQUFNLElBQ3hDRixTQUFTRSxNQUFNLENBQUNsRSxJQUFJLENBQUMsUUFDckJnRSxTQUFTRSxNQUFNO1lBQ25CLE9BQU8sQ0FBQyxFQUFFRCxRQUFRLEVBQUUsRUFBRSxFQUFFRCxTQUFTSyxVQUFVLENBQUMsRUFBRSxFQUFFSCxPQUFPLENBQUM7UUFDMUQsR0FBR2xFLElBQUksQ0FBQztJQUNWO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEJzRSxJQUFZLEVBQVU7UUFDOUMsT0FBT0EsSUFDTCxTQUFTO1NBQ1JqQixPQUFPLENBQUMseUNBQXlDLElBQUksdUJBQXVCO1NBQzVFQSxPQUFPLENBQUMsMkNBQTJDLElBQUkscUJBQXFCO1NBQzVFQSxPQUFPLENBQUMsdUNBQXVDLElBQUksdUJBQXVCO1NBQzFFQSxPQUFPLENBQUMsZUFBZSxJQUFJLHFCQUFxQjtTQUNoREEsT0FBTyxDQUFDLHdDQUF3QyxJQUFJLGNBQWM7U0FDbEVBLE9BQU8sQ0FBQyxrQkFBa0IsSUFBSSxnQkFBZ0I7U0FDOUNBLE9BQU8sQ0FBQyxpQkFBaUIsSUFBSSxlQUFlO1NBQzVDQSxPQUFPLENBQUMsc0JBQXNCLElBQUksWUFBWTtRQUUvQyxlQUFlO1NBQ2RBLE9BQU8sQ0FBQyxTQUFTLElBQUksU0FBUztTQUM5QkEsT0FBTyxDQUFDLGNBQWMsSUFBSSxTQUFTO1NBQ25DQSxPQUFPLENBQUMsT0FBTyxJQUFJLFNBQVM7U0FDNUJBLE9BQU8sQ0FBQyxXQUFXLElBQUksU0FBUztTQUNoQ0EsT0FBTyxDQUFDLGtCQUFrQixJQUFJLFNBQVM7U0FDdkNBLE9BQU8sQ0FBQyxXQUFXLFFBQVEsVUFBVTtTQUNyQ1MsSUFBSTtJQUNUO0lBRUE7O0dBRUMsR0FDRCx5QkFBaUNoRCxnQkFBZ0MsRUFBRUQsT0FBd0IsRUFBVTtRQUNuRyxPQUFPLENBQUM7OztnQkFHSSxFQUFFQyxpQkFBaUJJLElBQUksQ0FBQyxJQUFJLEVBQUVKLGlCQUFpQmdCLFdBQVcsQ0FBQzs7Ozs7QUFLM0UsRUFBRWhCLGlCQUFpQmlCLGVBQWUsQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDUSxNQUFNTixRQUFVLENBQUMsRUFBRUEsUUFBUSxFQUFFLEVBQUUsRUFBRU0sS0FBSyxDQUFDLEVBQUV2RSxJQUFJLENBQUMsTUFBTTs7OztBQUk1RixFQUFFYyxpQkFBaUJrQixTQUFTLENBQUMrQixHQUFHLENBQUMsQ0FBQ1MsVUFBVVAsUUFBVSxDQUFDLEVBQUVBLFFBQVEsRUFBRSxFQUFFLEVBQUVPLFNBQVMsQ0FBQyxFQUFFeEUsSUFBSSxDQUFDLE1BQU07Ozs7QUFJOUYsRUFBRWMsaUJBQWlCbUIsVUFBVSxDQUFDOEIsR0FBRyxDQUFDLENBQUNVLFdBQVdSLFFBQVUsQ0FBQyxFQUFFQSxRQUFRLEVBQUUsRUFBRSxFQUFFUSxVQUFVLENBQUMsRUFBRXpFLElBQUksQ0FBQyxNQUFNOzs7Ozs7Ozs7Ozs7SUFZN0YsQ0FBQyxDQUFDOEQsSUFBSTtJQUNSO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vc3JjL3NlcnZpY2VzL2FuYWx5c2lzL3NpbXBsZS1hbmFseXNpcy1zZXJ2aWNlLnRzPzRjYTkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiDnroDljJbnmoRPQ1RJ5YiG5p6Q5pyN5YqhXG4gKiDmjInnhafmiJDlip/moYjkvovnmoTmqKHlvI/vvJrnroDljZXnm7TmjqXvvIzpgb/lhY3lpI3mnYLnmoRKU09O6Kej5p6QXG4gKi9cblxuaW1wb3J0IGZzIGZyb20gJ2ZzJztcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnO1xuaW1wb3J0IHsgT0NUSVR5cGVDbGFzc2lmaWVyLCBPQ1RJVHlwZVJlc3VsdCB9IGZyb20gJy4vb2N0aS10eXBlLWNsYXNzaWZpZXInO1xuaW1wb3J0IHsgTExNQ2xpZW50IH0gZnJvbSAnLi4vbGxtL2xsbS1jbGllbnQnO1xuXG5pbnRlcmZhY2UgQW5hbHlzaXNSZXF1ZXN0IHtcbiAgcHJvZmlsZTogYW55O1xuICByZXNwb25zZXM6IGFueVtdO1xuICB2ZXJzaW9uOiAnc3RhbmRhcmQnIHwgJ3Byb2Zlc3Npb25hbCc7XG59XG5cbmludGVyZmFjZSBTaW1wbGVBbmFseXNpc1Jlc3VsdCB7XG4gIHN1Y2Nlc3M6IGJvb2xlYW47XG4gIGRhdGE6IHtcbiAgICAvLyDnrKzkuIDpg6jliIbvvJrnu4Tnu4fnsbvlnovliKTmlq1cbiAgICBvcmdhbml6YXRpb25UeXBlOiBPQ1RJVHlwZVJlc3VsdDtcbiAgICBcbiAgICAvLyDnrKzkuozpg6jliIbvvJpMTE3or6bnu4bliIbmnpBcbiAgICBkZXRhaWxlZEFuYWx5c2lzOiBzdHJpbmc7XG4gICAgXG4gICAgLy8g5Z+656GA5L+h5oGvXG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XG4gICAgdmVyc2lvbjogc3RyaW5nO1xuICB9O1xuICBlcnJvcj86IHN0cmluZztcbn1cblxuLyoqXG4gKiDnroDljJbnmoRPQ1RJ5YiG5p6Q5pyN5YqhXG4gKi9cbmV4cG9ydCBjbGFzcyBTaW1wbGVBbmFseXNpc1NlcnZpY2Uge1xuICBwcml2YXRlIGxsbUNsaWVudDogTExNQ2xpZW50O1xuICBwcml2YXRlIHByb21wdENvbmZpZzogYW55O1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMubGxtQ2xpZW50ID0gbmV3IExMTUNsaWVudCgpO1xuICAgIHRoaXMubG9hZFByb21wdENvbmZpZygpO1xuICB9XG5cbiAgLyoqXG4gICAqIOWKoOi9veaPkOekuuivjemFjee9riAtIOWtpuS5oOaIkOWKn+ahiOS+i+eahOaooeW8j1xuICAgKi9cbiAgcHJpdmF0ZSBsb2FkUHJvbXB0Q29uZmlnKCk6IHZvaWQge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBjb25maWdQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdjb25maWdzJywgJ29jdGlfYW5hbHlzaXNfcHJvbXB0Lmpzb24nKTtcbiAgICAgIGNvbnN0IGNvbmZpZ0NvbnRlbnQgPSBmcy5yZWFkRmlsZVN5bmMoY29uZmlnUGF0aCwgJ3V0Zi04Jyk7XG4gICAgICB0aGlzLnByb21wdENvbmZpZyA9IEpTT04ucGFyc2UoY29uZmlnQ29udGVudCk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIE9DVEnliIbmnpDmj5DnpLror43phY3nva7liqDovb3miJDlip8nKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWKoOi9vU9DVEnliIbmnpDmj5DnpLror43phY3nva7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5XliqDovb1PQ1RJ5YiG5p6Q6YWN572uJyk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOaJp+ihjE9DVEnliIbmnpAgLSDliIbkuLrkuKTkuKrpg6jliIZcbiAgICovXG4gIGFzeW5jIGFuYWx5emVPcmdhbml6YXRpb24ocmVxdWVzdDogQW5hbHlzaXNSZXF1ZXN0KTogUHJvbWlzZTxTaW1wbGVBbmFseXNpc1Jlc3VsdD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSDlvIDlp4tPQ1RJ566A5YyW5YiG5p6QLi4uJyk7XG4gICAgICBcbiAgICAgIC8vIOesrOS4gOmDqOWIhu+8muW/q+mAn+e7hOe7h+exu+Wei+WIpOaWrVxuICAgICAgY29uc3Qgb3JnYW5pemF0aW9uVHlwZSA9IE9DVElUeXBlQ2xhc3NpZmllci5jbGFzc2lmeU9yZ2FuaXphdGlvblR5cGUocmVxdWVzdC5yZXNwb25zZXMpO1xuICAgICAgY29uc29sZS5sb2coYPCfk4og57uE57uH57G75Z6L5Yik5pat5a6M5oiQOiAke29yZ2FuaXphdGlvblR5cGUuY29kZX0gLSAke29yZ2FuaXphdGlvblR5cGUubmFtZX1gKTtcbiAgICAgIFxuICAgICAgLy8g56ys5LqM6YOo5YiG77yaTExN6K+m57uG5YiG5p6QXG4gICAgICBjb25zdCBkZXRhaWxlZEFuYWx5c2lzID0gYXdhaXQgdGhpcy5nZW5lcmF0ZURldGFpbGVkQW5hbHlzaXMocmVxdWVzdCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+kliBMTE3or6bnu4bliIbmnpDlrozmiJAnKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIG9yZ2FuaXphdGlvblR5cGUsXG4gICAgICAgICAgZGV0YWlsZWRBbmFseXNpcyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICB2ZXJzaW9uOiByZXF1ZXN0LnZlcnNpb25cbiAgICAgICAgfVxuICAgICAgfTtcbiAgICAgIFxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgT0NUSeWIhuaekOWksei0pTonLCBlcnJvcik7XG4gICAgICBcbiAgICAgIC8vIOWuuemUmeWkhOeQhu+8muWmguaenExMTeWksei0pe+8jOiHs+Wwkei/lOWbnue7hOe7h+exu+Wei+WIpOaWrVxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgb3JnYW5pemF0aW9uVHlwZSA9IE9DVElUeXBlQ2xhc3NpZmllci5jbGFzc2lmeU9yZ2FuaXphdGlvblR5cGUocmVxdWVzdC5yZXNwb25zZXMpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgb3JnYW5pemF0aW9uVHlwZSxcbiAgICAgICAgICAgIGRldGFpbGVkQW5hbHlzaXM6IHRoaXMuZ2VuZXJhdGVGYWxsYmFja0FuYWx5c2lzKG9yZ2FuaXphdGlvblR5cGUsIHJlcXVlc3QpLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICB2ZXJzaW9uOiByZXF1ZXN0LnZlcnNpb25cbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICB9IGNhdGNoIChmYWxsYmFja0Vycm9yKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IGDliIbmnpDlpLHotKU6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6ZSZ6K+vJ31gLFxuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIG9yZ2FuaXphdGlvblR5cGU6IHtcbiAgICAgICAgICAgICAgY29kZTogJ1VOS05PV04nLFxuICAgICAgICAgICAgICBuYW1lOiAn5pyq55+l57G75Z6LJyxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICfliIbmnpDov4fnqIvkuK3lh7rnjrDplJnor6/vvIzml6Dms5Xnoa7lrprnu4Tnu4fnsbvlnosnLFxuICAgICAgICAgICAgICBjaGFyYWN0ZXJpc3RpY3M6IFtdLFxuICAgICAgICAgICAgICBzdHJlbmd0aHM6IFtdLFxuICAgICAgICAgICAgICBjaGFsbGVuZ2VzOiBbXVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGRldGFpbGVkQW5hbHlzaXM6ICfmirHmrYnvvIzliIbmnpDov4fnqIvkuK3lh7rnjrDplJnor6/vvIzor7fnqI3lkI7ph43or5XjgIInLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICB2ZXJzaW9uOiByZXF1ZXN0LnZlcnNpb25cbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOeUn+aIkExMTeivpue7huWIhuaekCAtIOWtpuS5oOaIkOWKn+ahiOS+i+eahOeugOWNleaooeW8j1xuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBnZW5lcmF0ZURldGFpbGVkQW5hbHlzaXMocmVxdWVzdDogQW5hbHlzaXNSZXF1ZXN0KTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICAvLyDmnoTlu7rns7vnu5/mj5DnpLror40gLSDnm7TmjqXkvb/nlKhKU09O6YWN572uXG4gICAgY29uc3Qgc3lzdGVtUHJvbXB0ID0gSlNPTi5zdHJpbmdpZnkodGhpcy5wcm9tcHRDb25maWcsIG51bGwsIDIpO1xuICAgIFxuICAgIC8vIOaehOW7uueUqOaIt+aPkOekuuivjVxuICAgIGNvbnN0IHVzZXJQcm9tcHQgPSB0aGlzLmJ1aWxkVXNlclByb21wdChyZXF1ZXN0KTtcbiAgICBcbiAgICBjb25zb2xlLmxvZygn8J+TjyDmj5DnpLror43plb/luqY6Jywgc3lzdGVtUHJvbXB0Lmxlbmd0aCArIHVzZXJQcm9tcHQubGVuZ3RoLCAn5a2X56ymJyk7XG4gICAgXG4gICAgLy8g6LCD55SoTExNIC0g566A5Y2V55u05o6l77yM5LiN5by65Yi2SlNPTuagvOW8j1xuICAgIGNvbnN0IHJhd1Jlc3BvbnNlID0gYXdhaXQgdGhpcy5sbG1DbGllbnQuY2FsbCh7XG4gICAgICBtb2RlbDogJ21pbmltYXgtTTEnLFxuICAgICAgbWVzc2FnZXM6IFtcbiAgICAgICAgeyByb2xlOiAnc3lzdGVtJywgY29udGVudDogc3lzdGVtUHJvbXB0IH0sXG4gICAgICAgIHsgcm9sZTogJ3VzZXInLCBjb250ZW50OiB1c2VyUHJvbXB0IH1cbiAgICAgIF0sXG4gICAgICB0ZW1wZXJhdHVyZTogMC43XG4gICAgfSk7XG4gICAgXG4gICAgLy8g566A5Y2V55qE5paH5pys5riF55CGIC0g56e76ZmkbWFya2Rvd27nrKblj7dcbiAgICByZXR1cm4gdGhpcy5jbGVhbkFuYWx5c2lzVGV4dChyYXdSZXNwb25zZSk7XG4gIH1cblxuICAvKipcbiAgICog5p6E5bu655So5oi35o+Q56S66K+NXG4gICAqL1xuICBwcml2YXRlIGJ1aWxkVXNlclByb21wdChyZXF1ZXN0OiBBbmFseXNpc1JlcXVlc3QpOiBzdHJpbmcge1xuICAgIGNvbnN0IHsgcHJvZmlsZSwgcmVzcG9uc2VzIH0gPSByZXF1ZXN0O1xuICAgIFxuICAgIC8vIOagvOW8j+WMlue7hOe7h+eUu+WDj1xuICAgIGNvbnN0IG9yZ2FuaXphdGlvblByb2ZpbGUgPSB0aGlzLmZvcm1hdE9yZ2FuaXphdGlvblByb2ZpbGUocHJvZmlsZSk7XG4gICAgXG4gICAgLy8g5qC85byP5YyW6Zeu5Y2357uT5p6cXG4gICAgY29uc3QgcXVlc3Rpb25uYWlyZVJlc3VsdHMgPSB0aGlzLmZvcm1hdFF1ZXN0aW9ubmFpcmVSZXN1bHRzKHJlc3BvbnNlcyk7XG4gICAgXG4gICAgLy8g5L2/55So5qih5p2/XG4gICAgcmV0dXJuIHRoaXMucHJvbXB0Q29uZmlnLnByb21wdFRlbXBsYXRlXG4gICAgICAucmVwbGFjZSgne29yZ2FuaXphdGlvblByb2ZpbGV9Jywgb3JnYW5pemF0aW9uUHJvZmlsZSlcbiAgICAgIC5yZXBsYWNlKCd7cXVlc3Rpb25uYWlyZVJlc3VsdHN9JywgcXVlc3Rpb25uYWlyZVJlc3VsdHMpO1xuICB9XG5cbiAgLyoqXG4gICAqIOagvOW8j+WMlue7hOe7h+eUu+WDj1xuICAgKi9cbiAgcHJpdmF0ZSBmb3JtYXRPcmdhbml6YXRpb25Qcm9maWxlKHByb2ZpbGU6IGFueSk6IHN0cmluZyB7XG4gICAgcmV0dXJuIGBcbue7hOe7h+exu+WeizogJHtwcm9maWxlLm9yZ2FuaXphdGlvblR5cGUgfHwgJ+acquefpSd9XG7mnI3liqHpoobln586ICR7cHJvZmlsZS5zZXJ2aWNlQXJlYSB8fCAn5pyq55+lJ31cbuWPkeWxlemYtuautTogJHtwcm9maWxlLmRldmVsb3BtZW50U3RhZ2UgfHwgJ+acquefpSd9XG7lm6LpmJ/op4TmqKE6ICR7cHJvZmlsZS50ZWFtU2l6ZSB8fCAn5pyq55+lJ31cbui/kOiQpeaooeW8jzogJHtwcm9maWxlLm9wZXJhdGluZ01vZGVsIHx8ICfmnKrnn6UnfVxu5b2x5ZON6IyD5Zu0OiAke3Byb2ZpbGUuaW1wYWN0U2NvcGUgfHwgJ+acquefpSd9XG7nu4Tnu4fmlofljJY6ICR7cHJvZmlsZS5vcmdhbml6YXRpb25DdWx0dXJlIHx8ICfmnKrnn6UnfVxu5Li76KaB5oyR5oiYOiAke3Byb2ZpbGUuY2hhbGxlbmdlc1ByaW9yaXRpZXMgfHwgJ+acquefpSd9XG7mnKrmnaXmhL/mma86ICR7cHJvZmlsZS5mdXR1cmVWaXNpb24gfHwgJ+acquefpSd9XG4gICAgYC50cmltKCk7XG4gIH1cblxuICAvKipcbiAgICog5qC85byP5YyW6Zeu5Y2357uT5p6cXG4gICAqL1xuICBwcml2YXRlIGZvcm1hdFF1ZXN0aW9ubmFpcmVSZXN1bHRzKHJlc3BvbnNlczogYW55W10pOiBzdHJpbmcge1xuICAgIHJldHVybiByZXNwb25zZXMubWFwKChyZXNwb25zZSwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IGFuc3dlciA9IEFycmF5LmlzQXJyYXkocmVzcG9uc2UuYW5zd2VyKSBcbiAgICAgICAgPyByZXNwb25zZS5hbnN3ZXIuam9pbignLCAnKVxuICAgICAgICA6IHJlc3BvbnNlLmFuc3dlcjtcbiAgICAgIHJldHVybiBgJHtpbmRleCArIDF9LiAke3Jlc3BvbnNlLnF1ZXN0aW9uSWR9OiAke2Fuc3dlcn1gO1xuICAgIH0pLmpvaW4oJ1xcbicpO1xuICB9XG5cbiAgLyoqXG4gICAqIOa4heeQhuWIhuaekOaWh+acrCAtIOenu+mZpG1hcmtkb3du56ym5Y+35ZKM6LCD6K+V5L+h5oGvXG4gICAqL1xuICBwcml2YXRlIGNsZWFuQW5hbHlzaXNUZXh0KHRleHQ6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgcmV0dXJuIHRleHRcbiAgICAgIC8vIOenu+mZpOiwg+ivleS/oeaBr1xuICAgICAgLnJlcGxhY2UoL1xcKFtBLVpdezIsfV9bUEldXFxkK1xccypbPTpdXFxzKlteKV0rXFwpL2csICcnKSAvLyDnp7vpmaQgKElUX0kwMDI6IDLliIbkvY4pIOexu+Wei1xuICAgICAgLnJlcGxhY2UoL1tBLVpdezIsfV9bUEldXFxkK1xccypbPTpdXFxzKlteLO+8jFxcc1xcbildKy9nLCAnJykgLy8g56e76ZmkIElUX0kwMDI6IDLliIbkvY4g57G75Z6LXG4gICAgICAucmVwbGFjZSgvW0EtWl17Mix9X1tQSV1cXGQrXFxzKjpcXHMqXFxkK+WIhlvkvY7kuK3pq5jnrYldL2csICcnKSAvLyDnp7vpmaQgXCJJVF9JMDAyOiAy5YiG5L2OXCIg57G75Z6LXG4gICAgICAucmVwbGFjZSgvXFxkK+WIhlvkvY7kuK3pq5jnrYldL2csICcnKSAvLyDnp7vpmaQgXCIy5YiG5L2OXCLjgIFcIjTliIbkuK3nrYlcIiDnsbvlnotcbiAgICAgIC5yZXBsYWNlKC9bQS1aXXsyLH1fW0EtWl1cXGQrXFxzKjpcXHMqXFxkK+WIhlvkvY7kuK3pq5jnrYldL2csICcnKSAvLyDnp7vpmaTlrozmlbTnmoTor4TliIbosIPor5Xkv6Hmga9cbiAgICAgIC5yZXBsYWNlKC/or4TliIZb77yaOl1cXHMqXFxkK+WIhi9nLCAnJykgLy8g56e76ZmkIFwi6K+E5YiG77yaNOWIhlwiIOexu+Wei1xuICAgICAgLnJlcGxhY2UoL+W+l+WIhlvvvJo6XVxccypcXGQrL2csICcnKSAvLyDnp7vpmaQgXCLlvpfliIbvvJo0XCIg57G75Z6LXG4gICAgICAucmVwbGFjZSgvW0EtWl17Mix9X1tQSV1cXGQrL2csICcnKSAvLyDnp7vpmaTljZXni6znmoTosIPor5Xku6PnoIFcblxuICAgICAgLy8g56e76ZmkbWFya2Rvd27nrKblj7dcbiAgICAgIC5yZXBsYWNlKC9cXCpcXCovZywgJycpIC8vIOenu+mZpOeyl+S9k+agh+iusFxuICAgICAgLnJlcGxhY2UoLyN7MSw2fVxccyovZywgJycpIC8vIOenu+mZpOagh+mimOagh+iusFxuICAgICAgLnJlcGxhY2UoL1xcKi9nLCAnJykgLy8g56e76Zmk5pac5L2T5qCH6K6wXG4gICAgICAucmVwbGFjZSgvYHsxLDN9L2csICcnKSAvLyDnp7vpmaTku6PnoIHmoIforrBcbiAgICAgIC5yZXBsYWNlKC9eXFxzKlstKitdXFxzKi9nbSwgJycpIC8vIOenu+mZpOWIl+ihqOagh+iusFxuICAgICAgLnJlcGxhY2UoL1xcbnszLH0vZywgJ1xcblxcbicpIC8vIOWQiOW5tuWkmuS9meeahOaNouihjFxuICAgICAgLnRyaW0oKTtcbiAgfVxuXG4gIC8qKlxuICAgKiDnlJ/miJDlpIfnlKjliIbmnpAgLSDlvZNMTE3lpLHotKXml7bkvb/nlKhcbiAgICovXG4gIHByaXZhdGUgZ2VuZXJhdGVGYWxsYmFja0FuYWx5c2lzKG9yZ2FuaXphdGlvblR5cGU6IE9DVElUeXBlUmVzdWx0LCByZXF1ZXN0OiBBbmFseXNpc1JlcXVlc3QpOiBzdHJpbmcge1xuICAgIHJldHVybiBgXG4xLiDnu4Tnu4fog73lipvmpoLop4hcblxu5qC55o2u6Zeu5Y236K+E5Lyw57uT5p6c77yM5oKo55qE57uE57uH5bGe5LqOXCIke29yZ2FuaXphdGlvblR5cGUubmFtZX1cIuexu+Wei+OAgiR7b3JnYW5pemF0aW9uVHlwZS5kZXNjcmlwdGlvbn1cblxuMi4g57uE57uH54m55b6B5YiG5p6QXG5cbuS4u+imgeeJueW+ge+8mlxuJHtvcmdhbml6YXRpb25UeXBlLmNoYXJhY3RlcmlzdGljcy5tYXAoKGNoYXIsIGluZGV4KSA9PiBgJHtpbmRleCArIDF9LiAke2NoYXJ9YCkuam9pbignXFxuJyl9XG5cbjMuIOaguOW/g+S8mOWKv1xuXG4ke29yZ2FuaXphdGlvblR5cGUuc3RyZW5ndGhzLm1hcCgoc3RyZW5ndGgsIGluZGV4KSA9PiBgJHtpbmRleCArIDF9LiAke3N0cmVuZ3RofWApLmpvaW4oJ1xcbicpfVxuXG40LiDlj5HlsZXmjJHmiJhcblxuJHtvcmdhbml6YXRpb25UeXBlLmNoYWxsZW5nZXMubWFwKChjaGFsbGVuZ2UsIGluZGV4KSA9PiBgJHtpbmRleCArIDF9LiAke2NoYWxsZW5nZX1gKS5qb2luKCdcXG4nKX1cblxuNS4g5Y+R5bGV5bu66K6uXG5cbuWfuuS6juaCqOe7hOe7h+eahOexu+Wei+eJueW+ge+8jOW7uuiurumHjeeCueWFs+azqOS7peS4i+aWuemdoueahOWPkeWxle+8mlxuXG4xLiDnu6fnu63lj5HmjKXnjrDmnInkvJjlir/vvIzlt6nlm7rmoLjlv4Pnq57kuonliptcbjIuIOmSiOWvueivhuWIq+WHuueahOaMkeaImOWItuWumuWFt+S9k+eahOaUuei/m+iuoeWIklxuMy4g5Yqg5by65Zui6Zif6IO95Yqb5bu66K6+77yM5o+Q5Y2H5pW05L2T5omn6KGM5YqbXG40LiDlu7rnq4vlrprmnJ/nmoTnu4Tnu4fog73lipvor4TkvLDmnLrliLbvvIzmjIHnu63mlLnov5tcblxu5rOo77ya5pys5YiG5p6Q5Z+65LqO5Z+656GA6K+E5Lyw5qih5Z6L55Sf5oiQ77yM5aaC6ZyA5pu06K+m57uG55qE5YiG5p6Q5bu66K6u77yM6K+356iN5ZCO6YeN6K+V5oiW6IGU57O75LiT5Lia6aG+6Zeu44CCXG4gICAgYC50cmltKCk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJmcyIsInBhdGgiLCJPQ1RJVHlwZUNsYXNzaWZpZXIiLCJMTE1DbGllbnQiLCJTaW1wbGVBbmFseXNpc1NlcnZpY2UiLCJjb25zdHJ1Y3RvciIsImxsbUNsaWVudCIsImxvYWRQcm9tcHRDb25maWciLCJjb25maWdQYXRoIiwiam9pbiIsInByb2Nlc3MiLCJjd2QiLCJjb25maWdDb250ZW50IiwicmVhZEZpbGVTeW5jIiwicHJvbXB0Q29uZmlnIiwiSlNPTiIsInBhcnNlIiwiY29uc29sZSIsImxvZyIsImVycm9yIiwiRXJyb3IiLCJhbmFseXplT3JnYW5pemF0aW9uIiwicmVxdWVzdCIsIm9yZ2FuaXphdGlvblR5cGUiLCJjbGFzc2lmeU9yZ2FuaXphdGlvblR5cGUiLCJyZXNwb25zZXMiLCJjb2RlIiwibmFtZSIsImRldGFpbGVkQW5hbHlzaXMiLCJnZW5lcmF0ZURldGFpbGVkQW5hbHlzaXMiLCJzdWNjZXNzIiwiZGF0YSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInZlcnNpb24iLCJnZW5lcmF0ZUZhbGxiYWNrQW5hbHlzaXMiLCJmYWxsYmFja0Vycm9yIiwibWVzc2FnZSIsImRlc2NyaXB0aW9uIiwiY2hhcmFjdGVyaXN0aWNzIiwic3RyZW5ndGhzIiwiY2hhbGxlbmdlcyIsInN5c3RlbVByb21wdCIsInN0cmluZ2lmeSIsInVzZXJQcm9tcHQiLCJidWlsZFVzZXJQcm9tcHQiLCJsZW5ndGgiLCJyYXdSZXNwb25zZSIsImNhbGwiLCJtb2RlbCIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZW1wZXJhdHVyZSIsImNsZWFuQW5hbHlzaXNUZXh0IiwicHJvZmlsZSIsIm9yZ2FuaXphdGlvblByb2ZpbGUiLCJmb3JtYXRPcmdhbml6YXRpb25Qcm9maWxlIiwicXVlc3Rpb25uYWlyZVJlc3VsdHMiLCJmb3JtYXRRdWVzdGlvbm5haXJlUmVzdWx0cyIsInByb21wdFRlbXBsYXRlIiwicmVwbGFjZSIsInNlcnZpY2VBcmVhIiwiZGV2ZWxvcG1lbnRTdGFnZSIsInRlYW1TaXplIiwib3BlcmF0aW5nTW9kZWwiLCJpbXBhY3RTY29wZSIsIm9yZ2FuaXphdGlvbkN1bHR1cmUiLCJjaGFsbGVuZ2VzUHJpb3JpdGllcyIsImZ1dHVyZVZpc2lvbiIsInRyaW0iLCJtYXAiLCJyZXNwb25zZSIsImluZGV4IiwiYW5zd2VyIiwiQXJyYXkiLCJpc0FycmF5IiwicXVlc3Rpb25JZCIsInRleHQiLCJjaGFyIiwic3RyZW5ndGgiLCJjaGFsbGVuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/simple-analysis-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/llm-client.ts":
/*!****************************************!*\
  !*** ./src/services/llm/llm-client.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLMClient: () => (/* binding */ LLMClient),\n/* harmony export */   createLLMClient: () => (/* binding */ createLLMClient)\n/* harmony export */ });\n/**\n * OCTI智能评估系统 - LLM API客户端\n * \n * 支持多个LLM服务提供商的统一接口\n */ /**\n * LLM API客户端\n */ class LLMClient {\n    constructor(provider = \"minimax\"){\n        this.provider = provider;\n        if (provider === \"deepseek\") {\n            this.apiKey = process.env.DEEPSEEK_API_KEY || \"\";\n            this.baseUrl = \"https://api.deepseek.com/v1\";\n        } else {\n            this.apiKey = process.env.MINIMAX_API_KEY || \"\";\n            this.baseUrl = \"https://api.minimaxi.com/v1\";\n        }\n        if (!this.apiKey) {\n            throw new Error(`缺少${provider.toUpperCase()}_API_KEY环境变量`);\n        }\n    }\n    /**\n   * 调用LLM API\n   */ async call(request) {\n        try {\n            console.log(\"\\uD83E\\uDD16 调用LLM API:\", {\n                model: request.model,\n                messages: request.messages.length,\n                temperature: request.temperature\n            });\n            // 根据提供商调整请求格式\n            const requestBody = this.provider === \"minimax\" ? {\n                model: request.model || \"MiniMax-M1\",\n                messages: request.messages,\n                temperature: request.temperature || 0.7,\n                max_tokens: request.max_tokens || 12000,\n                top_p: 0.9,\n                stream: false,\n                // MiniMax不支持response_format参数，移除它\n                ...request.response_format ? {} : {}\n            } : {\n                model: request.model || \"deepseek-reasoner\",\n                messages: request.messages,\n                temperature: request.temperature || 0.3,\n                max_tokens: request.max_tokens || 8000,\n                // DeepSeek支持response_format参数\n                ...request.response_format && {\n                    response_format: request.response_format\n                }\n            };\n            // 根据提供商使用不同的端点\n            const endpoint = this.provider === \"minimax\" ? `${this.baseUrl}/text/chatcompletion_v2` : `${this.baseUrl}/chat/completions`;\n            // 创建带超时的fetch请求\n            const controller = new AbortController();\n            // 根据模型设置不同的超时时间（智能问卷增加了分析复杂度）\n            const timeout = this.provider === \"deepseek\" ? 480000 : 180000; // DeepSeek 8分钟，MiniMax 3分钟\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            try {\n                const response = await fetch(endpoint, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.apiKey}`\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`LLM API错误 (${response.status}): ${errorText}`);\n                }\n                const result = await response.json();\n                console.log(\"✅ LLM API调用成功\");\n                return result;\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error.name === \"AbortError\") {\n                    throw new Error(\"LLM API调用超时（2分钟）\");\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"❌ LLM API调用失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 解析JSON响应（带容错处理）\n   */ static parseJSONResponse(content) {\n        try {\n            // 尝试直接解析\n            return JSON.parse(content);\n        } catch (e1) {\n            try {\n                // 尝试提取JSON代码块\n                const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n                if (jsonMatch && jsonMatch[1]) {\n                    return JSON.parse(jsonMatch[1]);\n                }\n            } catch (e2) {\n                // 尝试修复常见JSON错误\n                try {\n                    let fixed = content;\n                    // 移除尾随逗号\n                    fixed = fixed.replace(/,\\s*}/g, \"}\");\n                    fixed = fixed.replace(/,\\s*]/g, \"]\");\n                    // 修复未引用的键\n                    fixed = fixed.replace(/(\\w+):/g, '\"$1\":');\n                    return JSON.parse(fixed);\n                } catch (e3) {\n                    console.error(\"JSON解析失败:\", content);\n                    throw new Error(\"无法解析LLM返回的JSON格式\");\n                }\n            }\n        }\n        // 如果所有解析方法都失败，返回null\n        return null;\n    }\n}\n/**\n * 创建LLM客户端实例\n */ function createLLMClient(provider = \"deepseek\") {\n    return new LLMClient(provider);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/llm-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&page=%2Fapi%2Fassessment%2Fsimple-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fsimple-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();