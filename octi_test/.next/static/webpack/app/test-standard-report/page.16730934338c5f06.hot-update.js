"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standard-report/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/download.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Download; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.408.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Download = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Download\", [\n    [\n        \"path\",\n        {\n            d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n            key: \"ih7n3h\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"7 10 12 15 17 10\",\n            key: \"2ggqvy\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"15\",\n            y2: \"3\",\n            key: \"1vk2je\"\n        }\n    ]\n]);\n //# sourceMappingURL=download.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/test-standard-report/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/test-standard-report/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestStandardReportPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * 标准版分析报告样式测试页面\n */ function TestStandardReportPage() {\n    var _analysisResult_dimensions, _analysisResult_recommendations;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 生成标准版分析报告\n    const generateStandardReport = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 使用模拟数据生成标准版报告\n            const mockProfile = {\n                organizationType: \"社会服务机构\",\n                serviceArea: \"教育支持\",\n                resourceStructure: \"基金会支持型\",\n                developmentStage: \"成长期\",\n                teamSize: \"中型（21-50人）\",\n                operatingModel: \"直接服务型\",\n                impactScope: \"区域影响\",\n                organizationCulture: \"使命驱动\",\n                challengesPriorities: \"资金筹集\",\n                futureVision: \"成为区域领先的教育支持机构\"\n            };\n            // 生成模拟的60道题目答案\n            const mockResponses = [];\n            for(let i = 1; i <= 60; i++){\n                mockResponses.push({\n                    questionId: \"Q\".concat(i.toString().padStart(3, \"0\")),\n                    answer: [\n                        \"A\",\n                        \"B\",\n                        \"C\",\n                        \"D\"\n                    ][Math.floor(Math.random() * 4)],\n                    confidence: Math.floor(Math.random() * 3) + 3,\n                    timeSpent: Math.floor(Math.random() * 30) + 10\n                });\n            }\n            console.log(\"\\uD83D\\uDE80 开始生成标准版分析报告...\");\n            const response = await fetch(\"/api/assessment/analyze\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profile: mockProfile,\n                    responses: mockResponses,\n                    version: \"standard\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"分析失败: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error || \"分析失败\");\n            }\n            setAnalysisResult(result.data);\n            console.log(\"✅ 标准版分析报告生成成功:\", result.data);\n        } catch (err) {\n            console.error(\"❌ 生成标准版报告失败:\", err);\n            setError(err instanceof Error ? err.message : \"未知错误\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 下载PDF报告功能\n    const handleDownloadReport = async ()=>{\n        if (!analysisResult) return;\n        setIsDownloading(true);\n        try {\n            // 生成PDF报告\n            const pdf = generatePDFReport(analysisResult);\n            // 下载PDF文件\n            const fileName = \"OCTI组织能力评估报告_\".concat(new Date().toLocaleDateString().replace(/\\//g, \"-\"), \".pdf\");\n            pdf.save(fileName);\n            console.log(\"✅ PDF报告下载成功\");\n        } catch (error) {\n            console.error(\"❌ PDF报告下载失败:\", error);\n            alert(\"报告下载失败，请重试\");\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // 生成PDF报告\n    const generatePDFReport = (result)=>{\n        const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_6__[\"default\"]();\n        const timestamp = new Date().toLocaleString();\n        let yPosition = 20;\n        // 设置字体\n        pdf.setFont(\"helvetica\");\n        // 标题\n        pdf.setFontSize(20);\n        pdf.text(\"OCTI组织能力评估报告\", 20, yPosition);\n        yPosition += 15;\n        // 分隔线\n        pdf.setLineWidth(0.5);\n        pdf.line(20, yPosition, 190, yPosition);\n        yPosition += 15;\n        // 基本信息\n        pdf.setFontSize(12);\n        pdf.text(\"生成时间: \".concat(timestamp), 20, yPosition);\n        yPosition += 10;\n        pdf.text(\"总体得分: \".concat(result.overallScore || \"N/A\"), 20, yPosition);\n        yPosition += 10;\n        pdf.text(\"能力等级: \".concat(result.level || \"N/A\"), 20, yPosition);\n        yPosition += 15;\n        // 维度分析\n        if (result.dimensions && result.dimensions.length > 0) {\n            pdf.setFontSize(14);\n            pdf.text(\"维度分析\", 20, yPosition);\n            yPosition += 10;\n            result.dimensions.forEach((dimension)=>{\n                if (yPosition > 270) {\n                    pdf.addPage();\n                    yPosition = 20;\n                }\n                pdf.setFontSize(12);\n                pdf.text(\"\".concat(dimension.name, \": \").concat(dimension.score, \" (\").concat(dimension.level, \")\"), 25, yPosition);\n                yPosition += 8;\n                if (dimension.description) {\n                    pdf.setFontSize(10);\n                    const descLines = pdf.splitTextToSize(dimension.description, 160);\n                    pdf.text(descLines, 30, yPosition);\n                    yPosition += descLines.length * 5 + 5;\n                }\n            });\n            yPosition += 10;\n        }\n        // 改进建议\n        if (result.recommendations && result.recommendations.length > 0) {\n            if (yPosition > 250) {\n                pdf.addPage();\n                yPosition = 20;\n            }\n            pdf.setFontSize(14);\n            pdf.text(\"改进建议\", 20, yPosition);\n            yPosition += 10;\n            result.recommendations.forEach((rec, index)=>{\n                if (yPosition > 270) {\n                    pdf.addPage();\n                    yPosition = 20;\n                }\n                pdf.setFontSize(12);\n                pdf.text(\"\".concat(index + 1, \". \").concat(rec.title || rec.description), 25, yPosition);\n                yPosition += 8;\n                if (rec.actions && rec.actions.length > 0) {\n                    pdf.setFontSize(10);\n                    rec.actions.forEach((action)=>{\n                        if (yPosition > 270) {\n                            pdf.addPage();\n                            yPosition = 20;\n                        }\n                        pdf.text(\"• \".concat(action), 30, yPosition);\n                        yPosition += 6;\n                    });\n                }\n                yPosition += 5;\n            });\n        }\n        // 页脚\n        const pageCount = pdf.getNumberOfPages();\n        for(let i = 1; i <= pageCount; i++){\n            pdf.setPage(i);\n            pdf.setFontSize(8);\n            pdf.text(\"报告由OCTI智能评估系统生成\", 20, 285);\n            pdf.text(\"第 \".concat(i, \" 页，共 \").concat(pageCount, \" 页\"), 170, 290);\n        }\n        return pdf;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/test-full-flow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                \"返回测试页面\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"\\uD83D\\uDCCA 标准版分析报告样式预览\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"生成并查看OCTI标准版分析报告的完整样式和内容结构\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"报告生成控制\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"点击按钮生成标准版分析报告并查看样式\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: generateStandardReport,\n                                    disabled: isLoading,\n                                    size: \"lg\",\n                                    className: \"w-full\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"生成中...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"生成标准版分析报告\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDownloadReport,\n                                    disabled: isDownloading,\n                                    size: \"lg\",\n                                    className: \"w-full\",\n                                    variant: \"outline\",\n                                    children: isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"下载中...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"下载PDF报告\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-red-800\",\n                            children: \"生成失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this),\n            analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83D\\uDCCB 分析结果概览\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: analysisResult.overallScore || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"总体得分\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-sm\",\n                                                    children: analysisResult.level || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"能力等级\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: ((_analysisResult_dimensions = analysisResult.dimensions) === null || _analysisResult_dimensions === void 0 ? void 0 : _analysisResult_dimensions.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"评估维度\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: ((_analysisResult_recommendations = analysisResult.recommendations) === null || _analysisResult_recommendations === void 0 ? void 0 : _analysisResult_recommendations.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"改进建议\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this),\n                    analysisResult.dimensions && analysisResult.dimensions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83C\\uDFAF 维度分析详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: analysisResult.dimensions.map((dimension, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: dimension.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-blue-600\",\n                                                                    children: dimension.score\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: dimension.level\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mb-3\",\n                                                    children: dimension.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 23\n                                                }, this),\n                                                dimension.strengths && dimension.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-green-700 mb-1\",\n                                                            children: \"优势\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-green-600 list-disc list-inside\",\n                                                            children: dimension.strengths.map((strength, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: strength\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 25\n                                                }, this),\n                                                dimension.improvements && dimension.improvements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-orange-700 mb-1\",\n                                                            children: \"改进点\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-orange-600 list-disc list-inside\",\n                                                            children: dimension.improvements.map((improvement, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: improvement\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, this),\n                    analysisResult.recommendations && analysisResult.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83D\\uDCA1 改进建议\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: analysisResult.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: rec.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: rec.priority === \"high\" ? \"destructive\" : rec.priority === \"medium\" ? \"default\" : \"secondary\",\n                                                            children: rec.priority === \"high\" ? \"高优先级\" : rec.priority === \"medium\" ? \"中优先级\" : \"低优先级\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mb-3\",\n                                                    children: rec.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this),\n                                                rec.actions && rec.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium mb-1\",\n                                                            children: \"具体行动\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm list-disc list-inside space-y-1\",\n                                                            children: rec.actions.map((action, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: action\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"\\uD83D\\uDD0D 原始数据结构\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"用于开发调试的完整数据结构\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-96\",\n                                    children: JSON.stringify(analysisResult, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(TestStandardReportPage, \"3l4P3ylBbAn8j/dicBkTLmmClfY=\");\n_c = TestStandardReportPage;\nvar _c;\n$RefreshReg$(_c, \"TestStandardReportPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdGVzdC1zdGFuZGFyZC1yZXBvcnQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ1E7QUFDaUQ7QUFDbkQ7QUFDd0I7QUFDekM7QUFFSDtBQUUxQjs7Q0FFQyxHQUNjLFNBQVNlO1FBb1RIQyw0QkFNQUE7O0lBelRuQixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2UsZ0JBQWdCRyxrQkFBa0IsR0FBR2xCLCtDQUFRQSxDQUFNO0lBQzFELE1BQU0sQ0FBQ21CLE9BQU9DLFNBQVMsR0FBR3BCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNxQixlQUFlQyxpQkFBaUIsR0FBR3RCLCtDQUFRQSxDQUFDO0lBRW5ELFlBQVk7SUFDWixNQUFNdUIseUJBQXlCO1FBQzdCTixhQUFhO1FBQ2JHLFNBQVM7UUFFVCxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU1JLGNBQWM7Z0JBQ2xCQyxrQkFBa0I7Z0JBQ2xCQyxhQUFhO2dCQUNiQyxtQkFBbUI7Z0JBQ25CQyxrQkFBa0I7Z0JBQ2xCQyxVQUFVO2dCQUNWQyxnQkFBZ0I7Z0JBQ2hCQyxhQUFhO2dCQUNiQyxxQkFBcUI7Z0JBQ3JCQyxzQkFBc0I7Z0JBQ3RCQyxjQUFjO1lBQ2hCO1lBRUEsZUFBZTtZQUNmLE1BQU1DLGdCQUFnQixFQUFFO1lBQ3hCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxLQUFLLElBQUlBLElBQUs7Z0JBQzVCRCxjQUFjRSxJQUFJLENBQUM7b0JBQ2pCQyxZQUFZLElBQWtDLE9BQTlCRixFQUFFRyxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO29CQUN6Q0MsUUFBUTt3QkFBQzt3QkFBSzt3QkFBSzt3QkFBSztxQkFBSSxDQUFDQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxHQUFHO29CQUMzREMsWUFBWUgsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssS0FBSztvQkFDNUNFLFdBQVdKLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLE1BQU07Z0JBQzlDO1lBQ0Y7WUFFQUcsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTUMsV0FBVyxNQUFNQyxNQUFNLDJCQUEyQjtnQkFDdERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLFNBQVNoQztvQkFDVGlDLFdBQVd0QjtvQkFDWHVCLFNBQVM7Z0JBQ1g7WUFDRjtZQUVBLElBQUksQ0FBQ1QsU0FBU1UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0sU0FBeUIsT0FBaEJYLFNBQVNZLE1BQU07WUFDMUM7WUFFQSxNQUFNQyxTQUFTLE1BQU1iLFNBQVNjLElBQUk7WUFDbEMsSUFBSSxDQUFDRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ25CLE1BQU0sSUFBSUosTUFBTUUsT0FBTzNDLEtBQUssSUFBSTtZQUNsQztZQUVBRCxrQkFBa0I0QyxPQUFPRyxJQUFJO1lBQzdCbEIsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQmMsT0FBT0csSUFBSTtRQUUzQyxFQUFFLE9BQU9DLEtBQUs7WUFDWm5CLFFBQVE1QixLQUFLLENBQUMsZ0JBQWdCK0M7WUFDOUI5QyxTQUFTOEMsZUFBZU4sUUFBUU0sSUFBSUMsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUmxELGFBQWE7UUFDZjtJQUNGO0lBRUEsWUFBWTtJQUNaLE1BQU1tRCx1QkFBdUI7UUFDM0IsSUFBSSxDQUFDckQsZ0JBQWdCO1FBRXJCTyxpQkFBaUI7UUFDakIsSUFBSTtZQUNGLFVBQVU7WUFDVixNQUFNK0MsTUFBTUMsa0JBQWtCdkQ7WUFFOUIsVUFBVTtZQUNWLE1BQU13RCxXQUFXLGdCQUFvRSxPQUFwRCxJQUFJQyxPQUFPQyxrQkFBa0IsR0FBR0MsT0FBTyxDQUFDLE9BQU8sTUFBSztZQUNyRkwsSUFBSU0sSUFBSSxDQUFDSjtZQUVUeEIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPN0IsT0FBTztZQUNkNEIsUUFBUTVCLEtBQUssQ0FBQyxnQkFBZ0JBO1lBQzlCeUQsTUFBTTtRQUNSLFNBQVU7WUFDUnRELGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU1nRCxvQkFBb0IsQ0FBQ1I7UUFDekIsTUFBTU8sTUFBTSxJQUFJeEQsNkNBQUtBO1FBQ3JCLE1BQU1nRSxZQUFZLElBQUlMLE9BQU9NLGNBQWM7UUFDM0MsSUFBSUMsWUFBWTtRQUVoQixPQUFPO1FBQ1BWLElBQUlXLE9BQU8sQ0FBQztRQUVaLEtBQUs7UUFDTFgsSUFBSVksV0FBVyxDQUFDO1FBQ2hCWixJQUFJYSxJQUFJLENBQUMsZ0JBQWdCLElBQUlIO1FBQzdCQSxhQUFhO1FBRWIsTUFBTTtRQUNOVixJQUFJYyxZQUFZLENBQUM7UUFDakJkLElBQUllLElBQUksQ0FBQyxJQUFJTCxXQUFXLEtBQUtBO1FBQzdCQSxhQUFhO1FBRWIsT0FBTztRQUNQVixJQUFJWSxXQUFXLENBQUM7UUFDaEJaLElBQUlhLElBQUksQ0FBQyxTQUFtQixPQUFWTCxZQUFhLElBQUlFO1FBQ25DQSxhQUFhO1FBQ2JWLElBQUlhLElBQUksQ0FBQyxTQUFzQyxPQUE3QnBCLE9BQU91QixZQUFZLElBQUksUUFBUyxJQUFJTjtRQUN0REEsYUFBYTtRQUNiVixJQUFJYSxJQUFJLENBQUMsU0FBK0IsT0FBdEJwQixPQUFPd0IsS0FBSyxJQUFJLFFBQVMsSUFBSVA7UUFDL0NBLGFBQWE7UUFFYixPQUFPO1FBQ1AsSUFBSWpCLE9BQU95QixVQUFVLElBQUl6QixPQUFPeUIsVUFBVSxDQUFDQyxNQUFNLEdBQUcsR0FBRztZQUNyRG5CLElBQUlZLFdBQVcsQ0FBQztZQUNoQlosSUFBSWEsSUFBSSxDQUFDLFFBQVEsSUFBSUg7WUFDckJBLGFBQWE7WUFFYmpCLE9BQU95QixVQUFVLENBQUNFLE9BQU8sQ0FBQyxDQUFDQztnQkFDekIsSUFBSVgsWUFBWSxLQUFLO29CQUNuQlYsSUFBSXNCLE9BQU87b0JBQ1haLFlBQVk7Z0JBQ2Q7Z0JBRUFWLElBQUlZLFdBQVcsQ0FBQztnQkFDaEJaLElBQUlhLElBQUksQ0FBQyxHQUFzQlEsT0FBbkJBLFVBQVVFLElBQUksRUFBQyxNQUF3QkYsT0FBcEJBLFVBQVVHLEtBQUssRUFBQyxNQUFvQixPQUFoQkgsVUFBVUosS0FBSyxFQUFDLE1BQUksSUFBSVA7Z0JBQzNFQSxhQUFhO2dCQUViLElBQUlXLFVBQVVJLFdBQVcsRUFBRTtvQkFDekJ6QixJQUFJWSxXQUFXLENBQUM7b0JBQ2hCLE1BQU1jLFlBQVkxQixJQUFJMkIsZUFBZSxDQUFDTixVQUFVSSxXQUFXLEVBQUU7b0JBQzdEekIsSUFBSWEsSUFBSSxDQUFDYSxXQUFXLElBQUloQjtvQkFDeEJBLGFBQWFnQixVQUFVUCxNQUFNLEdBQUcsSUFBSTtnQkFDdEM7WUFDRjtZQUNBVCxhQUFhO1FBQ2Y7UUFFQSxPQUFPO1FBQ1AsSUFBSWpCLE9BQU9tQyxlQUFlLElBQUluQyxPQUFPbUMsZUFBZSxDQUFDVCxNQUFNLEdBQUcsR0FBRztZQUMvRCxJQUFJVCxZQUFZLEtBQUs7Z0JBQ25CVixJQUFJc0IsT0FBTztnQkFDWFosWUFBWTtZQUNkO1lBRUFWLElBQUlZLFdBQVcsQ0FBQztZQUNoQlosSUFBSWEsSUFBSSxDQUFDLFFBQVEsSUFBSUg7WUFDckJBLGFBQWE7WUFFYmpCLE9BQU9tQyxlQUFlLENBQUNSLE9BQU8sQ0FBQyxDQUFDUyxLQUFVQztnQkFDeEMsSUFBSXBCLFlBQVksS0FBSztvQkFDbkJWLElBQUlzQixPQUFPO29CQUNYWixZQUFZO2dCQUNkO2dCQUVBVixJQUFJWSxXQUFXLENBQUM7Z0JBQ2hCWixJQUFJYSxJQUFJLENBQUMsR0FBaUJnQixPQUFkQyxRQUFRLEdBQUUsTUFBaUMsT0FBN0JELElBQUlFLEtBQUssSUFBSUYsSUFBSUosV0FBVyxHQUFJLElBQUlmO2dCQUM5REEsYUFBYTtnQkFFYixJQUFJbUIsSUFBSUcsT0FBTyxJQUFJSCxJQUFJRyxPQUFPLENBQUNiLE1BQU0sR0FBRyxHQUFHO29CQUN6Q25CLElBQUlZLFdBQVcsQ0FBQztvQkFDaEJpQixJQUFJRyxPQUFPLENBQUNaLE9BQU8sQ0FBQyxDQUFDYTt3QkFDbkIsSUFBSXZCLFlBQVksS0FBSzs0QkFDbkJWLElBQUlzQixPQUFPOzRCQUNYWixZQUFZO3dCQUNkO3dCQUNBVixJQUFJYSxJQUFJLENBQUMsS0FBWSxPQUFQb0IsU0FBVSxJQUFJdkI7d0JBQzVCQSxhQUFhO29CQUNmO2dCQUNGO2dCQUNBQSxhQUFhO1lBQ2Y7UUFDRjtRQUVBLEtBQUs7UUFDTCxNQUFNd0IsWUFBWWxDLElBQUltQyxnQkFBZ0I7UUFDdEMsSUFBSyxJQUFJcEUsSUFBSSxHQUFHQSxLQUFLbUUsV0FBV25FLElBQUs7WUFDbkNpQyxJQUFJb0MsT0FBTyxDQUFDckU7WUFDWmlDLElBQUlZLFdBQVcsQ0FBQztZQUNoQlosSUFBSWEsSUFBSSxDQUFDLG1CQUFtQixJQUFJO1lBQ2hDYixJQUFJYSxJQUFJLENBQUMsS0FBY3FCLE9BQVRuRSxHQUFFLFNBQWlCLE9BQVZtRSxXQUFVLE9BQUssS0FBSztRQUM3QztRQUVBLE9BQU9sQztJQUNUO0lBRUEscUJBQ0UsOERBQUNxQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDL0YsaURBQUlBO3dCQUFDZ0csTUFBSztrQ0FDVCw0RUFBQzNHLHlEQUFNQTs0QkFBQzRHLFNBQVE7NEJBQVVDLE1BQUs7OzhDQUM3Qiw4REFBQ3BHLCtHQUFTQTtvQ0FBQ2lHLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7OztrQ0FJMUMsOERBQUMvRixpREFBSUE7d0JBQUNnRyxNQUFLO2tDQUNULDRFQUFDM0cseURBQU1BOzRCQUFDNEcsU0FBUTs0QkFBVUMsTUFBSztzQ0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXhDLDhEQUFDSjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNJO3dCQUFHSixXQUFVO2tDQUFxQjs7Ozs7O2tDQUNuQyw4REFBQ0s7d0JBQUVMLFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7MEJBTXZDLDhEQUFDekcscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBOzswQ0FDVCw4REFBQ0MsMERBQVNBO2dDQUFDcUcsV0FBVTs7a0RBQ25CLDhEQUFDbEcsK0dBQVFBO3dDQUFDa0csV0FBVTs7Ozs7O29DQUFZOzs7Ozs7OzBDQUdsQyw4REFBQ3ZHLGdFQUFlQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUluQiw4REFBQ0QsNERBQVdBO2tDQUNWLDRFQUFDdUc7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDMUcseURBQU1BO29DQUNMZ0gsU0FBUzFGO29DQUNUMkYsVUFBVWxHO29DQUNWOEYsTUFBSztvQ0FDTEgsV0FBVTs4Q0FFVDNGLDBCQUNDOzswREFDRSw4REFBQ1IsK0dBQU9BO2dEQUFDbUcsV0FBVTs7Ozs7OzRDQUE4Qjs7cUVBSW5EOzswREFDRSw4REFBQ2xHLCtHQUFRQTtnREFBQ2tHLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7O2dDQU0xQzVGLGdDQUNDLDhEQUFDZCx5REFBTUE7b0NBQ0xnSCxTQUFTN0M7b0NBQ1Q4QyxVQUFVN0Y7b0NBQ1Z5RixNQUFLO29DQUNMSCxXQUFVO29DQUNWRSxTQUFROzhDQUVQeEYsOEJBQ0M7OzBEQUNFLDhEQUFDYiwrR0FBT0E7Z0RBQUNtRyxXQUFVOzs7Ozs7NENBQThCOztxRUFJbkQ7OzBEQUNFLDhEQUFDaEcsZ0hBQVFBO2dEQUFDZ0csV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVdsRHhGLHVCQUNDLDhEQUFDakIscURBQUlBO2dCQUFDeUcsV0FBVTs7a0NBQ2QsOERBQUN0RywyREFBVUE7a0NBQ1QsNEVBQUNDLDBEQUFTQTs0QkFBQ3FHLFdBQVU7c0NBQWU7Ozs7Ozs7Ozs7O2tDQUV0Qyw4REFBQ3hHLDREQUFXQTtrQ0FDViw0RUFBQzZHOzRCQUFFTCxXQUFVO3NDQUFnQnhGOzs7Ozs7Ozs7Ozs7Ozs7OztZQU1sQ0osZ0NBQ0MsOERBQUMyRjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN6RyxxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTs4Q0FBQzs7Ozs7Ozs7Ozs7MENBRWIsOERBQUNILDREQUFXQTswQ0FDViw0RUFBQ3VHO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWjVGLGVBQWVzRSxZQUFZLElBQUk7Ozs7Ozs4REFFbEMsOERBQUNxQjtvREFBSUMsV0FBVTs4REFBZ0M7Ozs7Ozs7Ozs7OztzREFFakQsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3BHLHVEQUFLQTtvREFBQ3NHLFNBQVE7b0RBQVVGLFdBQVU7OERBQ2hDNUYsZUFBZXVFLEtBQUssSUFBSTs7Ozs7OzhEQUUzQiw4REFBQ29CO29EQUFJQyxXQUFVOzhEQUFxQzs7Ozs7Ozs7Ozs7O3NEQUV0RCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWjVGLEVBQUFBLDZCQUFBQSxlQUFld0UsVUFBVSxjQUF6QnhFLGlEQUFBQSwyQkFBMkJ5RSxNQUFNLEtBQUk7Ozs7Ozs4REFFeEMsOERBQUNrQjtvREFBSUMsV0FBVTs4REFBZ0M7Ozs7Ozs7Ozs7OztzREFFakQsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1o1RixFQUFBQSxrQ0FBQUEsZUFBZWtGLGVBQWUsY0FBOUJsRixzREFBQUEsZ0NBQWdDeUUsTUFBTSxLQUFJOzs7Ozs7OERBRTdDLDhEQUFDa0I7b0RBQUlDLFdBQVU7OERBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFPdEQ1RixlQUFld0UsVUFBVSxJQUFJeEUsZUFBZXdFLFVBQVUsQ0FBQ0MsTUFBTSxHQUFHLG1CQUMvRCw4REFBQ3RGLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTswQ0FDVCw0RUFBQ0MsMERBQVNBOzhDQUFDOzs7Ozs7Ozs7OzswQ0FFYiw4REFBQ0gsNERBQVdBOzBDQUNWLDRFQUFDdUc7b0NBQUlDLFdBQVU7OENBQ1o1RixlQUFld0UsVUFBVSxDQUFDNEIsR0FBRyxDQUFDLENBQUN6QixXQUFnQlMsc0JBQzlDLDhEQUFDTzs0Q0FBZ0JDLFdBQVU7OzhEQUN6Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDUzs0REFBR1QsV0FBVTtzRUFBaUJqQixVQUFVRSxJQUFJOzs7Ozs7c0VBQzdDLDhEQUFDYzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNVO29FQUFLVixXQUFVOzhFQUNiakIsVUFBVUcsS0FBSzs7Ozs7OzhFQUVsQiw4REFBQ3RGLHVEQUFLQTtvRUFBQ3NHLFNBQVE7OEVBQVduQixVQUFVSixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBRzdDLDhEQUFDMEI7b0RBQUVMLFdBQVU7OERBQ1ZqQixVQUFVSSxXQUFXOzs7Ozs7Z0RBR3ZCSixVQUFVNEIsU0FBUyxJQUFJNUIsVUFBVTRCLFNBQVMsQ0FBQzlCLE1BQU0sR0FBRyxtQkFDbkQsOERBQUNrQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFHWixXQUFVO3NFQUEwQzs7Ozs7O3NFQUN4RCw4REFBQ2E7NERBQUdiLFdBQVU7c0VBQ1hqQixVQUFVNEIsU0FBUyxDQUFDSCxHQUFHLENBQUMsQ0FBQ00sVUFBa0JyRixrQkFDMUMsOERBQUNzRjs4RUFBWUQ7bUVBQUpyRjs7Ozs7Ozs7Ozs7Ozs7OztnREFNaEJzRCxVQUFVaUMsWUFBWSxJQUFJakMsVUFBVWlDLFlBQVksQ0FBQ25DLE1BQU0sR0FBRyxtQkFDekQsOERBQUNrQjs7c0VBQ0MsOERBQUNhOzREQUFHWixXQUFVO3NFQUEyQzs7Ozs7O3NFQUN6RCw4REFBQ2E7NERBQUdiLFdBQVU7c0VBQ1hqQixVQUFVaUMsWUFBWSxDQUFDUixHQUFHLENBQUMsQ0FBQ1MsYUFBcUJ4RixrQkFDaEQsOERBQUNzRjs4RUFBWUU7bUVBQUp4Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBOUJUK0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkEyQ25CcEYsZUFBZWtGLGVBQWUsSUFBSWxGLGVBQWVrRixlQUFlLENBQUNULE1BQU0sR0FBRyxtQkFDekUsOERBQUN0RixxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7MENBQ1QsNEVBQUNDLDBEQUFTQTs4Q0FBQzs7Ozs7Ozs7Ozs7MENBRWIsOERBQUNILDREQUFXQTswQ0FDViw0RUFBQ3VHO29DQUFJQyxXQUFVOzhDQUNaNUYsZUFBZWtGLGVBQWUsQ0FBQ2tCLEdBQUcsQ0FBQyxDQUFDakIsS0FBVUMsc0JBQzdDLDhEQUFDTzs0Q0FBZ0JDLFdBQVU7OzhEQUN6Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDUzs0REFBR1QsV0FBVTtzRUFBaUJULElBQUlFLEtBQUs7Ozs7OztzRUFDeEMsOERBQUM3Rix1REFBS0E7NERBQ0pzRyxTQUFTWCxJQUFJMkIsUUFBUSxLQUFLLFNBQVMsZ0JBQzNCM0IsSUFBSTJCLFFBQVEsS0FBSyxXQUFXLFlBQVk7c0VBRS9DM0IsSUFBSTJCLFFBQVEsS0FBSyxTQUFTLFNBQzFCM0IsSUFBSTJCLFFBQVEsS0FBSyxXQUFXLFNBQVM7Ozs7Ozs7Ozs7Ozs4REFHMUMsOERBQUNiO29EQUFFTCxXQUFVOzhEQUNWVCxJQUFJSixXQUFXOzs7Ozs7Z0RBR2pCSSxJQUFJRyxPQUFPLElBQUlILElBQUlHLE9BQU8sQ0FBQ2IsTUFBTSxHQUFHLG1CQUNuQyw4REFBQ2tCOztzRUFDQyw4REFBQ2E7NERBQUdaLFdBQVU7c0VBQTJCOzs7Ozs7c0VBQ3pDLDhEQUFDYTs0REFBR2IsV0FBVTtzRUFDWFQsSUFBSUcsT0FBTyxDQUFDYyxHQUFHLENBQUMsQ0FBQ2IsUUFBZ0JsRSxrQkFDaEMsOERBQUNzRjs4RUFBWXBCO21FQUFKbEU7Ozs7Ozs7Ozs7Ozs7Ozs7OzJDQXBCVCtEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBaUNwQiw4REFBQ2pHLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTs7a0RBQ1QsOERBQUNDLDBEQUFTQTtrREFBQzs7Ozs7O2tEQUNYLDhEQUFDRixnRUFBZUE7a0RBQUM7Ozs7Ozs7Ozs7OzswQ0FJbkIsOERBQUNELDREQUFXQTswQ0FDViw0RUFBQzJIO29DQUFJbkIsV0FBVTs4Q0FDWnJELEtBQUtDLFNBQVMsQ0FBQ3hDLGdCQUFnQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF0RDtHQWhid0JEO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdGVzdC1zdGFuZGFyZC1yZXBvcnQvcGFnZS50c3g/NWQzYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHsgTG9hZGVyMiwgRmlsZVRleHQsIEFycm93TGVmdCwgRG93bmxvYWQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IE1hcmtkb3duUmVuZGVyZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbWFya2Rvd24tcmVuZGVyZXInO1xuaW1wb3J0IGpzUERGIGZyb20gJ2pzcGRmJztcblxuLyoqXG4gKiDmoIflh4bniYjliIbmnpDmiqXlkYrmoLflvI/mtYvor5XpobXpnaJcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGVzdFN0YW5kYXJkUmVwb3J0UGFnZSgpIHtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2FuYWx5c2lzUmVzdWx0LCBzZXRBbmFseXNpc1Jlc3VsdF0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNEb3dubG9hZGluZywgc2V0SXNEb3dubG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g55Sf5oiQ5qCH5YeG54mI5YiG5p6Q5oql5ZGKXG4gIGNvbnN0IGdlbmVyYXRlU3RhbmRhcmRSZXBvcnQgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOS9v+eUqOaooeaLn+aVsOaNrueUn+aIkOagh+WHhueJiOaKpeWRilxuICAgICAgY29uc3QgbW9ja1Byb2ZpbGUgPSB7XG4gICAgICAgIG9yZ2FuaXphdGlvblR5cGU6ICfnpL7kvJrmnI3liqHmnLrmnoQnLFxuICAgICAgICBzZXJ2aWNlQXJlYTogJ+aVmeiCsuaUr+aMgScsXG4gICAgICAgIHJlc291cmNlU3RydWN0dXJlOiAn5Z+66YeR5Lya5pSv5oyB5Z6LJyxcbiAgICAgICAgZGV2ZWxvcG1lbnRTdGFnZTogJ+aIkOmVv+acnycsXG4gICAgICAgIHRlYW1TaXplOiAn5Lit5Z6L77yIMjEtNTDkurrvvIknLFxuICAgICAgICBvcGVyYXRpbmdNb2RlbDogJ+ebtOaOpeacjeWKoeWeiycsXG4gICAgICAgIGltcGFjdFNjb3BlOiAn5Yy65Z+f5b2x5ZONJyxcbiAgICAgICAgb3JnYW5pemF0aW9uQ3VsdHVyZTogJ+S9v+WRvempseWKqCcsXG4gICAgICAgIGNoYWxsZW5nZXNQcmlvcml0aWVzOiAn6LWE6YeR56256ZuGJyxcbiAgICAgICAgZnV0dXJlVmlzaW9uOiAn5oiQ5Li65Yy65Z+f6aKG5YWI55qE5pWZ6IKy5pSv5oyB5py65p6EJ1xuICAgICAgfTtcblxuICAgICAgLy8g55Sf5oiQ5qih5ouf55qENjDpgZPpopjnm67nrZTmoYhcbiAgICAgIGNvbnN0IG1vY2tSZXNwb25zZXMgPSBbXTtcbiAgICAgIGZvciAobGV0IGkgPSAxOyBpIDw9IDYwOyBpKyspIHtcbiAgICAgICAgbW9ja1Jlc3BvbnNlcy5wdXNoKHtcbiAgICAgICAgICBxdWVzdGlvbklkOiBgUSR7aS50b1N0cmluZygpLnBhZFN0YXJ0KDMsICcwJyl9YCxcbiAgICAgICAgICBhbnN3ZXI6IFsnQScsICdCJywgJ0MnLCAnRCddW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDQpXSxcbiAgICAgICAgICBjb25maWRlbmNlOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzKSArIDMsXG4gICAgICAgICAgdGltZVNwZW50OiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzMCkgKyAxMFxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/CfmoAg5byA5aeL55Sf5oiQ5qCH5YeG54mI5YiG5p6Q5oql5ZGKLi4uJyk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXNzZXNzbWVudC9hbmFseXplJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBwcm9maWxlOiBtb2NrUHJvZmlsZSxcbiAgICAgICAgICByZXNwb25zZXM6IG1vY2tSZXNwb25zZXMsXG4gICAgICAgICAgdmVyc2lvbjogJ3N0YW5kYXJkJ1xuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihg5YiG5p6Q5aSx6LSlOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICfliIbmnpDlpLHotKUnKTtcbiAgICAgIH1cblxuICAgICAgc2V0QW5hbHlzaXNSZXN1bHQocmVzdWx0LmRhdGEpO1xuICAgICAgY29uc29sZS5sb2coJ+KchSDmoIflh4bniYjliIbmnpDmiqXlkYrnlJ/miJDmiJDlip86JywgcmVzdWx0LmRhdGEpO1xuXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg55Sf5oiQ5qCH5YeG54mI5oql5ZGK5aSx6LSlOicsIGVycik7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ+acquefpemUmeivrycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDkuIvovb1QREbmiqXlkYrlip/og71cbiAgY29uc3QgaGFuZGxlRG93bmxvYWRSZXBvcnQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFhbmFseXNpc1Jlc3VsdCkgcmV0dXJuO1xuXG4gICAgc2V0SXNEb3dubG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8g55Sf5oiQUERG5oql5ZGKXG4gICAgICBjb25zdCBwZGYgPSBnZW5lcmF0ZVBERlJlcG9ydChhbmFseXNpc1Jlc3VsdCk7XG5cbiAgICAgIC8vIOS4i+i9vVBERuaWh+S7tlxuICAgICAgY29uc3QgZmlsZU5hbWUgPSBgT0NUSee7hOe7h+iDveWKm+ivhOS8sOaKpeWRil8ke25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCkucmVwbGFjZSgvXFwvL2csICctJyl9LnBkZmA7XG4gICAgICBwZGYuc2F2ZShmaWxlTmFtZSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgUERG5oql5ZGK5LiL6L295oiQ5YqfJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBQREbmiqXlkYrkuIvovb3lpLHotKU6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ+aKpeWRiuS4i+i9veWksei0pe+8jOivt+mHjeivlScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0Rvd25sb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g55Sf5oiQUERG5oql5ZGKXG4gIGNvbnN0IGdlbmVyYXRlUERGUmVwb3J0ID0gKHJlc3VsdDogYW55KToganNQREYgPT4ge1xuICAgIGNvbnN0IHBkZiA9IG5ldyBqc1BERigpO1xuICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKTtcbiAgICBsZXQgeVBvc2l0aW9uID0gMjA7XG5cbiAgICAvLyDorr7nva7lrZfkvZNcbiAgICBwZGYuc2V0Rm9udCgnaGVsdmV0aWNhJyk7XG5cbiAgICAvLyDmoIfpophcbiAgICBwZGYuc2V0Rm9udFNpemUoMjApO1xuICAgIHBkZi50ZXh0KCdPQ1RJ57uE57uH6IO95Yqb6K+E5Lyw5oql5ZGKJywgMjAsIHlQb3NpdGlvbik7XG4gICAgeVBvc2l0aW9uICs9IDE1O1xuXG4gICAgLy8g5YiG6ZqU57q/XG4gICAgcGRmLnNldExpbmVXaWR0aCgwLjUpO1xuICAgIHBkZi5saW5lKDIwLCB5UG9zaXRpb24sIDE5MCwgeVBvc2l0aW9uKTtcbiAgICB5UG9zaXRpb24gKz0gMTU7XG5cbiAgICAvLyDln7rmnKzkv6Hmga9cbiAgICBwZGYuc2V0Rm9udFNpemUoMTIpO1xuICAgIHBkZi50ZXh0KGDnlJ/miJDml7bpl7Q6ICR7dGltZXN0YW1wfWAsIDIwLCB5UG9zaXRpb24pO1xuICAgIHlQb3NpdGlvbiArPSAxMDtcbiAgICBwZGYudGV4dChg5oC75L2T5b6X5YiGOiAke3Jlc3VsdC5vdmVyYWxsU2NvcmUgfHwgJ04vQSd9YCwgMjAsIHlQb3NpdGlvbik7XG4gICAgeVBvc2l0aW9uICs9IDEwO1xuICAgIHBkZi50ZXh0KGDog73lipvnrYnnuqc6ICR7cmVzdWx0LmxldmVsIHx8ICdOL0EnfWAsIDIwLCB5UG9zaXRpb24pO1xuICAgIHlQb3NpdGlvbiArPSAxNTtcblxuICAgIC8vIOe7tOW6puWIhuaekFxuICAgIGlmIChyZXN1bHQuZGltZW5zaW9ucyAmJiByZXN1bHQuZGltZW5zaW9ucy5sZW5ndGggPiAwKSB7XG4gICAgICBwZGYuc2V0Rm9udFNpemUoMTQpO1xuICAgICAgcGRmLnRleHQoJ+e7tOW6puWIhuaekCcsIDIwLCB5UG9zaXRpb24pO1xuICAgICAgeVBvc2l0aW9uICs9IDEwO1xuXG4gICAgICByZXN1bHQuZGltZW5zaW9ucy5mb3JFYWNoKChkaW1lbnNpb246IGFueSkgPT4ge1xuICAgICAgICBpZiAoeVBvc2l0aW9uID4gMjcwKSB7XG4gICAgICAgICAgcGRmLmFkZFBhZ2UoKTtcbiAgICAgICAgICB5UG9zaXRpb24gPSAyMDtcbiAgICAgICAgfVxuXG4gICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMik7XG4gICAgICAgIHBkZi50ZXh0KGAke2RpbWVuc2lvbi5uYW1lfTogJHtkaW1lbnNpb24uc2NvcmV9ICgke2RpbWVuc2lvbi5sZXZlbH0pYCwgMjUsIHlQb3NpdGlvbik7XG4gICAgICAgIHlQb3NpdGlvbiArPSA4O1xuXG4gICAgICAgIGlmIChkaW1lbnNpb24uZGVzY3JpcHRpb24pIHtcbiAgICAgICAgICBwZGYuc2V0Rm9udFNpemUoMTApO1xuICAgICAgICAgIGNvbnN0IGRlc2NMaW5lcyA9IHBkZi5zcGxpdFRleHRUb1NpemUoZGltZW5zaW9uLmRlc2NyaXB0aW9uLCAxNjApO1xuICAgICAgICAgIHBkZi50ZXh0KGRlc2NMaW5lcywgMzAsIHlQb3NpdGlvbik7XG4gICAgICAgICAgeVBvc2l0aW9uICs9IGRlc2NMaW5lcy5sZW5ndGggKiA1ICsgNTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICB5UG9zaXRpb24gKz0gMTA7XG4gICAgfVxuXG4gICAgLy8g5pS56L+b5bu66K6uXG4gICAgaWYgKHJlc3VsdC5yZWNvbW1lbmRhdGlvbnMgJiYgcmVzdWx0LnJlY29tbWVuZGF0aW9ucy5sZW5ndGggPiAwKSB7XG4gICAgICBpZiAoeVBvc2l0aW9uID4gMjUwKSB7XG4gICAgICAgIHBkZi5hZGRQYWdlKCk7XG4gICAgICAgIHlQb3NpdGlvbiA9IDIwO1xuICAgICAgfVxuXG4gICAgICBwZGYuc2V0Rm9udFNpemUoMTQpO1xuICAgICAgcGRmLnRleHQoJ+aUuei/m+W7uuiuricsIDIwLCB5UG9zaXRpb24pO1xuICAgICAgeVBvc2l0aW9uICs9IDEwO1xuXG4gICAgICByZXN1bHQucmVjb21tZW5kYXRpb25zLmZvckVhY2goKHJlYzogYW55LCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgICAgIGlmICh5UG9zaXRpb24gPiAyNzApIHtcbiAgICAgICAgICBwZGYuYWRkUGFnZSgpO1xuICAgICAgICAgIHlQb3NpdGlvbiA9IDIwO1xuICAgICAgICB9XG5cbiAgICAgICAgcGRmLnNldEZvbnRTaXplKDEyKTtcbiAgICAgICAgcGRmLnRleHQoYCR7aW5kZXggKyAxfS4gJHtyZWMudGl0bGUgfHwgcmVjLmRlc2NyaXB0aW9ufWAsIDI1LCB5UG9zaXRpb24pO1xuICAgICAgICB5UG9zaXRpb24gKz0gODtcblxuICAgICAgICBpZiAocmVjLmFjdGlvbnMgJiYgcmVjLmFjdGlvbnMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHBkZi5zZXRGb250U2l6ZSgxMCk7XG4gICAgICAgICAgcmVjLmFjdGlvbnMuZm9yRWFjaCgoYWN0aW9uOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIGlmICh5UG9zaXRpb24gPiAyNzApIHtcbiAgICAgICAgICAgICAgcGRmLmFkZFBhZ2UoKTtcbiAgICAgICAgICAgICAgeVBvc2l0aW9uID0gMjA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBwZGYudGV4dChg4oCiICR7YWN0aW9ufWAsIDMwLCB5UG9zaXRpb24pO1xuICAgICAgICAgICAgeVBvc2l0aW9uICs9IDY7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgeVBvc2l0aW9uICs9IDU7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyDpobXohJpcbiAgICBjb25zdCBwYWdlQ291bnQgPSBwZGYuZ2V0TnVtYmVyT2ZQYWdlcygpO1xuICAgIGZvciAobGV0IGkgPSAxOyBpIDw9IHBhZ2VDb3VudDsgaSsrKSB7XG4gICAgICBwZGYuc2V0UGFnZShpKTtcbiAgICAgIHBkZi5zZXRGb250U2l6ZSg4KTtcbiAgICAgIHBkZi50ZXh0KCfmiqXlkYrnlLFPQ1RJ5pm66IO96K+E5Lyw57O757uf55Sf5oiQJywgMjAsIDI4NSk7XG4gICAgICBwZGYudGV4dChg56ysICR7aX0g6aG177yM5YWxICR7cGFnZUNvdW50fSDpobVgLCAxNzAsIDI5MCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHBkZjtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcC02IHNwYWNlLXktNlwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICA8TGluayBocmVmPVwiL3Rlc3QtZnVsbC1mbG93XCI+XG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAg6L+U5Zue5rWL6K+V6aG16Z2iXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvTGluaz5cbiAgICAgICAgPExpbmsgaHJlZj1cIi9cIj5cbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICDov5Tlm57pppbpobVcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9MaW5rPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS00XCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGRcIj7wn5OKIOagh+WHhueJiOWIhuaekOaKpeWRiuagt+W8j+mihOiniDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgIOeUn+aIkOW5tuafpeeci09DVEnmoIflh4bniYjliIbmnpDmiqXlkYrnmoTlrozmlbTmoLflvI/lkozlhoXlrrnnu5PmnoRcbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDmjqfliLbpnaLmnb8gKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAg5oql5ZGK55Sf5oiQ5o6n5Yi2XG4gICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIOeCueWHu+aMiemSrueUn+aIkOagh+WHhueJiOWIhuaekOaKpeWRiuW5tuafpeeci+agt+W8j1xuICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtnZW5lcmF0ZVN0YW5kYXJkUmVwb3J0fVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICAgIOeUn+aIkOS4rS4uLlxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIOeUn+aIkOagh+WHhueJiOWIhuaekOaKpeWRilxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIHthbmFseXNpc1Jlc3VsdCAmJiAoXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVEb3dubG9hZFJlcG9ydH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEb3dubG9hZGluZ31cbiAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzRG93bmxvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTQgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg5LiL6L295LitLi4uXG4gICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIOS4i+i9vVBERuaKpeWRilxuICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7Lyog6ZSZ6K+v5L+h5oGvICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYm9yZGVyLXJlZC0yMDAgYmctcmVkLTUwXCI+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtcmVkLTgwMFwiPueUn+aIkOWksei0pTwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDBcIj57ZXJyb3J9PC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDliIbmnpDnu5PmnpzlsZXnpLogKi99XG4gICAgICB7YW5hbHlzaXNSZXN1bHQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGU+8J+TiyDliIbmnpDnu5PmnpzmpoLop4g8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7YW5hbHlzaXNSZXN1bHQub3ZlcmFsbFNjb3JlIHx8ICdOL0EnfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+5oC75L2T5b6X5YiGPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICB7YW5hbHlzaXNSZXN1bHQubGV2ZWwgfHwgJ04vQSd9XG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0xXCI+6IO95Yqb562J57qnPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2FuYWx5c2lzUmVzdWx0LmRpbWVuc2lvbnM/Lmxlbmd0aCB8fCAwfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+6K+E5Lyw57u05bqmPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHthbmFseXNpc1Jlc3VsdC5yZWNvbW1lbmRhdGlvbnM/Lmxlbmd0aCB8fCAwfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+5pS56L+b5bu66K6uPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICB7Lyog57u05bqm5YiG5p6QICovfVxuICAgICAgICAgIHthbmFseXNpc1Jlc3VsdC5kaW1lbnNpb25zICYmIGFuYWx5c2lzUmVzdWx0LmRpbWVuc2lvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZT7wn46vIOe7tOW6puWIhuaekOivpuaDhTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAge2FuYWx5c2lzUmVzdWx0LmRpbWVuc2lvbnMubWFwKChkaW1lbnNpb246IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e2RpbWVuc2lvbi5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGltZW5zaW9uLnNjb3JlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiPntkaW1lbnNpb24ubGV2ZWx9PC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtkaW1lbnNpb24uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIHtkaW1lbnNpb24uc3RyZW5ndGhzICYmIGRpbWVuc2lvbi5zdHJlbmd0aHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi03MDAgbWItMVwiPuS8mOWKvzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwIGxpc3QtZGlzYyBsaXN0LWluc2lkZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkaW1lbnNpb24uc3RyZW5ndGhzLm1hcCgoc3RyZW5ndGg6IHN0cmluZywgaTogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpfT57c3RyZW5ndGh9PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAge2RpbWVuc2lvbi5pbXByb3ZlbWVudHMgJiYgZGltZW5zaW9uLmltcHJvdmVtZW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtb3JhbmdlLTcwMCBtYi0xXCI+5pS56L+b54K5PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1vcmFuZ2UtNjAwIGxpc3QtZGlzYyBsaXN0LWluc2lkZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkaW1lbnNpb24uaW1wcm92ZW1lbnRzLm1hcCgoaW1wcm92ZW1lbnQ6IHN0cmluZywgaTogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpfT57aW1wcm92ZW1lbnR9PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiDmlLnov5vlu7rorq4gKi99XG4gICAgICAgICAge2FuYWx5c2lzUmVzdWx0LnJlY29tbWVuZGF0aW9ucyAmJiBhbmFseXNpc1Jlc3VsdC5yZWNvbW1lbmRhdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZT7wn5KhIOaUuei/m+W7uuiurjwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAge2FuYWx5c2lzUmVzdWx0LnJlY29tbWVuZGF0aW9ucy5tYXAoKHJlYzogYW55LCBpbmRleDogbnVtYmVyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGRcIj57cmVjLnRpdGxlfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e3JlYy5wcmlvcml0eSA9PT0gJ2hpZ2gnID8gJ2Rlc3RydWN0aXZlJyA6IFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlYy5wcmlvcml0eSA9PT0gJ21lZGl1bScgPyAnZGVmYXVsdCcgOiAnc2Vjb25kYXJ5J31cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3JlYy5wcmlvcml0eSA9PT0gJ2hpZ2gnID8gJ+mrmOS8mOWFiOe6pycgOiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlYy5wcmlvcml0eSA9PT0gJ21lZGl1bScgPyAn5Lit5LyY5YWI57qnJyA6ICfkvY7kvJjlhYjnuqcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmVjLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICB7cmVjLmFjdGlvbnMgJiYgcmVjLmFjdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBtYi0xXCI+5YW35L2T6KGM5YqoPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtc20gbGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZWMuYWN0aW9ucy5tYXAoKGFjdGlvbjogc3RyaW5nLCBpOiBudW1iZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2l9PnthY3Rpb259PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiDljp/lp4vmlbDmja4gKi99XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT7wn5SNIOWOn+Wni+aVsOaNrue7k+aehDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIOeUqOS6juW8gOWPkeiwg+ivleeahOWujOaVtOaVsOaNrue7k+aehFxuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBwLTQgcm91bmRlZC1sZyB0ZXh0LXNtIG92ZXJmbG93LWF1dG8gbWF4LWgtOTZcIj5cbiAgICAgICAgICAgICAgICB7SlNPTi5zdHJpbmdpZnkoYW5hbHlzaXNSZXN1bHQsIG51bGwsIDIpfVxuICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsIkxvYWRlcjIiLCJGaWxlVGV4dCIsIkFycm93TGVmdCIsIkRvd25sb2FkIiwiTGluayIsImpzUERGIiwiVGVzdFN0YW5kYXJkUmVwb3J0UGFnZSIsImFuYWx5c2lzUmVzdWx0IiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwic2V0QW5hbHlzaXNSZXN1bHQiLCJlcnJvciIsInNldEVycm9yIiwiaXNEb3dubG9hZGluZyIsInNldElzRG93bmxvYWRpbmciLCJnZW5lcmF0ZVN0YW5kYXJkUmVwb3J0IiwibW9ja1Byb2ZpbGUiLCJvcmdhbml6YXRpb25UeXBlIiwic2VydmljZUFyZWEiLCJyZXNvdXJjZVN0cnVjdHVyZSIsImRldmVsb3BtZW50U3RhZ2UiLCJ0ZWFtU2l6ZSIsIm9wZXJhdGluZ01vZGVsIiwiaW1wYWN0U2NvcGUiLCJvcmdhbml6YXRpb25DdWx0dXJlIiwiY2hhbGxlbmdlc1ByaW9yaXRpZXMiLCJmdXR1cmVWaXNpb24iLCJtb2NrUmVzcG9uc2VzIiwiaSIsInB1c2giLCJxdWVzdGlvbklkIiwidG9TdHJpbmciLCJwYWRTdGFydCIsImFuc3dlciIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImNvbmZpZGVuY2UiLCJ0aW1lU3BlbnQiLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInByb2ZpbGUiLCJyZXNwb25zZXMiLCJ2ZXJzaW9uIiwib2siLCJFcnJvciIsInN0YXR1cyIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiZGF0YSIsImVyciIsIm1lc3NhZ2UiLCJoYW5kbGVEb3dubG9hZFJlcG9ydCIsInBkZiIsImdlbmVyYXRlUERGUmVwb3J0IiwiZmlsZU5hbWUiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwicmVwbGFjZSIsInNhdmUiLCJhbGVydCIsInRpbWVzdGFtcCIsInRvTG9jYWxlU3RyaW5nIiwieVBvc2l0aW9uIiwic2V0Rm9udCIsInNldEZvbnRTaXplIiwidGV4dCIsInNldExpbmVXaWR0aCIsImxpbmUiLCJvdmVyYWxsU2NvcmUiLCJsZXZlbCIsImRpbWVuc2lvbnMiLCJsZW5ndGgiLCJmb3JFYWNoIiwiZGltZW5zaW9uIiwiYWRkUGFnZSIsIm5hbWUiLCJzY29yZSIsImRlc2NyaXB0aW9uIiwiZGVzY0xpbmVzIiwic3BsaXRUZXh0VG9TaXplIiwicmVjb21tZW5kYXRpb25zIiwicmVjIiwiaW5kZXgiLCJ0aXRsZSIsImFjdGlvbnMiLCJhY3Rpb24iLCJwYWdlQ291bnQiLCJnZXROdW1iZXJPZlBhZ2VzIiwic2V0UGFnZSIsImRpdiIsImNsYXNzTmFtZSIsImhyZWYiLCJ2YXJpYW50Iiwic2l6ZSIsImgxIiwicCIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsIm1hcCIsImgzIiwic3BhbiIsInN0cmVuZ3RocyIsImg0IiwidWwiLCJzdHJlbmd0aCIsImxpIiwiaW1wcm92ZW1lbnRzIiwiaW1wcm92ZW1lbnQiLCJwcmlvcml0eSIsInByZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-standard-report/page.tsx\n"));

/***/ })

});