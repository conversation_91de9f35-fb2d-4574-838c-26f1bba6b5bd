"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@iconify";
exports.ids = ["vendor-chunks/@iconify"];
exports.modules = {

/***/ "(ssr)/./node_modules/@iconify/utils/lib/customisations/defaults.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/customisations/defaults.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultIconCustomisations: () => (/* binding */ defaultIconCustomisations),\n/* harmony export */   defaultIconSizeCustomisations: () => (/* binding */ defaultIconSizeCustomisations)\n/* harmony export */ });\n/* harmony import */ var _icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icon/defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\");\n\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ..._icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultIconTransformations\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2N1c3RvbWlzYXRpb25zL2RlZmF1bHRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0U7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssMEVBQTBCO0FBQy9CLENBQUM7O0FBRW1FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AaWNvbmlmeS91dGlscy9saWIvY3VzdG9taXNhdGlvbnMvZGVmYXVsdHMubWpzP2UyYjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVmYXVsdEljb25UcmFuc2Zvcm1hdGlvbnMgfSBmcm9tICcuLi9pY29uL2RlZmF1bHRzLm1qcyc7XG5cbmNvbnN0IGRlZmF1bHRJY29uU2l6ZUN1c3RvbWlzYXRpb25zID0gT2JqZWN0LmZyZWV6ZSh7XG4gIHdpZHRoOiBudWxsLFxuICBoZWlnaHQ6IG51bGxcbn0pO1xuY29uc3QgZGVmYXVsdEljb25DdXN0b21pc2F0aW9ucyA9IE9iamVjdC5mcmVlemUoe1xuICAvLyBEaW1lbnNpb25zXG4gIC4uLmRlZmF1bHRJY29uU2l6ZUN1c3RvbWlzYXRpb25zLFxuICAvLyBUcmFuc2Zvcm1hdGlvbnNcbiAgLi4uZGVmYXVsdEljb25UcmFuc2Zvcm1hdGlvbnNcbn0pO1xuXG5leHBvcnQgeyBkZWZhdWx0SWNvbkN1c3RvbWlzYXRpb25zLCBkZWZhdWx0SWNvblNpemVDdXN0b21pc2F0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/customisations/defaults.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon-set/get-icon.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon-set/get-icon.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIconData: () => (/* binding */ getIconData),\n/* harmony export */   internalGetIconData: () => (/* binding */ internalGetIconData)\n/* harmony export */ });\n/* harmony import */ var _icon_merge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icon/merge.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/merge.mjs\");\n/* harmony import */ var _tree_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tree.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon-set/tree.mjs\");\n\n\n\n\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = (0,_icon_merge_mjs__WEBPACK_IMPORTED_MODULE_0__.mergeIconData)(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return (0,_icon_merge_mjs__WEBPACK_IMPORTED_MODULE_0__.mergeIconData)(data, currentProps);\n}\nfunction getIconData(data, name) {\n  if (data.icons[name]) {\n    return internalGetIconData(data, name, []);\n  }\n  const tree = (0,_tree_mjs__WEBPACK_IMPORTED_MODULE_1__.getIconsTree)(data, [name])[name];\n  return tree ? internalGetIconData(data, name, tree) : null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24tc2V0L2dldC1pY29uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ1I7QUFDWjtBQUNPOztBQUVyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhEQUFhO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOERBQWE7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsdURBQVk7QUFDM0I7QUFDQTs7QUFFNEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BpY29uaWZ5L3V0aWxzL2xpYi9pY29uLXNldC9nZXQtaWNvbi5tanM/ZTNhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtZXJnZUljb25EYXRhIH0gZnJvbSAnLi4vaWNvbi9tZXJnZS5tanMnO1xuaW1wb3J0IHsgZ2V0SWNvbnNUcmVlIH0gZnJvbSAnLi90cmVlLm1qcyc7XG5pbXBvcnQgJy4uL2ljb24vZGVmYXVsdHMubWpzJztcbmltcG9ydCAnLi4vaWNvbi90cmFuc2Zvcm1hdGlvbnMubWpzJztcblxuZnVuY3Rpb24gaW50ZXJuYWxHZXRJY29uRGF0YShkYXRhLCBuYW1lLCB0cmVlKSB7XG4gIGNvbnN0IGljb25zID0gZGF0YS5pY29ucztcbiAgY29uc3QgYWxpYXNlcyA9IGRhdGEuYWxpYXNlcyB8fCAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgbGV0IGN1cnJlbnRQcm9wcyA9IHt9O1xuICBmdW5jdGlvbiBwYXJzZShuYW1lMikge1xuICAgIGN1cnJlbnRQcm9wcyA9IG1lcmdlSWNvbkRhdGEoXG4gICAgICBpY29uc1tuYW1lMl0gfHwgYWxpYXNlc1tuYW1lMl0sXG4gICAgICBjdXJyZW50UHJvcHNcbiAgICApO1xuICB9XG4gIHBhcnNlKG5hbWUpO1xuICB0cmVlLmZvckVhY2gocGFyc2UpO1xuICByZXR1cm4gbWVyZ2VJY29uRGF0YShkYXRhLCBjdXJyZW50UHJvcHMpO1xufVxuZnVuY3Rpb24gZ2V0SWNvbkRhdGEoZGF0YSwgbmFtZSkge1xuICBpZiAoZGF0YS5pY29uc1tuYW1lXSkge1xuICAgIHJldHVybiBpbnRlcm5hbEdldEljb25EYXRhKGRhdGEsIG5hbWUsIFtdKTtcbiAgfVxuICBjb25zdCB0cmVlID0gZ2V0SWNvbnNUcmVlKGRhdGEsIFtuYW1lXSlbbmFtZV07XG4gIHJldHVybiB0cmVlID8gaW50ZXJuYWxHZXRJY29uRGF0YShkYXRhLCBuYW1lLCB0cmVlKSA6IG51bGw7XG59XG5cbmV4cG9ydCB7IGdldEljb25EYXRhLCBpbnRlcm5hbEdldEljb25EYXRhIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon-set/get-icon.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon-set/tree.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon-set/tree.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIconsTree: () => (/* binding */ getIconsTree)\n/* harmony export */ });\nfunction getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24tc2V0L3RyZWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AaWNvbmlmeS91dGlscy9saWIvaWNvbi1zZXQvdHJlZS5tanM/YjY1MiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBnZXRJY29uc1RyZWUoZGF0YSwgbmFtZXMpIHtcbiAgY29uc3QgaWNvbnMgPSBkYXRhLmljb25zO1xuICBjb25zdCBhbGlhc2VzID0gZGF0YS5hbGlhc2VzIHx8IC8qIEBfX1BVUkVfXyAqLyBPYmplY3QuY3JlYXRlKG51bGwpO1xuICBjb25zdCByZXNvbHZlZCA9IC8qIEBfX1BVUkVfXyAqLyBPYmplY3QuY3JlYXRlKG51bGwpO1xuICBmdW5jdGlvbiByZXNvbHZlKG5hbWUpIHtcbiAgICBpZiAoaWNvbnNbbmFtZV0pIHtcbiAgICAgIHJldHVybiByZXNvbHZlZFtuYW1lXSA9IFtdO1xuICAgIH1cbiAgICBpZiAoIShuYW1lIGluIHJlc29sdmVkKSkge1xuICAgICAgcmVzb2x2ZWRbbmFtZV0gPSBudWxsO1xuICAgICAgY29uc3QgcGFyZW50ID0gYWxpYXNlc1tuYW1lXSAmJiBhbGlhc2VzW25hbWVdLnBhcmVudDtcbiAgICAgIGNvbnN0IHZhbHVlID0gcGFyZW50ICYmIHJlc29sdmUocGFyZW50KTtcbiAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICByZXNvbHZlZFtuYW1lXSA9IFtwYXJlbnRdLmNvbmNhdCh2YWx1ZSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXNvbHZlZFtuYW1lXTtcbiAgfVxuICAobmFtZXMgfHwgT2JqZWN0LmtleXMoaWNvbnMpLmNvbmNhdChPYmplY3Qua2V5cyhhbGlhc2VzKSkpLmZvckVhY2gocmVzb2x2ZSk7XG4gIHJldHVybiByZXNvbHZlZDtcbn1cblxuZXhwb3J0IHsgZ2V0SWNvbnNUcmVlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon-set/tree.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/defaults.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultExtendedIconProps: () => (/* binding */ defaultExtendedIconProps),\n/* harmony export */   defaultIconDimensions: () => (/* binding */ defaultIconDimensions),\n/* harmony export */   defaultIconProps: () => (/* binding */ defaultIconProps),\n/* harmony export */   defaultIconTransformations: () => (/* binding */ defaultIconTransformations)\n/* harmony export */ });\nconst defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24vZGVmYXVsdHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFd0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BpY29uaWZ5L3V0aWxzL2xpYi9pY29uL2RlZmF1bHRzLm1qcz8xNDZhIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGRlZmF1bHRJY29uRGltZW5zaW9ucyA9IE9iamVjdC5mcmVlemUoXG4gIHtcbiAgICBsZWZ0OiAwLFxuICAgIHRvcDogMCxcbiAgICB3aWR0aDogMTYsXG4gICAgaGVpZ2h0OiAxNlxuICB9XG4pO1xuY29uc3QgZGVmYXVsdEljb25UcmFuc2Zvcm1hdGlvbnMgPSBPYmplY3QuZnJlZXplKHtcbiAgcm90YXRlOiAwLFxuICB2RmxpcDogZmFsc2UsXG4gIGhGbGlwOiBmYWxzZVxufSk7XG5jb25zdCBkZWZhdWx0SWNvblByb3BzID0gT2JqZWN0LmZyZWV6ZSh7XG4gIC4uLmRlZmF1bHRJY29uRGltZW5zaW9ucyxcbiAgLi4uZGVmYXVsdEljb25UcmFuc2Zvcm1hdGlvbnNcbn0pO1xuY29uc3QgZGVmYXVsdEV4dGVuZGVkSWNvblByb3BzID0gT2JqZWN0LmZyZWV6ZSh7XG4gIC4uLmRlZmF1bHRJY29uUHJvcHMsXG4gIGJvZHk6IFwiXCIsXG4gIGhpZGRlbjogZmFsc2Vcbn0pO1xuXG5leHBvcnQgeyBkZWZhdWx0RXh0ZW5kZWRJY29uUHJvcHMsIGRlZmF1bHRJY29uRGltZW5zaW9ucywgZGVmYXVsdEljb25Qcm9wcywgZGVmYXVsdEljb25UcmFuc2Zvcm1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/merge.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/merge.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeIconData: () => (/* binding */ mergeIconData)\n/* harmony export */ });\n/* harmony import */ var _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\");\n/* harmony import */ var _transformations_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transformations.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/transformations.mjs\");\n\n\n\nfunction mergeIconData(parent, child) {\n  const result = (0,_transformations_mjs__WEBPACK_IMPORTED_MODULE_0__.mergeIconTransformations)(parent, child);\n  for (const key in _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultExtendedIconProps) {\n    if (key in _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24vbWVyZ2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzRjtBQUNyQjs7QUFFakU7QUFDQSxpQkFBaUIsOEVBQXdCO0FBQ3pDLG9CQUFvQixtRUFBd0I7QUFDNUMsZUFBZSxxRUFBMEI7QUFDekM7QUFDQSxzQkFBc0IscUVBQTBCO0FBQ2hEO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AaWNvbmlmeS91dGlscy9saWIvaWNvbi9tZXJnZS5tanM/YThmZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWZhdWx0RXh0ZW5kZWRJY29uUHJvcHMsIGRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zIH0gZnJvbSAnLi9kZWZhdWx0cy5tanMnO1xuaW1wb3J0IHsgbWVyZ2VJY29uVHJhbnNmb3JtYXRpb25zIH0gZnJvbSAnLi90cmFuc2Zvcm1hdGlvbnMubWpzJztcblxuZnVuY3Rpb24gbWVyZ2VJY29uRGF0YShwYXJlbnQsIGNoaWxkKSB7XG4gIGNvbnN0IHJlc3VsdCA9IG1lcmdlSWNvblRyYW5zZm9ybWF0aW9ucyhwYXJlbnQsIGNoaWxkKTtcbiAgZm9yIChjb25zdCBrZXkgaW4gZGVmYXVsdEV4dGVuZGVkSWNvblByb3BzKSB7XG4gICAgaWYgKGtleSBpbiBkZWZhdWx0SWNvblRyYW5zZm9ybWF0aW9ucykge1xuICAgICAgaWYgKGtleSBpbiBwYXJlbnQgJiYgIShrZXkgaW4gcmVzdWx0KSkge1xuICAgICAgICByZXN1bHRba2V5XSA9IGRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zW2tleV07XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChrZXkgaW4gY2hpbGQpIHtcbiAgICAgIHJlc3VsdFtrZXldID0gY2hpbGRba2V5XTtcbiAgICB9IGVsc2UgaWYgKGtleSBpbiBwYXJlbnQpIHtcbiAgICAgIHJlc3VsdFtrZXldID0gcGFyZW50W2tleV07XG4gICAgfVxuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5cbmV4cG9ydCB7IG1lcmdlSWNvbkRhdGEgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/merge.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/name.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/name.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchIconName: () => (/* binding */ matchIconName),\n/* harmony export */   stringToIcon: () => (/* binding */ stringToIcon),\n/* harmony export */   validateIconName: () => (/* binding */ validateIconName)\n/* harmony export */ });\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/name.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/transformations.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/transformations.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeIconTransformations: () => (/* binding */ mergeIconTransformations)\n/* harmony export */ });\nfunction mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24vdHJhbnNmb3JtYXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFb0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BpY29uaWZ5L3V0aWxzL2xpYi9pY29uL3RyYW5zZm9ybWF0aW9ucy5tanM/Yjg0MiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBtZXJnZUljb25UcmFuc2Zvcm1hdGlvbnMob2JqMSwgb2JqMikge1xuICBjb25zdCByZXN1bHQgPSB7fTtcbiAgaWYgKCFvYmoxLmhGbGlwICE9PSAhb2JqMi5oRmxpcCkge1xuICAgIHJlc3VsdC5oRmxpcCA9IHRydWU7XG4gIH1cbiAgaWYgKCFvYmoxLnZGbGlwICE9PSAhb2JqMi52RmxpcCkge1xuICAgIHJlc3VsdC52RmxpcCA9IHRydWU7XG4gIH1cbiAgY29uc3Qgcm90YXRlID0gKChvYmoxLnJvdGF0ZSB8fCAwKSArIChvYmoyLnJvdGF0ZSB8fCAwKSkgJSA0O1xuICBpZiAocm90YXRlKSB7XG4gICAgcmVzdWx0LnJvdGF0ZSA9IHJvdGF0ZTtcbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufVxuXG5leHBvcnQgeyBtZXJnZUljb25UcmFuc2Zvcm1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/transformations.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/build.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/build.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iconToSVG: () => (/* binding */ iconToSVG),\n/* harmony export */   isUnsetKeyword: () => (/* binding */ isUnsetKeyword)\n/* harmony export */ });\n/* harmony import */ var _icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icon/defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\");\n/* harmony import */ var _customisations_defaults_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../customisations/defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/customisations/defaults.mjs\");\n/* harmony import */ var _size_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./size.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/svg/size.mjs\");\n/* harmony import */ var _defs_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defs.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/svg/defs.mjs\");\n\n\n\n\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ..._icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ..._customisations_defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = (0,_defs_mjs__WEBPACK_IMPORTED_MODULE_2__.wrapSVGContent)(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateSize)(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? (0,_size_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateSize)(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/build.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/defs.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/defs.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeDefsAndContent: () => (/* binding */ mergeDefsAndContent),\n/* harmony export */   splitSVGDefs: () => (/* binding */ splitSVGDefs),\n/* harmony export */   wrapSVGContent: () => (/* binding */ wrapSVGContent)\n/* harmony export */ });\nfunction splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL3N2Zy9kZWZzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFNkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BpY29uaWZ5L3V0aWxzL2xpYi9zdmcvZGVmcy5tanM/OTY2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBzcGxpdFNWR0RlZnMoY29udGVudCwgdGFnID0gXCJkZWZzXCIpIHtcbiAgbGV0IGRlZnMgPSBcIlwiO1xuICBjb25zdCBpbmRleCA9IGNvbnRlbnQuaW5kZXhPZihcIjxcIiArIHRhZyk7XG4gIHdoaWxlIChpbmRleCA+PSAwKSB7XG4gICAgY29uc3Qgc3RhcnQgPSBjb250ZW50LmluZGV4T2YoXCI+XCIsIGluZGV4KTtcbiAgICBjb25zdCBlbmQgPSBjb250ZW50LmluZGV4T2YoXCI8L1wiICsgdGFnKTtcbiAgICBpZiAoc3RhcnQgPT09IC0xIHx8IGVuZCA9PT0gLTEpIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgICBjb25zdCBlbmRFbmQgPSBjb250ZW50LmluZGV4T2YoXCI+XCIsIGVuZCk7XG4gICAgaWYgKGVuZEVuZCA9PT0gLTEpIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgICBkZWZzICs9IGNvbnRlbnQuc2xpY2Uoc3RhcnQgKyAxLCBlbmQpLnRyaW0oKTtcbiAgICBjb250ZW50ID0gY29udGVudC5zbGljZSgwLCBpbmRleCkudHJpbSgpICsgY29udGVudC5zbGljZShlbmRFbmQgKyAxKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIGRlZnMsXG4gICAgY29udGVudFxuICB9O1xufVxuZnVuY3Rpb24gbWVyZ2VEZWZzQW5kQ29udGVudChkZWZzLCBjb250ZW50KSB7XG4gIHJldHVybiBkZWZzID8gXCI8ZGVmcz5cIiArIGRlZnMgKyBcIjwvZGVmcz5cIiArIGNvbnRlbnQgOiBjb250ZW50O1xufVxuZnVuY3Rpb24gd3JhcFNWR0NvbnRlbnQoYm9keSwgc3RhcnQsIGVuZCkge1xuICBjb25zdCBzcGxpdCA9IHNwbGl0U1ZHRGVmcyhib2R5KTtcbiAgcmV0dXJuIG1lcmdlRGVmc0FuZENvbnRlbnQoc3BsaXQuZGVmcywgc3RhcnQgKyBzcGxpdC5jb250ZW50ICsgZW5kKTtcbn1cblxuZXhwb3J0IHsgbWVyZ2VEZWZzQW5kQ29udGVudCwgc3BsaXRTVkdEZWZzLCB3cmFwU1ZHQ29udGVudCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/defs.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/html.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/html.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iconToHTML: () => (/* binding */ iconToHTML)\n/* harmony export */ });\nfunction iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL3N2Zy9odG1sLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AaWNvbmlmeS91dGlscy9saWIvc3ZnL2h0bWwubWpzPzAzNjQiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaWNvblRvSFRNTChib2R5LCBhdHRyaWJ1dGVzKSB7XG4gIGxldCByZW5kZXJBdHRyaWJzSFRNTCA9IGJvZHkuaW5kZXhPZihcInhsaW5rOlwiKSA9PT0gLTEgPyBcIlwiIDogJyB4bWxuczp4bGluaz1cImh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmtcIic7XG4gIGZvciAoY29uc3QgYXR0ciBpbiBhdHRyaWJ1dGVzKSB7XG4gICAgcmVuZGVyQXR0cmlic0hUTUwgKz0gXCIgXCIgKyBhdHRyICsgJz1cIicgKyBhdHRyaWJ1dGVzW2F0dHJdICsgJ1wiJztcbiAgfVxuICByZXR1cm4gJzxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiJyArIHJlbmRlckF0dHJpYnNIVE1MICsgXCI+XCIgKyBib2R5ICsgXCI8L3N2Zz5cIjtcbn1cblxuZXhwb3J0IHsgaWNvblRvSFRNTCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/html.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/id.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/id.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   replaceIDs: () => (/* binding */ replaceIDs)\n/* harmony export */ });\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL3N2Zy9pZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQztBQUMzQztBQUNBLDBDQUEwQztBQUMxQztBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BpY29uaWZ5L3V0aWxzL2xpYi9zdmcvaWQubWpzP2UyNGQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgcmVnZXggPSAvXFxzaWQ9XCIoXFxTKylcIi9nO1xuY29uc3QgcmFuZG9tUHJlZml4ID0gXCJJY29uaWZ5SWRcIiArIERhdGUubm93KCkudG9TdHJpbmcoMTYpICsgKE1hdGgucmFuZG9tKCkgKiAxNjc3NzIxNiB8IDApLnRvU3RyaW5nKDE2KTtcbmxldCBjb3VudGVyID0gMDtcbmZ1bmN0aW9uIHJlcGxhY2VJRHMoYm9keSwgcHJlZml4ID0gcmFuZG9tUHJlZml4KSB7XG4gIGNvbnN0IGlkcyA9IFtdO1xuICBsZXQgbWF0Y2g7XG4gIHdoaWxlIChtYXRjaCA9IHJlZ2V4LmV4ZWMoYm9keSkpIHtcbiAgICBpZHMucHVzaChtYXRjaFsxXSk7XG4gIH1cbiAgaWYgKCFpZHMubGVuZ3RoKSB7XG4gICAgcmV0dXJuIGJvZHk7XG4gIH1cbiAgY29uc3Qgc3VmZml4ID0gXCJzdWZmaXhcIiArIChNYXRoLnJhbmRvbSgpICogMTY3NzcyMTYgfCBEYXRlLm5vdygpKS50b1N0cmluZygxNik7XG4gIGlkcy5mb3JFYWNoKChpZCkgPT4ge1xuICAgIGNvbnN0IG5ld0lEID0gdHlwZW9mIHByZWZpeCA9PT0gXCJmdW5jdGlvblwiID8gcHJlZml4KGlkKSA6IHByZWZpeCArIChjb3VudGVyKyspLnRvU3RyaW5nKCk7XG4gICAgY29uc3QgZXNjYXBlZElEID0gaWQucmVwbGFjZSgvWy4qKz9eJHt9KCl8W1xcXVxcXFxdL2csIFwiXFxcXCQmXCIpO1xuICAgIGJvZHkgPSBib2R5LnJlcGxhY2UoXG4gICAgICAvLyBBbGxvd2VkIGNoYXJhY3RlcnMgYmVmb3JlIGlkOiBbIztcIl1cbiAgICAgIC8vIEFsbG93ZWQgY2hhcmFjdGVycyBhZnRlciBpZDogWylcIl0sIC5bYS16XVxuICAgICAgbmV3IFJlZ0V4cCgnKFsjO1wiXSkoJyArIGVzY2FwZWRJRCArICcpKFtcIildfFxcXFwuW2Etel0pJywgXCJnXCIpLFxuICAgICAgXCIkMVwiICsgbmV3SUQgKyBzdWZmaXggKyBcIiQzXCJcbiAgICApO1xuICB9KTtcbiAgYm9keSA9IGJvZHkucmVwbGFjZShuZXcgUmVnRXhwKHN1ZmZpeCwgXCJnXCIpLCBcIlwiKTtcbiAgcmV0dXJuIGJvZHk7XG59XG5cbmV4cG9ydCB7IHJlcGxhY2VJRHMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/id.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/size.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/size.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize)\n/* harmony export */ });\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/size.mjs\n");

/***/ })

};
;