/**
 * OCTI智能评估系统 - 问题渲染组件
 * 
 * 根据问题类型渲染不同的问题界面
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

export interface Question {
  id: string;
  type: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'SCALE' | 'TEXT' | 'BOOLEAN' | 'RANKING';
  source: 'PRESET' | 'AI_GENERATED';
  category: string;
  subCategory?: string;
  title: string;
  description?: string;
  options?: any;
  required: boolean;
  order: number;
  metadata?: any;
}

export interface QuestionResponse {
  questionId: string;
  answer: any;
  confidence?: number;
  timeSpent?: number;
}

interface QuestionRendererProps {
  question: Question;
  response?: QuestionResponse;
  onAnswerChange: (response: QuestionResponse) => void;
  showProgress?: boolean;
  currentIndex?: number;
  totalQuestions?: number;
  className?: string;
}

// ============================================================================
// 问题渲染组件
// ============================================================================

export function QuestionRenderer({
  question,
  response,
  onAnswerChange,
  showProgress = false,
  currentIndex = 0,
  totalQuestions = 1,
  className,
}: QuestionRendererProps) {
  const [startTime] = useState(Date.now());
  const [currentAnswer, setCurrentAnswer] = useState(response?.answer || null);

  // 使用useCallback优化答案变化处理
  const handleAnswerChange = useCallback((newAnswer: any) => {
    if (newAnswer !== currentAnswer) {
      setCurrentAnswer(newAnswer);

      const timeSpent = Math.floor((Date.now() - startTime) / 1000);
      onAnswerChange({
        questionId: question.id,
        answer: newAnswer,
        timeSpent,
      });
    }
  }, [currentAnswer, question.id, startTime, onAnswerChange]);

  const renderQuestionContent = () => {
    switch (question.type) {
      case 'SINGLE_CHOICE':
        return renderSingleChoice();
      case 'MULTIPLE_CHOICE':
        return renderMultipleChoice();
      case 'SCALE':
        return renderScale();
      case 'TEXT':
        return renderText();
      case 'BOOLEAN':
        return renderBoolean();
      case 'RANKING':
        return renderRanking();
      default:
        return <div>不支持的问题类型</div>;
    }
  };

  const renderSingleChoice = () => {
    const options = question.options || [];
    
    return (
      <div className="space-y-3">
        {options.map((option: any, index: number) => (
          <div
            key={index}
            className={cn(
              'flex items-center space-x-3 rounded-lg border p-4 cursor-pointer transition-colors',
              currentAnswer === option.value
                ? 'border-primary bg-primary/5'
                : 'hover:bg-accent'
            )}
            onClick={() => handleAnswerChange(option.value)}
          >
            <div
              className={cn(
                'h-4 w-4 rounded-full border-2 flex items-center justify-center',
                currentAnswer === option.value
                  ? 'border-primary bg-primary'
                  : 'border-muted-foreground'
              )}
            >
              {currentAnswer === option.value && (
                <div className="h-2 w-2 rounded-full bg-white"></div>
              )}
            </div>
            <span className="flex-1">{option.text}</span>
          </div>
        ))}
      </div>
    );
  };

  const renderMultipleChoice = () => {
    const options = question.options || [];
    const selectedValues = Array.isArray(currentAnswer) ? currentAnswer : [];
    
    const toggleOption = (value: any) => {
      const newSelection = selectedValues.includes(value)
        ? selectedValues.filter((v: any) => v !== value)
        : [...selectedValues, value];
      handleAnswerChange(newSelection);
    };

    return (
      <div className="space-y-3">
        {options.map((option: any, index: number) => (
          <div
            key={index}
            className={cn(
              'flex items-center space-x-3 rounded-lg border p-4 cursor-pointer transition-colors',
              selectedValues.includes(option.value)
                ? 'border-primary bg-primary/5'
                : 'hover:bg-accent'
            )}
            onClick={() => toggleOption(option.value)}
          >
            <div
              className={cn(
                'h-4 w-4 rounded border-2 flex items-center justify-center',
                selectedValues.includes(option.value)
                  ? 'border-primary bg-primary'
                  : 'border-muted-foreground'
              )}
            >
              {selectedValues.includes(option.value) && (
                <span className="text-white text-xs">✓</span>
              )}
            </div>
            <span className="flex-1">{option.text}</span>
          </div>
        ))}
      </div>
    );
  };

  const renderScale = () => {
    const { min = 1, max = 5, labels = [] } = question.options || {};
    const values = Array.from({ length: max - min + 1 }, (_, i) => min + i);

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          {labels.length > 0 && (
            <>
              <span className="text-sm text-muted-foreground">{labels[0]}</span>
              <span className="text-sm text-muted-foreground">{labels[labels.length - 1]}</span>
            </>
          )}
        </div>
        <div className="flex justify-between items-center space-x-2">
          {values.map((value) => (
            <button
              key={value}
              className={cn(
                'h-12 w-12 rounded-full border-2 transition-colors',
                currentAnswer === value
                  ? 'border-primary bg-primary text-white'
                  : 'border-muted-foreground hover:border-primary'
              )}
              onClick={() => handleAnswerChange(value)}
            >
              {value}
            </button>
          ))}
        </div>
        {labels.length > 0 && (
          <div className="flex justify-between text-xs text-muted-foreground">
            {labels.map((label: string, index: number) => (
              <span key={index} className="text-center max-w-16">
                {label}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderText = () => {
    const { maxLength = 500, placeholder = '请输入您的回答...' } = question.options || {};

    return (
      <div className="space-y-2">
        <Textarea
          placeholder={placeholder}
          value={currentAnswer || ''}
          onChange={(e) => handleAnswerChange(e.target.value)}
          maxLength={maxLength}
          className="min-h-24"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>最多{maxLength}字</span>
          <span>{(currentAnswer || '').length}/{maxLength}</span>
        </div>
      </div>
    );
  };

  const renderBoolean = () => {
    const options = question.options || [
      { text: '是', value: true },
      { text: '否', value: false },
    ];

    return (
      <div className="flex space-x-4">
        {options.map((option: any, index: number) => (
          <button
            key={index}
            className={cn(
              'flex-1 py-3 px-6 rounded-lg border-2 transition-colors',
              currentAnswer === option.value
                ? 'border-primary bg-primary text-white'
                : 'border-muted-foreground hover:border-primary'
            )}
            onClick={() => handleAnswerChange(option.value)}
          >
            {option.text}
          </button>
        ))}
      </div>
    );
  };

  const renderRanking = () => {
    const options = question.options || [];
    const rankings = Array.isArray(currentAnswer) ? currentAnswer : [];

    const addToRanking = (value: any) => {
      if (!rankings.includes(value)) {
        handleAnswerChange([...rankings, value]);
      }
    };

    const removeFromRanking = (value: any) => {
      handleAnswerChange(rankings.filter((v: any) => v !== value));
    };

    const moveUp = (index: number) => {
      if (index > 0) {
        const newRankings = [...rankings];
        [newRankings[index - 1], newRankings[index]] = [newRankings[index], newRankings[index - 1]];
        handleAnswerChange(newRankings);
      }
    };

    const moveDown = (index: number) => {
      if (index < rankings.length - 1) {
        const newRankings = [...rankings];
        [newRankings[index], newRankings[index + 1]] = [newRankings[index + 1], newRankings[index]];
        handleAnswerChange(newRankings);
      }
    };

    return (
      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-2">可选项目：</h4>
          <div className="space-y-2">
            {options
              .filter((option: any) => !rankings.includes(option.value))
              .map((option: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent cursor-pointer"
                  onClick={() => addToRanking(option.value)}
                >
                  <span>{option.text}</span>
                  <Button size="sm" variant="outline">
                    添加
                  </Button>
                </div>
              ))}
          </div>
        </div>

        {rankings.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">您的排序：</h4>
            <div className="space-y-2">
              {rankings.map((value: any, index: number) => {
                const option = options.find((opt: any) => opt.value === value);
                return (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg bg-primary/5"
                  >
                    <div className="flex items-center space-x-3">
                      <Badge variant="secondary">{index + 1}</Badge>
                      <span>{option?.text}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => moveUp(index)}
                        disabled={index === 0}
                      >
                        ↑
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => moveDown(index)}
                        disabled={index === rankings.length - 1}
                      >
                        ↓
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeFromRanking(value)}
                      >
                        移除
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* 进度条 */}
      {showProgress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>问题 {currentIndex + 1} / {totalQuestions}</span>
            <span>{Math.round(((currentIndex + 1) / totalQuestions) * 100)}%</span>
          </div>
          <Progress value={((currentIndex + 1) / totalQuestions) * 100} />
        </div>
      )}

      {/* 问题信息 */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Badge variant={question.source === 'PRESET' ? 'default' : 'secondary'}>
            {question.source === 'PRESET' ? '预设题目' : 'AI生成'}
          </Badge>
          <Badge variant="outline">{question.category}</Badge>
          {question.required && (
            <Badge variant="destructive">必答</Badge>
          )}
        </div>
        
        <h2 className="text-xl font-semibold">{question.title}</h2>
        
        {question.description && (
          <p className="text-muted-foreground">{question.description}</p>
        )}
      </div>

      {/* 问题内容 */}
      <div>{renderQuestionContent()}</div>
    </div>
  );
}
