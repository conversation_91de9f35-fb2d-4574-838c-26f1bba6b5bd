"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vscode-uri";
exports.ids = ["vendor-chunks/vscode-uri"];
exports.modules = {

/***/ "(ssr)/./node_modules/vscode-uri/lib/esm/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/vscode-uri/lib/esm/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   URI: () => (/* binding */ URI),\n/* harmony export */   Utils: () => (/* binding */ Utils)\n/* harmony export */ });\nvar LIB;(()=>{\"use strict\";var t={470:t=>{function e(t){if(\"string\"!=typeof t)throw new TypeError(\"Path must be a string. Received \"+JSON.stringify(t))}function r(t,e){for(var r,n=\"\",i=0,o=-1,s=0,h=0;h<=t.length;++h){if(h<t.length)r=t.charCodeAt(h);else{if(47===r)break;r=47}if(47===r){if(o===h-1||1===s);else if(o!==h-1&&2===s){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var a=n.lastIndexOf(\"/\");if(a!==n.length-1){-1===a?(n=\"\",i=0):i=(n=n.slice(0,a)).length-1-n.lastIndexOf(\"/\"),o=h,s=0;continue}}else if(2===n.length||1===n.length){n=\"\",i=0,o=h,s=0;continue}e&&(n.length>0?n+=\"/..\":n=\"..\",i=2)}else n.length>0?n+=\"/\"+t.slice(o+1,h):n=t.slice(o+1,h),i=h-o-1;o=h,s=0}else 46===r&&-1!==s?++s:s=-1}return n}var n={resolve:function(){for(var t,n=\"\",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var s;o>=0?s=arguments[o]:(void 0===t&&(t=process.cwd()),s=t),e(s),0!==s.length&&(n=s+\"/\"+n,i=47===s.charCodeAt(0))}return n=r(n,!i),i?n.length>0?\"/\"+n:\"/\":n.length>0?n:\".\"},normalize:function(t){if(e(t),0===t.length)return\".\";var n=47===t.charCodeAt(0),i=47===t.charCodeAt(t.length-1);return 0!==(t=r(t,!n)).length||n||(t=\".\"),t.length>0&&i&&(t+=\"/\"),n?\"/\"+t:t},isAbsolute:function(t){return e(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return\".\";for(var t,r=0;r<arguments.length;++r){var i=arguments[r];e(i),i.length>0&&(void 0===t?t=i:t+=\"/\"+i)}return void 0===t?\".\":n.normalize(t)},relative:function(t,r){if(e(t),e(r),t===r)return\"\";if((t=n.resolve(t))===(r=n.resolve(r)))return\"\";for(var i=1;i<t.length&&47===t.charCodeAt(i);++i);for(var o=t.length,s=o-i,h=1;h<r.length&&47===r.charCodeAt(h);++h);for(var a=r.length-h,c=s<a?s:a,f=-1,u=0;u<=c;++u){if(u===c){if(a>c){if(47===r.charCodeAt(h+u))return r.slice(h+u+1);if(0===u)return r.slice(h+u)}else s>c&&(47===t.charCodeAt(i+u)?f=u:0===u&&(f=0));break}var l=t.charCodeAt(i+u);if(l!==r.charCodeAt(h+u))break;47===l&&(f=u)}var g=\"\";for(u=i+f+1;u<=o;++u)u!==o&&47!==t.charCodeAt(u)||(0===g.length?g+=\"..\":g+=\"/..\");return g.length>0?g+r.slice(h+f):(h+=f,47===r.charCodeAt(h)&&++h,r.slice(h))},_makeLong:function(t){return t},dirname:function(t){if(e(t),0===t.length)return\".\";for(var r=t.charCodeAt(0),n=47===r,i=-1,o=!0,s=t.length-1;s>=1;--s)if(47===(r=t.charCodeAt(s))){if(!o){i=s;break}}else o=!1;return-1===i?n?\"/\":\".\":n&&1===i?\"//\":t.slice(0,i)},basename:function(t,r){if(void 0!==r&&\"string\"!=typeof r)throw new TypeError('\"ext\" argument must be a string');e(t);var n,i=0,o=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=t.length){if(r.length===t.length&&r===t)return\"\";var h=r.length-1,a=-1;for(n=t.length-1;n>=0;--n){var c=t.charCodeAt(n);if(47===c){if(!s){i=n+1;break}}else-1===a&&(s=!1,a=n+1),h>=0&&(c===r.charCodeAt(h)?-1==--h&&(o=n):(h=-1,o=a))}return i===o?o=a:-1===o&&(o=t.length),t.slice(i,o)}for(n=t.length-1;n>=0;--n)if(47===t.charCodeAt(n)){if(!s){i=n+1;break}}else-1===o&&(s=!1,o=n+1);return-1===o?\"\":t.slice(i,o)},extname:function(t){e(t);for(var r=-1,n=0,i=-1,o=!0,s=0,h=t.length-1;h>=0;--h){var a=t.charCodeAt(h);if(47!==a)-1===i&&(o=!1,i=h+1),46===a?-1===r?r=h:1!==s&&(s=1):-1!==r&&(s=-1);else if(!o){n=h+1;break}}return-1===r||-1===i||0===s||1===s&&r===i-1&&r===n+1?\"\":t.slice(r,i)},format:function(t){if(null===t||\"object\"!=typeof t)throw new TypeError('The \"pathObject\" argument must be of type Object. Received type '+typeof t);return function(t,e){var r=e.dir||e.root,n=e.base||(e.name||\"\")+(e.ext||\"\");return r?r===e.root?r+n:r+\"/\"+n:n}(0,t)},parse:function(t){e(t);var r={root:\"\",dir:\"\",base:\"\",ext:\"\",name:\"\"};if(0===t.length)return r;var n,i=t.charCodeAt(0),o=47===i;o?(r.root=\"/\",n=1):n=0;for(var s=-1,h=0,a=-1,c=!0,f=t.length-1,u=0;f>=n;--f)if(47!==(i=t.charCodeAt(f)))-1===a&&(c=!1,a=f+1),46===i?-1===s?s=f:1!==u&&(u=1):-1!==s&&(u=-1);else if(!c){h=f+1;break}return-1===s||-1===a||0===u||1===u&&s===a-1&&s===h+1?-1!==a&&(r.base=r.name=0===h&&o?t.slice(1,a):t.slice(h,a)):(0===h&&o?(r.name=t.slice(1,s),r.base=t.slice(1,a)):(r.name=t.slice(h,s),r.base=t.slice(h,a)),r.ext=t.slice(s,a)),h>0?r.dir=t.slice(0,h-1):o&&(r.dir=\"/\"),r},sep:\"/\",delimiter:\":\",win32:null,posix:null};n.posix=n,t.exports=n}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,r),o.exports}r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})};var n={};(()=>{let t;if(r.r(n),r.d(n,{URI:()=>f,Utils:()=>P}),\"object\"==typeof process)t=\"win32\"===process.platform;else if(\"object\"==typeof navigator){let e=navigator.userAgent;t=e.indexOf(\"Windows\")>=0}const e=/^\\w[\\w\\d+.-]*$/,i=/^\\//,o=/^\\/\\//;function s(t,r){if(!t.scheme&&r)throw new Error(`[UriError]: Scheme is missing: {scheme: \"\", authority: \"${t.authority}\", path: \"${t.path}\", query: \"${t.query}\", fragment: \"${t.fragment}\"}`);if(t.scheme&&!e.test(t.scheme))throw new Error(\"[UriError]: Scheme contains illegal characters.\");if(t.path)if(t.authority){if(!i.test(t.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash (\"/\") character')}else if(o.test(t.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters (\"//\")')}const h=\"\",a=\"/\",c=/^(([^:/?#]+?):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;class f{static isUri(t){return t instanceof f||!!t&&\"string\"==typeof t.authority&&\"string\"==typeof t.fragment&&\"string\"==typeof t.path&&\"string\"==typeof t.query&&\"string\"==typeof t.scheme&&\"string\"==typeof t.fsPath&&\"function\"==typeof t.with&&\"function\"==typeof t.toString}scheme;authority;path;query;fragment;constructor(t,e,r,n,i,o=!1){\"object\"==typeof t?(this.scheme=t.scheme||h,this.authority=t.authority||h,this.path=t.path||h,this.query=t.query||h,this.fragment=t.fragment||h):(this.scheme=function(t,e){return t||e?t:\"file\"}(t,o),this.authority=e||h,this.path=function(t,e){switch(t){case\"https\":case\"http\":case\"file\":e?e[0]!==a&&(e=a+e):e=a}return e}(this.scheme,r||h),this.query=n||h,this.fragment=i||h,s(this,o))}get fsPath(){return m(this,!1)}with(t){if(!t)return this;let{scheme:e,authority:r,path:n,query:i,fragment:o}=t;return void 0===e?e=this.scheme:null===e&&(e=h),void 0===r?r=this.authority:null===r&&(r=h),void 0===n?n=this.path:null===n&&(n=h),void 0===i?i=this.query:null===i&&(i=h),void 0===o?o=this.fragment:null===o&&(o=h),e===this.scheme&&r===this.authority&&n===this.path&&i===this.query&&o===this.fragment?this:new l(e,r,n,i,o)}static parse(t,e=!1){const r=c.exec(t);return r?new l(r[2]||h,C(r[4]||h),C(r[5]||h),C(r[7]||h),C(r[9]||h),e):new l(h,h,h,h,h)}static file(e){let r=h;if(t&&(e=e.replace(/\\\\/g,a)),e[0]===a&&e[1]===a){const t=e.indexOf(a,2);-1===t?(r=e.substring(2),e=a):(r=e.substring(2,t),e=e.substring(t)||a)}return new l(\"file\",r,e,h,h)}static from(t){const e=new l(t.scheme,t.authority,t.path,t.query,t.fragment);return s(e,!0),e}toString(t=!1){return y(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof f)return t;{const e=new l(t);return e._formatted=t.external,e._fsPath=t._sep===u?t.fsPath:null,e}}return t}}const u=t?1:void 0;class l extends f{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=m(this,!1)),this._fsPath}toString(t=!1){return t?y(this,!0):(this._formatted||(this._formatted=y(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=u),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}}const g={58:\"%3A\",47:\"%2F\",63:\"%3F\",35:\"%23\",91:\"%5B\",93:\"%5D\",64:\"%40\",33:\"%21\",36:\"%24\",38:\"%26\",39:\"%27\",40:\"%28\",41:\"%29\",42:\"%2A\",43:\"%2B\",44:\"%2C\",59:\"%3B\",61:\"%3D\",32:\"%20\"};function d(t,e,r){let n,i=-1;for(let o=0;o<t.length;o++){const s=t.charCodeAt(o);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||e&&47===s||r&&91===s||r&&93===s||r&&58===s)-1!==i&&(n+=encodeURIComponent(t.substring(i,o)),i=-1),void 0!==n&&(n+=t.charAt(o));else{void 0===n&&(n=t.substr(0,o));const e=g[s];void 0!==e?(-1!==i&&(n+=encodeURIComponent(t.substring(i,o)),i=-1),n+=e):-1===i&&(i=o)}}return-1!==i&&(n+=encodeURIComponent(t.substring(i))),void 0!==n?n:t}function p(t){let e;for(let r=0;r<t.length;r++){const n=t.charCodeAt(r);35===n||63===n?(void 0===e&&(e=t.substr(0,r)),e+=g[n]):void 0!==e&&(e+=t[r])}return void 0!==e?e:t}function m(e,r){let n;return n=e.authority&&e.path.length>1&&\"file\"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?r?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,t&&(n=n.replace(/\\//g,\"\\\\\")),n}function y(t,e){const r=e?p:d;let n=\"\",{scheme:i,authority:o,path:s,query:h,fragment:c}=t;if(i&&(n+=i,n+=\":\"),(o||\"file\"===i)&&(n+=a,n+=a),o){let t=o.indexOf(\"@\");if(-1!==t){const e=o.substr(0,t);o=o.substr(t+1),t=e.lastIndexOf(\":\"),-1===t?n+=r(e,!1,!1):(n+=r(e.substr(0,t),!1,!1),n+=\":\",n+=r(e.substr(t+1),!1,!0)),n+=\"@\"}o=o.toLowerCase(),t=o.lastIndexOf(\":\"),-1===t?n+=r(o,!1,!0):(n+=r(o.substr(0,t),!1,!0),n+=o.substr(t))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){const t=s.charCodeAt(1);t>=65&&t<=90&&(s=`/${String.fromCharCode(t+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){const t=s.charCodeAt(0);t>=65&&t<=90&&(s=`${String.fromCharCode(t+32)}:${s.substr(2)}`)}n+=r(s,!0,!1)}return h&&(n+=\"?\",n+=r(h,!1,!1)),c&&(n+=\"#\",n+=e?c:d(c,!1,!1)),n}function v(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+v(t.substr(3)):t}}const b=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function C(t){return t.match(b)?t.replace(b,(t=>v(t))):t}var A=r(470);const w=A.posix||A,x=\"/\";var P;!function(t){t.joinPath=function(t,...e){return t.with({path:w.join(t.path,...e)})},t.resolvePath=function(t,...e){let r=t.path,n=!1;r[0]!==x&&(r=x+r,n=!0);let i=w.resolve(r,...e);return n&&i[0]===x&&!t.authority&&(i=i.substring(1)),t.with({path:i})},t.dirname=function(t){if(0===t.path.length||t.path===x)return t;let e=w.dirname(t.path);return 1===e.length&&46===e.charCodeAt(0)&&(e=\"\"),t.with({path:e})},t.basename=function(t){return w.basename(t.path)},t.extname=function(t){return w.extname(t.path)}}(P||(P={}))})(),LIB=n})();const{URI,Utils}=LIB;\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vscode-uri/lib/esm/index.mjs\n");

/***/ })

};
;