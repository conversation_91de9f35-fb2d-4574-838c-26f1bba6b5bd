'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Play, CheckCircle, XCircle, Clock, Zap, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
}

interface Question {
  id: string;
  text: string;
  type: string;
  options: Array<{ value: string; text: string }>;
  dimension: string;
  metadata?: any;
}

/**
 * 智能问卷生成测试页面
 */
export default function TestIntelligentQuestionnairePage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [stats, setStats] = useState<any>(null);

  // 模拟组织画像数据
  const mockProfile = {
    organizationType: '社会服务机构',
    serviceArea: '教育支持',
    organizationScale: '中型',
    developmentStage: '成长期',
    operatingModel: '直接服务型',
    impactPositioning: '区域影响',
    organizationalCulture: '使命驱动',
    mission: '致力于改善教育资源不均衡问题'
  };

  /**
   * 测试智能问卷生成
   */
  const testIntelligentGeneration = async () => {
    setIsGenerating(true);
    setTestResult(null);
    setQuestions([]);
    setStats(null);

    const startTime = Date.now();

    try {
      console.log('🧪 开始测试智能问卷生成...');
      console.log('📋 使用模拟组织画像:', mockProfile);

      const response = await fetch('/api/questionnaire/generate-background', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ profile: mockProfile }),
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '智能问卷生成失败');
      }

      const generatedQuestions = result.data.questions;
      
      // 统计信息
      const dimensionStats = {};
      const batchStats = {};
      let successCount = 0;
      let templateCount = 0;

      generatedQuestions.forEach((q: Question) => {
        // 按维度统计
        if (!dimensionStats[q.dimension]) {
          dimensionStats[q.dimension] = 0;
        }
        dimensionStats[q.dimension]++;

        // 按批次统计
        if (q.metadata?.batchNumber) {
          const key = `${q.dimension}_批次${q.metadata.batchNumber}`;
          if (!batchStats[key]) {
            batchStats[key] = 0;
          }
          batchStats[key]++;
        }

        // 成功率统计
        if (q.metadata?.generatedAt) {
          successCount++;
        } else {
          templateCount++;
        }
      });

      const statsData = {
        总题目数: generatedQuestions.length,
        LLM生成: successCount,
        模板降级: templateCount,
        成功率: `${Math.round((successCount / generatedQuestions.length) * 100)}%`,
        生成时间: `${(duration / 1000).toFixed(1)}秒`,
        按维度分布: dimensionStats,
        按批次分布: batchStats
      };

      setTestResult({
        success: true,
        data: result.data,
        duration
      });
      setQuestions(generatedQuestions);
      setStats(statsData);

      console.log('✅ 智能问卷生成测试成功:', statsData);

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.error('❌ 智能问卷生成测试失败:', error);

      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        duration
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <Link href="/test-llm">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回LLM测试
          </Button>
        </Link>
        <Link href="/">
          <Button variant="outline" size="sm">
            返回首页
          </Button>
        </Link>
      </div>

      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">🧪 智能问卷生成测试</h1>
        <p className="text-muted-foreground">
          测试XML格式 + 分批并发 + 多API Key的智能问卷生成方案
        </p>
      </div>

      {/* 测试控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            测试控制面板
          </CardTitle>
          <CardDescription>
            点击按钮测试28道智能题目生成（2+2+2+1分批策略）
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">模拟组织画像</h4>
              <div className="text-sm space-y-1">
                <div><strong>组织类型:</strong> {mockProfile.organizationType}</div>
                <div><strong>服务领域:</strong> {mockProfile.serviceArea}</div>
                <div><strong>发展阶段:</strong> {mockProfile.developmentStage}</div>
                <div><strong>运营模式:</strong> {mockProfile.operatingModel}</div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">测试策略</h4>
              <div className="text-sm space-y-1">
                <div>• 4个维度 × 4批次 = 16个并发任务</div>
                <div>• 每个维度: 2+2+2+1道题目</div>
                <div>• XML格式 + 正则表达式解析</div>
                <div>• 多API密钥轮询调用</div>
              </div>
            </div>
          </div>

          <Button 
            onClick={testIntelligentGeneration}
            disabled={isGenerating}
            className="w-full"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                正在生成智能问卷...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                开始测试智能问卷生成
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {testResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              测试结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            {testResult.success ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    生成成功
                  </Badge>
                  <Badge variant="outline">
                    <Clock className="mr-1 h-3 w-3" />
                    {(testResult.duration / 1000).toFixed(1)}秒
                  </Badge>
                </div>

                {stats && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">生成统计</h4>
                      <div className="text-sm space-y-1">
                        <div><strong>总题目数:</strong> {stats.总题目数}</div>
                        <div><strong>LLM生成:</strong> {stats.LLM生成}</div>
                        <div><strong>模板降级:</strong> {stats.模板降级}</div>
                        <div><strong>成功率:</strong> {stats.成功率}</div>
                        <div><strong>生成时间:</strong> {stats.生成时间}</div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">维度分布</h4>
                      <div className="text-sm space-y-1">
                        {Object.entries(stats.按维度分布).map(([dimension, count]) => (
                          <div key={dimension}>
                            <strong>{dimension}:</strong> {count}道题目
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="destructive">
                  <XCircle className="mr-1 h-3 w-3" />
                  生成失败
                </Badge>
                <p className="text-red-600">{testResult.error}</p>
                <p className="text-sm text-muted-foreground">
                  耗时: {(testResult.duration / 1000).toFixed(1)}秒
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 生成的题目预览 */}
      {questions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>生成的题目预览</CardTitle>
            <CardDescription>
              共{questions.length}道题目，按维度和批次分组显示
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {['SF', 'IT', 'MV', 'AD'].map(dimension => {
                const dimensionQuestions = questions.filter(q => q.dimension === dimension);
                if (dimensionQuestions.length === 0) return null;

                return (
                  <div key={dimension} className="border rounded-lg p-4">
                    <h3 className="font-medium mb-3 flex items-center gap-2">
                      <Badge variant="outline">{dimension}维度</Badge>
                      <span className="text-sm text-muted-foreground">
                        {dimensionQuestions.length}道题目
                      </span>
                    </h3>
                    <div className="space-y-3">
                      {dimensionQuestions.map((question, index) => (
                        <div key={question.id} className="border-l-2 border-blue-200 pl-4">
                          <div className="flex items-start gap-2 mb-2">
                            <Badge variant="secondary" className="text-xs">
                              {question.id}
                            </Badge>
                            {question.metadata?.batchNumber && (
                              <Badge variant="outline" className="text-xs">
                                第{question.metadata.batchNumber}批
                              </Badge>
                            )}
                          </div>
                          <p className="font-medium mb-2">{question.text}</p>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            {question.options.map((option, optIndex) => (
                              <div key={optIndex} className="flex items-center gap-2">
                                <span className="font-mono text-xs bg-gray-100 px-1 rounded">
                                  {option.value}
                                </span>
                                <span>{option.text}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
