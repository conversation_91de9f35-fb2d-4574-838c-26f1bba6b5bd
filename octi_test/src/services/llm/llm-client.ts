/**
 * OCTI智能评估系统 - LLM API客户端
 * 
 * 支持多个LLM服务提供商的统一接口
 */

export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMRequest {
  model: string;
  messages: LLMMessage[];
  temperature?: number;
  max_tokens?: number;
  response_format?: { type: 'json_object' };
}

export interface LLMResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * LLM API客户端
 */
export class LLMClient {
  private apiKey: string;
  private baseUrl: string;
  private provider: 'deepseek' | 'minimax';

  constructor(provider: 'deepseek' | 'minimax' = 'minimax') {
    this.provider = provider;

    if (provider === 'deepseek') {
      this.apiKey = process.env.DEEPSEEK_API_KEY || '';
      this.baseUrl = 'https://api.deepseek.com/v1';
    } else {
      this.apiKey = process.env.MINIMAX_API_KEY || '';
      this.baseUrl = 'https://api.minimaxi.com/v1';
    }

    if (!this.apiKey) {
      throw new Error(`缺少${provider.toUpperCase()}_API_KEY环境变量`);
    }
  }

  /**
   * 调用LLM API
   */
  async call(request: LLMRequest): Promise<LLMResponse> {
    try {
      console.log('🤖 调用LLM API:', {
        model: request.model,
        messages: request.messages.length,
        temperature: request.temperature
      });

      // 根据提供商调整请求格式
      const requestBody = this.provider === 'minimax' ? {
        model: request.model || 'MiniMax-M1',
        messages: request.messages,
        temperature: request.temperature || 0.7,
        max_tokens: request.max_tokens || 12000, // 增加MiniMax的token限制
        top_p: 0.9,
        stream: false,
        // MiniMax不支持response_format参数，移除它
        ...(request.response_format ? {} : {})
      } : {
        model: request.model || 'deepseek-reasoner',
        messages: request.messages,
        temperature: request.temperature || 0.3,
        max_tokens: request.max_tokens || 8000, // 增加DeepSeek的token限制
        // DeepSeek支持response_format参数
        ...(request.response_format && { response_format: request.response_format })
      };

      // 根据提供商使用不同的端点
      const endpoint = this.provider === 'minimax'
        ? `${this.baseUrl}/text/chatcompletion_v2`
        : `${this.baseUrl}/chat/completions`;

      // 创建带超时的fetch请求
      const controller = new AbortController();
      // 根据模型设置不同的超时时间（智能问卷增加了分析复杂度）
      const timeout = this.provider === 'deepseek' ? 480000 : 180000; // DeepSeek 8分钟，MiniMax 3分钟
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`LLM API错误 (${response.status}): ${errorText}`);
        }

        const result = await response.json();
        console.log('✅ LLM API调用成功');

        return result;
      } catch (error: any) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
          throw new Error('LLM API调用超时（2分钟）');
        }
        throw error;
      }
    } catch (error) {
      console.error('❌ LLM API调用失败:', error);
      throw error;
    }
  }

  /**
   * 解析JSON响应（带容错处理）
   */
  static parseJSONResponse(content: string): any {
    try {
      // 尝试直接解析
      return JSON.parse(content);
    } catch (e1) {
      try {
        // 尝试提取JSON代码块
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch && jsonMatch[1]) {
          return JSON.parse(jsonMatch[1]);
        }
      } catch (e2) {
        // 尝试修复常见JSON错误
        try {
          let fixed = content;
          // 移除尾随逗号
          fixed = fixed.replace(/,\s*}/g, '}');
          fixed = fixed.replace(/,\s*]/g, ']');
          // 修复未引用的键
          fixed = fixed.replace(/(\w+):/g, '"$1":');
          return JSON.parse(fixed);
        } catch (e3) {
          console.error('JSON解析失败:', content);
          throw new Error('无法解析LLM返回的JSON格式');
        }
      }
    }
    // 如果所有解析方法都失败，返回null
    return null;
  }
}

/**
 * 创建LLM客户端实例
 */
export function createLLMClient(provider: 'deepseek' | 'minimax' = 'deepseek'): LLMClient {
  return new LLMClient(provider);
}
