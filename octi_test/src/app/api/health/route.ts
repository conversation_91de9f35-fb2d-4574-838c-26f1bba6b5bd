/**
 * OCTI智能评估系统 - 健康检查API
 * 
 * 用于Docker容器和负载均衡器的健康检查
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    database: {
      status: 'connected' | 'disconnected';
      responseTime?: number;
    };
    redis: {
      status: 'connected' | 'disconnected';
      responseTime?: number;
    };
    ai_services: {
      minimax: 'available' | 'unavailable';
      deepseek: 'available' | 'unavailable';
    };
  };
  system: {
    uptime: number;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    node_version: string;
  };
}

/**
 * 检查数据库连接
 */
async function checkDatabase(): Promise<{ status: 'connected' | 'disconnected'; responseTime?: number }> {
  try {
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'connected',
      responseTime
    };
  } catch (error) {
    console.error('Database health check failed:', error);
    return {
      status: 'disconnected'
    };
  }
}

/**
 * 检查Redis连接
 */
async function checkRedis(): Promise<{ status: 'connected' | 'disconnected'; responseTime?: number }> {
  try {
    // 这里应该导入Redis客户端进行检查
    // 暂时返回连接状态
    return {
      status: 'connected',
      responseTime: 5
    };
  } catch (error) {
    console.error('Redis health check failed:', error);
    return {
      status: 'disconnected'
    };
  }
}

/**
 * 检查AI服务可用性
 */
async function checkAIServices(): Promise<{ minimax: 'available' | 'unavailable'; deepseek: 'available' | 'unavailable' }> {
  const minimaxStatus = process.env.MINIMAX_API_KEY ? 'available' : 'unavailable';
  const deepseekStatus = process.env.DEEPSEEK_API_KEY ? 'available' : 'unavailable';
  
  return {
    minimax: minimaxStatus,
    deepseek: deepseekStatus
  };
}

/**
 * 获取系统信息
 */
function getSystemInfo() {
  const memUsage = process.memoryUsage();
  
  return {
    uptime: process.uptime(),
    memory: {
      used: memUsage.heapUsed,
      total: memUsage.heapTotal,
      percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    },
    node_version: process.version
  };
}

/**
 * GET /api/health - 健康检查端点
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const startTime = Date.now();
    
    // 并行检查所有服务
    const [databaseStatus, redisStatus, aiServices] = await Promise.all([
      checkDatabase(),
      checkRedis(),
      checkAIServices()
    ]);
    
    const systemInfo = getSystemInfo();
    
    // 判断整体健康状态
    const isHealthy = 
      databaseStatus.status === 'connected' &&
      redisStatus.status === 'connected';
    
    const healthStatus: HealthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '4.0.0',
      services: {
        database: databaseStatus,
        redis: redisStatus,
        ai_services: aiServices
      },
      system: systemInfo
    };
    
    const responseTime = Date.now() - startTime;
    
    // 根据健康状态返回相应的HTTP状态码
    const statusCode = isHealthy ? 200 : 503;
    
    return NextResponse.json(
      {
        ...healthStatus,
        response_time: responseTime
      },
      { 
        status: statusCode,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
    
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { 
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  }
}

/**
 * HEAD /api/health - 简单健康检查（仅返回状态码）
 */
export async function HEAD(request: NextRequest): Promise<NextResponse> {
  try {
    // 快速检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
