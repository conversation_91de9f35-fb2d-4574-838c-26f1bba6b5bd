"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-fetch";
exports.ids = ["vendor-chunks/d3-fetch"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-fetch/src/blob.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-fetch/src/blob.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction responseBlob(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.blob();\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(input, init) {\n  return fetch(input, init).then(responseBlob);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2Jsb2IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWZldGNoL3NyYy9ibG9iLmpzPzdkZjciXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVzcG9uc2VCbG9iKHJlc3BvbnNlKSB7XG4gIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5zdGF0dXMgKyBcIiBcIiArIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuICByZXR1cm4gcmVzcG9uc2UuYmxvYigpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihpbnB1dCwgaW5pdCkge1xuICByZXR1cm4gZmV0Y2goaW5wdXQsIGluaXQpLnRoZW4ocmVzcG9uc2VCbG9iKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/blob.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/buffer.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-fetch/src/buffer.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction responseArrayBuffer(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.arrayBuffer();\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(input, init) {\n  return fetch(input, init).then(responseArrayBuffer);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2J1ZmZlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2J1ZmZlci5qcz9iYWU0Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHJlc3BvbnNlQXJyYXlCdWZmZXIocmVzcG9uc2UpIHtcbiAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLnN0YXR1cyArIFwiIFwiICsgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gIHJldHVybiByZXNwb25zZS5hcnJheUJ1ZmZlcigpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihpbnB1dCwgaW5pdCkge1xuICByZXR1cm4gZmV0Y2goaW5wdXQsIGluaXQpLnRoZW4ocmVzcG9uc2VBcnJheUJ1ZmZlcik7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/dsv.js":
/*!******************************************!*\
  !*** ./node_modules/d3-fetch/src/dsv.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   csv: () => (/* binding */ csv),\n/* harmony export */   \"default\": () => (/* binding */ dsv),\n/* harmony export */   tsv: () => (/* binding */ tsv)\n/* harmony export */ });\n/* harmony import */ var d3_dsv__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-dsv */ \"(ssr)/./node_modules/d3-dsv/src/dsv.js\");\n/* harmony import */ var d3_dsv__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-dsv */ \"(ssr)/./node_modules/d3-dsv/src/csv.js\");\n/* harmony import */ var d3_dsv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-dsv */ \"(ssr)/./node_modules/d3-dsv/src/tsv.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/d3-fetch/src/text.js\");\n\n\n\nfunction dsvParse(parse) {\n  return function(input, init, row) {\n    if (arguments.length === 2 && typeof init === \"function\") row = init, init = undefined;\n    return (0,_text_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(input, init).then(function(response) {\n      return parse(response, row);\n    });\n  };\n}\n\nfunction dsv(delimiter, input, init, row) {\n  if (arguments.length === 3 && typeof init === \"function\") row = init, init = undefined;\n  var format = (0,d3_dsv__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(delimiter);\n  return (0,_text_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(input, init).then(function(response) {\n    return format.parse(response, row);\n  });\n}\n\nvar csv = dsvParse(d3_dsv__WEBPACK_IMPORTED_MODULE_2__.csvParse);\nvar tsv = dsvParse(d3_dsv__WEBPACK_IMPORTED_MODULE_3__.tsvParse);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2Rzdi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXFEO0FBQ3hCOztBQUU3QjtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9EQUFJO0FBQ2Y7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFZTtBQUNmO0FBQ0EsZUFBZSxrREFBUztBQUN4QixTQUFTLG9EQUFJO0FBQ2I7QUFDQSxHQUFHO0FBQ0g7O0FBRU8sbUJBQW1CLDRDQUFRO0FBQzNCLG1CQUFtQiw0Q0FBUSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2Rzdi5qcz8yNDU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3N2UGFyc2UsIGRzdkZvcm1hdCwgdHN2UGFyc2V9IGZyb20gXCJkMy1kc3ZcIjtcbmltcG9ydCB0ZXh0IGZyb20gXCIuL3RleHQuanNcIjtcblxuZnVuY3Rpb24gZHN2UGFyc2UocGFyc2UpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKGlucHV0LCBpbml0LCByb3cpIHtcbiAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCA9PT0gMiAmJiB0eXBlb2YgaW5pdCA9PT0gXCJmdW5jdGlvblwiKSByb3cgPSBpbml0LCBpbml0ID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB0ZXh0KGlucHV0LCBpbml0KS50aGVuKGZ1bmN0aW9uKHJlc3BvbnNlKSB7XG4gICAgICByZXR1cm4gcGFyc2UocmVzcG9uc2UsIHJvdyk7XG4gICAgfSk7XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRzdihkZWxpbWl0ZXIsIGlucHV0LCBpbml0LCByb3cpIHtcbiAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPT09IDMgJiYgdHlwZW9mIGluaXQgPT09IFwiZnVuY3Rpb25cIikgcm93ID0gaW5pdCwgaW5pdCA9IHVuZGVmaW5lZDtcbiAgdmFyIGZvcm1hdCA9IGRzdkZvcm1hdChkZWxpbWl0ZXIpO1xuICByZXR1cm4gdGV4dChpbnB1dCwgaW5pdCkudGhlbihmdW5jdGlvbihyZXNwb25zZSkge1xuICAgIHJldHVybiBmb3JtYXQucGFyc2UocmVzcG9uc2UsIHJvdyk7XG4gIH0pO1xufVxuXG5leHBvcnQgdmFyIGNzdiA9IGRzdlBhcnNlKGNzdlBhcnNlKTtcbmV4cG9ydCB2YXIgdHN2ID0gZHN2UGFyc2UodHN2UGFyc2UpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/dsv.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/image.js":
/*!********************************************!*\
  !*** ./node_modules/d3-fetch/src/image.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(input, init) {\n  return new Promise(function(resolve, reject) {\n    var image = new Image;\n    for (var key in init) image[key] = init[key];\n    image.onerror = reject;\n    image.onload = function() { resolve(image); };\n    image.src = input;\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2ltYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQztBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2ltYWdlLmpzPzUyZmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oaW5wdXQsIGluaXQpIHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uKHJlc29sdmUsIHJlamVjdCkge1xuICAgIHZhciBpbWFnZSA9IG5ldyBJbWFnZTtcbiAgICBmb3IgKHZhciBrZXkgaW4gaW5pdCkgaW1hZ2Vba2V5XSA9IGluaXRba2V5XTtcbiAgICBpbWFnZS5vbmVycm9yID0gcmVqZWN0O1xuICAgIGltYWdlLm9ubG9hZCA9IGZ1bmN0aW9uKCkgeyByZXNvbHZlKGltYWdlKTsgfTtcbiAgICBpbWFnZS5zcmMgPSBpbnB1dDtcbiAgfSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-fetch/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blob: () => (/* reexport safe */ _blob_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   buffer: () => (/* reexport safe */ _buffer_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   csv: () => (/* reexport safe */ _dsv_js__WEBPACK_IMPORTED_MODULE_2__.csv),\n/* harmony export */   dsv: () => (/* reexport safe */ _dsv_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   html: () => (/* reexport safe */ _xml_js__WEBPACK_IMPORTED_MODULE_6__.html),\n/* harmony export */   image: () => (/* reexport safe */ _image_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   json: () => (/* reexport safe */ _json_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   svg: () => (/* reexport safe */ _xml_js__WEBPACK_IMPORTED_MODULE_6__.svg),\n/* harmony export */   text: () => (/* reexport safe */ _text_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   tsv: () => (/* reexport safe */ _dsv_js__WEBPACK_IMPORTED_MODULE_2__.tsv),\n/* harmony export */   xml: () => (/* reexport safe */ _xml_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _blob_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blob.js */ \"(ssr)/./node_modules/d3-fetch/src/blob.js\");\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/./node_modules/d3-fetch/src/buffer.js\");\n/* harmony import */ var _dsv_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dsv.js */ \"(ssr)/./node_modules/d3-fetch/src/dsv.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/d3-fetch/src/image.js\");\n/* harmony import */ var _json_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./json.js */ \"(ssr)/./node_modules/d3-fetch/src/json.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/d3-fetch/src/text.js\");\n/* harmony import */ var _xml_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./xml.js */ \"(ssr)/./node_modules/d3-fetch/src/xml.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQztBQUNJO0FBQ0k7QUFDTjtBQUNGO0FBQ0E7QUFDUyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2luZGV4LmpzP2VjYjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0IGFzIGJsb2J9IGZyb20gXCIuL2Jsb2IuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBidWZmZXJ9IGZyb20gXCIuL2J1ZmZlci5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIGRzdiwgY3N2LCB0c3Z9IGZyb20gXCIuL2Rzdi5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIGltYWdlfSBmcm9tIFwiLi9pbWFnZS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIGpzb259IGZyb20gXCIuL2pzb24uanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyB0ZXh0fSBmcm9tIFwiLi90ZXh0LmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgeG1sLCBodG1sLCBzdmd9IGZyb20gXCIuL3htbC5qc1wiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/json.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-fetch/src/json.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction responseJson(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  if (response.status === 204 || response.status === 205) return;\n  return response.json();\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(input, init) {\n  return fetch(input, init).then(responseJson);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2pzb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL2pzb24uanM/OTYzMyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiByZXNwb25zZUpzb24ocmVzcG9uc2UpIHtcbiAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLnN0YXR1cyArIFwiIFwiICsgcmVzcG9uc2Uuc3RhdHVzVGV4dCk7XG4gIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDIwNCB8fCByZXNwb25zZS5zdGF0dXMgPT09IDIwNSkgcmV0dXJuO1xuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihpbnB1dCwgaW5pdCkge1xuICByZXR1cm4gZmV0Y2goaW5wdXQsIGluaXQpLnRoZW4ocmVzcG9uc2VKc29uKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/text.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-fetch/src/text.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction responseText(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.text();\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(input, init) {\n  return fetch(input, init).then(responseText);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWZldGNoL3NyYy90ZXh0LmpzP2ZjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcmVzcG9uc2VUZXh0KHJlc3BvbnNlKSB7XG4gIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5zdGF0dXMgKyBcIiBcIiArIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuICByZXR1cm4gcmVzcG9uc2UudGV4dCgpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihpbnB1dCwgaW5pdCkge1xuICByZXR1cm4gZmV0Y2goaW5wdXQsIGluaXQpLnRoZW4ocmVzcG9uc2VUZXh0KTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-fetch/src/xml.js":
/*!******************************************!*\
  !*** ./node_modules/d3-fetch/src/xml.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/d3-fetch/src/text.js\");\n\n\nfunction parser(type) {\n  return (input, init) => (0,_text_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(input, init)\n    .then(text => (new DOMParser).parseFromString(text, type));\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parser(\"application/xml\"));\n\nvar html = parser(\"text/html\");\n\nvar svg = parser(\"image/svg+xml\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL3htbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZCOztBQUU3QjtBQUNBLDBCQUEwQixvREFBSTtBQUM5QjtBQUNBOztBQUVBLGlFQUFlLHlCQUF5QixFQUFDOztBQUVsQzs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtZmV0Y2gvc3JjL3htbC5qcz80YjBjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0ZXh0IGZyb20gXCIuL3RleHQuanNcIjtcblxuZnVuY3Rpb24gcGFyc2VyKHR5cGUpIHtcbiAgcmV0dXJuIChpbnB1dCwgaW5pdCkgPT4gdGV4dChpbnB1dCwgaW5pdClcbiAgICAudGhlbih0ZXh0ID0+IChuZXcgRE9NUGFyc2VyKS5wYXJzZUZyb21TdHJpbmcodGV4dCwgdHlwZSkpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBwYXJzZXIoXCJhcHBsaWNhdGlvbi94bWxcIik7XG5cbmV4cG9ydCB2YXIgaHRtbCA9IHBhcnNlcihcInRleHQvaHRtbFwiKTtcblxuZXhwb3J0IHZhciBzdmcgPSBwYXJzZXIoXCJpbWFnZS9zdmcreG1sXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-fetch/src/xml.js\n");

/***/ })

};
;