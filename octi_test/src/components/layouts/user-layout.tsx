/**
 * OCTI智能评估系统 - 用户端专用布局
 * 
 * 为普通用户提供简洁、专注的评估体验
 * 移除所有管理功能，只保留评估相关的导航
 */

import React from 'react';
import Link from 'next/link';
import { Brain, ArrowLeft, HelpCircle } from 'lucide-react';

interface UserLayoutProps {
  children: React.ReactNode;
  showBackButton?: boolean;
  backHref?: string;
  title?: string;
  subtitle?: string;
}

/**
 * 用户端专用布局组件
 * 提供简洁的头部导航和页脚
 */
export function UserLayout({ 
  children, 
  showBackButton = false, 
  backHref = '/',
  title,
  subtitle 
}: UserLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* 简洁的头部 */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* 左侧：返回按钮或Logo */}
            <div className="flex items-center space-x-4">
              {showBackButton ? (
                <Link 
                  href={backHref}
                  className="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                  <span>返回</span>
                </Link>
              ) : (
                <Link href="/" className="flex items-center space-x-2">
                  <Brain className="h-8 w-8 text-primary" />
                  <span className="text-xl font-bold text-gray-900">OCTI评估</span>
                </Link>
              )}
            </div>

            {/* 右侧：帮助链接 */}
            <div className="flex items-center space-x-4">
              <Link 
                href="/help" 
                className="flex items-center space-x-1 text-gray-600 hover:text-primary transition-colors"
              >
                <HelpCircle className="h-4 w-4" />
                <span className="text-sm">帮助</span>
              </Link>
            </div>
          </div>

          {/* 页面标题区域 */}
          {(title || subtitle) && (
            <div className="mt-4 text-center">
              {title && (
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {title}
                </h1>
              )}
              {subtitle && (
                <p className="text-gray-600">
                  {subtitle}
                </p>
              )}
            </div>
          )}
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-1">
        {children}
      </main>

      {/* 简洁的页脚 */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Logo和版权 */}
            <div className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-primary" />
              <span className="text-sm text-gray-600">
                © 2025 OCTI智能评估系统. 保留所有权利.
              </span>
            </div>

            {/* 帮助链接 */}
            <div className="flex items-center space-x-6 text-sm">
              <Link 
                href="/help" 
                className="text-gray-600 hover:text-primary transition-colors"
              >
                使用帮助
              </Link>
              <Link 
                href="/privacy" 
                className="text-gray-600 hover:text-primary transition-colors"
              >
                隐私政策
              </Link>
              <Link 
                href="/contact" 
                className="text-gray-600 hover:text-primary transition-colors"
              >
                联系我们
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

/**
 * 用户端页面容器组件
 * 提供标准的内容区域样式
 */
export function UserPageContainer({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={`container mx-auto px-4 py-8 max-w-4xl ${className}`}>
      {children}
    </div>
  );
}

/**
 * 评估步骤指示器组件
 */
interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps: string[];
}

export function StepIndicator({ currentStep, totalSteps, steps }: StepIndicatorProps) {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-center space-x-4">
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;
          
          return (
            <div key={index} className="flex items-center">
              {/* 步骤圆圈 */}
              <div className={`
                flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                ${isActive ? 'bg-primary text-white' : 
                  isCompleted ? 'bg-green-500 text-white' : 
                  'bg-gray-200 text-gray-600'}
              `}>
                {stepNumber}
              </div>
              
              {/* 步骤标签 */}
              <span className={`
                ml-2 text-sm font-medium
                ${isActive ? 'text-primary' : 
                  isCompleted ? 'text-green-600' : 
                  'text-gray-500'}
              `}>
                {step}
              </span>
              
              {/* 连接线 */}
              {index < steps.length - 1 && (
                <div className={`
                  w-8 h-0.5 mx-4
                  ${isCompleted ? 'bg-green-500' : 'bg-gray-200'}
                `} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
