"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vscode-languageserver-textdocument";
exports.ids = ["vendor-chunks/vscode-languageserver-textdocument"];
exports.modules = {

/***/ "(ssr)/./node_modules/vscode-languageserver-textdocument/lib/esm/main.js":
/*!*************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-textdocument/lib/esm/main.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextDocument: () => (/* binding */ TextDocument)\n/* harmony export */ });\n/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n\nclass FullTextDocument {\n    constructor(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    get uri() {\n        return this._uri;\n    }\n    get languageId() {\n        return this._languageId;\n    }\n    get version() {\n        return this._version;\n    }\n    getText(range) {\n        if (range) {\n            const start = this.offsetAt(range.start);\n            const end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    }\n    update(changes, version) {\n        for (const change of changes) {\n            if (FullTextDocument.isIncremental(change)) {\n                // makes sure start is before end\n                const range = getWellformedRange(change.range);\n                // update content\n                const startOffset = this.offsetAt(range.start);\n                const endOffset = this.offsetAt(range.end);\n                this._content = this._content.substring(0, startOffset) + change.text + this._content.substring(endOffset, this._content.length);\n                // update the offsets\n                const startLine = Math.max(range.start.line, 0);\n                const endLine = Math.max(range.end.line, 0);\n                let lineOffsets = this._lineOffsets;\n                const addedLineOffsets = computeLineOffsets(change.text, false, startOffset);\n                if (endLine - startLine === addedLineOffsets.length) {\n                    for (let i = 0, len = addedLineOffsets.length; i < len; i++) {\n                        lineOffsets[i + startLine + 1] = addedLineOffsets[i];\n                    }\n                }\n                else {\n                    if (addedLineOffsets.length < 10000) {\n                        lineOffsets.splice(startLine + 1, endLine - startLine, ...addedLineOffsets);\n                    }\n                    else { // avoid too many arguments for splice\n                        this._lineOffsets = lineOffsets = lineOffsets.slice(0, startLine + 1).concat(addedLineOffsets, lineOffsets.slice(endLine + 1));\n                    }\n                }\n                const diff = change.text.length - (endOffset - startOffset);\n                if (diff !== 0) {\n                    for (let i = startLine + 1 + addedLineOffsets.length, len = lineOffsets.length; i < len; i++) {\n                        lineOffsets[i] = lineOffsets[i] + diff;\n                    }\n                }\n            }\n            else if (FullTextDocument.isFull(change)) {\n                this._content = change.text;\n                this._lineOffsets = undefined;\n            }\n            else {\n                throw new Error('Unknown change event received');\n            }\n        }\n        this._version = version;\n    }\n    getLineOffsets() {\n        if (this._lineOffsets === undefined) {\n            this._lineOffsets = computeLineOffsets(this._content, true);\n        }\n        return this._lineOffsets;\n    }\n    positionAt(offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        const lineOffsets = this.getLineOffsets();\n        let low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return { line: 0, character: offset };\n        }\n        while (low < high) {\n            const mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        const line = low - 1;\n        offset = this.ensureBeforeEOL(offset, lineOffsets[line]);\n        return { line, character: offset - lineOffsets[line] };\n    }\n    offsetAt(position) {\n        const lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        const lineOffset = lineOffsets[position.line];\n        if (position.character <= 0) {\n            return lineOffset;\n        }\n        const nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        const offset = Math.min(lineOffset + position.character, nextLineOffset);\n        return this.ensureBeforeEOL(offset, lineOffset);\n    }\n    ensureBeforeEOL(offset, lineOffset) {\n        while (offset > lineOffset && isEOL(this._content.charCodeAt(offset - 1))) {\n            offset--;\n        }\n        return offset;\n    }\n    get lineCount() {\n        return this.getLineOffsets().length;\n    }\n    static isIncremental(event) {\n        const candidate = event;\n        return candidate !== undefined && candidate !== null &&\n            typeof candidate.text === 'string' && candidate.range !== undefined &&\n            (candidate.rangeLength === undefined || typeof candidate.rangeLength === 'number');\n    }\n    static isFull(event) {\n        const candidate = event;\n        return candidate !== undefined && candidate !== null &&\n            typeof candidate.text === 'string' && candidate.range === undefined && candidate.rangeLength === undefined;\n    }\n}\nvar TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new text document.\n     *\n     * @param uri The document's uri.\n     * @param languageId  The document's language Id.\n     * @param version The document's initial version number.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Updates a TextDocument by modifying its content.\n     *\n     * @param document the document to update. Only documents created by TextDocument.create are valid inputs.\n     * @param changes the changes to apply to the document.\n     * @param version the changes version for the document.\n     * @returns The updated TextDocument. Note: That's the same document instance passed in as first parameter.\n     *\n     */\n    function update(document, changes, version) {\n        if (document instanceof FullTextDocument) {\n            document.update(changes, version);\n            return document;\n        }\n        else {\n            throw new Error('TextDocument.update: document must be created by TextDocument.create');\n        }\n    }\n    TextDocument.update = update;\n    function applyEdits(document, edits) {\n        const text = document.getText();\n        const sortedEdits = mergeSort(edits.map(getWellformedEdit), (a, b) => {\n            const diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        let lastModifiedOffset = 0;\n        const spans = [];\n        for (const e of sortedEdits) {\n            const startOffset = document.offsetAt(e.range.start);\n            if (startOffset < lastModifiedOffset) {\n                throw new Error('Overlapping edit');\n            }\n            else if (startOffset > lastModifiedOffset) {\n                spans.push(text.substring(lastModifiedOffset, startOffset));\n            }\n            if (e.newText.length) {\n                spans.push(e.newText);\n            }\n            lastModifiedOffset = document.offsetAt(e.range.end);\n        }\n        spans.push(text.substr(lastModifiedOffset));\n        return spans.join('');\n    }\n    TextDocument.applyEdits = applyEdits;\n})(TextDocument || (TextDocument = {}));\nfunction mergeSort(data, compare) {\n    if (data.length <= 1) {\n        // sorted\n        return data;\n    }\n    const p = (data.length / 2) | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n        const ret = compare(left[leftIdx], right[rightIdx]);\n        if (ret <= 0) {\n            // smaller_equal -> take left to preserve order\n            data[i++] = left[leftIdx++];\n        }\n        else {\n            // greater -> take right\n            data[i++] = right[rightIdx++];\n        }\n    }\n    while (leftIdx < left.length) {\n        data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n        data[i++] = right[rightIdx++];\n    }\n    return data;\n}\nfunction computeLineOffsets(text, isAtLineStart, textOffset = 0) {\n    const result = isAtLineStart ? [textOffset] : [];\n    for (let i = 0; i < text.length; i++) {\n        const ch = text.charCodeAt(i);\n        if (isEOL(ch)) {\n            if (ch === 13 /* CharCode.CarriageReturn */ && i + 1 < text.length && text.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n                i++;\n            }\n            result.push(textOffset + i + 1);\n        }\n    }\n    return result;\n}\nfunction isEOL(char) {\n    return char === 13 /* CharCode.CarriageReturn */ || char === 10 /* CharCode.LineFeed */;\n}\nfunction getWellformedRange(range) {\n    const start = range.start;\n    const end = range.end;\n    if (start.line > end.line || (start.line === end.line && start.character > end.character)) {\n        return { start: end, end: start };\n    }\n    return range;\n}\nfunction getWellformedEdit(textEdit) {\n    const range = getWellformedRange(textEdit.range);\n    if (range !== textEdit.range) {\n        return { newText: textEdit.newText, range };\n    }\n    return textEdit;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vscode-languageserver-textdocument/lib/esm/main.js\n");

/***/ })

};
;