/**
 * LLM集成测试页面
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function TestLLMPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [version, setVersion] = useState<'standard' | 'professional'>('standard');

  const testAnalysis = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      // 模拟组织画像数据
      const mockProfile = {
        organizationType: '公益组织',
        serviceArea: '教育',
        resourceStructure: '基金会支持型',
        developmentStage: '成长期',
        teamSize: '中型（21-50人）',
        operatingModel: '直接服务型',
        impactScope: '区域影响',
        organizationCulture: '使命驱动',
        challengesPriorities: '资金筹集和团队建设',
        futureVision: '成为区域内教育公益的领导者'
      };

      // 模拟问卷回答数据
      const mockResponses = [
        { questionId: 'SF_P001', answer: 'very_clear' },
        { questionId: 'SF_P002', answer: 'good' },
        { questionId: 'IT_P001', answer: 4 },
        { questionId: 'MV_P001', answer: ['mission_training', 'value_sharing'] },
        { questionId: 'AD_P001', answer: 'excellent' }
      ];

      console.log('🧪 开始测试OCTI分析...', version);

      const response = await fetch('/api/assessment/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profile: mockProfile,
          responses: mockResponses,
          version: version
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || '分析失败');
      }

      setResult(data.data);
      console.log('✅ LLM分析测试成功');

    } catch (err) {
      console.error('❌ LLM分析测试失败:', err);
      setError(err instanceof Error ? err.message : '测试失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>OCTI专业分析测试</CardTitle>
            <div className="flex gap-2">
              <Link href="/test-intelligent-questionnaire">
                <Button variant="outline" size="sm">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  智能问卷测试
                </Button>
              </Link>
              <Link href="/test-full-flow">
                <Button variant="outline" size="sm">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  全流程测试
                </Button>
              </Link>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-sm text-muted-foreground">
            <p>此页面用于测试基于OCTI四维八极框架的专业分析功能。</p>
            <p>使用完整的organization_tutor_prompt.json配置和MiniMax长上下文能力。</p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">分析版本</label>
            <div className="flex space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="standard"
                  checked={version === 'standard'}
                  onChange={(e) => setVersion(e.target.value as 'standard')}
                />
                <span>标准版 (MiniMax)</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="professional"
                  checked={version === 'professional'}
                  onChange={(e) => setVersion(e.target.value as 'professional')}
                />
                <span>专业版 (MiniMax + DeepSeek)</span>
              </label>
            </div>
          </div>

          <Button
            onClick={testAnalysis}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                测试中...
              </>
            ) : (
              `开始测试OCTI ${version === 'standard' ? '标准版' : '专业版'}分析`
            )}
          </Button>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="font-semibold text-red-800 mb-2">测试失败</h3>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {result && (
            <div className="space-y-4">
              <h3 className="font-semibold text-green-800">测试成功！</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-800">总体得分</h4>
                  <div className="text-2xl font-bold text-blue-600">
                    {result.overallScore}
                  </div>
                  <div className="text-sm text-blue-600">{result.level}</div>
                </div>

                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-semibold text-green-800">维度数量</h4>
                  <div className="text-2xl font-bold text-green-600">
                    {result.dimensions?.length || 0}
                  </div>
                  <div className="text-sm text-green-600">个维度</div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h4 className="font-semibold mb-2">维度详情</h4>
                <div className="space-y-2">
                  {result.dimensions?.map((dim: any, index: number) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm">{dim.name}</span>
                      <span className="font-semibold">{dim.score}分</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">建议数量</h4>
                <div className="text-sm text-yellow-700">
                  共 {result.recommendations?.length || 0} 条发展建议
                </div>
              </div>

              <details className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <summary className="font-semibold cursor-pointer">查看完整结果</summary>
                <pre className="mt-2 text-xs overflow-auto">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
