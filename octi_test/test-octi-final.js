/**
 * 最终测试OCTI专业分析功能
 */

const testOCTIFinal = async (version = 'standard') => {
  console.log(`🧪 最终测试OCTI ${version}版分析功能...`);

  // 模拟真实的组织画像数据
  const realProfile = {
    organizationType: '成长期公益组织',
    serviceArea: '教育',
    resourceStructure: '基金会支持型',
    developmentStage: '成长期',
    teamSize: '中型（21-50人）',
    operatingModel: '直接服务型',
    impactScope: '区域影响',
    organizationCulture: '使命驱动',
    challengesPriorities: '资金筹集和团队建设',
    futureVision: '成为区域内教育公益的领导者'
  };

  // 模拟丰富的问卷回答数据
  const richResponses = [
    // SF维度 - 战略与财务
    { questionId: 'SF_P001', answer: 'very_clear' },
    { questionId: 'SF_P002', answer: 'good' },
    { questionId: 'SF_P003', answer: 4 },
    { questionId: 'SF_P004', answer: ['strategic_planning', 'resource_allocation'] },
    { questionId: 'SF_P005', answer: 'excellent' },
    
    // IT维度 - 影响力与透明度
    { questionId: 'IT_P001', answer: 'excellent' },
    { questionId: 'IT_P002', answer: 5 },
    { questionId: 'IT_P003', answer: ['stakeholder_engagement', 'transparency'] },
    { questionId: 'IT_P004', answer: 'good' },
    { questionId: 'IT_P005', answer: 4 },
    
    // MV维度 - 使命与价值观
    { questionId: 'MV_P001', answer: ['mission_training', 'value_sharing'] },
    { questionId: 'MV_P002', answer: 4 },
    { questionId: 'MV_P003', answer: 'very_clear' },
    { questionId: 'MV_P004', answer: 'excellent' },
    { questionId: 'MV_P005', answer: 5 },
    
    // AD维度 - 适应性与发展
    { questionId: 'AD_P001', answer: 'excellent' },
    { questionId: 'AD_P002', answer: 3 },
    { questionId: 'AD_P003', answer: ['innovation', 'learning'] },
    { questionId: 'AD_P004', answer: 'good' },
    { questionId: 'AD_P005', answer: 4 }
  ];

  try {
    const startTime = Date.now();
    
    const response = await fetch('http://localhost:3000/api/assessment/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        profile: realProfile,
        responses: richResponses,
        version: version
      }),
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`HTTP ${response.status}: ${errorData.error || errorData.details}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || '分析失败');
    }

    console.log(`✅ OCTI ${version}版分析测试成功！`);
    console.log(`⏱️  分析耗时: ${duration}ms`);
    console.log('📊 总体得分:', result.data.overallScore);
    console.log('🏆 评级:', result.data.level);
    console.log('📈 维度数量:', result.data.dimensions.length);
    console.log('💡 建议数量:', result.data.recommendations.length);

    // 显示OCTI四维详情
    console.log('\n📋 OCTI四维分析:');
    result.data.dimensions.forEach((dim, index) => {
      console.log(`${index + 1}. ${dim.name}: ${dim.score}分 (${dim.level})`);
      console.log(`   描述: ${dim.description}`);
      console.log(`   优势: ${dim.strengths.slice(0, 2).join(', ')}`);
      console.log(`   改进: ${dim.improvements.slice(0, 2).join(', ')}`);
      console.log('');
    });

    // 显示发展建议
    console.log('💡 发展建议:');
    result.data.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.title}`);
      console.log(`   ${rec.description}`);
      console.log(`   行动: ${rec.actions.slice(0, 2).join(', ')}`);
      console.log('');
    });

    return result.data;

  } catch (error) {
    console.error(`❌ OCTI ${version}版分析测试失败:`, error);
    throw error;
  }
};

// 运行完整测试
const runCompleteTest = async () => {
  try {
    console.log('🚀 开始OCTI专业分析功能完整测试\n');
    
    // 测试标准版
    await testOCTIFinal('standard');
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 测试专业版
    await testOCTIFinal('professional');
    
    console.log('\n🎉 所有OCTI分析测试通过！');
    console.log('✨ 系统已成功集成MiniMax长上下文AI分析能力');
    console.log('🎯 基于OCTI四维八极框架的专业分析功能正常工作');
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  }
};

// 如果直接运行此脚本
if (typeof window === 'undefined') {
  runCompleteTest()
    .then(() => {
      console.log('\n🎊 OCTI专业分析系统测试完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 系统测试失败:', error.message);
      process.exit(1);
    });
}

module.exports = { testOCTIFinal, runCompleteTest };
