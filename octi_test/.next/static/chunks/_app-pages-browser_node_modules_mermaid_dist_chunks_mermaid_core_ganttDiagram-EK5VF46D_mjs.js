/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_ganttDiagram-EK5VF46D_mjs"],{

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js":
/*!*****************************************************!*\
  !*** ./node_modules/dayjs/plugin/advancedFormat.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js":
/*!********************************************************!*\
  !*** ./node_modules/dayjs/plugin/customParseFormat.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},t=/(\\[[^[]*\\])|([-_:/.,()\\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\\d/,r=/\\d\\d/,i=/\\d\\d?/,o=/\\d*[^-_:/,()\\s\\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)};var f=function(e){return function(t){this[e]=+t}},h=[/[+-]\\d\\d:?(\\d\\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if(\"Z\"===e)return 0;var t=e.match(/([+-]|\\d\\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:\"+\"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?\"pm\":\"PM\");return n},c={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\\d{3}/,function(e){this.milliseconds=+e}],s:[i,f(\"seconds\")],ss:[i,f(\"seconds\")],m:[i,f(\"minutes\")],mm:[i,f(\"minutes\")],H:[i,f(\"hours\")],h:[i,f(\"hours\")],HH:[i,f(\"hours\")],hh:[i,f(\"hours\")],D:[i,f(\"day\")],DD:[r,f(\"day\")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\\[|\\]/g,\"\")===e&&(this.day=r)}],w:[i,f(\"week\")],ww:[r,f(\"week\")],M:[i,f(\"month\")],MM:[r,f(\"month\")],MMM:[o,function(e){var t=u(\"months\"),n=(u(\"monthsShort\")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u(\"months\").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\\d+/,f(\"year\")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\\d{4}/,f(\"year\")],Z:h,ZZ:h};function l(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,f=0;f<a;f+=1){var h=o[f],u=c[h],d=u&&u[0],l=u&&u[1];o[f]=l?{regex:d,parser:l}:h.replace(/^\\[|\\]$/g,\"\")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if(\"string\"==typeof i)r+=i.length;else{var s=i.regex,f=i.parser,h=e.slice(r),u=s.exec(h)[0];f.call(t,u),e=e.replace(u,\"\")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if(\"string\"==typeof a){var f=!0===o[2],h=!0===o[3],u=f||h,d=o[2];h&&(d=o[2]),s=this.$locale(),!f&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if([\"x\",\"X\"].indexOf(t)>-1)return new Date((\"X\"===t?1e3:1)*e);var i=l(t)(e),o=i.year,s=i.month,a=i.day,f=i.hours,h=i.minutes,u=i.seconds,d=i.milliseconds,c=i.zone,m=i.week,M=new Date,Y=a||(o||s?1:M.getDate()),p=o||M.getFullYear(),v=0;o&&!s||(v=s>0?s-1:M.getMonth());var D,w=f||0,g=h||0,y=u||0,L=d||0;return c?new Date(Date.UTC(p,v,Y,w,g,y,L+60*c.offset*1e3)):n?new Date(Date.UTC(p,v,Y,w,g,y,L)):(D=new Date(p,v,Y,w,g,y,L),m&&(D=r(D).week(m).toDate()),D)}catch(e){return new Date(\"\")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date(\"\")),s={}}else if(a instanceof Array)for(var c=a.length,m=1;m<=c;m+=1){o[1]=a[m-1];var M=n.apply(this,o);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}m===c&&(this.$d=new Date(\"\"))}else i.call(this,e)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js":
/*!**********************************************!*\
  !*** ./node_modules/dayjs/plugin/isoWeek.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e=\"day\";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf(\"year\"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,\"week\")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return\"isoweek\"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf(\"day\"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf(\"day\"):n.bind(this)(e,t)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNvV2Vlay5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLEtBQW9ELG9CQUFvQixDQUE4SCxDQUFDLGtCQUFrQixhQUFhLFlBQVksdUJBQXVCLGtCQUFrQixpQ0FBaUMsZUFBZSx5QkFBeUIsc0JBQXNCLHVCQUF1QiwrREFBK0Qsd0pBQXdKLDBCQUEwQiwwQkFBMEIsc0VBQXNFLGdCQUFnQix3QkFBd0Isa0NBQWtDLHlLQUF5SyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZGF5anMvcGx1Z2luL2lzb1dlZWsuanM/OWJlMCJdLCJzb3VyY2VzQ29udGVudCI6WyIhZnVuY3Rpb24oZSx0KXtcIm9iamVjdFwiPT10eXBlb2YgZXhwb3J0cyYmXCJ1bmRlZmluZWRcIiE9dHlwZW9mIG1vZHVsZT9tb2R1bGUuZXhwb3J0cz10KCk6XCJmdW5jdGlvblwiPT10eXBlb2YgZGVmaW5lJiZkZWZpbmUuYW1kP2RlZmluZSh0KTooZT1cInVuZGVmaW5lZFwiIT10eXBlb2YgZ2xvYmFsVGhpcz9nbG9iYWxUaGlzOmV8fHNlbGYpLmRheWpzX3BsdWdpbl9pc29XZWVrPXQoKX0odGhpcywoZnVuY3Rpb24oKXtcInVzZSBzdHJpY3RcIjt2YXIgZT1cImRheVwiO3JldHVybiBmdW5jdGlvbih0LGkscyl7dmFyIGE9ZnVuY3Rpb24odCl7cmV0dXJuIHQuYWRkKDQtdC5pc29XZWVrZGF5KCksZSl9LGQ9aS5wcm90b3R5cGU7ZC5pc29XZWVrWWVhcj1mdW5jdGlvbigpe3JldHVybiBhKHRoaXMpLnllYXIoKX0sZC5pc29XZWVrPWZ1bmN0aW9uKHQpe2lmKCF0aGlzLiR1dGlscygpLnUodCkpcmV0dXJuIHRoaXMuYWRkKDcqKHQtdGhpcy5pc29XZWVrKCkpLGUpO3ZhciBpLGQsbixvLHI9YSh0aGlzKSx1PShpPXRoaXMuaXNvV2Vla1llYXIoKSxkPXRoaXMuJHUsbj0oZD9zLnV0YzpzKSgpLnllYXIoaSkuc3RhcnRPZihcInllYXJcIiksbz00LW4uaXNvV2Vla2RheSgpLG4uaXNvV2Vla2RheSgpPjQmJihvKz03KSxuLmFkZChvLGUpKTtyZXR1cm4gci5kaWZmKHUsXCJ3ZWVrXCIpKzF9LGQuaXNvV2Vla2RheT1mdW5jdGlvbihlKXtyZXR1cm4gdGhpcy4kdXRpbHMoKS51KGUpP3RoaXMuZGF5KCl8fDc6dGhpcy5kYXkodGhpcy5kYXkoKSU3P2U6ZS03KX07dmFyIG49ZC5zdGFydE9mO2Quc3RhcnRPZj1mdW5jdGlvbihlLHQpe3ZhciBpPXRoaXMuJHV0aWxzKCkscz0hIWkudSh0KXx8dDtyZXR1cm5cImlzb3dlZWtcIj09PWkucChlKT9zP3RoaXMuZGF0ZSh0aGlzLmRhdGUoKS0odGhpcy5pc29XZWVrZGF5KCktMSkpLnN0YXJ0T2YoXCJkYXlcIik6dGhpcy5kYXRlKHRoaXMuZGF0ZSgpLTEtKHRoaXMuaXNvV2Vla2RheSgpLTEpKzcpLmVuZE9mKFwiZGF5XCIpOm4uYmluZCh0aGlzKShlLHQpfX19KSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-EK5VF46D.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-EK5VF46D.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-55PJQP7W.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/isoWeek.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js\");\n/* harmony import */ var dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/customParseFormat.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js\");\n/* harmony import */ var dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/advancedFormat.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n\n// src/diagrams/gantt/parser/gantt.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35, 36, 38, 40], $V1 = [1, 26], $V2 = [1, 27], $V3 = [1, 28], $V4 = [1, 29], $V5 = [1, 30], $V6 = [1, 31], $V7 = [1, 32], $V8 = [1, 33], $V9 = [1, 34], $Va = [1, 9], $Vb = [1, 10], $Vc = [1, 11], $Vd = [1, 12], $Ve = [1, 13], $Vf = [1, 14], $Vg = [1, 15], $Vh = [1, 16], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 23], $Vn = [1, 25], $Vo = [1, 35];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"gantt\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NL\": 10, \"weekday\": 11, \"weekday_monday\": 12, \"weekday_tuesday\": 13, \"weekday_wednesday\": 14, \"weekday_thursday\": 15, \"weekday_friday\": 16, \"weekday_saturday\": 17, \"weekday_sunday\": 18, \"weekend\": 19, \"weekend_friday\": 20, \"weekend_saturday\": 21, \"dateFormat\": 22, \"inclusiveEndDates\": 23, \"topAxis\": 24, \"axisFormat\": 25, \"tickInterval\": 26, \"excludes\": 27, \"includes\": 28, \"todayMarker\": 29, \"title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"section\": 36, \"clickStatement\": 37, \"taskTxt\": 38, \"taskData\": 39, \"click\": 40, \"callbackname\": 41, \"callbackargs\": 42, \"href\": 43, \"clickStatementDebug\": 44, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"gantt\", 6: \"EOF\", 8: \"SPACE\", 10: \"NL\", 12: \"weekday_monday\", 13: \"weekday_tuesday\", 14: \"weekday_wednesday\", 15: \"weekday_thursday\", 16: \"weekday_friday\", 17: \"weekday_saturday\", 18: \"weekday_sunday\", 20: \"weekend_friday\", 21: \"weekend_saturday\", 22: \"dateFormat\", 23: \"inclusiveEndDates\", 24: \"topAxis\", 25: \"axisFormat\", 26: \"tickInterval\", 27: \"excludes\", 28: \"includes\", 29: \"todayMarker\", 30: \"title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"section\", 38: \"taskTxt\", 39: \"taskData\", 40: \"click\", 41: \"callbackname\", 42: \"callbackargs\", 43: \"href\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [19, 1], [19, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 2], [37, 2], [37, 3], [37, 3], [37, 4], [37, 3], [37, 4], [37, 2], [44, 2], [44, 3], [44, 3], [44, 4], [44, 3], [44, 4], [44, 2]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setWeekday(\"monday\");\n          break;\n        case 9:\n          yy.setWeekday(\"tuesday\");\n          break;\n        case 10:\n          yy.setWeekday(\"wednesday\");\n          break;\n        case 11:\n          yy.setWeekday(\"thursday\");\n          break;\n        case 12:\n          yy.setWeekday(\"friday\");\n          break;\n        case 13:\n          yy.setWeekday(\"saturday\");\n          break;\n        case 14:\n          yy.setWeekday(\"sunday\");\n          break;\n        case 15:\n          yy.setWeekend(\"friday\");\n          break;\n        case 16:\n          yy.setWeekend(\"saturday\");\n          break;\n        case 17:\n          yy.setDateFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 18:\n          yy.enableInclusiveEndDates();\n          this.$ = $$[$0].substr(18);\n          break;\n        case 19:\n          yy.TopAxis();\n          this.$ = $$[$0].substr(8);\n          break;\n        case 20:\n          yy.setAxisFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 21:\n          yy.setTickInterval($$[$0].substr(13));\n          this.$ = $$[$0].substr(13);\n          break;\n        case 22:\n          yy.setExcludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 23:\n          yy.setIncludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 24:\n          yy.setTodayMarker($$[$0].substr(12));\n          this.$ = $$[$0].substr(12);\n          break;\n        case 27:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 28:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 29:\n        case 30:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 31:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 33:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n        case 34:\n          this.$ = $$[$0 - 1];\n          yy.setClickEvent($$[$0 - 1], $$[$0], null);\n          break;\n        case 35:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 36:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], null);\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);\n          yy.setLink($$[$0 - 3], $$[$0]);\n          break;\n        case 38:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0], null);\n          yy.setLink($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 39:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          yy.setLink($$[$0 - 3], $$[$0 - 2]);\n          break;\n        case 40:\n          this.$ = $$[$0 - 1];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 41:\n        case 47:\n          this.$ = $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 42:\n        case 43:\n        case 45:\n          this.$ = $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 44:\n        case 46:\n          this.$ = $$[$0 - 3] + \" \" + $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 36, 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 17]), o($V0, [2, 18]), o($V0, [2, 19]), o($V0, [2, 20]), o($V0, [2, 21]), o($V0, [2, 22]), o($V0, [2, 23]), o($V0, [2, 24]), o($V0, [2, 25]), o($V0, [2, 26]), o($V0, [2, 27]), { 32: [1, 37] }, { 34: [1, 38] }, o($V0, [2, 30]), o($V0, [2, 31]), o($V0, [2, 32]), { 39: [1, 39] }, o($V0, [2, 8]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), { 41: [1, 40], 43: [1, 41] }, o($V0, [2, 4]), o($V0, [2, 28]), o($V0, [2, 29]), o($V0, [2, 33]), o($V0, [2, 34], { 42: [1, 42], 43: [1, 43] }), o($V0, [2, 40], { 41: [1, 44] }), o($V0, [2, 35], { 43: [1, 45] }), o($V0, [2, 36]), o($V0, [2, 38], { 42: [1, 46] }), o($V0, [2, 37]), o($V0, [2, 39])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"open_directive\");\n            return \"open_directive\";\n            break;\n          case 1:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 3:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            return 10;\n            break;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            this.begin(\"href\");\n            break;\n          case 15:\n            this.popState();\n            break;\n          case 16:\n            return 43;\n            break;\n          case 17:\n            this.begin(\"callbackname\");\n            break;\n          case 18:\n            this.popState();\n            break;\n          case 19:\n            this.popState();\n            this.begin(\"callbackargs\");\n            break;\n          case 20:\n            return 41;\n            break;\n          case 21:\n            this.popState();\n            break;\n          case 22:\n            return 42;\n            break;\n          case 23:\n            this.begin(\"click\");\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return 40;\n            break;\n          case 26:\n            return 4;\n            break;\n          case 27:\n            return 22;\n            break;\n          case 28:\n            return 23;\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 25;\n            break;\n          case 31:\n            return 26;\n            break;\n          case 32:\n            return 28;\n            break;\n          case 33:\n            return 27;\n            break;\n          case 34:\n            return 29;\n            break;\n          case 35:\n            return 12;\n            break;\n          case 36:\n            return 13;\n            break;\n          case 37:\n            return 14;\n            break;\n          case 38:\n            return 15;\n            break;\n          case 39:\n            return 16;\n            break;\n          case 40:\n            return 17;\n            break;\n          case 41:\n            return 18;\n            break;\n          case 42:\n            return 20;\n            break;\n          case 43:\n            return 21;\n            break;\n          case 44:\n            return \"date\";\n            break;\n          case 45:\n            return 30;\n            break;\n          case 46:\n            return \"accDescription\";\n            break;\n          case 47:\n            return 36;\n            break;\n          case 48:\n            return 38;\n            break;\n          case 49:\n            return 39;\n            break;\n          case 50:\n            return \":\";\n            break;\n          case 51:\n            return 6;\n            break;\n          case 52:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%\\{)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:%%(?!\\{)*[^\\n]*)/i, /^(?:[^\\}]%%*[^\\n]*)/i, /^(?:%%*[^\\n]*[\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:%[^\\n]*)/i, /^(?:href[\\s]+[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:call[\\s]+)/i, /^(?:\\([\\s]*\\))/i, /^(?:\\()/i, /^(?:[^(]*)/i, /^(?:\\))/i, /^(?:[^)]*)/i, /^(?:click[\\s]+)/i, /^(?:[\\s\\n])/i, /^(?:[^\\s\\n]*)/i, /^(?:gantt\\b)/i, /^(?:dateFormat\\s[^#\\n;]+)/i, /^(?:inclusiveEndDates\\b)/i, /^(?:topAxis\\b)/i, /^(?:axisFormat\\s[^#\\n;]+)/i, /^(?:tickInterval\\s[^#\\n;]+)/i, /^(?:includes\\s[^#\\n;]+)/i, /^(?:excludes\\s[^#\\n;]+)/i, /^(?:todayMarker\\s[^\\n;]+)/i, /^(?:weekday\\s+monday\\b)/i, /^(?:weekday\\s+tuesday\\b)/i, /^(?:weekday\\s+wednesday\\b)/i, /^(?:weekday\\s+thursday\\b)/i, /^(?:weekday\\s+friday\\b)/i, /^(?:weekday\\s+saturday\\b)/i, /^(?:weekday\\s+sunday\\b)/i, /^(?:weekend\\s+friday\\b)/i, /^(?:weekend\\s+saturday\\b)/i, /^(?:\\d\\d\\d\\d-\\d\\d-\\d\\d\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accDescription\\s[^#\\n;]+)/i, /^(?:section\\s[^\\n]+)/i, /^(?:[^:\\n]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4], \"inclusive\": false }, \"acc_title\": { \"rules\": [2], \"inclusive\": false }, \"callbackargs\": { \"rules\": [21, 22], \"inclusive\": false }, \"callbackname\": { \"rules\": [18, 19, 20], \"inclusive\": false }, \"href\": { \"rules\": [15, 16], \"inclusive\": false }, \"click\": { \"rules\": [24, 25], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 17, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar gantt_default = parser;\n\n// src/diagrams/gantt/ganttDb.js\n\n\n\n\n\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_4__);\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_5__);\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_6__);\nvar WEEKEND_START_DAY = { friday: 5, saturday: 6 };\nvar dateFormat = \"\";\nvar axisFormat = \"\";\nvar tickInterval = void 0;\nvar todayMarker = \"\";\nvar includes = [];\nvar excludes = [];\nvar links = /* @__PURE__ */ new Map();\nvar sections = [];\nvar tasks = [];\nvar currentSection = \"\";\nvar displayMode = \"\";\nvar tags = [\"active\", \"done\", \"crit\", \"milestone\", \"vert\"];\nvar funs = [];\nvar inclusiveEndDates = false;\nvar topAxis = false;\nvar weekday = \"sunday\";\nvar weekend = \"saturday\";\nvar lastOrder = 0;\nvar clear2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  sections = [];\n  tasks = [];\n  currentSection = \"\";\n  funs = [];\n  taskCnt = 0;\n  lastTask = void 0;\n  lastTaskID = void 0;\n  rawTasks = [];\n  dateFormat = \"\";\n  axisFormat = \"\";\n  displayMode = \"\";\n  tickInterval = void 0;\n  todayMarker = \"\";\n  includes = [];\n  excludes = [];\n  inclusiveEndDates = false;\n  topAxis = false;\n  lastOrder = 0;\n  links = /* @__PURE__ */ new Map();\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.clear)();\n  weekday = \"sunday\";\n  weekend = \"saturday\";\n}, \"clear\");\nvar setAxisFormat = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  axisFormat = txt;\n}, \"setAxisFormat\");\nvar getAxisFormat = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return axisFormat;\n}, \"getAxisFormat\");\nvar setTickInterval = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  tickInterval = txt;\n}, \"setTickInterval\");\nvar getTickInterval = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return tickInterval;\n}, \"getTickInterval\");\nvar setTodayMarker = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  todayMarker = txt;\n}, \"setTodayMarker\");\nvar getTodayMarker = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return todayMarker;\n}, \"getTodayMarker\");\nvar setDateFormat = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  dateFormat = txt;\n}, \"setDateFormat\");\nvar enableInclusiveEndDates = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  inclusiveEndDates = true;\n}, \"enableInclusiveEndDates\");\nvar endDatesAreInclusive = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return inclusiveEndDates;\n}, \"endDatesAreInclusive\");\nvar enableTopAxis = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  topAxis = true;\n}, \"enableTopAxis\");\nvar topAxisEnabled = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return topAxis;\n}, \"topAxisEnabled\");\nvar setDisplayMode = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  displayMode = txt;\n}, \"setDisplayMode\");\nvar getDisplayMode = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return displayMode;\n}, \"getDisplayMode\");\nvar getDateFormat = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return dateFormat;\n}, \"getDateFormat\");\nvar setIncludes = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  includes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setIncludes\");\nvar getIncludes = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return includes;\n}, \"getIncludes\");\nvar setExcludes = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  excludes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setExcludes\");\nvar getExcludes = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return excludes;\n}, \"getExcludes\");\nvar getLinks = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return links;\n}, \"getLinks\");\nvar addSection = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 10;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks = rawTasks;\n  return tasks;\n}, \"getTasks\");\nvar isInvalidDate = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(date, dateFormat2, excludes2, includes2) {\n  if (includes2.includes(date.format(dateFormat2.trim()))) {\n    return false;\n  }\n  if (excludes2.includes(\"weekends\") && (date.isoWeekday() === WEEKEND_START_DAY[weekend] || date.isoWeekday() === WEEKEND_START_DAY[weekend] + 1)) {\n    return true;\n  }\n  if (excludes2.includes(date.format(\"dddd\").toLowerCase())) {\n    return true;\n  }\n  return excludes2.includes(date.format(dateFormat2.trim()));\n}, \"isInvalidDate\");\nvar setWeekday = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  weekday = txt;\n}, \"setWeekday\");\nvar getWeekday = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return weekday;\n}, \"getWeekday\");\nvar setWeekend = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startDay) {\n  weekend = startDay;\n}, \"setWeekend\");\nvar checkTaskDates = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(task, dateFormat2, excludes2, includes2) {\n  if (!excludes2.length || task.manualEndTime) {\n    return;\n  }\n  let startTime;\n  if (task.startTime instanceof Date) {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.startTime);\n  } else {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.startTime, dateFormat2, true);\n  }\n  startTime = startTime.add(1, \"d\");\n  let originalEndTime;\n  if (task.endTime instanceof Date) {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.endTime);\n  } else {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.endTime, dateFormat2, true);\n  }\n  const [fixedEndTime, renderEndTime] = fixTaskDates(\n    startTime,\n    originalEndTime,\n    dateFormat2,\n    excludes2,\n    includes2\n  );\n  task.endTime = fixedEndTime.toDate();\n  task.renderEndTime = renderEndTime;\n}, \"checkTaskDates\");\nvar fixTaskDates = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startTime, endTime, dateFormat2, excludes2, includes2) {\n  let invalid = false;\n  let renderEndTime = null;\n  while (startTime <= endTime) {\n    if (!invalid) {\n      renderEndTime = endTime.toDate();\n    }\n    invalid = isInvalidDate(startTime, dateFormat2, excludes2, includes2);\n    if (invalid) {\n      endTime = endTime.add(1, \"d\");\n    }\n    startTime = startTime.add(1, \"d\");\n  }\n  return [endTime, renderEndTime];\n}, \"fixTaskDates\");\nvar getStartDate = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTime, dateFormat2, str) {\n  str = str.trim();\n  const afterRePattern = /^after\\s+(?<ids>[\\d\\w- ]+)/;\n  const afterStatement = afterRePattern.exec(str);\n  if (afterStatement !== null) {\n    let latestTask = null;\n    for (const id of afterStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!latestTask || task.endTime > latestTask.endTime)) {\n        latestTask = task;\n      }\n    }\n    if (latestTask) {\n      return latestTask.endTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let mDate = dayjs__WEBPACK_IMPORTED_MODULE_3__(str, dateFormat2.trim(), true);\n  if (mDate.isValid()) {\n    return mDate.toDate();\n  } else {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"Invalid date:\" + str);\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"With date format:\" + dateFormat2.trim());\n    const d = new Date(str);\n    if (d === void 0 || isNaN(d.getTime()) || // WebKit browsers can mis-parse invalid dates to be ridiculously\n    // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.\n    // This can cause virtually infinite loops while rendering, so for the\n    // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as\n    // invalid.\n    d.getFullYear() < -1e4 || d.getFullYear() > 1e4) {\n      throw new Error(\"Invalid date:\" + str);\n    }\n    return d;\n  }\n}, \"getStartDate\");\nvar parseDuration = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(str) {\n  const statement = /^(\\d+(?:\\.\\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());\n  if (statement !== null) {\n    return [Number.parseFloat(statement[1]), statement[2]];\n  }\n  return [NaN, \"ms\"];\n}, \"parseDuration\");\nvar getEndDate = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTime, dateFormat2, str, inclusive = false) {\n  str = str.trim();\n  const untilRePattern = /^until\\s+(?<ids>[\\d\\w- ]+)/;\n  const untilStatement = untilRePattern.exec(str);\n  if (untilStatement !== null) {\n    let earliestTask = null;\n    for (const id of untilStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!earliestTask || task.startTime < earliestTask.startTime)) {\n        earliestTask = task;\n      }\n    }\n    if (earliestTask) {\n      return earliestTask.startTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let parsedDate = dayjs__WEBPACK_IMPORTED_MODULE_3__(str, dateFormat2.trim(), true);\n  if (parsedDate.isValid()) {\n    if (inclusive) {\n      parsedDate = parsedDate.add(1, \"d\");\n    }\n    return parsedDate.toDate();\n  }\n  let endTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(prevTime);\n  const [durationValue, durationUnit] = parseDuration(str);\n  if (!Number.isNaN(durationValue)) {\n    const newEndTime = endTime.add(durationValue, durationUnit);\n    if (newEndTime.isValid()) {\n      endTime = newEndTime;\n    }\n  }\n  return endTime.toDate();\n}, \"getEndDate\");\nvar taskCnt = 0;\nvar parseId = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(idStr) {\n  if (idStr === void 0) {\n    taskCnt = taskCnt + 1;\n    return \"task\" + taskCnt;\n  }\n  return idStr;\n}, \"parseId\");\nvar compileData = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTask, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  let endTimeData = \"\";\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = prevTask.endTime;\n      endTimeData = data[0];\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = getStartDate(void 0, dateFormat, data[0]);\n      endTimeData = data[1];\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = getStartDate(void 0, dateFormat, data[1]);\n      endTimeData = data[2];\n      break;\n    default:\n  }\n  if (endTimeData) {\n    task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);\n    task.manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(endTimeData, \"YYYY-MM-DD\", true).isValid();\n    checkTaskDates(task, dateFormat, excludes, includes);\n  }\n  return task;\n}, \"compileData\");\nvar parseData = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTaskId, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = {\n        type: \"prevTaskEnd\",\n        id: prevTaskId\n      };\n      task.endTime = {\n        data: data[0]\n      };\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[0]\n      };\n      task.endTime = {\n        data: data[1]\n      };\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[1]\n      };\n      task.endTime = {\n        data: data[2]\n      };\n      break;\n    default:\n  }\n  return task;\n}, \"parseData\");\nvar lastTask;\nvar lastTaskID;\nvar rawTasks = [];\nvar taskDb = {};\nvar addTask = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, data) {\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    processed: false,\n    manualEndTime: false,\n    renderEndTime: null,\n    raw: { data },\n    task: descr,\n    classes: []\n  };\n  const taskInfo = parseData(lastTaskID, data);\n  rawTask.raw.startTime = taskInfo.startTime;\n  rawTask.raw.endTime = taskInfo.endTime;\n  rawTask.id = taskInfo.id;\n  rawTask.prevTaskId = lastTaskID;\n  rawTask.active = taskInfo.active;\n  rawTask.done = taskInfo.done;\n  rawTask.crit = taskInfo.crit;\n  rawTask.milestone = taskInfo.milestone;\n  rawTask.vert = taskInfo.vert;\n  rawTask.order = lastOrder;\n  lastOrder++;\n  const pos = rawTasks.push(rawTask);\n  lastTaskID = rawTask.id;\n  taskDb[rawTask.id] = pos - 1;\n}, \"addTask\");\nvar findTaskById = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id) {\n  const pos = taskDb[id];\n  return rawTasks[pos];\n}, \"findTaskById\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, data) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  const taskInfo = compileData(lastTask, data);\n  newTask.startTime = taskInfo.startTime;\n  newTask.endTime = taskInfo.endTime;\n  newTask.id = taskInfo.id;\n  newTask.active = taskInfo.active;\n  newTask.done = taskInfo.done;\n  newTask.crit = taskInfo.crit;\n  newTask.milestone = taskInfo.milestone;\n  newTask.vert = taskInfo.vert;\n  lastTask = newTask;\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(pos) {\n    const task = rawTasks[pos];\n    let startTime = \"\";\n    switch (rawTasks[pos].raw.startTime.type) {\n      case \"prevTaskEnd\": {\n        const prevTask = findTaskById(task.prevTaskId);\n        task.startTime = prevTask.endTime;\n        break;\n      }\n      case \"getStartDate\":\n        startTime = getStartDate(void 0, dateFormat, rawTasks[pos].raw.startTime.startData);\n        if (startTime) {\n          rawTasks[pos].startTime = startTime;\n        }\n        break;\n    }\n    if (rawTasks[pos].startTime) {\n      rawTasks[pos].endTime = getEndDate(\n        rawTasks[pos].startTime,\n        dateFormat,\n        rawTasks[pos].raw.endTime.data,\n        inclusiveEndDates\n      );\n      if (rawTasks[pos].endTime) {\n        rawTasks[pos].processed = true;\n        rawTasks[pos].manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(\n          rawTasks[pos].raw.endTime.data,\n          \"YYYY-MM-DD\",\n          true\n        ).isValid();\n        checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);\n      }\n    }\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar setLink = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, _linkStr) {\n  let linkStr = _linkStr;\n  if ((0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel !== \"loose\") {\n    linkStr = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_2__.sanitizeUrl)(_linkStr);\n  }\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      pushFun(id, () => {\n        window.open(linkStr, \"_self\");\n      });\n      links.set(id, linkStr);\n    }\n  });\n  setClass(ids, \"clickable\");\n}, \"setLink\");\nvar setClass = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, className) {\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      rawTask.classes.push(className);\n    }\n  });\n}, \"setClass\");\nvar setClickFun = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id, functionName, functionArgs) {\n  if ((0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel !== \"loose\") {\n    return;\n  }\n  if (functionName === void 0) {\n    return;\n  }\n  let argList = [];\n  if (typeof functionArgs === \"string\") {\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      if (item.startsWith('\"') && item.endsWith('\"')) {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n  let rawTask = findTaskById(id);\n  if (rawTask !== void 0) {\n    pushFun(id, () => {\n      _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_0__.utils_default.runFunc(functionName, ...argList);\n    });\n  }\n}, \"setClickFun\");\nvar pushFun = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id, callbackFunction) {\n  funs.push(\n    function() {\n      const elem = document.querySelector(`[id=\"${id}\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    },\n    function() {\n      const elem = document.querySelector(`[id=\"${id}-text\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    }\n  );\n}, \"pushFun\");\nvar setClickEvent = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, functionName, functionArgs) {\n  ids.split(\",\").forEach(function(id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, \"clickable\");\n}, \"setClickEvent\");\nvar bindFunctions = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element) {\n  funs.forEach(function(fun) {\n    fun(element);\n  });\n}, \"bindFunctions\");\nvar ganttDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(() => (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().gantt, \"getConfig\"),\n  clear: clear2,\n  setDateFormat,\n  getDateFormat,\n  enableInclusiveEndDates,\n  endDatesAreInclusive,\n  enableTopAxis,\n  topAxisEnabled,\n  setAxisFormat,\n  getAxisFormat,\n  setTickInterval,\n  getTickInterval,\n  setTodayMarker,\n  getTodayMarker,\n  setAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccTitle,\n  getAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccTitle,\n  setDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.setDiagramTitle,\n  getDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getDiagramTitle,\n  setDisplayMode,\n  getDisplayMode,\n  setAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccDescription,\n  getAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  findTaskById,\n  addTaskOrg,\n  setIncludes,\n  getIncludes,\n  setExcludes,\n  getExcludes,\n  setClickEvent,\n  setLink,\n  getLinks,\n  bindFunctions,\n  parseDuration,\n  isInvalidDate,\n  setWeekday,\n  getWeekday,\n  setWeekend\n};\nfunction getTaskTags(data, task, tags2) {\n  let matchFound = true;\n  while (matchFound) {\n    matchFound = false;\n    tags2.forEach(function(t) {\n      const pattern = \"^\\\\s*\" + t + \"\\\\s*$\";\n      const regex = new RegExp(pattern);\n      if (data[0].match(regex)) {\n        task[t] = true;\n        data.shift(1);\n        matchFound = true;\n      }\n    });\n  }\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(getTaskTags, \"getTaskTags\");\n\n// src/diagrams/gantt/ganttRenderer.js\n\n\nvar setConf = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"Something is calling, setConf, remove the call\");\n}, \"setConf\");\nvar mapWeekdayToTimeFunction = {\n  monday: d3__WEBPACK_IMPORTED_MODULE_7__.timeMonday,\n  tuesday: d3__WEBPACK_IMPORTED_MODULE_7__.timeTuesday,\n  wednesday: d3__WEBPACK_IMPORTED_MODULE_7__.timeWednesday,\n  thursday: d3__WEBPACK_IMPORTED_MODULE_7__.timeThursday,\n  friday: d3__WEBPACK_IMPORTED_MODULE_7__.timeFriday,\n  saturday: d3__WEBPACK_IMPORTED_MODULE_7__.timeSaturday,\n  sunday: d3__WEBPACK_IMPORTED_MODULE_7__.timeSunday\n};\nvar getMaxIntersections = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((tasks2, orderOffset) => {\n  let timeline = [...tasks2].map(() => -Infinity);\n  let sorted = [...tasks2].sort((a, b) => a.startTime - b.startTime || a.order - b.order);\n  let maxIntersections = 0;\n  for (const element of sorted) {\n    for (let j = 0; j < timeline.length; j++) {\n      if (element.startTime >= timeline[j]) {\n        timeline[j] = element.endTime;\n        element.order = j + orderOffset;\n        if (j > maxIntersections) {\n          maxIntersections = j;\n        }\n        break;\n      }\n    }\n  }\n  return maxIntersections;\n}, \"getMaxIntersections\");\nvar w;\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(text, id, version, diagObj) {\n  const conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().gantt;\n  const securityLevel = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  const elem = doc.getElementById(id);\n  w = elem.parentElement.offsetWidth;\n  if (w === void 0) {\n    w = 1200;\n  }\n  if (conf.useWidth !== void 0) {\n    w = conf.useWidth;\n  }\n  const taskArray = diagObj.db.getTasks();\n  let categories = [];\n  for (const element of taskArray) {\n    categories.push(element.type);\n  }\n  categories = checkUnique(categories);\n  const categoryHeights = {};\n  let h = 2 * conf.topPadding;\n  if (diagObj.db.getDisplayMode() === \"compact\" || conf.displayMode === \"compact\") {\n    const categoryElements = {};\n    for (const element of taskArray) {\n      if (categoryElements[element.section] === void 0) {\n        categoryElements[element.section] = [element];\n      } else {\n        categoryElements[element.section].push(element);\n      }\n    }\n    let intersections = 0;\n    for (const category of Object.keys(categoryElements)) {\n      const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;\n      intersections += categoryHeight;\n      h += categoryHeight * (conf.barHeight + conf.barGap);\n      categoryHeights[category] = categoryHeight;\n    }\n  } else {\n    h += taskArray.length * (conf.barHeight + conf.barGap);\n    for (const category of categories) {\n      categoryHeights[category] = taskArray.filter((task) => task.type === category).length;\n    }\n  }\n  elem.setAttribute(\"viewBox\", \"0 0 \" + w + \" \" + h);\n  const svg = root.select(`[id=\"${id}\"]`);\n  const timeScale = (0,d3__WEBPACK_IMPORTED_MODULE_7__.scaleTime)().domain([\n    (0,d3__WEBPACK_IMPORTED_MODULE_7__.min)(taskArray, function(d) {\n      return d.startTime;\n    }),\n    (0,d3__WEBPACK_IMPORTED_MODULE_7__.max)(taskArray, function(d) {\n      return d.endTime;\n    })\n  ]).rangeRound([0, w - conf.leftPadding - conf.rightPadding]);\n  function taskCompare(a, b) {\n    const taskA = a.startTime;\n    const taskB = b.startTime;\n    let result = 0;\n    if (taskA > taskB) {\n      result = 1;\n    } else if (taskA < taskB) {\n      result = -1;\n    }\n    return result;\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(taskCompare, \"taskCompare\");\n  taskArray.sort(taskCompare);\n  makeGantt(taskArray, w, h);\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.configureSvgSize)(svg, h, w, conf.useMaxWidth);\n  svg.append(\"text\").text(diagObj.db.getDiagramTitle()).attr(\"x\", w / 2).attr(\"y\", conf.titleTopMargin).attr(\"class\", \"titleText\");\n  function makeGantt(tasks2, pageWidth, pageHeight) {\n    const barHeight = conf.barHeight;\n    const gap = barHeight + conf.barGap;\n    const topPadding = conf.topPadding;\n    const leftPadding = conf.leftPadding;\n    const colorScale = (0,d3__WEBPACK_IMPORTED_MODULE_7__.scaleLinear)().domain([0, categories.length]).range([\"#00B9FA\", \"#F95002\"]).interpolate(d3__WEBPACK_IMPORTED_MODULE_7__.interpolateHcl);\n    drawExcludeDays(\n      gap,\n      topPadding,\n      leftPadding,\n      pageWidth,\n      pageHeight,\n      tasks2,\n      diagObj.db.getExcludes(),\n      diagObj.db.getIncludes()\n    );\n    makeGrid(leftPadding, topPadding, pageWidth, pageHeight);\n    drawRects(tasks2, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth, pageHeight);\n    vertLabels(gap, topPadding, leftPadding, barHeight, colorScale);\n    drawToday(leftPadding, topPadding, pageWidth, pageHeight);\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(makeGantt, \"makeGantt\");\n  function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w2) {\n    theArray.sort((a, b) => a.vert === b.vert ? 0 : a.vert ? 1 : -1);\n    const uniqueTaskOrderIds = [...new Set(theArray.map((item) => item.order))];\n    const uniqueTasks = uniqueTaskOrderIds.map((id2) => theArray.find((item) => item.order === id2));\n    svg.append(\"g\").selectAll(\"rect\").data(uniqueTasks).enter().append(\"rect\").attr(\"x\", 0).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad - 2;\n    }).attr(\"width\", function() {\n      return w2 - conf.rightPadding / 2;\n    }).attr(\"height\", theGap).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          return \"section section\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"section section0\";\n    }).enter();\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(theArray).enter();\n    const links2 = diagObj.db.getLinks();\n    rectangles.append(\"rect\").attr(\"id\", function(d) {\n      return d.id;\n    }).attr(\"rx\", 3).attr(\"ry\", 3).attr(\"x\", function(d) {\n      if (d.milestone) {\n        return timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      return timeScale(d.startTime) + theSidePad;\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      if (d.vert) {\n        return conf.gridLineStartPadding;\n      }\n      return i * theGap + theTopPad;\n    }).attr(\"width\", function(d) {\n      if (d.milestone) {\n        return theBarHeight;\n      }\n      if (d.vert) {\n        return 0.08 * theBarHeight;\n      }\n      return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);\n    }).attr(\"height\", function(d) {\n      if (d.vert) {\n        return taskArray.length * (conf.barHeight + conf.barGap) + conf.barHeight * 2;\n      }\n      return theBarHeight;\n    }).attr(\"transform-origin\", function(d, i) {\n      i = d.order;\n      return (timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime))).toString() + \"px \" + (i * theGap + theTopPad + 0.5 * theBarHeight).toString() + \"px\";\n    }).attr(\"class\", function(d) {\n      const res = \"task\";\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskClass = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskClass += \" activeCrit\";\n        } else {\n          taskClass = \" active\";\n        }\n      } else if (d.done) {\n        if (d.crit) {\n          taskClass = \" doneCrit\";\n        } else {\n          taskClass = \" done\";\n        }\n      } else {\n        if (d.crit) {\n          taskClass += \" crit\";\n        }\n      }\n      if (taskClass.length === 0) {\n        taskClass = \" task\";\n      }\n      if (d.milestone) {\n        taskClass = \" milestone \" + taskClass;\n      }\n      if (d.vert) {\n        taskClass = \" vert \" + taskClass;\n      }\n      taskClass += secNum;\n      taskClass += \" \" + classStr;\n      return res + taskClass;\n    });\n    rectangles.append(\"text\").attr(\"id\", function(d) {\n      return d.id + \"-text\";\n    }).text(function(d) {\n      return d.task;\n    }).attr(\"font-size\", conf.fontSize).attr(\"x\", function(d) {\n      let startX = timeScale(d.startTime);\n      let endX = timeScale(d.renderEndTime || d.endTime);\n      if (d.milestone) {\n        startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n        endX = startX + theBarHeight;\n      }\n      if (d.vert) {\n        return timeScale(d.startTime) + theSidePad;\n      }\n      const textWidth = this.getBBox().width;\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return startX + theSidePad - 5;\n        } else {\n          return endX + theSidePad + 5;\n        }\n      } else {\n        return (endX - startX) / 2 + startX + theSidePad;\n      }\n    }).attr(\"y\", function(d, i) {\n      if (d.vert) {\n        return conf.gridLineStartPadding + taskArray.length * (conf.barHeight + conf.barGap) + 60;\n      }\n      i = d.order;\n      return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;\n    }).attr(\"text-height\", theBarHeight).attr(\"class\", function(d) {\n      const startX = timeScale(d.startTime);\n      let endX = timeScale(d.endTime);\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskType = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskType = \"activeCritText\" + secNum;\n        } else {\n          taskType = \"activeText\" + secNum;\n        }\n      }\n      if (d.done) {\n        if (d.crit) {\n          taskType = taskType + \" doneCritText\" + secNum;\n        } else {\n          taskType = taskType + \" doneText\" + secNum;\n        }\n      } else {\n        if (d.crit) {\n          taskType = taskType + \" critText\" + secNum;\n        }\n      }\n      if (d.milestone) {\n        taskType += \" milestoneText\";\n      }\n      if (d.vert) {\n        taskType += \" vertText\";\n      }\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return classStr + \" taskTextOutsideLeft taskTextOutside\" + secNum + \" \" + taskType;\n        } else {\n          return classStr + \" taskTextOutsideRight taskTextOutside\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n        }\n      } else {\n        return classStr + \" taskText taskText\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n      }\n    });\n    const securityLevel2 = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n    if (securityLevel2 === \"sandbox\") {\n      let sandboxElement2;\n      sandboxElement2 = (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"#i\" + id);\n      const doc2 = sandboxElement2.nodes()[0].contentDocument;\n      rectangles.filter(function(d) {\n        return links2.has(d.id);\n      }).each(function(o) {\n        var taskRect = doc2.querySelector(\"#\" + o.id);\n        var taskText = doc2.querySelector(\"#\" + o.id + \"-text\");\n        const oldParent = taskRect.parentNode;\n        var Link = doc2.createElement(\"a\");\n        Link.setAttribute(\"xlink:href\", links2.get(o.id));\n        Link.setAttribute(\"target\", \"_top\");\n        oldParent.appendChild(Link);\n        Link.appendChild(taskRect);\n        Link.appendChild(taskText);\n      });\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawRects, \"drawRects\");\n  function drawExcludeDays(theGap, theTopPad, theSidePad, w2, h2, tasks2, excludes2, includes2) {\n    if (excludes2.length === 0 && includes2.length === 0) {\n      return;\n    }\n    let minTime;\n    let maxTime;\n    for (const { startTime, endTime } of tasks2) {\n      if (minTime === void 0 || startTime < minTime) {\n        minTime = startTime;\n      }\n      if (maxTime === void 0 || endTime > maxTime) {\n        maxTime = endTime;\n      }\n    }\n    if (!minTime || !maxTime) {\n      return;\n    }\n    if (dayjs__WEBPACK_IMPORTED_MODULE_3__(maxTime).diff(dayjs__WEBPACK_IMPORTED_MODULE_3__(minTime), \"year\") > 5) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.log.warn(\n        \"The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.\"\n      );\n      return;\n    }\n    const dateFormat2 = diagObj.db.getDateFormat();\n    const excludeRanges = [];\n    let range = null;\n    let d = dayjs__WEBPACK_IMPORTED_MODULE_3__(minTime);\n    while (d.valueOf() <= maxTime) {\n      if (diagObj.db.isInvalidDate(d, dateFormat2, excludes2, includes2)) {\n        if (!range) {\n          range = {\n            start: d,\n            end: d\n          };\n        } else {\n          range.end = d;\n        }\n      } else {\n        if (range) {\n          excludeRanges.push(range);\n          range = null;\n        }\n      }\n      d = d.add(1, \"d\");\n    }\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(excludeRanges).enter();\n    rectangles.append(\"rect\").attr(\"id\", function(d2) {\n      return \"exclude-\" + d2.start.format(\"YYYY-MM-DD\");\n    }).attr(\"x\", function(d2) {\n      return timeScale(d2.start) + theSidePad;\n    }).attr(\"y\", conf.gridLineStartPadding).attr(\"width\", function(d2) {\n      const renderEnd = d2.end.add(1, \"day\");\n      return timeScale(renderEnd) - timeScale(d2.start);\n    }).attr(\"height\", h2 - theTopPad - conf.gridLineStartPadding).attr(\"transform-origin\", function(d2, i) {\n      return (timeScale(d2.start) + theSidePad + 0.5 * (timeScale(d2.end) - timeScale(d2.start))).toString() + \"px \" + (i * theGap + 0.5 * h2).toString() + \"px\";\n    }).attr(\"class\", \"exclude-range\");\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawExcludeDays, \"drawExcludeDays\");\n  function makeGrid(theSidePad, theTopPad, w2, h2) {\n    let bottomXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_7__.axisBottom)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_7__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n    const reTickInterval = /^([1-9]\\d*)(millisecond|second|minute|hour|day|week|month)$/;\n    const resultTickInterval = reTickInterval.exec(\n      diagObj.db.getTickInterval() || conf.tickInterval\n    );\n    if (resultTickInterval !== null) {\n      const every = resultTickInterval[1];\n      const interval = resultTickInterval[2];\n      const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n      switch (interval) {\n        case \"millisecond\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMillisecond.every(every));\n          break;\n        case \"second\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeSecond.every(every));\n          break;\n        case \"minute\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMinute.every(every));\n          break;\n        case \"hour\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeHour.every(every));\n          break;\n        case \"day\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeDay.every(every));\n          break;\n        case \"week\":\n          bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n          break;\n        case \"month\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMonth.every(every));\n          break;\n      }\n    }\n    svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + (h2 - 50) + \")\").call(bottomXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10).attr(\"dy\", \"1em\");\n    if (diagObj.db.topAxisEnabled() || conf.topAxis) {\n      let topXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_7__.axisTop)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_7__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n      if (resultTickInterval !== null) {\n        const every = resultTickInterval[1];\n        const interval = resultTickInterval[2];\n        const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n        switch (interval) {\n          case \"millisecond\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMillisecond.every(every));\n            break;\n          case \"second\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeSecond.every(every));\n            break;\n          case \"minute\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMinute.every(every));\n            break;\n          case \"hour\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeHour.every(every));\n            break;\n          case \"day\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeDay.every(every));\n            break;\n          case \"week\":\n            topXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n            break;\n          case \"month\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMonth.every(every));\n            break;\n        }\n      }\n      svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + theTopPad + \")\").call(topXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10);\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(makeGrid, \"makeGrid\");\n  function vertLabels(theGap, theTopPad) {\n    let prevGap = 0;\n    const numOccurrences = Object.keys(categoryHeights).map((d) => [d, categoryHeights[d]]);\n    svg.append(\"g\").selectAll(\"text\").data(numOccurrences).enter().append(function(d) {\n      const rows = d[0].split(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.common_default.lineBreakRegex);\n      const dy = -(rows.length - 1) / 2;\n      const svgLabel = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n      svgLabel.setAttribute(\"dy\", dy + \"em\");\n      for (const [j, row] of rows.entries()) {\n        const tspan = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n        tspan.setAttribute(\"alignment-baseline\", \"central\");\n        tspan.setAttribute(\"x\", \"10\");\n        if (j > 0) {\n          tspan.setAttribute(\"dy\", \"1em\");\n        }\n        tspan.textContent = row;\n        svgLabel.appendChild(tspan);\n      }\n      return svgLabel;\n    }).attr(\"x\", 10).attr(\"y\", function(d, i) {\n      if (i > 0) {\n        for (let j = 0; j < i; j++) {\n          prevGap += numOccurrences[i - 1][1];\n          return d[1] * theGap / 2 + prevGap * theGap + theTopPad;\n        }\n      } else {\n        return d[1] * theGap / 2 + theTopPad;\n      }\n    }).attr(\"font-size\", conf.sectionFontSize).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d[0] === category) {\n          return \"sectionTitle sectionTitle\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"sectionTitle\";\n    });\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(vertLabels, \"vertLabels\");\n  function drawToday(theSidePad, theTopPad, w2, h2) {\n    const todayMarker2 = diagObj.db.getTodayMarker();\n    if (todayMarker2 === \"off\") {\n      return;\n    }\n    const todayG = svg.append(\"g\").attr(\"class\", \"today\");\n    const today = /* @__PURE__ */ new Date();\n    const todayLine = todayG.append(\"line\");\n    todayLine.attr(\"x1\", timeScale(today) + theSidePad).attr(\"x2\", timeScale(today) + theSidePad).attr(\"y1\", conf.titleTopMargin).attr(\"y2\", h2 - conf.titleTopMargin).attr(\"class\", \"today\");\n    if (todayMarker2 !== \"\") {\n      todayLine.attr(\"style\", todayMarker2.replace(/,/g, \";\"));\n    }\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawToday, \"drawToday\");\n  function checkUnique(arr) {\n    const hash = {};\n    const result = [];\n    for (let i = 0, l = arr.length; i < l; ++i) {\n      if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {\n        hash[arr[i]] = true;\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(checkUnique, \"checkUnique\");\n}, \"draw\");\nvar ganttRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/gantt/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((options) => `\n  .mermaid-main-font {\n        font-family: ${options.fontFamily};\n  }\n\n  .exclude-range {\n    fill: ${options.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${options.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${options.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${options.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${options.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${options.fontFamily};\n    fill: ${options.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${options.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideRight {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${options.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${options.taskBkgColor};\n    stroke: ${options.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${options.activeTaskBkgColor};\n    stroke: ${options.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${options.doneTaskBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .vert {\n    stroke: ${options.vertLineColor};\n  }\n\n  .vertText {\n    font-size: 15px;\n    text-anchor: middle;\n    fill: ${options.vertLineColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.titleColor || options.textColor};\n    font-family: ${options.fontFamily};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/gantt/ganttDiagram.ts\nvar diagram = {\n  parser: gantt_default,\n  db: ganttDb_default,\n  renderer: ganttRenderer_default,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-EK5VF46D.mjs\n"));

/***/ })

}]);