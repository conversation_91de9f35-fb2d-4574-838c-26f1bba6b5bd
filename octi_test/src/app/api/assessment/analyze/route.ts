/**
 * OCTI智能评估系统 - 分析API路由
 * 
 * 处理组织能力分析请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { AnalysisService, OrganizationProfile, QuestionResponse } from '@/services/analysis/analysis-service';

export async function POST(request: NextRequest) {
  try {
    console.log('📥 收到分析请求');

    // 解析请求数据
    const body = await request.json();
    const { profile, responses, version = 'standard' } = body;

    // 验证请求数据
    if (!profile || !responses) {
      return NextResponse.json(
        { error: '缺少必要的分析数据' },
        { status: 400 }
      );
    }

    if (!Array.isArray(responses) || responses.length === 0) {
      return NextResponse.json(
        { error: '问卷回答数据无效' },
        { status: 400 }
      );
    }

    // 验证版本参数
    if (version !== 'standard' && version !== 'professional') {
      return NextResponse.json(
        { error: '无效的分析版本' },
        { status: 400 }
      );
    }

    console.log('📊 OCTI分析数据验证通过');
    console.log('👤 组织画像:', profile);
    console.log('📝 回答数量:', responses.length);
    console.log('🎯 分析版本:', version);

    // 创建分析服务实例
    const analysisService = new AnalysisService();

    // 生成OCTI专业分析报告
    const analysisResult = await analysisService.generateAnalysisReport(
      profile, // 传入原始数据，让分析服务内部转换
      responses as QuestionResponse[],
      version as 'standard' | 'professional'
    );

    console.log('✅ 分析报告生成成功');

    // 返回分析结果
    return NextResponse.json({
      success: true,
      data: analysisResult
    });

  } catch (error) {
    console.error('❌ 分析API错误:', error);

    // 返回错误信息
    return NextResponse.json(
      { 
        error: '分析服务暂时不可用',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 支持OPTIONS请求（CORS预检）
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
