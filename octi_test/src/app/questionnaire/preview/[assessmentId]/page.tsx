/**
 * OCTI智能评估系统 - 问卷预览页面
 * 
 * 在正式答题前预览问卷内容
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { MainLayout, PageContainer, CardContainer, LoadingState } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Question } from '@/components/questionnaire/question-renderer';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

const mockQuestionnaire = {
  id: 'questionnaire-1',
  title: '北京环保基金会 - 组织能力评估问卷',
  description: '基于您的组织画像生成的个性化评估问卷',
  totalQuestions: 60,
  estimatedDuration: 45,
  generatedAt: new Date('2024-01-15T10:30:00Z'),
  categories: [
    { name: '战略规划', count: 12, color: 'bg-blue-100 text-blue-800' },
    { name: '治理结构', count: 10, color: 'bg-green-100 text-green-800' },
    { name: '资源管理', count: 8, color: 'bg-yellow-100 text-yellow-800' },
    { name: '项目执行', count: 10, color: 'bg-purple-100 text-purple-800' },
    { name: '影响评估', count: 8, color: 'bg-red-100 text-red-800' },
    { name: '环保项目', count: 7, color: 'bg-emerald-100 text-emerald-800' },
    { name: '技术应用', count: 5, color: 'bg-indigo-100 text-indigo-800' },
  ],
  breakdown: {
    preset: 32,
    aiGenerated: 28,
    required: 45,
    optional: 15,
  },
};

const mockQuestions: Question[] = [
  {
    id: 'q1',
    type: 'SINGLE_CHOICE',
    source: 'PRESET',
    category: '战略规划',
    title: '您的组织是否有明确的使命陈述？',
    description: '使命陈述是组织存在的根本目的和价值主张的简洁表达。',
    required: true,
    order: 1,
  },
  {
    id: 'q2',
    type: 'SCALE',
    source: 'PRESET',
    category: '治理结构',
    title: '您认为组织的决策过程透明度如何？',
    required: true,
    order: 2,
  },
  {
    id: 'q3',
    type: 'MULTIPLE_CHOICE',
    source: 'AI_GENERATED',
    category: '环保项目',
    title: '基于您的组织特点，请选择目前正在开展的环保项目类型：',
    description: '根据您之前提到的环保基金会背景，我们想了解您具体的项目领域。',
    required: true,
    order: 3,
  },
  {
    id: 'q4',
    type: 'TEXT',
    source: 'AI_GENERATED',
    category: '资源管理',
    title: '请描述您的组织在资金筹集方面面临的主要挑战：',
    required: false,
    order: 4,
  },
  {
    id: 'q5',
    type: 'BOOLEAN',
    source: 'PRESET',
    category: '信息技术',
    title: '您的组织是否使用专门的项目管理软件？',
    required: true,
    order: 5,
  },
];

// ============================================================================
// 问卷预览页面组件
// ============================================================================

export default function QuestionnairePreviewPage() {
  const params = useParams();
  const router = useRouter();
  const assessmentId = params.assessmentId as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [questionnaire, setQuestionnaire] = useState(mockQuestionnaire);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    const loadQuestionnaire = async () => {
      try {
        setIsLoading(true);
        
        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setQuestionnaire(mockQuestionnaire);
        setQuestions(mockQuestions);
      } catch (err) {
        console.error('加载问卷失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (assessmentId) {
      loadQuestionnaire();
    }
  }, [assessmentId]);

  const filteredQuestions = selectedCategory === 'all' 
    ? questions 
    : questions.filter(q => q.category === selectedCategory);

  const getQuestionTypeLabel = (type: string) => {
    const types = {
      'SINGLE_CHOICE': '单选题',
      'MULTIPLE_CHOICE': '多选题',
      'SCALE': '量表题',
      'TEXT': '文本题',
      'BOOLEAN': '是非题',
      'RANKING': '排序题',
    };
    return types[type as keyof typeof types] || type;
  };

  const handleStartQuestionnaire = () => {
    router.push(`/questionnaire/${assessmentId}`);
  };

  if (isLoading) {
    return (
      <MainLayout user={mockUser}>
        <PageContainer>
          <LoadingState
            title="正在加载问卷预览..."
            description="请稍候，我们正在准备问卷内容"
          />
        </PageContainer>
      </MainLayout>
    );
  }

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title="问卷预览"
        description="在开始答题前，您可以预览问卷的整体结构和内容"
        breadcrumb={[
          { title: '首页', href: '/' },
          { title: '评估管理', href: '/assessments' },
          { title: '问卷预览' },
        ]}
        actions={
          <Button onClick={handleStartQuestionnaire} size="lg">
            开始答题
          </Button>
        }
      >
        {/* 问卷基本信息 */}
        <CardContainer
          title={questionnaire.title}
          description={questionnaire.description}
        >
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-primary">{questionnaire.totalQuestions}</div>
              <div className="text-sm text-muted-foreground">总题数</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">{questionnaire.estimatedDuration}</div>
              <div className="text-sm text-muted-foreground">预计用时(分钟)</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{questionnaire.breakdown.preset}</div>
              <div className="text-sm text-muted-foreground">预设题目</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{questionnaire.breakdown.aiGenerated}</div>
              <div className="text-sm text-muted-foreground">AI生成题目</div>
            </div>
          </div>

          <div className="mt-6">
            <h4 className="font-medium mb-3">题目分布</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>必答题目</span>
                <span>{questionnaire.breakdown.required} / {questionnaire.totalQuestions}</span>
              </div>
              <Progress value={(questionnaire.breakdown.required / questionnaire.totalQuestions) * 100} className="h-2" />
            </div>
          </div>
        </CardContainer>

        {/* 分类统计 */}
        <CardContainer title="题目分类" description="按评估维度分类的题目分布">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {questionnaire.categories.map((category, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg cursor-pointer transition-colors ${
                  selectedCategory === category.name
                    ? 'ring-2 ring-primary'
                    : 'hover:bg-accent'
                }`}
                onClick={() => setSelectedCategory(category.name)}
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{category.name}</h4>
                  <Badge className={category.color}>{category.count}</Badge>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              查看全部
            </Button>
          </div>
        </CardContainer>

        {/* 题目列表 */}
        <CardContainer
          title={`题目列表 ${selectedCategory !== 'all' ? `- ${selectedCategory}` : ''}`}
          description={`共 ${filteredQuestions.length} 道题目`}
        >
          <div className="space-y-4">
            {filteredQuestions.map((question, index) => (
              <div key={question.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">
                      {index + 1}
                    </Badge>
                    <Badge variant={question.source === 'PRESET' ? 'default' : 'secondary'}>
                      {question.source === 'PRESET' ? '预设' : 'AI生成'}
                    </Badge>
                    <Badge variant="outline">
                      {getQuestionTypeLabel(question.type)}
                    </Badge>
                    <Badge variant="outline">
                      {question.category}
                    </Badge>
                    {question.required && (
                      <Badge variant="destructive">必答</Badge>
                    )}
                  </div>
                </div>
                
                <h4 className="font-medium mb-2">{question.title}</h4>
                
                {question.description && (
                  <p className="text-sm text-muted-foreground mb-2">
                    {question.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </CardContainer>

        {/* 开始答题提示 */}
        <Alert>
          <AlertDescription>
            <div className="flex items-center justify-between">
              <div>
                <strong>准备好开始了吗？</strong>
                <p className="text-sm mt-1">
                  问卷支持随时保存草稿，您可以分多次完成。建议在安静的环境中专心答题，以获得最准确的评估结果。
                </p>
              </div>
              <Button onClick={handleStartQuestionnaire} className="ml-4">
                开始答题
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </PageContainer>
    </MainLayout>
  );
}
