{"version": "4.0.0", "agent_type": "QUESTION_DESIGNER", "prompt_template": {"system_message": "你是一个专业的OCTI（组织能力MBTI）问卷设计师，专门为公益机构生成个性化能力评估问卷。你深度理解公益机构的使命驱动特性、多利益相关者环境、社会影响力测量需求和资源约束挑战，能够根据公益机构的组织画像生成针对性的评估问题。你采用混合问卷模式，在32道预设标准题目基础上，智能生成28道个性化题目，确保评估的标准性与个性化并重。", "task_description": "基于OCTI四维八极框架和公益机构组织画像，智能生成28道个性化问题，与32道预设标准题目组成混合问卷，帮助公益机构识别自身能力类型和发展方向。重点关注公益机构的使命驱动特性、利益相关者管理、社会影响力测量和可持续发展能力。", "framework": {"dimensions": {"SF": {"name": "S/F维度：战略聚焦度", "sub_dimensions": {"positioning_clarity": "定位清晰度", "professional_depth": "专业深度", "competitive_positioning": "竞争定位", "development_direction": "发展方向", "resource_focus": "资源聚焦"}}, "IT": {"name": "I/T维度：团队协同度", "sub_dimensions": {"decision_mode": "决策模式", "talent_dependency": "人才依赖", "collaboration_mechanism": "协作机制", "knowledge_management": "知识管理", "organizational_culture": "组织文化"}}, "MV": {"name": "M/V维度：价值导向度", "sub_dimensions": {"motivation_source": "动机来源", "success_definition": "成功定义", "resource_attitude": "资源态度", "communication_strategy": "传播策略", "long_term_vision": "长期愿景"}}, "AD": {"name": "A/D维度：能力发展度", "sub_dimensions": {"adaptation_strategy": "应变策略", "standardization_level": "标准化程度", "learning_mode": "学习模式", "capacity_building": "能力建设", "innovation_frequency": "创新频率"}}}}}, "nonprofit_organization_profile": {"organizationType": ["基金会", "公益组织", "社会团体", "民办非企业", "国际NGO", "政府机构"], "serviceArea": ["教育", "环保", "扶贫", "医疗", "养老", "儿童", "残障", "文化", "体育", "科技", "其他"], "organizationScale": ["微型", "小型", "中型", "大型", "超大型"], "developmentStage": ["初创期", "成长期", "成熟期", "转型期", "扩张期"], "operatingModel": ["直接服务", "资助型", "倡导型", "研究型", "平台型", "混合型"], "impactPositioning": ["本地影响", "区域影响", "全国影响", "国际影响"], "organizationalCulture": ["使命驱动", "创新导向", "协作共享", "专业严谨", "草根活力"]}, "intelligent_prompt_system": {"contextual_adaptation": {"service_area_context": {"教育": "关注教育公平、学习成果、师资发展、教育创新", "环保": "关注环境保护、可持续发展、生态修复、绿色倡导", "扶贫": "关注贫困减缓、能力建设、可持续脱贫、社区发展", "医疗": "关注健康促进、医疗可及性、疾病预防、健康教育"}, "development_stage_context": {"初创期": "关注基础能力建设、团队组建、资源获取、项目启动", "成长期": "关注规模扩张、流程优化、品牌建设、影响力提升", "成熟期": "关注可持续发展、创新转型、深度影响、行业引领", "转型期": "关注战略调整、模式创新、能力重构、风险管控"}, "operating_model_context": {"直接服务": "关注服务质量、受益者满意度、服务创新、规模效应", "资助型": "关注资助策略、项目筛选、监督评估、资源配置", "倡导型": "关注政策影响、公众参与、议题设置、联盟建设"}}}, "configuration_parameters": {"hybrid_questionnaire_mode": {"preset_questions": 32, "intelligent_questions": 28, "total_questions": 60, "description": "混合问卷模式：32道预设标准题目+28道智能生成题目"}, "intelligent_questions_per_dimension": 7, "question_types_distribution": {"choice_percent": 60, "scenario_percent": 25, "ranking_percent": 10, "scale_percent": 5}, "nonprofit_focus_areas": ["使命驱动特性", "利益相关者管理", "社会影响力测量", "透明度与问责", "志愿者管理", "资源筹集能力", "可持续发展", "治理结构"], "version_specific_settings": {"standard": {"question_depth_level": "surface", "complexity_keywords": ["基本", "通常", "一般", "简单", "常见"], "scenario_complexity": "简单情境", "cognitive_load": "低", "question_style": "直接明了，易于理解"}, "professional": {"question_depth_level": "deep", "complexity_keywords": ["深入", "复杂", "多维度", "综合", "系统性"], "scenario_complexity": "复合情境", "cognitive_load": "高", "question_style": "多层次思考，需要综合判断"}}, "depth_control_instructions": {"standard": "请生成适合初级管理者和小型公益组织的问题，语言简洁明了，情境单一清晰，避免过于复杂的概念。问题应该让受访者能够快速理解并基于直观经验作答。重点关注公益组织的基础运营能力和使命实现。", "professional": "请生成适合资深管理者和成熟公益组织的问题，可以使用专业术语，设置复合情境，要求受访者进行深度思考和综合判断。问题应该能够挖掘公益组织的深层能力特征和潜在问题，包括治理结构、影响力测量、可持续发展等高级议题。"}, "intelligent_generation_strategy": {"contextual_adaptation": "根据组织画像动态调整问题内容和难度", "nonprofit_specialization": "针对公益机构特有的运营模式和挑战设计问题", "stakeholder_perspective": "从多利益相关者角度设计评估维度", "impact_measurement_focus": "强化社会影响力和价值创造的评估"}}, "scoring_system": {"scale_definition": {"min_score": 1, "max_score": 5, "neutral_score": 3}, "dimension_mapping": {"SF": {"S_indicators": ["公益专业化", "服务聚焦", "专业深度", "领域专精", "使命聚焦"], "F_indicators": ["服务多元化", "受益群体广泛", "服务灵活", "需求适应", "跨领域合作"]}, "IT": {"I_indicators": ["创始人决策", "专业独立", "自主运营", "个体专长", "领导驱动"], "T_indicators": ["集体决策", "团队协作", "共识建设", "利益相关者参与", "民主治理"]}, "MV": {"M_indicators": ["使命驱动", "价值导向", "社会理想", "公益初心", "变革使命"], "V_indicators": ["愿景驱动", "目标导向", "实用主义", "组织发展", "可持续增长"]}, "AD": {"A_indicators": ["环境适应", "服务灵活", "创新变化", "敏捷响应", "需求导向"], "D_indicators": ["流程标准化", "服务稳定", "制度规范", "体系建设", "专业化发展"]}}, "nonprofit_specific_scoring": {"stakeholder_impact_weight": 0.3, "mission_alignment_weight": 0.25, "social_value_weight": 0.25, "sustainability_weight": 0.2}, "hybrid_questionnaire_scoring": {"preset_questions_weight": 0.6, "intelligent_questions_weight": 0.4, "consistency_check": true, "cross_validation": "preset_intelligent_correlation"}, "scoring_rules": {"choice_questions": {"option_weights": [1, 2, 3, 4, 5], "calculation_method": "direct_mapping"}, "scenario_questions": {"option_weights": [1, 2, 3, 4, 5], "calculation_method": "weighted_average"}, "ranking_questions": {"rank_weights": [5, 4, 3, 2, 1], "calculation_method": "rank_based"}, "scale_questions": {"scale_range": [1, 5], "calculation_method": "direct_score"}}}, "output_format": {"structure": {"questionnaire": {"version": "string", "questionnaire_mode": "hybrid", "total_questions": "number", "composition": {"preset_questions": 32, "intelligent_questions": 28}, "dimensions": {"SF": {"preset_questions": "array", "intelligent_questions": "array", "total_count": 15}, "IT": {"preset_questions": "array", "intelligent_questions": "array", "total_count": 15}, "MV": {"preset_questions": "array", "intelligent_questions": "array", "total_count": 15}, "AD": {"preset_questions": "array", "intelligent_questions": "array", "total_count": 15}}, "nonprofit_focus": {"mission_driven_questions": "array", "stakeholder_questions": "array", "impact_measurement_questions": "array"}}}, "question_schema": {"id": "string", "dimension": "string", "sub_dimension": "string", "type": "choice|scenario|ranking|scale", "text": "string", "options": "array", "scoring": {"dimension_weight": "number", "sub_dimension_weight": "number", "option_scores": "array", "reverse_scoring": "boolean"}}}, "api_integration": {"model_config": {"primary_model": "minimax", "backup_model": "deepseek", "timeout": 30000, "max_retries": 3}, "error_handling": {"retry_strategy": "exponential_backoff", "fallback_behavior": "use_cached_template", "error_logging": true}}, "quality_control": {"validation_rules": ["每个维度必须包含15道题目（8道预设+7道智能生成）", "智能生成的28道题目必须与预设题目形成有效补充", "问题文本长度应在20-200字之间", "选项数量应符合题型要求", "必须覆盖所有子维度", "问题表述应清晰无歧义", "评分权重总和必须为1", "必须体现公益机构特色", "必须考虑利益相关者视角", "必须包含社会影响力测量", "智能生成题目必须基于组织画像个性化", "混合问卷必须保持内在逻辑一致性"], "hybrid_questionnaire_validation": {"preset_intelligent_consistency": "确保预设题目与智能生成题目的一致性", "coverage_completeness": "确保混合问卷完整覆盖OCTI框架", "nonprofit_specialization": "确保智能生成题目体现公益机构特色", "difficulty_balance": "确保题目难度分布合理"}, "nonprofit_specific_validation": {"mission_relevance_check": "确保问题与公益使命相关", "stakeholder_perspective_check": "确保涵盖多利益相关者视角", "social_impact_check": "确保包含社会影响力评估", "sustainability_check": "确保考虑可持续发展因素"}, "depth_validation": {"keyword_match_threshold": 0.3, "complexity_indicators": ["问题长度", "选项复杂度", "情境层次", "认知要求"]}, "output_validation": {"json_schema_validation": true, "content_quality_check": true, "scoring_consistency_check": true}}}