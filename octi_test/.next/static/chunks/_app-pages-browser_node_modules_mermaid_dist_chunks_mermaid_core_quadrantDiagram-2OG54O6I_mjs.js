"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_quadrantDiagram-2OG54O6I_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-2OG54O6I.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-2OG54O6I.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n// src/diagrams/quadrant-chart/parser/quadrant.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [1, 7], $V5 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V6 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 28, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V7 = [55, 56, 57], $V8 = [2, 36], $V9 = [1, 37], $Va = [1, 36], $Vb = [1, 38], $Vc = [1, 35], $Vd = [1, 43], $Ve = [1, 41], $Vf = [1, 14], $Vg = [1, 23], $Vh = [1, 18], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 24], $Vn = [1, 25], $Vo = [1, 26], $Vp = [1, 27], $Vq = [1, 28], $Vr = [1, 29], $Vs = [1, 32], $Vt = [1, 33], $Vu = [1, 34], $Vv = [1, 39], $Vw = [1, 40], $Vx = [1, 42], $Vy = [1, 44], $Vz = [1, 62], $VA = [1, 61], $VB = [4, 5, 8, 10, 12, 13, 14, 18, 44, 47, 49, 55, 56, 57, 63, 64, 65, 66, 67], $VC = [1, 65], $VD = [1, 66], $VE = [1, 67], $VF = [1, 68], $VG = [1, 69], $VH = [1, 70], $VI = [1, 71], $VJ = [1, 72], $VK = [1, 73], $VL = [1, 74], $VM = [1, 75], $VN = [1, 76], $VO = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18], $VP = [1, 90], $VQ = [1, 91], $VR = [1, 92], $VS = [1, 99], $VT = [1, 93], $VU = [1, 96], $VV = [1, 94], $VW = [1, 95], $VX = [1, 97], $VY = [1, 98], $VZ = [1, 102], $V_ = [10, 55, 56, 57], $V$ = [4, 5, 6, 8, 10, 11, 13, 17, 18, 19, 20, 55, 56, 57];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"idStringToken\": 3, \"ALPHA\": 4, \"NUM\": 5, \"NODE_STRING\": 6, \"DOWN\": 7, \"MINUS\": 8, \"DEFAULT\": 9, \"COMMA\": 10, \"COLON\": 11, \"AMP\": 12, \"BRKT\": 13, \"MULT\": 14, \"UNICODE_TEXT\": 15, \"styleComponent\": 16, \"UNIT\": 17, \"SPACE\": 18, \"STYLE\": 19, \"PCT\": 20, \"idString\": 21, \"style\": 22, \"stylesOpt\": 23, \"classDefStatement\": 24, \"CLASSDEF\": 25, \"start\": 26, \"eol\": 27, \"QUADRANT\": 28, \"document\": 29, \"line\": 30, \"statement\": 31, \"axisDetails\": 32, \"quadrantDetails\": 33, \"points\": 34, \"title\": 35, \"title_value\": 36, \"acc_title\": 37, \"acc_title_value\": 38, \"acc_descr\": 39, \"acc_descr_value\": 40, \"acc_descr_multiline_value\": 41, \"section\": 42, \"text\": 43, \"point_start\": 44, \"point_x\": 45, \"point_y\": 46, \"class_name\": 47, \"X-AXIS\": 48, \"AXIS-TEXT-DELIMITER\": 49, \"Y-AXIS\": 50, \"QUADRANT_1\": 51, \"QUADRANT_2\": 52, \"QUADRANT_3\": 53, \"QUADRANT_4\": 54, \"NEWLINE\": 55, \"SEMI\": 56, \"EOF\": 57, \"alphaNumToken\": 58, \"textNoTagsToken\": 59, \"STR\": 60, \"MD_STR\": 61, \"alphaNum\": 62, \"PUNCTUATION\": 63, \"PLUS\": 64, \"EQUALS\": 65, \"DOT\": 66, \"UNDERSCORE\": 67, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"ALPHA\", 5: \"NUM\", 6: \"NODE_STRING\", 7: \"DOWN\", 8: \"MINUS\", 9: \"DEFAULT\", 10: \"COMMA\", 11: \"COLON\", 12: \"AMP\", 13: \"BRKT\", 14: \"MULT\", 15: \"UNICODE_TEXT\", 17: \"UNIT\", 18: \"SPACE\", 19: \"STYLE\", 20: \"PCT\", 25: \"CLASSDEF\", 28: \"QUADRANT\", 35: \"title\", 36: \"title_value\", 37: \"acc_title\", 38: \"acc_title_value\", 39: \"acc_descr\", 40: \"acc_descr_value\", 41: \"acc_descr_multiline_value\", 42: \"section\", 44: \"point_start\", 45: \"point_x\", 46: \"point_y\", 47: \"class_name\", 48: \"X-AXIS\", 49: \"AXIS-TEXT-DELIMITER\", 50: \"Y-AXIS\", 51: \"QUADRANT_1\", 52: \"QUADRANT_2\", 53: \"QUADRANT_3\", 54: \"QUADRANT_4\", 55: \"NEWLINE\", 56: \"SEMI\", 57: \"EOF\", 60: \"STR\", 61: \"MD_STR\", 63: \"PUNCTUATION\", 64: \"PLUS\", 65: \"EQUALS\", 66: \"DOT\", 67: \"UNDERSCORE\" },\n    productions_: [0, [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [21, 1], [21, 2], [22, 1], [22, 2], [23, 1], [23, 3], [24, 5], [26, 2], [26, 2], [26, 2], [29, 0], [29, 2], [30, 2], [31, 0], [31, 1], [31, 2], [31, 1], [31, 1], [31, 1], [31, 2], [31, 2], [31, 2], [31, 1], [31, 1], [34, 4], [34, 5], [34, 5], [34, 6], [32, 4], [32, 3], [32, 2], [32, 4], [32, 3], [32, 2], [33, 2], [33, 2], [33, 2], [33, 2], [27, 1], [27, 1], [27, 1], [43, 1], [43, 2], [43, 1], [43, 1], [62, 1], [62, 2], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [59, 1], [59, 1], [59, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 23:\n          this.$ = $$[$0];\n          break;\n        case 24:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n        case 26:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 27:\n          this.$ = [$$[$0].trim()];\n          break;\n        case 28:\n          $$[$0 - 2].push($$[$0].trim());\n          this.$ = $$[$0 - 2];\n          break;\n        case 29:\n          this.$ = $$[$0 - 4];\n          yy.addClass($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = [];\n          break;\n        case 42:\n          this.$ = $$[$0].trim();\n          yy.setDiagramTitle(this.$);\n          break;\n        case 43:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 44:\n        case 45:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 46:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 47:\n          yy.addPoint($$[$0 - 3], \"\", $$[$0 - 1], $$[$0], []);\n          break;\n        case 48:\n          yy.addPoint($$[$0 - 4], $$[$0 - 3], $$[$0 - 1], $$[$0], []);\n          break;\n        case 49:\n          yy.addPoint($$[$0 - 4], \"\", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 50:\n          yy.addPoint($$[$0 - 5], $$[$0 - 4], $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 51:\n          yy.setXAxisLeftText($$[$0 - 2]);\n          yy.setXAxisRightText($$[$0]);\n          break;\n        case 52:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setXAxisLeftText($$[$0 - 1]);\n          break;\n        case 53:\n          yy.setXAxisLeftText($$[$0]);\n          break;\n        case 54:\n          yy.setYAxisBottomText($$[$0 - 2]);\n          yy.setYAxisTopText($$[$0]);\n          break;\n        case 55:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setYAxisBottomText($$[$0 - 1]);\n          break;\n        case 56:\n          yy.setYAxisBottomText($$[$0]);\n          break;\n        case 57:\n          yy.setQuadrant1Text($$[$0]);\n          break;\n        case 58:\n          yy.setQuadrant2Text($$[$0]);\n          break;\n        case 59:\n          yy.setQuadrant3Text($$[$0]);\n          break;\n        case 60:\n          yy.setQuadrant4Text($$[$0]);\n          break;\n        case 64:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 65:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 66:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 67:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 68:\n          this.$ = $$[$0];\n          break;\n        case 69:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 18: $V0, 26: 1, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 1: [3] }, { 18: $V0, 26: 8, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 18: $V0, 26: 9, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, o($V5, [2, 33], { 29: 10 }), o($V6, [2, 61]), o($V6, [2, 62]), o($V6, [2, 63]), { 1: [2, 30] }, { 1: [2, 31] }, o($V7, $V8, { 30: 11, 31: 12, 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 1: [2, 32], 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V5, [2, 34]), { 27: 45, 55: $V2, 56: $V3, 57: $V4 }, o($V7, [2, 37]), o($V7, $V8, { 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 31: 46, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 39]), o($V7, [2, 40]), o($V7, [2, 41]), { 36: [1, 47] }, { 38: [1, 48] }, { 40: [1, 49] }, o($V7, [2, 45]), o($V7, [2, 46]), { 18: [1, 50] }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 51, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 52, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 53, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 54, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 55, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 56, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 44: [1, 57], 47: [1, 58], 58: 60, 59: 59, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, o($VB, [2, 64]), o($VB, [2, 66]), o($VB, [2, 67]), o($VB, [2, 70]), o($VB, [2, 71]), o($VB, [2, 72]), o($VB, [2, 73]), o($VB, [2, 74]), o($VB, [2, 75]), o($VB, [2, 76]), o($VB, [2, 77]), o($VB, [2, 78]), o($VB, [2, 79]), o($VB, [2, 80]), o($V5, [2, 35]), o($V7, [2, 38]), o($V7, [2, 42]), o($V7, [2, 43]), o($V7, [2, 44]), { 3: 64, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 21: 63 }, o($V7, [2, 53], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 77], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 56], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 78], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 57], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 58], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 59], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 60], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 45: [1, 79] }, { 44: [1, 80] }, o($VB, [2, 65]), o($VB, [2, 81]), o($VB, [2, 82]), o($VB, [2, 83]), { 3: 82, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 18: [1, 81] }, o($VO, [2, 23]), o($VO, [2, 1]), o($VO, [2, 2]), o($VO, [2, 3]), o($VO, [2, 4]), o($VO, [2, 5]), o($VO, [2, 6]), o($VO, [2, 7]), o($VO, [2, 8]), o($VO, [2, 9]), o($VO, [2, 10]), o($VO, [2, 11]), o($VO, [2, 12]), o($V7, [2, 52], { 58: 31, 43: 83, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 55], { 58: 31, 43: 84, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 46: [1, 85] }, { 45: [1, 86] }, { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 88, 23: 87 }, o($VO, [2, 24]), o($V7, [2, 51], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 54], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 47], { 22: 88, 16: 89, 23: 100, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 46: [1, 101] }, o($V7, [2, 29], { 10: $VZ }), o($V_, [2, 27], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), o($V$, [2, 25]), o($V$, [2, 13]), o($V$, [2, 14]), o($V$, [2, 15]), o($V$, [2, 16]), o($V$, [2, 17]), o($V$, [2, 18]), o($V$, [2, 19]), o($V$, [2, 20]), o($V$, [2, 21]), o($V$, [2, 22]), o($V7, [2, 49], { 10: $VZ }), o($V7, [2, 48], { 22: 88, 16: 89, 23: 104, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 105 }, o($V$, [2, 26]), o($V7, [2, 50], { 10: $VZ }), o($V_, [2, 28], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY })],\n    defaultActions: { 8: [2, 30], 9: [2, 31] },\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 55;\n            break;\n          case 3:\n            break;\n          case 4:\n            this.begin(\"title\");\n            return 35;\n            break;\n          case 5:\n            this.popState();\n            return \"title_value\";\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 37;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 39;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            return 48;\n            break;\n          case 14:\n            return 50;\n            break;\n          case 15:\n            return 49;\n            break;\n          case 16:\n            return 51;\n            break;\n          case 17:\n            return 52;\n            break;\n          case 18:\n            return 53;\n            break;\n          case 19:\n            return 54;\n            break;\n          case 20:\n            return 25;\n            break;\n          case 21:\n            this.begin(\"md_string\");\n            break;\n          case 22:\n            return \"MD_STR\";\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            this.begin(\"string\");\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            return \"STR\";\n            break;\n          case 27:\n            this.begin(\"class_name\");\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.begin(\"point_start\");\n            return 44;\n            break;\n          case 30:\n            this.begin(\"point_x\");\n            return 45;\n            break;\n          case 31:\n            this.popState();\n            break;\n          case 32:\n            this.popState();\n            this.begin(\"point_y\");\n            break;\n          case 33:\n            this.popState();\n            return 46;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 4;\n            break;\n          case 36:\n            return 11;\n            break;\n          case 37:\n            return 64;\n            break;\n          case 38:\n            return 10;\n            break;\n          case 39:\n            return 65;\n            break;\n          case 40:\n            return 65;\n            break;\n          case 41:\n            return 14;\n            break;\n          case 42:\n            return 13;\n            break;\n          case 43:\n            return 67;\n            break;\n          case 44:\n            return 66;\n            break;\n          case 45:\n            return 12;\n            break;\n          case 46:\n            return 8;\n            break;\n          case 47:\n            return 5;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 56;\n            break;\n          case 50:\n            return 63;\n            break;\n          case 51:\n            return 57;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?: *x-axis *)/i, /^(?: *y-axis *)/i, /^(?: *--+> *)/i, /^(?: *quadrant-1 *)/i, /^(?: *quadrant-2 *)/i, /^(?: *quadrant-3 *)/i, /^(?: *quadrant-4 *)/i, /^(?:classDef\\b)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?::::)/i, /^(?:^\\w+)/i, /^(?:\\s*:\\s*\\[\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?:\\s*\\] *)/i, /^(?:\\s*,\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?: *quadrantChart *)/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s)/i, /^(?:;)/i, /^(?:[!\"#$%&'*+,-.`?\\\\_/])/i, /^(?:$)/i],\n      conditions: { \"class_name\": { \"rules\": [28], \"inclusive\": false }, \"point_y\": { \"rules\": [33], \"inclusive\": false }, \"point_x\": { \"rules\": [32], \"inclusive\": false }, \"point_start\": { \"rules\": [30, 31], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"title\": { \"rules\": [5], \"inclusive\": false }, \"md_string\": { \"rules\": [22, 23], \"inclusive\": false }, \"string\": { \"rules\": [25, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 27, 29, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar quadrant_default = parser;\n\n// src/diagrams/quadrant-chart/quadrantBuilder.ts\n\nvar defaultThemeVariables = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getThemeVariables)();\nvar QuadrantBuilder = class {\n  constructor() {\n    this.classes = /* @__PURE__ */ new Map();\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(this, \"QuadrantBuilder\");\n  }\n  getDefaultData() {\n    return {\n      titleText: \"\",\n      quadrant1Text: \"\",\n      quadrant2Text: \"\",\n      quadrant3Text: \"\",\n      quadrant4Text: \"\",\n      xAxisLeftText: \"\",\n      xAxisRightText: \"\",\n      yAxisBottomText: \"\",\n      yAxisTopText: \"\",\n      points: []\n    };\n  }\n  getDefaultConfig() {\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.chartWidth || 500,\n      chartWidth: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.chartHeight || 500,\n      titlePadding: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.titlePadding || 10,\n      titleFontSize: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.titleFontSize || 20,\n      quadrantPadding: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantPadding || 5,\n      xAxisLabelPadding: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.xAxisLabelPadding || 5,\n      yAxisLabelPadding: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.yAxisLabelPadding || 5,\n      xAxisLabelFontSize: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.xAxisLabelFontSize || 16,\n      yAxisLabelFontSize: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.yAxisLabelFontSize || 16,\n      quadrantLabelFontSize: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantLabelFontSize || 16,\n      quadrantTextTopPadding: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantTextTopPadding || 5,\n      pointTextPadding: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.pointTextPadding || 5,\n      pointLabelFontSize: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.pointLabelFontSize || 12,\n      pointRadius: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.pointRadius || 5,\n      xAxisPosition: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.xAxisPosition || \"top\",\n      yAxisPosition: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.yAxisPosition || \"left\",\n      quadrantInternalBorderStrokeWidth: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantInternalBorderStrokeWidth || 1,\n      quadrantExternalBorderStrokeWidth: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantExternalBorderStrokeWidth || 2\n    };\n  }\n  getDefaultThemeConfig() {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill\n    };\n  }\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    this.classes = /* @__PURE__ */ new Map();\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.info(\"clear called\");\n  }\n  setData(data) {\n    this.data = { ...this.data, ...data };\n  }\n  addPoints(points) {\n    this.data.points = [...points, ...this.data.points];\n  }\n  addClass(className, styles) {\n    this.classes.set(className, styles);\n  }\n  setConfig(config2) {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.trace(\"setConfig called with: \", config2);\n    this.config = { ...this.config, ...config2 };\n  }\n  setThemeConfig(themeConfig) {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.trace(\"setThemeConfig called with: \", themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n  calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle) {\n    const xAxisSpaceCalculation = this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === \"top\" && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === \"bottom\" && showXAxis ? xAxisSpaceCalculation : 0\n    };\n    const yAxisSpaceCalculation = this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === \"left\" && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === \"right\" && showYAxis ? yAxisSpaceCalculation : 0\n    };\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0\n    };\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth = this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight = this.config.chartHeight - this.config.quadrantPadding * 2 - xAxisSpace.top - xAxisSpace.bottom - titleSpace.top;\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight\n    };\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace\n    };\n  }\n  getAxisLabels(xAxisPosition, showXAxis, showYAxis, spaceData) {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n    const axisLabels = [];\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    return axisLabels;\n  }\n  getQuadrants(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n    const quadrants = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill\n      }\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = \"middle\";\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = \"top\";\n      }\n    }\n    return quadrants;\n  }\n  getQuadrantPoints(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n    const xAxis = (0,d3__WEBPACK_IMPORTED_MODULE_1__.scaleLinear)().domain([0, 1]).range([quadrantLeft, quadrantWidth + quadrantLeft]);\n    const yAxis = (0,d3__WEBPACK_IMPORTED_MODULE_1__.scaleLinear)().domain([0, 1]).range([quadrantHeight + quadrantTop, quadrantTop]);\n    const points = this.data.points.map((point) => {\n      const classStyles = this.classes.get(point.className);\n      if (classStyles) {\n        point = { ...classStyles, ...point };\n      }\n      const props = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: point.color ?? this.themeConfig.quadrantPointFill,\n        radius: point.radius ?? this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: \"center\",\n          horizontalPos: \"top\",\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0\n        },\n        strokeColor: point.strokeColor ?? this.themeConfig.quadrantPointFill,\n        strokeWidth: point.strokeWidth ?? \"0px\"\n      };\n      return props;\n    });\n    return points;\n  }\n  getBorders(spaceData) {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const borderLines = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight\n      }\n    ];\n    return borderLines;\n  }\n  getTitle(showTitle) {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: \"top\",\n        verticalPos: \"center\",\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2\n      };\n    }\n    return;\n  }\n  build() {\n    const showXAxis = this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis = this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n    const xAxisPosition = this.data.points.length > 0 ? \"bottom\" : this.config.xAxisPosition;\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle)\n    };\n  }\n};\n\n// src/diagrams/quadrant-chart/utils.ts\nvar InvalidStyleError = class extends Error {\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(this, \"InvalidStyleError\");\n  }\n  constructor(style, value, type) {\n    super(`value for ${style} ${value} is invalid, please use a valid ${type}`);\n    this.name = \"InvalidStyleError\";\n  }\n};\nfunction validateHexCode(value) {\n  return !/^#?([\\dA-Fa-f]{6}|[\\dA-Fa-f]{3})$/.test(value);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(validateHexCode, \"validateHexCode\");\nfunction validateNumber(value) {\n  return !/^\\d+$/.test(value);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(validateNumber, \"validateNumber\");\nfunction validateSizeInPixels(value) {\n  return !/^\\d+px$/.test(value);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(validateSizeInPixels, \"validateSizeInPixels\");\n\n// src/diagrams/quadrant-chart/quadrantDb.ts\nvar config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\nfunction textSanitizer(text) {\n  return (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.sanitizeText)(text.trim(), config);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(textSanitizer, \"textSanitizer\");\nvar quadrantBuilder = new QuadrantBuilder();\nfunction setQuadrant1Text(textObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant1Text, \"setQuadrant1Text\");\nfunction setQuadrant2Text(textObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant2Text, \"setQuadrant2Text\");\nfunction setQuadrant3Text(textObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant3Text, \"setQuadrant3Text\");\nfunction setQuadrant4Text(textObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant4Text, \"setQuadrant4Text\");\nfunction setXAxisLeftText(textObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setXAxisLeftText, \"setXAxisLeftText\");\nfunction setXAxisRightText(textObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setXAxisRightText, \"setXAxisRightText\");\nfunction setYAxisTopText(textObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setYAxisTopText, \"setYAxisTopText\");\nfunction setYAxisBottomText(textObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setYAxisBottomText, \"setYAxisBottomText\");\nfunction parseStyles(styles) {\n  const stylesObject = {};\n  for (const style of styles) {\n    const [key, value] = style.trim().split(/\\s*:\\s*/);\n    if (key === \"radius\") {\n      if (validateNumber(value)) {\n        throw new InvalidStyleError(key, value, \"number\");\n      }\n      stylesObject.radius = parseInt(value);\n    } else if (key === \"color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.color = value;\n    } else if (key === \"stroke-color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.strokeColor = value;\n    } else if (key === \"stroke-width\") {\n      if (validateSizeInPixels(value)) {\n        throw new InvalidStyleError(key, value, \"number of pixels (eg. 10px)\");\n      }\n      stylesObject.strokeWidth = value;\n    } else {\n      throw new Error(`style named ${key} is not supported.`);\n    }\n  }\n  return stylesObject;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(parseStyles, \"parseStyles\");\nfunction addPoint(textObj, className, x, y, styles) {\n  const stylesObject = parseStyles(styles);\n  quadrantBuilder.addPoints([\n    {\n      x,\n      y,\n      text: textSanitizer(textObj.text),\n      className,\n      ...stylesObject\n    }\n  ]);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(addPoint, \"addPoint\");\nfunction addClass(className, styles) {\n  quadrantBuilder.addClass(className, parseStyles(styles));\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(addClass, \"addClass\");\nfunction setWidth(width) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setWidth, \"setWidth\");\nfunction setHeight(height) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setHeight, \"setHeight\");\nfunction getQuadrantData() {\n  const config2 = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config2;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill\n  });\n  quadrantBuilder.setData({ titleText: (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramTitle)() });\n  return quadrantBuilder.build();\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getQuadrantData, \"getQuadrantData\");\nvar clear2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  quadrantBuilder.clear();\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.clear)();\n}, \"clear\");\nvar quadrantDb_default = {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  parseStyles,\n  addPoint,\n  addClass,\n  getQuadrantData,\n  clear: clear2,\n  setAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.setAccTitle,\n  getAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getAccTitle,\n  setDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.setDiagramTitle,\n  getDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramTitle,\n  getAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getAccDescription,\n  setAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.setAccDescription\n};\n\n// src/diagrams/quadrant-chart/quadrantRenderer.ts\n\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((txt, id, _version, diagObj) => {\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"hanging\" : \"middle\";\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : \"middle\";\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getTextAnchor, \"getTextAnchor\");\n  function getTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getTransformation, \"getTransformation\");\n  const conf = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"Rendering quadrant chart\\n\" + txt);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const width = conf.quadrantChart?.chartWidth ?? 500;\n  const height = conf.quadrantChart?.chartHeight ?? 500;\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, conf.quadrantChart?.useMaxWidth ?? true);\n  svg.attr(\"viewBox\", \"0 0 \" + width + \" \" + height);\n  diagObj.db.setHeight(height);\n  diagObj.db.setWidth(width);\n  const quadrantData = diagObj.db.getQuadrantData();\n  const quadrantsGroup = group.append(\"g\").attr(\"class\", \"quadrants\");\n  const borderGroup = group.append(\"g\").attr(\"class\", \"border\");\n  const dataPointGroup = group.append(\"g\").attr(\"class\", \"data-points\");\n  const labelGroup = group.append(\"g\").attr(\"class\", \"labels\");\n  const titleGroup = group.append(\"g\").attr(\"class\", \"title\");\n  if (quadrantData.title) {\n    titleGroup.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", quadrantData.title.fill).attr(\"font-size\", quadrantData.title.fontSize).attr(\"dominant-baseline\", getDominantBaseLine(quadrantData.title.horizontalPos)).attr(\"text-anchor\", getTextAnchor(quadrantData.title.verticalPos)).attr(\"transform\", getTransformation(quadrantData.title)).text(quadrantData.title.text);\n  }\n  if (quadrantData.borderLines) {\n    borderGroup.selectAll(\"line\").data(quadrantData.borderLines).enter().append(\"line\").attr(\"x1\", (data) => data.x1).attr(\"y1\", (data) => data.y1).attr(\"x2\", (data) => data.x2).attr(\"y2\", (data) => data.y2).style(\"stroke\", (data) => data.strokeFill).style(\"stroke-width\", (data) => data.strokeWidth);\n  }\n  const quadrants = quadrantsGroup.selectAll(\"g.quadrant\").data(quadrantData.quadrants).enter().append(\"g\").attr(\"class\", \"quadrant\");\n  quadrants.append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill);\n  quadrants.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text)).text((data) => data.text.text);\n  const labels = labelGroup.selectAll(\"g.label\").data(quadrantData.axisLabels).enter().append(\"g\").attr(\"class\", \"label\");\n  labels.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.horizontalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.verticalPos)).attr(\"transform\", (data) => getTransformation(data));\n  const dataPoints = dataPointGroup.selectAll(\"g.data-point\").data(quadrantData.points).enter().append(\"g\").attr(\"class\", \"data-point\");\n  dataPoints.append(\"circle\").attr(\"cx\", (data) => data.x).attr(\"cy\", (data) => data.y).attr(\"r\", (data) => data.radius).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeColor).attr(\"stroke-width\", (data) => data.strokeWidth);\n  dataPoints.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text.text).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text));\n}, \"draw\");\nvar quadrantRenderer_default = {\n  draw\n};\n\n// src/diagrams/quadrant-chart/quadrantDiagram.ts\nvar diagram = {\n  parser: quadrant_default,\n  db: quadrantDb_default,\n  renderer: quadrantRenderer_default,\n  styles: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => \"\", \"styles\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-2OG54O6I.mjs\n"));

/***/ })

}]);