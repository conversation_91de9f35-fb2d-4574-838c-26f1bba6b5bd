#!/bin/bash

# OCTI智能评估系统 - Docker部署脚本
# 用于生产环境的一键部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量配置..."
    
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，从模板创建..."
        cp .env.example .env
        log_warning "请编辑.env文件并填入正确的配置值"
        exit 1
    fi
    
    # 检查关键环境变量
    source .env
    
    if [ -z "$POSTGRES_PASSWORD" ] || [ "$POSTGRES_PASSWORD" = "your_strong_password_here" ]; then
        log_error "请在.env文件中设置POSTGRES_PASSWORD"
        exit 1
    fi
    
    if [ -z "$REDIS_PASSWORD" ] || [ "$REDIS_PASSWORD" = "your_redis_password_here" ]; then
        log_error "请在.env文件中设置REDIS_PASSWORD"
        exit 1
    fi
    
    if [ -z "$NEXTAUTH_SECRET" ] || [ "$NEXTAUTH_SECRET" = "your_nextauth_secret_32_chars_min" ]; then
        log_error "请在.env文件中设置NEXTAUTH_SECRET"
        exit 1
    fi
    
    log_success "环境变量检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p backups
    mkdir -p ssl
    mkdir -p uploads
    
    log_success "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    # 先启动数据库服务
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    docker-compose run --rm app npx prisma migrate deploy
    
    # 启动所有服务
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查各个服务
    services=("postgres" "redis" "app" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "${service}.*Up"; then
            log_success "${service} 服务运行正常"
        else
            log_error "${service} 服务启动失败"
            docker-compose logs $service
            exit 1
        fi
    done
    
    # 检查应用健康状态
    log_info "检查应用健康状态..."
    sleep 5
    
    if curl -f http://localhost/api/health > /dev/null 2>&1; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败，请检查日志"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "OCTI智能评估系统部署完成！"
    echo ""
    echo "访问信息："
    echo "  应用地址: http://localhost"
    echo "  API地址: http://localhost/api"
    echo "  健康检查: http://localhost/api/health"
    echo ""
    echo "管理命令："
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo "  查看状态: docker-compose ps"
    echo ""
    echo "数据库连接："
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: $POSTGRES_DB"
    echo "  用户: $POSTGRES_USER"
    echo ""
    echo "Redis连接："
    echo "  主机: localhost"
    echo "  端口: 6379"
    echo ""
}

# 主函数
main() {
    log_info "开始部署OCTI智能评估系统..."
    
    check_dependencies
    check_environment
    create_directories
    build_images
    start_services
    check_services
    show_deployment_info
    
    log_success "部署完成！"
}

# 处理命令行参数
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "停止所有服务..."
        docker-compose down
        log_success "服务已停止"
        ;;
    "restart")
        log_info "重启所有服务..."
        docker-compose restart
        log_success "服务已重启"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "clean")
        log_warning "清理所有容器和数据..."
        read -p "确定要删除所有数据吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v
            docker system prune -f
            log_success "清理完成"
        else
            log_info "取消清理操作"
        fi
        ;;
    *)
        echo "用法: $0 {deploy|stop|restart|logs|status|clean}"
        echo ""
        echo "命令说明："
        echo "  deploy  - 部署系统（默认）"
        echo "  stop    - 停止所有服务"
        echo "  restart - 重启所有服务"
        echo "  logs    - 查看实时日志"
        echo "  status  - 查看服务状态"
        echo "  clean   - 清理所有容器和数据"
        exit 1
        ;;
esac
