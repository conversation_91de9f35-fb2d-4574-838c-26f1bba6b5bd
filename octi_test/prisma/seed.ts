/**
 * OCTI智能评估系统 - 数据库种子文件
 *
 * 初始化数据库基础数据，包括预设题目、用户角色等
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 生成UUID的简单函数
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function main() {
  console.log('开始数据库种子数据初始化...')

  // 创建系统管理员用户
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: generateUUID(),
      email: '<EMAIL>',
      name: 'OCTI系统管理员',
      password: '$2b$10$example.hash.for.testing', // 实际应用中需要正确的密码哈希
      role: 'ADMIN',
    },
  })

  console.log('创建系统管理员用户:', adminUser.email)

  // 创建示例组织
  const organization = await prisma.organization.create({
    data: {
      id: generateUUID(),
      name: '示例公益组织',
      type: 'NGO',
      scale: 'SMALL',
      stage: 'GROWTH',
      foundedYear: 2020,
      location: '北京市',
      website: 'https://demo-org.example.com',
      description: '这是一个用于测试OCTI系统的示例公益组织',
      focusAreas: ['教育', '环保'],
      userId: adminUser.id,
    },
  })

  console.log('创建示例组织:', organization.name)

  // 创建非营利组织画像
  const nonprofitProfile = await prisma.nonprofitProfile.create({
    data: {
      id: generateUUID(),
      organizationId: organization.id,
      organizationType: '公益组织',
      serviceArea: ['教育', '环保'],
      organizationScale: '小型组织',
      developmentStage: '成长期',
      operatingModel: '项目运营',
      impactPositioning: '社区影响',
      organizationalCulture: '开放协作',
      missionVision: {
        mission: '通过教育和环保项目改善社区生活',
        vision: '创建一个更美好、更可持续的未来',
        values: ['诚信', '创新', '合作', '可持续']
      },
      governance: {
        boardStructure: '理事会制',
        decisionMaking: '民主决策',
        transparency: '公开透明',
        accountability: ['年度报告', '财务公开']
      },
      resourceProfile: {
        fundingSources: ['政府资助', '企业捐赠', '个人捐款'],
        volunteerBase: '活跃志愿者团队',
        partnerships: ['政府部门', '其他NGO', '企业'],
        capacity: '中等能力'
      },
      impactMeasurement: {
        hasTheory: true,
        measurementTools: ['问卷调查', '数据分析'],
        reportingFrequency: '季度报告',
        stakeholderFeedback: true
      },
      challenges: ['资金不足', '人才缺乏', '影响力有限'],
      goals: ['扩大服务范围', '提升专业能力', '增强可持续性'],
      region: '华北地区',
    },
  })

  console.log('创建非营利组织画像:', nonprofitProfile.id)

  // 创建预设问题
  const presetQuestions = [
    {
      dimension: 'governance',
      subDimension: 'board_structure',
      questionType: 'SINGLE_CHOICE',
      questionContent: {
        title: '您的组织是否建立了正式的理事会或董事会？',
        description: '评估组织治理结构的完善程度',
        options: [
          { text: '已建立完善的理事会，定期召开会议', value: 5 },
          { text: '已建立理事会，但运作不够规范', value: 4 },
          { text: '正在筹建理事会', value: 3 },
          { text: '计划建立理事会', value: 2 },
          { text: '暂无理事会计划', value: 1 }
        ]
      },
      displayOrder: 1,
      version: '4.0'
    },
    {
      dimension: 'governance',
      subDimension: 'decision_making',
      questionType: 'MULTIPLE_CHOICE',
      questionContent: {
        title: '您的组织在重大决策时会采用哪些方式？',
        description: '了解组织决策机制的民主化程度',
        options: [
          { text: '理事会投票决定', value: 'board_vote' },
          { text: '管理层讨论决定', value: 'management_discussion' },
          { text: '全员参与讨论', value: 'all_staff_discussion' },
          { text: '专家咨询', value: 'expert_consultation' },
          { text: '受益人意见征询', value: 'beneficiary_consultation' },
          { text: '创始人/负责人决定', value: 'founder_decision' }
        ]
      },
      displayOrder: 2,
      version: '4.0'
    },
    {
      dimension: 'strategy',
      subDimension: 'strategic_planning',
      questionType: 'SINGLE_CHOICE',
      questionContent: {
        title: '您的组织是否制定了明确的战略规划？',
        description: '评估组织战略规划的完善程度',
        options: [
          { text: '有完整的3-5年战略规划，定期更新', value: 5 },
          { text: '有基本的战略规划，但更新不及时', value: 4 },
          { text: '有简单的发展计划', value: 3 },
          { text: '正在制定战略规划', value: 2 },
          { text: '暂无明确的战略规划', value: 1 }
        ]
      },
      displayOrder: 3,
      version: '4.0'
    },
    {
      dimension: 'operations',
      subDimension: 'project_management',
      questionType: 'SCALE',
      questionContent: {
        title: '请评价您的组织在项目管理方面的能力',
        description: '1分表示能力很弱，5分表示能力很强',
        options: {
          min: 1,
          max: 5,
          labels: ['很弱', '较弱', '一般', '较强', '很强']
        }
      },
      displayOrder: 4,
      version: '4.0'
    },
    {
      dimension: 'finance',
      subDimension: 'fundraising',
      questionType: 'MULTIPLE_CHOICE',
      questionContent: {
        title: '您的组织主要通过哪些渠道进行筹资？',
        description: '了解组织的筹资渠道多样性',
        options: [
          { text: '政府资助', value: 'government' },
          { text: '企业捐赠', value: 'corporate' },
          { text: '个人捐款', value: 'individual' },
          { text: '基金会资助', value: 'foundation' },
          { text: '服务收费', value: 'service_fee' },
          { text: '其他', value: 'other' }
        ]
      },
      displayOrder: 5,
      version: '4.0'
    }
  ];

  for (const questionData of presetQuestions) {
    await prisma.presetQuestion.create({
      data: {
        id: generateUUID(),
        dimension: questionData.dimension,
        subDimension: questionData.subDimension,
        questionType: questionData.questionType as any,
        questionContent: questionData.questionContent,
        displayOrder: questionData.displayOrder,
        version: questionData.version,
        isActive: true,
      },
    })
  }

  console.log('创建预设问题:', presetQuestions.length, '个')

  console.log('数据库种子数据初始化完成！')
}

main()
  .catch((e) => {
    console.error('种子数据初始化失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })