{"llm": {"minimax": {"baseUrl": "https://api.minimax.chat/v1", "model": "abab6.5s-chat", "maxTokens": 4000, "temperature": 0.7}, "deepseek": {"baseUrl": "https://api.deepseek.com/v1", "model": "deepseek-reasoner", "maxTokens": 4000, "temperature": 0.5}}, "questionnaire": {"totalQuestions": 60, "presetQuestions": 32, "aiGeneratedQuestions": 28, "dimensions": {"governance": {"name": "治理能力", "description": "组织治理结构、决策机制、透明度和问责制", "presetCount": 8, "aiGeneratedCount": 7, "weight": 0.25}, "strategy": {"name": "战略能力", "description": "战略规划、目标设定、创新能力和适应性", "presetCount": 8, "aiGeneratedCount": 7, "weight": 0.25}, "operations": {"name": "运营能力", "description": "项目管理、服务交付、质量控制和效率优化", "presetCount": 8, "aiGeneratedCount": 7, "weight": 0.25}, "finance": {"name": "财务能力", "description": "财务管理、筹资能力、成本控制和可持续性", "presetCount": 8, "aiGeneratedCount": 7, "weight": 0.25}}, "qualityThresholds": {"relevance": 0.8, "clarity": 0.7, "uniqueness": 0.6}}, "analysis": {"confidenceThreshold": 0.7, "enableDualModel": true, "fusionStrategy": "weighted", "scoringRanges": {"expert": [80, 100], "advanced": [60, 79], "proficient": [40, 59], "developing": [20, 39], "beginner": [0, 19]}}, "security": {"enableRateLimit": true, "rateLimitRequests": 100, "rateLimitWindow": 900, "enableCors": true, "allowedOrigins": ["http://localhost:3000"], "enableEncryption": true}, "features": {"enableDialogue": true, "enableDualModel": true, "enableReports": true, "enableExport": true, "maxAssessmentsPerOrg": 10, "maxQuestionsPerQuestionnaire": 100}}