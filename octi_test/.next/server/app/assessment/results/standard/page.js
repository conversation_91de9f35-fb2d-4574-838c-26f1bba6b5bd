/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/assessment/results/standard/page";
exports.ids = ["app/assessment/results/standard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fresults%2Fstandard%2Fpage&page=%2Fassessment%2Fresults%2Fstandard%2Fpage&appPaths=%2Fassessment%2Fresults%2Fstandard%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fresults%2Fstandard%2Fpage&page=%2Fassessment%2Fresults%2Fstandard%2Fpage&appPaths=%2Fassessment%2Fresults%2Fstandard%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'assessment',\n        {\n        children: [\n        'results',\n        {\n        children: [\n        'standard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/assessment/results/standard/page.tsx */ \"(rsc)/./src/app/assessment/results/standard/page.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/assessment/results/standard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/assessment/results/standard/page\",\n        pathname: \"/assessment/results/standard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fresults%2Fstandard%2Fpage&page=%2Fassessment%2Fresults%2Fstandard%2Fpage&appPaths=%2Fassessment%2Fresults%2Fstandard%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXBwbGUlMkZEb2N1bWVudHMlMkYyLjElMjBBSSUyMEpvdXJuZXklMkZDdXJzb3JfcHJvamVjdHMlMkZvY3RpX3Rlc3QlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLz8wYTFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FwcGxlL0RvY3VtZW50cy8yLjEgQUkgSm91cm5leS9DdXJzb3JfcHJvamVjdHMvb2N0aV90ZXN0L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fclient-error-handler.tsx%22%2C%22ids%22%3A%5B%22ClientErrorHandler%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fclient-error-handler.tsx%22%2C%22ids%22%3A%5B%22ClientErrorHandler%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-error-handler.tsx */ \"(ssr)/./src/app/client-error-handler.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/error-boundary.tsx */ \"(ssr)/./src/components/error-boundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fclient-error-handler.tsx%22%2C%22ids%22%3A%5B%22ClientErrorHandler%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/assessment/results/standard/page.tsx */ \"(ssr)/./src/app/assessment/results/standard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXBwbGUlMkZEb2N1bWVudHMlMkYyLjElMjBBSSUyMEpvdXJuZXklMkZDdXJzb3JfcHJvamVjdHMlMkZvY3RpX3Rlc3QlMkZzcmMlMkZhcHAlMkZhc3Nlc3NtZW50JTJGcmVzdWx0cyUyRnN0YW5kYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vP2E3ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXBwbGUvRG9jdW1lbnRzLzIuMSBBSSBKb3VybmV5L0N1cnNvcl9wcm9qZWN0cy9vY3RpX3Rlc3Qvc3JjL2FwcC9hc3Nlc3NtZW50L3Jlc3VsdHMvc3RhbmRhcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXBwbGUlMkZEb2N1bWVudHMlMkYyLjElMjBBSSUyMEpvdXJuZXklMkZDdXJzb3JfcHJvamVjdHMlMkZvY3RpX3Rlc3QlMkZzcmMlMkZhcHAlMkZlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUFzSCIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vPzg0OTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXBwbGUvRG9jdW1lbnRzLzIuMSBBSSBKb3VybmV5L0N1cnNvcl9wcm9qZWN0cy9vY3RpX3Rlc3Qvc3JjL2FwcC9lcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/assessment/results/standard/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/assessment/results/standard/page.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StandardResultsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layouts/user-layout */ \"(ssr)/./src/components/layouts/user-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Download,Loader2,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Download,Loader2,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Download,Loader2,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Download,Loader2,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _components_ui_markdown_renderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/markdown-renderer */ \"(ssr)/./src/components/ui/markdown-renderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n/**\n * 标准版评估结果页面\n */ function StandardResultsPage() {\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 获取分析结果\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchAnalysisResult = async ()=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                // 优先从localStorage获取数据\n                let profileData = localStorage.getItem(\"profileAnswers\");\n                let responsesData = localStorage.getItem(\"assessmentResponses\");\n                // 如果本地没有数据，尝试从服务器恢复\n                if (!profileData || !responsesData) {\n                    console.log(\"\\uD83D\\uDD04 本地数据缺失，尝试从服务器恢复...\");\n                    const sessionId = localStorage.getItem(\"assessmentSessionId\") || \"default\";\n                    try {\n                        const response = await fetch(`/api/assessment/responses?sessionId=${sessionId}`);\n                        if (response.ok) {\n                            const serverData = await response.json();\n                            if (serverData.success && serverData.data) {\n                                profileData = JSON.stringify(serverData.data.profile);\n                                responsesData = JSON.stringify(serverData.data.responses);\n                                console.log(\"✅ 从服务器恢复数据成功\");\n                                // 更新本地存储\n                                localStorage.setItem(\"profileAnswers\", profileData);\n                                localStorage.setItem(\"assessmentResponses\", responsesData);\n                            }\n                        }\n                    } catch (serverError) {\n                        console.warn(\"⚠️ 服务器数据恢复失败:\", serverError);\n                    }\n                }\n                if (!profileData || !responsesData) {\n                    throw new Error(\"缺少评估数据，请重新完成评估\");\n                }\n                const profile = JSON.parse(profileData);\n                const responses = JSON.parse(responsesData);\n                console.log(\"\\uD83D\\uDCCA 开始请求标准版分析结果...\");\n                // 调用标准版分析API\n                const response = await fetch(\"/api/assessment/simple-analyze\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        profile,\n                        responses,\n                        version: \"standard\"\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || \"分析服务暂时不可用\");\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error || \"分析失败\");\n                }\n                setAnalysisResult(result.data);\n                console.log(\"✅ 标准版分析结果获取成功:\", result.data);\n            } catch (err) {\n                console.error(\"❌ 获取分析结果失败:\", err);\n                setError(err instanceof Error ? err.message : \"获取分析结果失败\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchAnalysisResult();\n    }, []);\n    // 下载PDF报告功能（使用浏览器打印功能）\n    const handleDownloadReport = async ()=>{\n        if (!analysisResult) return;\n        setIsDownloading(true);\n        try {\n            // 生成HTML内容\n            const htmlContent = generateHTMLReport(analysisResult);\n            // 创建新窗口并打印\n            const printWindow = window.open(\"\", \"_blank\");\n            if (printWindow) {\n                printWindow.document.write(htmlContent);\n                printWindow.document.close();\n                // 等待内容加载完成后打印\n                printWindow.onload = ()=>{\n                    printWindow.print();\n                    printWindow.close();\n                };\n            }\n            console.log(\"✅ PDF报告打印窗口已打开\");\n        } catch (error) {\n            console.error(\"❌ PDF报告生成失败:\", error);\n            alert(\"报告生成失败，请重试\");\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // 生成HTML报告内容\n    const generateHTMLReport = (result)=>{\n        const timestamp = new Date().toLocaleString();\n        return `\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>OCTI组织能力评估报告</title>\n    <style>\n        body {\n            font-family: 'Microsoft YaHei', 'SimSun', sans-serif;\n            line-height: 1.6;\n            margin: 40px;\n            color: #333;\n        }\n        .header {\n            text-align: center;\n            color: #2563eb;\n            font-size: 24px;\n            font-weight: bold;\n            margin-bottom: 30px;\n            border-bottom: 2px solid #2563eb;\n            padding-bottom: 10px;\n        }\n        .info-section {\n            margin-bottom: 25px;\n            padding: 15px;\n            background-color: #f8f9fa;\n            border-radius: 5px;\n        }\n        .section-title {\n            font-size: 18px;\n            font-weight: bold;\n            color: #1f2937;\n            margin-bottom: 10px;\n            border-left: 4px solid #2563eb;\n            padding-left: 10px;\n        }\n        .content {\n            margin-bottom: 20px;\n        }\n        ul {\n            margin: 10px 0;\n            padding-left: 20px;\n        }\n        li {\n            margin-bottom: 5px;\n        }\n        .footer {\n            margin-top: 40px;\n            text-align: center;\n            font-size: 12px;\n            color: #666;\n            border-top: 1px solid #ddd;\n            padding-top: 20px;\n        }\n        @media print {\n            body { margin: 20px; }\n            .header { page-break-after: avoid; }\n            .section-title { page-break-after: avoid; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        OCTI组织能力评估报告\n    </div>\n\n    <div class=\"info-section\">\n        <strong>生成时间：</strong>${timestamp}<br>\n        <strong>组织类型：</strong>${result.organizationType.name} (${result.organizationType.code})\n    </div>\n\n    <div class=\"content\">\n        <div class=\"section-title\">组织类型描述</div>\n        <p>${result.organizationType.description}</p>\n    </div>\n\n    <div class=\"content\">\n        <div class=\"section-title\">组织特征</div>\n        <ul>\n            ${result.organizationType.characteristics.map((item)=>`<li>${item}</li>`).join(\"\")}\n        </ul>\n    </div>\n\n    <div class=\"content\">\n        <div class=\"section-title\">组织优势</div>\n        <ul>\n            ${result.organizationType.strengths.map((item)=>`<li>${item}</li>`).join(\"\")}\n        </ul>\n    </div>\n\n    <div class=\"content\">\n        <div class=\"section-title\">面临挑战</div>\n        <ul>\n            ${result.organizationType.challenges.map((item)=>`<li>${item}</li>`).join(\"\")}\n        </ul>\n    </div>\n\n    <div class=\"content\">\n        <div class=\"section-title\">详细分析</div>\n        <p>${result.detailedAnalysis}</p>\n    </div>\n\n    <div class=\"footer\">\n        报告由OCTI智能评估系统生成 | 版本：${result.version} | 生成时间：${timestamp}\n    </div>\n</body>\n</html>\n    `.trim();\n    };\n    // 加载状态\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserLayout, {\n            showBackButton: true,\n            backHref: \"/assessment/questionnaire/assessment-1\",\n            title: \"标准版分析结果\",\n            subtitle: \"AI正在为您生成专业的组织能力分析报告...\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserPageContainer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-12 w-12 animate-spin text-primary mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"组织能力评估师正在分析您的数据\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"这可能需要30-60秒，请耐心等待...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserLayout, {\n            showBackButton: true,\n            backHref: \"/assessment/questionnaire/assessment-1\",\n            title: \"标准版分析结果\",\n            subtitle: \"获取分析结果时出现问题\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserPageContainer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"分析失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>window.location.reload(),\n                                    children: \"重试\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/assessment/start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        children: \"重新开始评估\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n            lineNumber: 294,\n            columnNumber: 7\n        }, this);\n    }\n    // 没有数据\n    if (!analysisResult) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserLayout, {\n            showBackButton: true,\n            backHref: \"/assessment/start\",\n            title: \"标准版分析结果\",\n            subtitle: \"没有找到分析结果\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserPageContainer, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-6xl mb-4\",\n                            children: \"\\uD83D\\uDCCA\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"暂无分析结果\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6\",\n                            children: \"请先完成组织画像和能力评估问卷\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/assessment/start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                children: \"开始评估\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserLayout, {\n        showBackButton: true,\n        backHref: \"/assessment/questionnaire/assessment-1\",\n        title: \"标准版分析结果\",\n        subtitle: \"恭喜您完成评估！以下是您的组织能力分析报告\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.UserPageContainer, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold border border-blue-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            \"标准版分析 - 组织能力评估师\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_user_layout__WEBPACK_IMPORTED_MODULE_3__.StepIndicator, {\n                    currentStep: 4,\n                    totalSteps: 4,\n                    steps: [\n                        \"组织信息\",\n                        \"能力评估\",\n                        \"结果分析\",\n                        \"发展建议\"\n                    ]\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"组织类型识别\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 font-bold text-lg\",\n                                                children: analysisResult.organizationType.code\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    children: analysisResult.organizationType.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: analysisResult.organizationType.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-800 mb-2\",\n                                                    children: \"核心优势\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: analysisResult.organizationType.strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-600 flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mr-2\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                strength\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-800 mb-2\",\n                                                    children: \"发展挑战\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: analysisResult.organizationType.challenges.map((challenge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-600 flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-orange-500 mr-2\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                challenge\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-6\",\n                            children: \"AI详细分析报告\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_markdown_renderer__WEBPACK_IMPORTED_MODULE_5__.MarkdownRenderer, {\n                                content: analysisResult.detailedAnalysis,\n                                className: \"text-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"flex items-center space-x-2\",\n                                    onClick: handleDownloadReport,\n                                    disabled: isDownloading,\n                                    children: [\n                                        isDownloading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isDownloading ? \"下载中...\" : \"下载报告\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Download_Loader2_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"分享结果\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm\",\n                            children: [\n                                \"完成时间：\",\n                                new Date(analysisResult.timestamp).toLocaleString(\"zh-CN\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-2\",\n                                    children: \"想要更深入的分析？\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"专业版提供DeepSeek深度推理分析，获得更全面的战略洞察和发展建议\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/assessment/start?version=professional\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700\",\n                                        children: \"升级到专业版\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx\",\n        lineNumber: 343,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/assessment/results/standard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/client-error-handler.tsx":
/*!******************************************!*\
  !*** ./src/app/client-error-handler.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientErrorHandler: () => (/* binding */ ClientErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * OCTI智能评估系统 - 客户端错误处理组件\n * \n * 在客户端设置全局错误处理器，忽略已知的外部错误\n */ /* __next_internal_client_entry_do_not_use__ ClientErrorHandler auto */ \nfunction ClientErrorHandler() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 处理未捕获的Promise拒绝\n        const handleUnhandledRejection = (event)=>{\n            console.warn(\"Unhandled promise rejection:\", event.reason);\n            // 忽略已知的外部错误\n            const reason = event.reason;\n            const reasonString = String(reason);\n            if (reasonString.includes(\"MetaMask\") || reasonString.includes(\"chrome-extension\") || reasonString.includes(\"Failed to connect to MetaMask\") || reason?.stack && reason.stack.includes(\"chrome-extension\")) {\n                event.preventDefault();\n                return;\n            }\n        };\n        // 处理全局JavaScript错误\n        const handleGlobalError = (event)=>{\n            console.warn(\"Global error:\", event.error);\n            // 忽略已知的外部错误\n            if (event.filename?.includes(\"chrome-extension\") || event.error?.stack?.includes(\"chrome-extension\") || event.message?.includes(\"MetaMask\") || event.message?.includes(\"Failed to connect to MetaMask\")) {\n                return;\n            }\n        };\n        // 添加事件监听器\n        window.addEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        window.addEventListener(\"error\", handleGlobalError);\n        // 清理函数\n        return ()=>{\n            window.removeEventListener(\"unhandledrejection\", handleUnhandledRejection);\n            window.removeEventListener(\"error\", handleGlobalError);\n        };\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/client-error-handler.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/**\n * OCTI智能评估系统 - 全局错误页面\n * \n * 处理应用级别的错误，提供友好的错误界面和恢复选项\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 记录错误到控制台\n        console.error(\"Application error:\", error);\n    // 这里可以添加错误上报逻辑\n    // 例如发送到错误监控服务\n    }, [\n        error\n    ]);\n    // 检查是否是已知的外部错误\n    const isExternalError = error.message?.includes(\"MetaMask\") || error.message?.includes(\"chrome-extension\") || error.stack?.includes(\"chrome-extension\");\n    if (isExternalError) {\n        // 对于外部错误，自动重试\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            const timer = setTimeout(()=>{\n                reset();\n            }, 1000);\n            return ()=>clearTimeout(timer);\n        }, [\n            reset\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl\",\n                        children: \"\\uD83D\\uDD04\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"正在恢复...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"检测到外部扩展冲突，正在自动恢复应用状态。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl\",\n                            children: \"\\uD83D\\uDE35\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"出现了一些问题\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"很抱歉，应用遇到了意外错误。请尝试重新加载或联系技术支持。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"cursor-pointer font-medium mb-2\",\n                                    children: \"错误详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"错误消息:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-xs bg-muted p-2 rounded mt-1 overflow-auto\",\n                                                    children: error.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"错误ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"text-xs bg-muted px-1 py-0.5 rounded ml-1\",\n                                                    children: error.digest\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this),\n                                         true && error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"堆栈跟踪:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-xs bg-muted p-2 rounded mt-1 overflow-auto max-h-32\",\n                                                    children: error.stack\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: reset,\n                            className: \"w-full\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"flex-1\",\n                                    children: \"刷新页面\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.href = \"/\",\n                                    className: \"flex-1\",\n                                    children: \"返回首页\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"如果问题持续存在，请尝试：\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-xs text-muted-foreground space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 清除浏览器缓存和Cookie\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 禁用浏览器扩展\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 使用无痕模式访问\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• 联系技术支持团队\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center pt-4 border-t\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        children: \"联系技术支持\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/error-boundary.tsx":
/*!*******************************************!*\
  !*** ./src/components/error-boundary.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   setupGlobalErrorHandlers: () => (/* binding */ setupGlobalErrorHandlers),\n/* harmony export */   useAsyncError: () => (/* binding */ useAsyncError),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/**\n * OCTI智能评估系统 - 错误边界组件\n * \n * 捕获和处理React组件错误，提供友好的错误界面\n */ /* __next_internal_client_entry_do_not_use__ ErrorBoundary,useErrorHandler,useAsyncError,setupGlobalErrorHandlers,withErrorBoundary auto */ \n\n\n\n// ============================================================================\n// 错误边界组件\n// ============================================================================\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // 调用错误回调\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            const { fallback: Fallback } = this.props;\n            if (Fallback && this.state.error) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Fallback, {\n                    error: this.state.error,\n                    resetError: this.resetError\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE35\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"出现了一些问题\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-6\",\n                            children: \"很抱歉，应用遇到了意外错误。请尝试刷新页面或联系技术支持。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"cursor-pointer font-medium mb-2\",\n                                    children: \"错误详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"whitespace-pre-wrap text-xs bg-muted p-2 rounded mt-2 overflow-auto\",\n                                    children: [\n                                        error.message,\n                                        error.stack && `\\n\\n${error.stack}`\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: resetError,\n                            className: \"flex-1\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.location.reload(),\n                            className: \"flex-1\",\n                            children: \"刷新页面\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>window.location.href = \"/\",\n                        children: \"返回首页\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n// ============================================================================\n// Hook版本的错误边界\n// ============================================================================\nfunction useErrorHandler() {\n    return (error, errorInfo)=>{\n        console.error(\"Error caught by useErrorHandler:\", error, errorInfo);\n    // 这里可以添加错误上报逻辑\n    // 例如发送到错误监控服务\n    };\n}\n// ============================================================================\n// 异步错误处理Hook\n// ============================================================================\nfunction useAsyncError() {\n    const [, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState();\n    return react__WEBPACK_IMPORTED_MODULE_1___default().useCallback((error)=>{\n        setError(()=>{\n            throw error;\n        });\n    }, []);\n}\n// ============================================================================\n// 全局错误处理器\n// ============================================================================\nfunction setupGlobalErrorHandlers() {\n    // 处理未捕获的Promise拒绝\n    window.addEventListener(\"unhandledrejection\", (event)=>{\n        console.error(\"Unhandled promise rejection:\", event.reason);\n        // 忽略某些已知的外部错误\n        if (event.reason?.message?.includes(\"MetaMask\") || event.reason?.message?.includes(\"chrome-extension\") || event.reason?.stack?.includes(\"chrome-extension\")) {\n            event.preventDefault();\n            return;\n        }\n    // 这里可以添加错误上报逻辑\n    });\n    // 处理全局JavaScript错误\n    window.addEventListener(\"error\", (event)=>{\n        console.error(\"Global error:\", event.error);\n        // 忽略某些已知的外部错误\n        if (event.filename?.includes(\"chrome-extension\") || event.error?.stack?.includes(\"chrome-extension\") || event.message?.includes(\"MetaMask\")) {\n            return;\n        }\n    // 这里可以添加错误上报逻辑\n    });\n}\n// ============================================================================\n// 错误边界HOC\n// ============================================================================\nfunction withErrorBoundary(Component, errorFallback) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            fallback: errorFallback,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx\",\n            lineNumber: 223,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/error-boundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layouts/user-layout.tsx":
/*!************************************************!*\
  !*** ./src/components/layouts/user-layout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StepIndicator: () => (/* binding */ StepIndicator),\n/* harmony export */   UserLayout: () => (/* binding */ UserLayout),\n/* harmony export */   UserPageContainer: () => (/* binding */ UserPageContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Brain,HelpCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Brain,HelpCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Brain,HelpCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/**\n * OCTI智能评估系统 - 用户端专用布局\n * \n * 为普通用户提供简洁、专注的评估体验\n * 移除所有管理功能，只保留评估相关的导航\n */ \n\n\n\n/**\n * 用户端专用布局组件\n * 提供简洁的头部导航和页脚\n */ function UserLayout({ children, showBackButton = false, backHref = \"/\", title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: showBackButton ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: backHref,\n                                        className: \"flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"返回\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"OCTI评估\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/help\",\n                                        className: \"flex items-center space-x-1 text-gray-600 hover:text-primary transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"帮助\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        (title || subtitle) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 17\n                                }, this),\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Brain_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"\\xa9 2025 OCTI智能评估系统. 保留所有权利.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/help\",\n                                        className: \"text-gray-600 hover:text-primary transition-colors\",\n                                        children: \"使用帮助\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/privacy\",\n                                        className: \"text-gray-600 hover:text-primary transition-colors\",\n                                        children: \"隐私政策\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-primary transition-colors\",\n                                        children: \"联系我们\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n/**\n * 用户端页面容器组件\n * 提供标准的内容区域样式\n */ function UserPageContainer({ children, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `container mx-auto px-4 py-8 max-w-4xl ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\nfunction StepIndicator({ currentStep, totalSteps, steps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center space-x-4\",\n            children: steps.map((step, index)=>{\n                const stepNumber = index + 1;\n                const isActive = stepNumber === currentStep;\n                const isCompleted = stepNumber < currentStep;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `\n                flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium\n                ${isActive ? \"bg-primary text-white\" : isCompleted ? \"bg-green-500 text-white\" : \"bg-gray-200 text-gray-600\"}\n              `,\n                            children: stepNumber\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `\n                ml-2 text-sm font-medium\n                ${isActive ? \"text-primary\" : isCompleted ? \"text-green-600\" : \"text-gray-500\"}\n              `,\n                            children: step\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 15\n                        }, this),\n                        index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `\n                  w-8 h-0.5 mx-4\n                  ${isCompleted ? \"bg-green-500\" : \"bg-gray-200\"}\n                `\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/layouts/user-layout.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layouts/user-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/markdown-renderer.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/markdown-renderer.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownRenderer: () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mermaid_diagram__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mermaid-diagram */ \"(ssr)/./src/components/ui/mermaid-diagram.tsx\");\n/* __next_internal_client_entry_do_not_use__ MarkdownRenderer auto */ \n\n\n/**\n * 清理和格式化分析文本\n * 去除 Markdown 符号，优化分行和层次结构\n */ function cleanAnalysisText(text) {\n    return text// 去除 Markdown 格式符号\n    .replace(/\\*\\*(.*?)\\*\\*/g, \"$1\") // 去除粗体标记\n    .replace(/\\*(.*?)\\*/g, \"$1\") // 去除斜体标记\n    .replace(/#{1,6}\\s*/g, \"\") // 去除标题标记\n    .replace(/`(.*?)`/g, \"$1\") // 去除代码标记\n    // 优化分行和格式\n    .replace(/---+/g, \"\\n---\\n\") // 分隔线前后加换行\n    .replace(/(\\d+\\.\\s*\\*\\*[^*]+\\*\\*)/g, \"\\n$1\") // 数字标题前加换行\n    .replace(/(\\*\\*[^*]+\\*\\*：)/g, \"\\n$1\") // 粗体标题后加换行\n    .replace(/(\\*\\*[^*]+\\*\\*)/g, \"\\n$1\\n\") // 独立粗体标题前后加换行\n    // 处理列表项\n    .replace(/^(\\s*)-\\s+/gm, \"\\n- \") // 列表项前加换行\n    .replace(/^(\\s*)•\\s+/gm, \"\\n- \") // 统一列表符号\n    // 清理多余的换行\n    .replace(/\\n{3,}/g, \"\\n\\n\") // 多个换行合并为两个\n    .trim();\n}\n/**\n * Markdown渲染组件\n * 专门处理OCTI分析结果中的表格、列表等格式\n */ function MarkdownRenderer({ content, className = \"\" }) {\n    // 首先清理和格式化文本\n    const cleanedContent = cleanAnalysisText(content);\n    /**\n   * 解析并渲染表格\n   */ const renderTable = (tableText)=>{\n        const lines = tableText.trim().split(\"\\n\");\n        if (lines.length < 3) return null; // 至少需要标题行、分隔行、数据行\n        // 解析表头\n        const headers = lines[0].split(\"|\").map((h)=>h.trim()).filter((h)=>h);\n        // 解析数据行（跳过分隔行）\n        const rows = lines.slice(2).map((line)=>line.split(\"|\").map((cell)=>cell.trim()).filter((cell)=>cell)).filter((row)=>row.length > 0);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto my-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full border-collapse border border-gray-300 bg-white rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gradient-to-r from-blue-50 to-purple-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: headers.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700\",\n                                    children: header\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: rows.map((row, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: row.map((cell, cellIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"border border-gray-300 px-4 py-3 text-sm text-gray-600\",\n                                        children: cell\n                                    }, cellIndex, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 19\n                                    }, this))\n                            }, rowIndex, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    };\n    /**\n   * 解析并渲染Mermaid图表\n   */ const renderMermaid = (mermaidCode, index)=>{\n        // 提取mermaid代码块内容\n        const codeMatch = mermaidCode.match(/```mermaid\\s*([\\s\\S]*?)\\s*```/);\n        if (!codeMatch) return null;\n        const diagramCode = codeMatch[1].trim();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mermaid_diagram__WEBPACK_IMPORTED_MODULE_2__.SimpleMermaidRenderer, {\n            code: diagramCode,\n            className: \"my-6\"\n        }, `mermaid-${index}`, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    };\n    /**\n   * 解析并渲染内容\n   */ const renderContent = ()=>{\n        // 分割清理后的内容为段落\n        const paragraphs = cleanedContent.split(\"\\n\\n\");\n        return paragraphs.map((paragraph, index)=>{\n            const trimmed = paragraph.trim();\n            if (!trimmed) return null;\n            // 检查是否为Mermaid图表\n            if (trimmed.includes(\"```mermaid\")) {\n                return renderMermaid(trimmed, index);\n            }\n            // 检查是否为表格\n            if (trimmed.includes(\"|\") && trimmed.includes(\"---\")) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: renderTable(trimmed)\n                }, index, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this);\n            }\n            // 检查是否为分隔线\n            if (trimmed === \"---\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                    className: \"my-6 border-gray-300\"\n                }, index, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this);\n            }\n            // 检查是否为标题\n            if (trimmed.match(/^[一二三四五六七八九十]+、/)) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-800 mt-6 mb-3 border-l-4 border-blue-500 pl-3\",\n                    children: trimmed\n                }, index, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, this);\n            }\n            // 检查是否为子标题\n            if (trimmed.match(/^\\d+\\.\\s+/)) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"text-md font-medium text-gray-700 mt-5 mb-3 bg-gray-50 p-3 rounded-lg border-l-4 border-gray-400\",\n                    children: trimmed\n                }, index, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this);\n            }\n            // 检查是否为重要标题（如\"当前表现：\"、\"主要优势：\"等）\n            if (trimmed.match(/^(当前表现|主要优势|改进空间|核心建议|分析说明)：?\\s*$/)) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    className: \"text-sm font-semibold text-green-700 mt-4 mb-2 uppercase tracking-wide bg-green-50 px-2 py-1 rounded\",\n                    children: trimmed\n                }, index, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, this);\n            }\n            // 检查是否为列表项\n            if (trimmed.startsWith(\"- \") || trimmed.startsWith(\"• \")) {\n                const items = trimmed.split(\"\\n\").filter((line)=>line.trim() && (line.startsWith(\"- \") || line.startsWith(\"• \")));\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-none space-y-2 my-3 ml-4\",\n                    children: items.map((item, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"text-gray-600 flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-500 mr-2 mt-1\",\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: item.replace(/^[-•]\\s*/, \"\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, itemIndex, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 15\n                        }, this))\n                }, index, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this);\n            }\n            // 检查是否为带冒号的描述性内容\n            if (trimmed.includes(\"：\") && !trimmed.match(/^(当前表现|主要优势|改进空间|核心建议|分析说明)：?\\s*$/)) {\n                const [label, ...contentParts] = trimmed.split(\"：\");\n                const content = contentParts.join(\"：\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-700\",\n                            children: [\n                                label,\n                                \"：\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-600 ml-1\",\n                            children: content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 11\n                }, this);\n            }\n            // 普通段落\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 leading-relaxed my-3 text-justify\",\n                children: trimmed\n            }, index, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this);\n        }).filter(Boolean);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: renderContent()\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/markdown-renderer.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/markdown-renderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/mermaid-diagram.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/mermaid-diagram.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MermaidDiagram: () => (/* binding */ MermaidDiagram),\n/* harmony export */   SimpleMermaidRenderer: () => (/* binding */ SimpleMermaidRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MermaidDiagram,SimpleMermaidRenderer auto */ \n\n/**\n * Mermaid图表渲染组件\n * 用于渲染DeepSeek分析结果中的Mermaid图表\n */ function MermaidDiagram({ code, className = \"\" }) {\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 动态加载Mermaid库\n        const loadMermaid = async ()=>{\n            try {\n                // 检查是否已经加载了Mermaid\n                if (false) {}\n                // 动态导入Mermaid\n                const mermaidModule = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/mermaid\"), __webpack_require__.e(\"vendor-chunks/lodash-es\"), __webpack_require__.e(\"vendor-chunks/khroma\"), __webpack_require__.e(\"vendor-chunks/dayjs\"), __webpack_require__.e(\"vendor-chunks/d3-array\"), __webpack_require__.e(\"vendor-chunks/d3-shape\"), __webpack_require__.e(\"vendor-chunks/d3-geo\"), __webpack_require__.e(\"vendor-chunks/d3-selection\"), __webpack_require__.e(\"vendor-chunks/d3-scale-chromatic\"), __webpack_require__.e(\"vendor-chunks/d3-hierarchy\"), __webpack_require__.e(\"vendor-chunks/d3-transition\"), __webpack_require__.e(\"vendor-chunks/d3-interpolate\"), __webpack_require__.e(\"vendor-chunks/d3-scale\"), __webpack_require__.e(\"vendor-chunks/d3-random\"), __webpack_require__.e(\"vendor-chunks/d3-format\"), __webpack_require__.e(\"vendor-chunks/d3-quadtree\"), __webpack_require__.e(\"vendor-chunks/d3-time\"), __webpack_require__.e(\"vendor-chunks/d3-force\"), __webpack_require__.e(\"vendor-chunks/d3-ease\"), __webpack_require__.e(\"vendor-chunks/@iconify\"), __webpack_require__.e(\"vendor-chunks/d3-contour\"), __webpack_require__.e(\"vendor-chunks/d3-fetch\"), __webpack_require__.e(\"vendor-chunks/d3-polygon\"), __webpack_require__.e(\"vendor-chunks/robust-predicates\"), __webpack_require__.e(\"vendor-chunks/d3-zoom\"), __webpack_require__.e(\"vendor-chunks/d3-drag\"), __webpack_require__.e(\"vendor-chunks/d3-color\"), __webpack_require__.e(\"vendor-chunks/d3-chord\"), __webpack_require__.e(\"vendor-chunks/stylis\"), __webpack_require__.e(\"vendor-chunks/d3-time-format\"), __webpack_require__.e(\"vendor-chunks/d3-dsv\"), __webpack_require__.e(\"vendor-chunks/d3-delaunay\"), __webpack_require__.e(\"vendor-chunks/d3-brush\"), __webpack_require__.e(\"vendor-chunks/d3-timer\"), __webpack_require__.e(\"vendor-chunks/d3-axis\"), __webpack_require__.e(\"vendor-chunks/d3-path\"), __webpack_require__.e(\"vendor-chunks/d3-dispatch\"), __webpack_require__.e(\"vendor-chunks/@braintree\"), __webpack_require__.e(\"vendor-chunks/marked\"), __webpack_require__.e(\"vendor-chunks/internmap\"), __webpack_require__.e(\"vendor-chunks/dompurify\"), __webpack_require__.e(\"vendor-chunks/delaunator\"), __webpack_require__.e(\"vendor-chunks/d3\"), __webpack_require__.e(\"vendor-chunks/ts-dedent\"), __webpack_require__.e(\"vendor-chunks/roughjs\")]).then(__webpack_require__.bind(__webpack_require__, /*! mermaid */ \"(ssr)/./node_modules/mermaid/dist/mermaid.core.mjs\"));\n                const mermaid = mermaidModule.default;\n                // 初始化Mermaid\n                mermaid.initialize({\n                    startOnLoad: false,\n                    theme: \"default\",\n                    securityLevel: \"loose\",\n                    fontFamily: \"Arial, sans-serif\",\n                    fontSize: 14,\n                    flowchart: {\n                        useMaxWidth: true,\n                        htmlLabels: true,\n                        curve: \"basis\"\n                    }\n                });\n                renderDiagram();\n            } catch (error) {\n                console.warn(\"Mermaid加载失败，使用文本显示:\", error);\n                renderFallback();\n            }\n        };\n        const renderDiagram = async ()=>{\n            if (!containerRef.current) return;\n            try {\n                const mermaidModule = window.mermaid || await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/mermaid\"), __webpack_require__.e(\"vendor-chunks/lodash-es\"), __webpack_require__.e(\"vendor-chunks/khroma\"), __webpack_require__.e(\"vendor-chunks/dayjs\"), __webpack_require__.e(\"vendor-chunks/d3-array\"), __webpack_require__.e(\"vendor-chunks/d3-shape\"), __webpack_require__.e(\"vendor-chunks/d3-geo\"), __webpack_require__.e(\"vendor-chunks/d3-selection\"), __webpack_require__.e(\"vendor-chunks/d3-scale-chromatic\"), __webpack_require__.e(\"vendor-chunks/d3-hierarchy\"), __webpack_require__.e(\"vendor-chunks/d3-transition\"), __webpack_require__.e(\"vendor-chunks/d3-interpolate\"), __webpack_require__.e(\"vendor-chunks/d3-scale\"), __webpack_require__.e(\"vendor-chunks/d3-random\"), __webpack_require__.e(\"vendor-chunks/d3-format\"), __webpack_require__.e(\"vendor-chunks/d3-quadtree\"), __webpack_require__.e(\"vendor-chunks/d3-time\"), __webpack_require__.e(\"vendor-chunks/d3-force\"), __webpack_require__.e(\"vendor-chunks/d3-ease\"), __webpack_require__.e(\"vendor-chunks/@iconify\"), __webpack_require__.e(\"vendor-chunks/d3-contour\"), __webpack_require__.e(\"vendor-chunks/d3-fetch\"), __webpack_require__.e(\"vendor-chunks/d3-polygon\"), __webpack_require__.e(\"vendor-chunks/robust-predicates\"), __webpack_require__.e(\"vendor-chunks/d3-zoom\"), __webpack_require__.e(\"vendor-chunks/d3-drag\"), __webpack_require__.e(\"vendor-chunks/d3-color\"), __webpack_require__.e(\"vendor-chunks/d3-chord\"), __webpack_require__.e(\"vendor-chunks/stylis\"), __webpack_require__.e(\"vendor-chunks/d3-time-format\"), __webpack_require__.e(\"vendor-chunks/d3-dsv\"), __webpack_require__.e(\"vendor-chunks/d3-delaunay\"), __webpack_require__.e(\"vendor-chunks/d3-brush\"), __webpack_require__.e(\"vendor-chunks/d3-timer\"), __webpack_require__.e(\"vendor-chunks/d3-axis\"), __webpack_require__.e(\"vendor-chunks/d3-path\"), __webpack_require__.e(\"vendor-chunks/d3-dispatch\"), __webpack_require__.e(\"vendor-chunks/@braintree\"), __webpack_require__.e(\"vendor-chunks/marked\"), __webpack_require__.e(\"vendor-chunks/internmap\"), __webpack_require__.e(\"vendor-chunks/dompurify\"), __webpack_require__.e(\"vendor-chunks/delaunator\"), __webpack_require__.e(\"vendor-chunks/d3\"), __webpack_require__.e(\"vendor-chunks/ts-dedent\"), __webpack_require__.e(\"vendor-chunks/roughjs\")]).then(__webpack_require__.bind(__webpack_require__, /*! mermaid */ \"(ssr)/./node_modules/mermaid/dist/mermaid.core.mjs\"));\n                const mermaid = mermaidModule.default || mermaidModule;\n                // 清空容器\n                containerRef.current.innerHTML = \"\";\n                // 生成唯一ID\n                const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n                // 渲染图表\n                const { svg } = await mermaid.render(id, code);\n                if (containerRef.current) {\n                    containerRef.current.innerHTML = svg;\n                }\n            } catch (error) {\n                console.warn(\"Mermaid渲染失败:\", error);\n                renderFallback();\n            }\n        };\n        const renderFallback = ()=>{\n            if (!containerRef.current) return;\n            containerRef.current.innerHTML = `\n        <div class=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n          <div class=\"text-sm text-gray-600 mb-2\">📊 流程图（文本格式）</div>\n          <pre class=\"text-xs text-gray-700 whitespace-pre-wrap font-mono bg-white p-3 rounded border overflow-x-auto\">${code}</pre>\n          <div class=\"text-xs text-gray-400 mt-2\">注：图表渲染需要Mermaid库支持</div>\n        </div>\n      `;\n        };\n        loadMermaid();\n    }, [\n        code\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mermaid-container my-6 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: containerRef,\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 border border-gray-200 rounded-lg p-4 animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"正在加载图表...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n/**\n * 简化版Mermaid渲染器（不依赖外部库）\n */ function SimpleMermaidRenderer({ code, className = \"\" }) {\n    // 解析简单的流程图结构\n    const parseSimpleFlowchart = (mermaidCode)=>{\n        const lines = mermaidCode.split(\"\\n\").filter((line)=>line.trim());\n        const nodes = {};\n        const edges = [];\n        lines.forEach((line)=>{\n            const trimmed = line.trim();\n            // 解析节点定义 A[文本]\n            const nodeMatch = trimmed.match(/^([A-Z]+)\\[([^\\]]+)\\]$/);\n            if (nodeMatch) {\n                nodes[nodeMatch[1]] = nodeMatch[2];\n                return;\n            }\n            // 解析连接 A --> B\n            const edgeMatch = trimmed.match(/^([A-Z]+)\\s*-->\\s*([A-Z]+)$/);\n            if (edgeMatch) {\n                edges.push({\n                    from: edgeMatch[1],\n                    to: edgeMatch[2]\n                });\n                return;\n            }\n            // 解析带标签的连接 A -->|标签| B\n            const labeledEdgeMatch = trimmed.match(/^([A-Z]+)\\s*-->\\|([^|]+)\\|\\s*([A-Z]+)$/);\n            if (labeledEdgeMatch) {\n                edges.push({\n                    from: labeledEdgeMatch[1],\n                    to: labeledEdgeMatch[3],\n                    label: labeledEdgeMatch[2]\n                });\n            }\n        });\n        return {\n            nodes,\n            edges\n        };\n    };\n    const { nodes, edges } = parseSimpleFlowchart(code);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `simple-mermaid my-6 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-600 mb-4 font-medium\",\n                    children: \"\\uD83D\\uDCCA 流程图\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                Object.keys(nodes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\",\n                            children: Object.entries(nodes).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border border-blue-300 rounded-lg p-3 text-center shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-500 font-mono\",\n                                            children: key\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-700 mt-1\",\n                                            children: value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, key, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        edges.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 mb-2\",\n                                    children: \"流程关系：\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: edges.map((edge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-blue-100 px-2 py-1 rounded text-xs\",\n                                                    children: nodes[edge.from] || edge.from\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-2 text-blue-500\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 23\n                                                }, this),\n                                                edge.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-blue-600 mx-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        edge.label,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 38\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-purple-100 px-2 py-1 rounded text-xs\",\n                                                    children: nodes[edge.to] || edge.to\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"text-xs text-gray-700 whitespace-pre-wrap font-mono bg-white p-3 rounded border overflow-x-auto\",\n                    children: code\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/mermaid-diagram.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/mermaid-diagram.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateUUID: () => (/* binding */ generateUUID),\n/* harmony export */   getEnvVar: () => (/* binding */ getEnvVar),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名的工具函数\n * 使用clsx处理条件类名，使用tailwind-merge处理冲突的Tailwind类\n * \n * @param inputs - 类名输入\n * @returns 合并后的类名字符串\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期为中文格式\n * \n * @param date - 日期对象或日期字符串\n * @param options - 格式化选项\n * @returns 格式化后的日期字符串\n */ function formatDate(date, options = {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\"\n}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"zh-CN\", options).format(dateObj);\n}\n/**\n * 格式化相对时间（如：2小时前）\n * \n * @param date - 日期对象或日期字符串\n * @returns 相对时间字符串\n */ function formatRelativeTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"刚刚\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `${diffInMinutes}分钟前`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `${diffInHours}小时前`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 30) {\n        return `${diffInDays}天前`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n        return `${diffInMonths}个月前`;\n    }\n    const diffInYears = Math.floor(diffInMonths / 12);\n    return `${diffInYears}年前`;\n}\n/**\n * 生成随机ID\n * \n * @param length - ID长度，默认为8\n * @returns 随机ID字符串\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 生成UUID v4\n * \n * @returns UUID字符串\n */ function generateUUID() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c === \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * 延迟执行函数\n * \n * @param ms - 延迟毫秒数\n * @returns Promise\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 防抖函数\n * \n * @param func - 要防抖的函数\n * @param wait - 等待时间（毫秒）\n * @returns 防抖后的函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n * \n * @param func - 要节流的函数\n * @param limit - 限制时间（毫秒）\n * @returns 节流后的函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 深度克隆对象\n * \n * @param obj - 要克隆的对象\n * @returns 克隆后的对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") {\n        return obj;\n    }\n    if (obj instanceof Date) {\n        return new Date(obj.getTime());\n    }\n    if (obj instanceof Array) {\n        return obj.map((item)=>deepClone(item));\n    }\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 检查对象是否为空\n * \n * @param obj - 要检查的对象\n * @returns 是否为空\n */ function isEmpty(obj) {\n    if (obj == null) return true;\n    if (typeof obj === \"string\" || Array.isArray(obj)) return obj.length === 0;\n    if (typeof obj === \"object\") return Object.keys(obj).length === 0;\n    return false;\n}\n/**\n * 格式化文件大小\n * \n * @param bytes - 字节数\n * @param decimals - 小数位数，默认为2\n * @returns 格式化后的文件大小字符串\n */ function formatFileSize(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\",\n        \"EB\",\n        \"ZB\",\n        \"YB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\n/**\n * 格式化数字（添加千分位分隔符）\n * \n * @param num - 数字\n * @returns 格式化后的数字字符串\n */ function formatNumber(num) {\n    return new Intl.NumberFormat(\"zh-CN\").format(num);\n}\n/**\n * 格式化百分比\n * \n * @param value - 数值（0-1之间）\n * @param decimals - 小数位数，默认为1\n * @returns 格式化后的百分比字符串\n */ function formatPercentage(value, decimals = 1) {\n    return `${(value * 100).toFixed(decimals)}%`;\n}\n/**\n * 截断文本\n * \n * @param text - 原始文本\n * @param maxLength - 最大长度\n * @param suffix - 后缀，默认为'...'\n * @returns 截断后的文本\n */ function truncateText(text, maxLength, suffix = \"...\") {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength - suffix.length) + suffix;\n}\n/**\n * 获取文件扩展名\n * \n * @param filename - 文件名\n * @returns 文件扩展名（不包含点）\n */ function getFileExtension(filename) {\n    return filename.slice((filename.lastIndexOf(\".\") - 1 >>> 0) + 2);\n}\n/**\n * 验证邮箱格式\n * \n * @param email - 邮箱地址\n * @returns 是否为有效邮箱\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 验证手机号格式（中国大陆）\n * \n * @param phone - 手机号\n * @returns 是否为有效手机号\n */ function isValidPhone(phone) {\n    const phoneRegex = /^1[3-9]\\d{9}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * 获取环境变量\n * \n * @param key - 环境变量键名\n * @param defaultValue - 默认值\n * @returns 环境变量值\n */ function getEnvVar(key, defaultValue) {\n    const value = process.env[key];\n    if (value === undefined) {\n        if (defaultValue !== undefined) {\n            return defaultValue;\n        }\n        throw new Error(`Environment variable ${key} is not defined`);\n    }\n    return value;\n}\n/**\n * 安全的JSON解析\n * \n * @param jsonString - JSON字符串\n * @param defaultValue - 解析失败时的默认值\n * @returns 解析后的对象或默认值\n */ function safeJsonParse(jsonString, defaultValue) {\n    try {\n        return JSON.parse(jsonString);\n    } catch  {\n        return defaultValue;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b90fdc2465c3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NmRlMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI5MGZkYzI0NjVjM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/assessment/results/standard/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/assessment/results/standard/page.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/results/standard/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/client-error-handler.tsx":
/*!******************************************!*\
  !*** ./src/app/client-error-handler.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientErrorHandler: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/client-error-handler.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/client-error-handler.tsx#ClientErrorHandler`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/client-error-handler.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $$typeof: () => (/* binding */ $$typeof),\n/* harmony export */   __esModule: () => (/* binding */ __esModule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/error.tsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/error.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-sans\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-sans\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var _components_error_boundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/error-boundary */ \"(rsc)/./src/components/error-boundary.tsx\");\n/* harmony import */ var _client_error_handler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client-error-handler */ \"(rsc)/./src/app/client-error-handler.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"OCTI智能评估系统\",\n        template: \"%s | OCTI智能评估系统\"\n    },\n    description: \"基于配置驱动和智能体模块化的组织能力评估平台，专为公益机构设计\",\n    keywords: [\n        \"OCTI\",\n        \"组织能力评估\",\n        \"公益机构\",\n        \"智能评估\",\n        \"人工智能\",\n        \"组织发展\",\n        \"能力建设\"\n    ],\n    authors: [\n        {\n            name: \"OCTI Team\"\n        }\n    ],\n    creator: \"OCTI Team\",\n    publisher: \"OCTI Team\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://octi.example.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"zh_CN\",\n        url: \"https://octi.example.com\",\n        title: \"OCTI智能评估系统\",\n        description: \"基于配置驱动和智能体模块化的组织能力评估平台\",\n        siteName: \"OCTI智能评估系统\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"OCTI智能评估系统\",\n        description: \"基于配置驱动和智能体模块化的组织能力评估平台\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"google-site-verification-code\"\n    }\n};\n/**\n * OCTI智能评估系统根布局组件\n * \n * 提供全局样式、字体配置和元数据设置\n * 支持响应式设计和可访问性优化\n */ function RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"color-scheme\",\n                        content: \"light dark\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"min-h-screen bg-background font-sans antialiased\", (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable), (next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_6___default().variable)),\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_client_error_handler__WEBPACK_IMPORTED_MODULE_4__.ClientErrorHandler, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_error_boundary__WEBPACK_IMPORTED_MODULE_3__.ErrorBoundary, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex min-h-screen flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * OCTI智能评估系统 - 全局加载页面\n * \n * 在页面切换时显示的加载界面\n */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 border-4 border-muted rounded-full animate-spin border-t-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-primary rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"加载中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: \"正在为您准备OCTI智能评估系统\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/**\n * OCTI智能评估系统 - 404页面\n * \n * 当用户访问不存在的页面时显示的友好错误页面\n */ \n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-8xl\",\n                            children: \"\\uD83D\\uDD0D\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-muted-foreground\",\n                            children: \"页面未找到\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"很抱歉，您访问的页面不存在或已被移动。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"w-full\",\n                                children: \"返回首页\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        children: \"进入系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/assessments\",\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        children: \"评估管理\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-4 border-t\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: [\n                            \"如果您认为这是一个错误，请\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"text-primary hover:underline\",\n                                children: \"联系我们\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/not-found.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/error-boundary.tsx":
/*!*******************************************!*\
  !*** ./src/components/error-boundary.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ e0),\n/* harmony export */   setupGlobalErrorHandlers: () => (/* binding */ e3),\n/* harmony export */   useAsyncError: () => (/* binding */ e2),\n/* harmony export */   useErrorHandler: () => (/* binding */ e1),\n/* harmony export */   withErrorBoundary: () => (/* binding */ e4)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx#ErrorBoundary`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx#useErrorHandler`);\n\nconst e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx#useAsyncError`);\n\nconst e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx#setupGlobalErrorHandlers`);\n\nconst e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/error-boundary.tsx#withErrorBoundary`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7Ozs7Q0FJQyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9zcmMvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS50c3g/NjdlZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE9DVEnmmbrog73or4TkvLDns7vnu58gLSDplJnor6/ovrnnlYznu4Tku7ZcbiAqIFxuICog5o2V6I635ZKM5aSE55CGUmVhY3Tnu4Tku7bplJnor6/vvIzmj5Dkvpvlj4vlpb3nmoTplJnor6/nlYzpnaJcbiAqL1xuXG4ndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IEFsZXJ0LCBBbGVydERlc2NyaXB0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2FsZXJ0JztcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8g57G75Z6L5a6a5LmJXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmludGVyZmFjZSBFcnJvckJvdW5kYXJ5U3RhdGUge1xuICBoYXNFcnJvcjogYm9vbGVhbjtcbiAgZXJyb3I/OiBFcnJvcjtcbiAgZXJyb3JJbmZvPzogUmVhY3QuRXJyb3JJbmZvO1xufVxuXG5pbnRlcmZhY2UgRXJyb3JCb3VuZGFyeVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgZmFsbGJhY2s/OiBSZWFjdC5Db21wb25lbnRUeXBlPHsgZXJyb3I6IEVycm9yOyByZXNldEVycm9yOiAoKSA9PiB2b2lkIH0+O1xuICBvbkVycm9yPzogKGVycm9yOiBFcnJvciwgZXJyb3JJbmZvOiBSZWFjdC5FcnJvckluZm8pID0+IHZvaWQ7XG59XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIOmUmeivr+i+ueeVjOe7hOS7tlxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQgY2xhc3MgRXJyb3JCb3VuZGFyeSBleHRlbmRzIFJlYWN0LkNvbXBvbmVudDxFcnJvckJvdW5kYXJ5UHJvcHMsIEVycm9yQm91bmRhcnlTdGF0ZT4ge1xuICBjb25zdHJ1Y3Rvcihwcm9wczogRXJyb3JCb3VuZGFyeVByb3BzKSB7XG4gICAgc3VwZXIocHJvcHMpO1xuICAgIHRoaXMuc3RhdGUgPSB7IGhhc0Vycm9yOiBmYWxzZSB9O1xuICB9XG5cbiAgc3RhdGljIGdldERlcml2ZWRTdGF0ZUZyb21FcnJvcihlcnJvcjogRXJyb3IpOiBFcnJvckJvdW5kYXJ5U3RhdGUge1xuICAgIHJldHVybiB7XG4gICAgICBoYXNFcnJvcjogdHJ1ZSxcbiAgICAgIGVycm9yLFxuICAgIH07XG4gIH1cblxuICBjb21wb25lbnREaWRDYXRjaChlcnJvcjogRXJyb3IsIGVycm9ySW5mbzogUmVhY3QuRXJyb3JJbmZvKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3JCb3VuZGFyeSBjYXVnaHQgYW4gZXJyb3I6JywgZXJyb3IsIGVycm9ySW5mbyk7XG4gICAgXG4gICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICBlcnJvcixcbiAgICAgIGVycm9ySW5mbyxcbiAgICB9KTtcblxuICAgIC8vIOiwg+eUqOmUmeivr+Wbnuiwg1xuICAgIGlmICh0aGlzLnByb3BzLm9uRXJyb3IpIHtcbiAgICAgIHRoaXMucHJvcHMub25FcnJvcihlcnJvciwgZXJyb3JJbmZvKTtcbiAgICB9XG4gIH1cblxuICByZXNldEVycm9yID0gKCkgPT4ge1xuICAgIHRoaXMuc2V0U3RhdGUoeyBoYXNFcnJvcjogZmFsc2UsIGVycm9yOiB1bmRlZmluZWQsIGVycm9ySW5mbzogdW5kZWZpbmVkIH0pO1xuICB9O1xuXG4gIHJlbmRlcigpIHtcbiAgICBpZiAodGhpcy5zdGF0ZS5oYXNFcnJvcikge1xuICAgICAgY29uc3QgeyBmYWxsYmFjazogRmFsbGJhY2sgfSA9IHRoaXMucHJvcHM7XG4gICAgICBcbiAgICAgIGlmIChGYWxsYmFjayAmJiB0aGlzLnN0YXRlLmVycm9yKSB7XG4gICAgICAgIHJldHVybiA8RmFsbGJhY2sgZXJyb3I9e3RoaXMuc3RhdGUuZXJyb3J9IHJlc2V0RXJyb3I9e3RoaXMucmVzZXRFcnJvcn0gLz47XG4gICAgICB9XG5cbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxEZWZhdWx0RXJyb3JGYWxsYmFjayBcbiAgICAgICAgICBlcnJvcj17dGhpcy5zdGF0ZS5lcnJvcn0gXG4gICAgICAgICAgcmVzZXRFcnJvcj17dGhpcy5yZXNldEVycm9yfVxuICAgICAgICAvPlxuICAgICAgKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpcy5wcm9wcy5jaGlsZHJlbjtcbiAgfVxufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyDpu5jorqTplJnor6/lm57pgIDnu4Tku7Zcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuaW50ZXJmYWNlIERlZmF1bHRFcnJvckZhbGxiYWNrUHJvcHMge1xuICBlcnJvcj86IEVycm9yO1xuICByZXNldEVycm9yOiAoKSA9PiB2b2lkO1xufVxuXG5mdW5jdGlvbiBEZWZhdWx0RXJyb3JGYWxsYmFjayh7IGVycm9yLCByZXNldEVycm9yIH06IERlZmF1bHRFcnJvckZhbGxiYWNrUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWQgdy1mdWxsIHNwYWNlLXktNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi00XCI+8J+YtTwvZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItMlwiPuWHuueOsOS6huS4gOS6m+mXrumimDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTZcIj5cbiAgICAgICAgICAgIOW+iOaKseatie+8jOW6lOeUqOmBh+WIsOS6huaEj+WklumUmeivr+OAguivt+WwneivleWIt+aWsOmhtemdouaIluiBlOezu+aKgOacr+aUr+aMgeOAglxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICA8QWxlcnQ+XG4gICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgPGRldGFpbHMgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDxzdW1tYXJ5IGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGZvbnQtbWVkaXVtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIOmUmeivr+ivpuaDhVxuICAgICAgICAgICAgICAgIDwvc3VtbWFyeT5cbiAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cIndoaXRlc3BhY2UtcHJlLXdyYXAgdGV4dC14cyBiZy1tdXRlZCBwLTIgcm91bmRlZCBtdC0yIG92ZXJmbG93LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIHtlcnJvci5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAge2Vycm9yLnN0YWNrICYmIGBcXG5cXG4ke2Vycm9yLnN0YWNrfWB9XG4gICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgIDwvZGV0YWlscz5cbiAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e3Jlc2V0RXJyb3J9IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAg6YeN6K+VXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCIgXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIOWIt+aWsOmhtemdolxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiIFxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy8nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIOi/lOWbnummlumhtVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBIb29r54mI5pys55qE6ZSZ6K+v6L6555WMXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VFcnJvckhhbmRsZXIoKSB7XG4gIHJldHVybiAoZXJyb3I6IEVycm9yLCBlcnJvckluZm8/OiBSZWFjdC5FcnJvckluZm8pID0+IHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYXVnaHQgYnkgdXNlRXJyb3JIYW5kbGVyOicsIGVycm9yLCBlcnJvckluZm8pO1xuICAgIFxuICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOmUmeivr+S4iuaKpemAu+i+kVxuICAgIC8vIOS+i+WmguWPkemAgeWIsOmUmeivr+ebkeaOp+acjeWKoVxuICB9O1xufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyDlvILmraXplJnor6/lpITnkIZIb29rXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBc3luY0Vycm9yKCkge1xuICBjb25zdCBbLCBzZXRFcnJvcl0gPSBSZWFjdC51c2VTdGF0ZSgpO1xuICBcbiAgcmV0dXJuIFJlYWN0LnVzZUNhbGxiYWNrKChlcnJvcjogRXJyb3IpID0+IHtcbiAgICBzZXRFcnJvcigoKSA9PiB7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9KTtcbiAgfSwgW10pO1xufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyDlhajlsYDplJnor6/lpITnkIblmahcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuZXhwb3J0IGZ1bmN0aW9uIHNldHVwR2xvYmFsRXJyb3JIYW5kbGVycygpIHtcbiAgLy8g5aSE55CG5pyq5o2V6I6355qEUHJvbWlzZeaLkue7nVxuICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndW5oYW5kbGVkcmVqZWN0aW9uJywgKGV2ZW50KSA9PiB7XG4gICAgY29uc29sZS5lcnJvcignVW5oYW5kbGVkIHByb21pc2UgcmVqZWN0aW9uOicsIGV2ZW50LnJlYXNvbik7XG4gICAgXG4gICAgLy8g5b+955Wl5p+Q5Lqb5bey55+l55qE5aSW6YOo6ZSZ6K+vXG4gICAgaWYgKFxuICAgICAgZXZlbnQucmVhc29uPy5tZXNzYWdlPy5pbmNsdWRlcygnTWV0YU1hc2snKSB8fFxuICAgICAgZXZlbnQucmVhc29uPy5tZXNzYWdlPy5pbmNsdWRlcygnY2hyb21lLWV4dGVuc2lvbicpIHx8XG4gICAgICBldmVudC5yZWFzb24/LnN0YWNrPy5pbmNsdWRlcygnY2hyb21lLWV4dGVuc2lvbicpXG4gICAgKSB7XG4gICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDplJnor6/kuIrmiqXpgLvovpFcbiAgfSk7XG5cbiAgLy8g5aSE55CG5YWo5bGASmF2YVNjcmlwdOmUmeivr1xuICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignZXJyb3InLCAoZXZlbnQpID0+IHtcbiAgICBjb25zb2xlLmVycm9yKCdHbG9iYWwgZXJyb3I6JywgZXZlbnQuZXJyb3IpO1xuICAgIFxuICAgIC8vIOW/veeVpeafkOS6m+W3suefpeeahOWklumDqOmUmeivr1xuICAgIGlmIChcbiAgICAgIGV2ZW50LmZpbGVuYW1lPy5pbmNsdWRlcygnY2hyb21lLWV4dGVuc2lvbicpIHx8XG4gICAgICBldmVudC5lcnJvcj8uc3RhY2s/LmluY2x1ZGVzKCdjaHJvbWUtZXh0ZW5zaW9uJykgfHxcbiAgICAgIGV2ZW50Lm1lc3NhZ2U/LmluY2x1ZGVzKCdNZXRhTWFzaycpXG4gICAgKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIFxuICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOmUmeivr+S4iuaKpemAu+i+kVxuICB9KTtcbn1cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8g6ZSZ6K+v6L6555WMSE9DXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBmdW5jdGlvbiB3aXRoRXJyb3JCb3VuZGFyeTxQIGV4dGVuZHMgb2JqZWN0PihcbiAgQ29tcG9uZW50OiBSZWFjdC5Db21wb25lbnRUeXBlPFA+LFxuICBlcnJvckZhbGxiYWNrPzogUmVhY3QuQ29tcG9uZW50VHlwZTx7IGVycm9yOiBFcnJvcjsgcmVzZXRFcnJvcjogKCkgPT4gdm9pZCB9PlxuKSB7XG4gIGNvbnN0IFdyYXBwZWRDb21wb25lbnQgPSAocHJvcHM6IFApID0+IChcbiAgICA8RXJyb3JCb3VuZGFyeSBmYWxsYmFjaz17ZXJyb3JGYWxsYmFja30+XG4gICAgICA8Q29tcG9uZW50IHsuLi5wcm9wc30gLz5cbiAgICA8L0Vycm9yQm91bmRhcnk+XG4gICk7XG5cbiAgV3JhcHBlZENvbXBvbmVudC5kaXNwbGF5TmFtZSA9IGB3aXRoRXJyb3JCb3VuZGFyeSgke0NvbXBvbmVudC5kaXNwbGF5TmFtZSB8fCBDb21wb25lbnQubmFtZX0pYDtcbiAgXG4gIHJldHVybiBXcmFwcGVkQ29tcG9uZW50O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/error-boundary.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4Qix5U0FDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4PzZhMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTEgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIFsmX3N2Z106cG9pbnRlci1ldmVudHMtbm9uZSBbJl9zdmddOnNpemUtNCBbJl9zdmddOnNocmluay0wXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBzaGFkb3cgaG92ZXI6YmctcHJpbWFyeS85MFwiLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBzaGFkb3ctc20gaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBzaGFkb3ctc20gaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXNtIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDogXCJob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBsaW5rOiBcInRleHQtcHJpbWFyeSB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lXCIsXG4gICAgICB9LFxuICAgICAgc2l6ZToge1xuICAgICAgICBkZWZhdWx0OiBcImgtOSBweC00IHB5LTJcIixcbiAgICAgICAgc206IFwiaC04IHJvdW5kZWQtbWQgcHgtMyB0ZXh0LXhzXCIsXG4gICAgICAgIGxnOiBcImgtMTAgcm91bmRlZC1tZCBweC04XCIsXG4gICAgICAgIGljb246IFwiaC05IHctOVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb21wXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzaXplIiwic20iLCJsZyIsImljb24iLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiQ29tcCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateUUID: () => (/* binding */ generateUUID),\n/* harmony export */   getEnvVar: () => (/* binding */ getEnvVar),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名的工具函数\n * 使用clsx处理条件类名，使用tailwind-merge处理冲突的Tailwind类\n * \n * @param inputs - 类名输入\n * @returns 合并后的类名字符串\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期为中文格式\n * \n * @param date - 日期对象或日期字符串\n * @param options - 格式化选项\n * @returns 格式化后的日期字符串\n */ function formatDate(date, options = {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\"\n}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"zh-CN\", options).format(dateObj);\n}\n/**\n * 格式化相对时间（如：2小时前）\n * \n * @param date - 日期对象或日期字符串\n * @returns 相对时间字符串\n */ function formatRelativeTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"刚刚\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `${diffInMinutes}分钟前`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `${diffInHours}小时前`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 30) {\n        return `${diffInDays}天前`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n        return `${diffInMonths}个月前`;\n    }\n    const diffInYears = Math.floor(diffInMonths / 12);\n    return `${diffInYears}年前`;\n}\n/**\n * 生成随机ID\n * \n * @param length - ID长度，默认为8\n * @returns 随机ID字符串\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 生成UUID v4\n * \n * @returns UUID字符串\n */ function generateUUID() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c === \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * 延迟执行函数\n * \n * @param ms - 延迟毫秒数\n * @returns Promise\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 防抖函数\n * \n * @param func - 要防抖的函数\n * @param wait - 等待时间（毫秒）\n * @returns 防抖后的函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n * \n * @param func - 要节流的函数\n * @param limit - 限制时间（毫秒）\n * @returns 节流后的函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 深度克隆对象\n * \n * @param obj - 要克隆的对象\n * @returns 克隆后的对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") {\n        return obj;\n    }\n    if (obj instanceof Date) {\n        return new Date(obj.getTime());\n    }\n    if (obj instanceof Array) {\n        return obj.map((item)=>deepClone(item));\n    }\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 检查对象是否为空\n * \n * @param obj - 要检查的对象\n * @returns 是否为空\n */ function isEmpty(obj) {\n    if (obj == null) return true;\n    if (typeof obj === \"string\" || Array.isArray(obj)) return obj.length === 0;\n    if (typeof obj === \"object\") return Object.keys(obj).length === 0;\n    return false;\n}\n/**\n * 格式化文件大小\n * \n * @param bytes - 字节数\n * @param decimals - 小数位数，默认为2\n * @returns 格式化后的文件大小字符串\n */ function formatFileSize(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\",\n        \"EB\",\n        \"ZB\",\n        \"YB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\n/**\n * 格式化数字（添加千分位分隔符）\n * \n * @param num - 数字\n * @returns 格式化后的数字字符串\n */ function formatNumber(num) {\n    return new Intl.NumberFormat(\"zh-CN\").format(num);\n}\n/**\n * 格式化百分比\n * \n * @param value - 数值（0-1之间）\n * @param decimals - 小数位数，默认为1\n * @returns 格式化后的百分比字符串\n */ function formatPercentage(value, decimals = 1) {\n    return `${(value * 100).toFixed(decimals)}%`;\n}\n/**\n * 截断文本\n * \n * @param text - 原始文本\n * @param maxLength - 最大长度\n * @param suffix - 后缀，默认为'...'\n * @returns 截断后的文本\n */ function truncateText(text, maxLength, suffix = \"...\") {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength - suffix.length) + suffix;\n}\n/**\n * 获取文件扩展名\n * \n * @param filename - 文件名\n * @returns 文件扩展名（不包含点）\n */ function getFileExtension(filename) {\n    return filename.slice((filename.lastIndexOf(\".\") - 1 >>> 0) + 2);\n}\n/**\n * 验证邮箱格式\n * \n * @param email - 邮箱地址\n * @returns 是否为有效邮箱\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 验证手机号格式（中国大陆）\n * \n * @param phone - 手机号\n * @returns 是否为有效手机号\n */ function isValidPhone(phone) {\n    const phoneRegex = /^1[3-9]\\d{9}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * 获取环境变量\n * \n * @param key - 环境变量键名\n * @param defaultValue - 默认值\n * @returns 环境变量值\n */ function getEnvVar(key, defaultValue) {\n    const value = process.env[key];\n    if (value === undefined) {\n        if (defaultValue !== undefined) {\n            return defaultValue;\n        }\n        throw new Error(`Environment variable ${key} is not defined`);\n    }\n    return value;\n}\n/**\n * 安全的JSON解析\n * \n * @param jsonString - JSON字符串\n * @param defaultValue - 解析失败时的默认值\n * @returns 解析后的对象或默认值\n */ function safeJsonParse(jsonString, defaultValue) {\n    try {\n        return JSON.parse(jsonString);\n    } catch  {\n        return defaultValue;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fresults%2Fstandard%2Fpage&page=%2Fassessment%2Fresults%2Fstandard%2Fpage&appPaths=%2Fassessment%2Fresults%2Fstandard%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fresults%2Fstandard%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();