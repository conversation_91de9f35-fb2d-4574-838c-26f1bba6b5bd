"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/robust-predicates";
exports.ids = ["vendor-chunks/robust-predicates"];
exports.modules = {

/***/ "(ssr)/./node_modules/robust-predicates/esm/incircle.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/incircle.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* binding */ incircle),\n/* harmony export */   incirclefast: () => (/* binding */ incirclefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst iccerrboundA = (10 + 96 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundB = (4 + 48 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundC = (44 + 576 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst aa = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst axtbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst aytbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bxtca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bytca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cxtab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cytab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abtt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bctt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst catt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _32 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _32b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _64 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(64);\n\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adx, _8), _8, adx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdx, _8), _8, bdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdx, _8), _8, cdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, 2 * adx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, 2 * ady, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, 2 * bdy, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, 2 * cdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, 2 * cdy, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, adxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * adx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, adytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * ady, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, bdxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, bdytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdy, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, cdxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, cdytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdy, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nfunction incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nfunction incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/incircle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/insphere.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/insphere.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   insphere: () => (/* binding */ insphere),\n/* harmony export */   inspherefast: () => (/* binding */ inspherefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst isperrboundA = (16 + 224 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundB = (5 + 72 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundC = (71 + 1408 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst de = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst da = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst eb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst abc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bcd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cde = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst dea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst abd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cda = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst deb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\n\nconst adet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst bdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst cdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst ddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst edet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst abdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cdedet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(3456);\nconst deter = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(5760);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _24 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _48b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _96 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst _192 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nconst _384x = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384y = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384z = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _768 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(768);\n\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, a, az, _8), _8,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, b, bz, _8b), _8b,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, c, cz, _8c), _8c, _16, out);\n}\n\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(alen, a, blen, b, _48), _48,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(clen, c, dlen, d, _48b), _48b), _48b, _96);\n\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, x, _192), _192, x, _384x), _384x,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, y, _192), _192, y, _384y), _384y,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\n\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    s1 = ax * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n\n    const deterlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet,\n        liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet,\n            liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet,\n            liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n\n    return deter[deterlen - 1];\n}\n\nconst xdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst ydet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst zdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\n\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, x, _48), _48, x, xdet), xdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, y, _48), _48, y, ydet), ydet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\n\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    s1 = aex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n\n    const finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet,\n            liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet,\n            liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 &&\n        bextail === 0 && beytail === 0 && beztail === 0 &&\n        cextail === 0 && ceytail === 0 && ceztail === 0 &&\n        dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n\n    errbound = isperrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n\n    const abeps = (aex * beytail + bey * aextail) - (aey * bextail + bex * aeytail);\n    const bceps = (bex * ceytail + cey * bextail) - (bey * cextail + cex * beytail);\n    const cdeps = (cex * deytail + dey * cextail) - (cey * dextail + dex * ceytail);\n    const daeps = (dex * aeytail + aey * dextail) - (dey * aextail + aex * deytail);\n    const aceps = (aex * ceytail + cey * aextail) - (aey * cextail + cex * aeytail);\n    const bdeps = (bex * deytail + dey * bextail) - (bey * dextail + dex * beytail);\n    det +=\n        (((bex * bex + bey * bey + bez * bez) * ((cez * daeps + dez * aceps + aez * cdeps) +\n        (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) *\n        ((aez * bceps - bez * aceps + cez * abeps) + (aeztail * bc3 - beztail * ac3 + ceztail * ab3))) -\n        ((aex * aex + aey * aey + aez * aez) * ((bez * cdeps - cez * bdeps + dez * bceps) +\n        (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) *\n        ((dez * abeps + aez * bdeps + bez * daeps) + (deztail * ab3 + aeztail * bd3 + beztail * da3)))) +\n        2 * (((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) +\n        (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3)) -\n        ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) +\n        (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\n\nfunction insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    const det =\n        (clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab)) +\n        (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent =\n        (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift +\n        (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift +\n        (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift +\n        (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\n\nfunction inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    return (clift * dab - dlift * abc) + (alift * bcd - blift * cda);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/insphere.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient2d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient2d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient2d: () => (/* binding */ orient2d),\n/* harmony export */   orient2dfast: () => (/* binding */ orient2dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst ccwerrboundA = (3 + 16 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundB = (2 + 12 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundC = (9 + 64 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst B = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst C1 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst C2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nconst D = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nfunction orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nfunction orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient2d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient3d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient3d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient3d: () => (/* binding */ orient3d),\n/* harmony export */   orient3dfast: () => (/* binding */ orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst o3derrboundA = (7 + 56 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundB = (3 + 28 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundC = (26 + 288 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _12 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\n\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adz, _8), _8,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdz, _8b), _8b, _16), _16,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdz, _8), _8, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adz, _16), _16);\n\n    const catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdz, _16), _16);\n\n    const abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nfunction orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nfunction orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient3d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/util.js":
/*!****************************************************!*\
  !*** ./node_modules/robust-predicates/esm/util.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   estimate: () => (/* binding */ estimate),\n/* harmony export */   negate: () => (/* binding */ negate),\n/* harmony export */   resulterrbound: () => (/* binding */ resulterrbound),\n/* harmony export */   scale: () => (/* binding */ scale),\n/* harmony export */   splitter: () => (/* binding */ splitter),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   sum_three: () => (/* binding */ sum_three),\n/* harmony export */   vec: () => (/* binding */ vec)\n/* harmony export */ });\nconst epsilon = 1.1102230246251565e-16;\nconst splitter = 134217729;\nconst resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nfunction sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nfunction sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nfunction scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nfunction negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nfunction estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nfunction vec(n) {\n    return new Float64Array(n);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/index.js":
/*!*************************************************!*\
  !*** ./node_modules/robust-predicates/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incircle),\n/* harmony export */   incirclefast: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incirclefast),\n/* harmony export */   insphere: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.insphere),\n/* harmony export */   inspherefast: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.inspherefast),\n/* harmony export */   orient2d: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2d),\n/* harmony export */   orient2dfast: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2dfast),\n/* harmony export */   orient3d: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3d),\n/* harmony export */   orient3dfast: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./esm/orient2d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient2d.js\");\n/* harmony import */ var _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./esm/orient3d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient3d.js\");\n/* harmony import */ var _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./esm/incircle.js */ \"(ssr)/./node_modules/robust-predicates/esm/incircle.js\");\n/* harmony import */ var _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./esm/insphere.js */ \"(ssr)/./node_modules/robust-predicates/esm/insphere.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUN5RDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvaW5kZXguanM/MWJmMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7b3JpZW50MmQsIG9yaWVudDJkZmFzdH0gZnJvbSAnLi9lc20vb3JpZW50MmQuanMnO1xuZXhwb3J0IHtvcmllbnQzZCwgb3JpZW50M2RmYXN0fSBmcm9tICcuL2VzbS9vcmllbnQzZC5qcyc7XG5leHBvcnQge2luY2lyY2xlLCBpbmNpcmNsZWZhc3R9IGZyb20gJy4vZXNtL2luY2lyY2xlLmpzJztcbmV4cG9ydCB7aW5zcGhlcmUsIGluc3BoZXJlZmFzdH0gZnJvbSAnLi9lc20vaW5zcGhlcmUuanMnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/index.js\n");

/***/ })

};
;