/**
 * 简化的OCTI分析服务
 * 按照成功案例的模式：简单直接，避免复杂的JSON解析
 */

import fs from 'fs';
import path from 'path';
import { OCTITypeClassifier, OCTITypeResult } from './octi-type-classifier';
import { LLMClient } from '../llm/llm-client';

interface AnalysisRequest {
  profile: any;
  responses: any[];
  version: 'standard' | 'professional';
}

interface SimpleAnalysisResult {
  success: boolean;
  data: {
    // 第一部分：组织类型判断
    organizationType: OCTITypeResult;
    
    // 第二部分：LLM详细分析
    detailedAnalysis: string;
    
    // 基础信息
    timestamp: string;
    version: string;
  };
  error?: string;
}

/**
 * 简化的OCTI分析服务
 */
export class SimpleAnalysisService {
  private llmClient: LLMClient;
  private promptConfig: any;

  constructor() {
    this.llmClient = new LLMClient();
    this.loadPromptConfig();
  }

  /**
   * 加载提示词配置 - 学习成功案例的模式
   */
  private loadPromptConfig(): void {
    try {
      const configPath = path.join(process.cwd(), 'configs', 'octi_analysis_prompt.json');
      const configContent = fs.readFileSync(configPath, 'utf-8');
      this.promptConfig = JSON.parse(configContent);
      console.log('✅ OCTI分析提示词配置加载成功');
    } catch (error) {
      console.error('❌ 加载OCTI分析提示词配置失败:', error);
      throw new Error('无法加载OCTI分析配置');
    }
  }

  /**
   * 执行OCTI分析 - 分为两个部分
   */
  async analyzeOrganization(request: AnalysisRequest): Promise<SimpleAnalysisResult> {
    try {
      console.log('🔍 开始OCTI简化分析...');
      
      // 第一部分：快速组织类型判断
      const organizationType = OCTITypeClassifier.classifyOrganizationType(request.responses);
      console.log(`📊 组织类型判断完成: ${organizationType.code} - ${organizationType.name}`);
      
      // 第二部分：LLM详细分析
      const detailedAnalysis = await this.generateDetailedAnalysis(request);
      console.log('🤖 LLM详细分析完成');
      
      return {
        success: true,
        data: {
          organizationType,
          detailedAnalysis,
          timestamp: new Date().toISOString(),
          version: request.version
        }
      };
      
    } catch (error) {
      console.error('❌ OCTI分析失败:', error);
      
      // 容错处理：如果LLM失败，至少返回组织类型判断
      try {
        const organizationType = OCTITypeClassifier.classifyOrganizationType(request.responses);
        return {
          success: true,
          data: {
            organizationType,
            detailedAnalysis: this.generateFallbackAnalysis(organizationType, request),
            timestamp: new Date().toISOString(),
            version: request.version
          }
        };
      } catch (fallbackError) {
        return {
          success: false,
          error: `分析失败: ${error instanceof Error ? error.message : '未知错误'}`,
          data: {
            organizationType: {
              code: 'UNKNOWN',
              name: '未知类型',
              description: '分析过程中出现错误，无法确定组织类型',
              characteristics: [],
              strengths: [],
              challenges: []
            },
            detailedAnalysis: '抱歉，分析过程中出现错误，请稍后重试。',
            timestamp: new Date().toISOString(),
            version: request.version
          }
        };
      }
    }
  }

  /**
   * 生成LLM详细分析 - 学习成功案例的简单模式
   */
  private async generateDetailedAnalysis(request: AnalysisRequest): Promise<string> {
    // 构建系统提示词 - 直接使用JSON配置
    const systemPrompt = JSON.stringify(this.promptConfig, null, 2);
    
    // 构建用户提示词
    const userPrompt = this.buildUserPrompt(request);
    
    console.log('📏 提示词长度:', systemPrompt.length + userPrompt.length, '字符');
    
    // 调用LLM - 简单直接，不强制JSON格式
    const rawResponse = await this.llmClient.call({
      model: 'minimax-M1',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7
    });
    
    // 简单的文本清理 - 移除markdown符号
    return this.cleanAnalysisText(rawResponse);
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(request: AnalysisRequest): string {
    const { profile, responses } = request;
    
    // 格式化组织画像
    const organizationProfile = this.formatOrganizationProfile(profile);
    
    // 格式化问卷结果
    const questionnaireResults = this.formatQuestionnaireResults(responses);
    
    // 使用模板
    return this.promptConfig.promptTemplate
      .replace('{organizationProfile}', organizationProfile)
      .replace('{questionnaireResults}', questionnaireResults);
  }

  /**
   * 格式化组织画像
   */
  private formatOrganizationProfile(profile: any): string {
    return `
组织类型: ${profile.organizationType || '未知'}
服务领域: ${profile.serviceArea || '未知'}
发展阶段: ${profile.developmentStage || '未知'}
团队规模: ${profile.teamSize || '未知'}
运营模式: ${profile.operatingModel || '未知'}
影响范围: ${profile.impactScope || '未知'}
组织文化: ${profile.organizationCulture || '未知'}
主要挑战: ${profile.challengesPriorities || '未知'}
未来愿景: ${profile.futureVision || '未知'}
    `.trim();
  }

  /**
   * 格式化问卷结果
   */
  private formatQuestionnaireResults(responses: any[]): string {
    return responses.map((response, index) => {
      const answer = Array.isArray(response.answer) 
        ? response.answer.join(', ')
        : response.answer;
      return `${index + 1}. ${response.questionId}: ${answer}`;
    }).join('\n');
  }

  /**
   * 清理分析文本 - 移除markdown符号和调试信息
   */
  private cleanAnalysisText(text: string): string {
    return text
      // 移除调试信息
      .replace(/\([A-Z]{2,}_[PI]\d+\s*[=:]\s*[^)]+\)/g, '') // 移除 (IT_I002: 2分低) 类型
      .replace(/[A-Z]{2,}_[PI]\d+\s*[=:]\s*[^,，\s\n)]+/g, '') // 移除 IT_I002: 2分低 类型
      .replace(/[A-Z]{2,}_[PI]\d+\s*:\s*\d+分[低中高等]/g, '') // 移除 "IT_I002: 2分低" 类型
      .replace(/\d+分[低中高等]/g, '') // 移除 "2分低"、"4分中等" 类型
      .replace(/[A-Z]{2,}_[A-Z]\d+\s*:\s*\d+分[低中高等]/g, '') // 移除完整的评分调试信息
      .replace(/评分[：:]\s*\d+分/g, '') // 移除 "评分：4分" 类型
      .replace(/得分[：:]\s*\d+/g, '') // 移除 "得分：4" 类型
      .replace(/[A-Z]{2,}_[PI]\d+/g, '') // 移除单独的调试代码

      // 移除markdown符号
      .replace(/\*\*/g, '') // 移除粗体标记
      .replace(/#{1,6}\s*/g, '') // 移除标题标记
      .replace(/\*/g, '') // 移除斜体标记
      .replace(/`{1,3}/g, '') // 移除代码标记
      .replace(/^\s*[-*+]\s*/gm, '') // 移除列表标记
      .replace(/\n{3,}/g, '\n\n') // 合并多余的换行
      .trim();
  }

  /**
   * 生成备用分析 - 当LLM失败时使用
   */
  private generateFallbackAnalysis(organizationType: OCTITypeResult, request: AnalysisRequest): string {
    return `
1. 组织能力概览

根据问卷评估结果，您的组织属于"${organizationType.name}"类型。${organizationType.description}

2. 组织特征分析

主要特征：
${organizationType.characteristics.map((char, index) => `${index + 1}. ${char}`).join('\n')}

3. 核心优势

${organizationType.strengths.map((strength, index) => `${index + 1}. ${strength}`).join('\n')}

4. 发展挑战

${organizationType.challenges.map((challenge, index) => `${index + 1}. ${challenge}`).join('\n')}

5. 发展建议

基于您组织的类型特征，建议重点关注以下方面的发展：

1. 继续发挥现有优势，巩固核心竞争力
2. 针对识别出的挑战制定具体的改进计划
3. 加强团队能力建设，提升整体执行力
4. 建立定期的组织能力评估机制，持续改进

注：本分析基于基础评估模型生成，如需更详细的分析建议，请稍后重试或联系专业顾问。
    `.trim();
  }
}
