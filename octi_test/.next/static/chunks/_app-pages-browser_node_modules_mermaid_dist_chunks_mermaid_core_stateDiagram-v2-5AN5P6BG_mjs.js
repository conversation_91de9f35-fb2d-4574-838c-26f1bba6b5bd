"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_stateDiagram-v2-5AN5P6BG_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BFAMUDN2.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BFAMUDN2.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDiagramElement: function() { return /* binding */ getDiagramElement; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n// src/rendering-util/insertElementsForSize.js\n\nvar getDiagramElement = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1CRkFNVUROMi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRThCOztBQUU5QjtBQUM0QjtBQUM1Qix3Q0FBd0MsMkRBQU07QUFDOUM7QUFDQTtBQUNBLHFCQUFxQiwwQ0FBTTtBQUMzQjtBQUNBLDZDQUE2QywwQ0FBTSxtREFBbUQsMENBQU07QUFDNUcsa0NBQWtDLEdBQUc7QUFDckM7QUFDQSxDQUFDOztBQUlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1CRkFNVUROMi5tanM/MGU4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfX25hbWVcbn0gZnJvbSBcIi4vY2h1bmstM1hZUkg1QVAubWpzXCI7XG5cbi8vIHNyYy9yZW5kZXJpbmctdXRpbC9pbnNlcnRFbGVtZW50c0ZvclNpemUuanNcbmltcG9ydCB7IHNlbGVjdCB9IGZyb20gXCJkM1wiO1xudmFyIGdldERpYWdyYW1FbGVtZW50ID0gLyogQF9fUFVSRV9fICovIF9fbmFtZSgoaWQsIHNlY3VyaXR5TGV2ZWwpID0+IHtcbiAgbGV0IHNhbmRib3hFbGVtZW50O1xuICBpZiAoc2VjdXJpdHlMZXZlbCA9PT0gXCJzYW5kYm94XCIpIHtcbiAgICBzYW5kYm94RWxlbWVudCA9IHNlbGVjdChcIiNpXCIgKyBpZCk7XG4gIH1cbiAgY29uc3Qgcm9vdCA9IHNlY3VyaXR5TGV2ZWwgPT09IFwic2FuZGJveFwiID8gc2VsZWN0KHNhbmRib3hFbGVtZW50Lm5vZGVzKClbMF0uY29udGVudERvY3VtZW50LmJvZHkpIDogc2VsZWN0KFwiYm9keVwiKTtcbiAgY29uc3Qgc3ZnID0gcm9vdC5zZWxlY3QoYFtpZD1cIiR7aWR9XCJdYCk7XG4gIHJldHVybiBzdmc7XG59LCBcImdldERpYWdyYW1FbGVtZW50XCIpO1xuXG5leHBvcnQge1xuICBnZXREaWFncmFtRWxlbWVudFxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BFAMUDN2.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-OW32GOEJ.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-OW32GOEJ.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StateDB: function() { return /* binding */ StateDB; },\n/* harmony export */   stateDiagram_default: function() { return /* binding */ stateDiagram_default; },\n/* harmony export */   stateRenderer_v3_unified_default: function() { return /* binding */ stateRenderer_v3_unified_default; },\n/* harmony export */   styles_default: function() { return /* binding */ styles_default; }\n/* harmony export */ });\n/* harmony import */ var _chunk_BFAMUDN2_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-BFAMUDN2.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BFAMUDN2.mjs\");\n/* harmony import */ var _chunk_SKB7J2MH_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-SKB7J2MH.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-SKB7J2MH.mjs\");\n/* harmony import */ var _chunk_IWUHOULB_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-IWUHOULB.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-IWUHOULB.mjs\");\n/* harmony import */ var _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-55PJQP7W.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n\n\n\n\n\n\n// src/diagrams/state/parser/stateDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 16], $V7 = [1, 17], $V8 = [1, 18], $V9 = [1, 19], $Va = [1, 33], $Vb = [1, 20], $Vc = [1, 21], $Vd = [1, 22], $Ve = [1, 23], $Vf = [1, 24], $Vg = [1, 26], $Vh = [1, 27], $Vi = [1, 28], $Vj = [1, 29], $Vk = [1, 30], $Vl = [1, 31], $Vm = [1, 32], $Vn = [1, 35], $Vo = [1, 36], $Vp = [1, 37], $Vq = [1, 38], $Vr = [1, 34], $Vs = [1, 4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 41, 45, 48, 51, 52, 53, 54, 57], $Vt = [1, 4, 5, 14, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 39, 40, 41, 45, 48, 51, 52, 53, 54, 57], $Vu = [4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 41, 45, 48, 51, 52, 53, 54, 57];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NL\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"classDefStatement\": 10, \"styleStatement\": 11, \"cssClassStatement\": 12, \"idStatement\": 13, \"DESCR\": 14, \"-->\": 15, \"HIDE_EMPTY\": 16, \"scale\": 17, \"WIDTH\": 18, \"COMPOSIT_STATE\": 19, \"STRUCT_START\": 20, \"STRUCT_STOP\": 21, \"STATE_DESCR\": 22, \"AS\": 23, \"ID\": 24, \"FORK\": 25, \"JOIN\": 26, \"CHOICE\": 27, \"CONCURRENT\": 28, \"note\": 29, \"notePosition\": 30, \"NOTE_TEXT\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"CLICK\": 38, \"STRING\": 39, \"HREF\": 40, \"classDef\": 41, \"CLASSDEF_ID\": 42, \"CLASSDEF_STYLEOPTS\": 43, \"DEFAULT\": 44, \"style\": 45, \"STYLE_IDS\": 46, \"STYLEDEF_STYLEOPTS\": 47, \"class\": 48, \"CLASSENTITY_IDS\": 49, \"STYLECLASS\": 50, \"direction_tb\": 51, \"direction_bt\": 52, \"direction_rl\": 53, \"direction_lr\": 54, \"eol\": 55, \";\": 56, \"EDGE_STATE\": 57, \"STYLE_SEPARATOR\": 58, \"left_of\": 59, \"right_of\": 60, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NL\", 6: \"SD\", 14: \"DESCR\", 15: \"-->\", 16: \"HIDE_EMPTY\", 17: \"scale\", 18: \"WIDTH\", 19: \"COMPOSIT_STATE\", 20: \"STRUCT_START\", 21: \"STRUCT_STOP\", 22: \"STATE_DESCR\", 23: \"AS\", 24: \"ID\", 25: \"FORK\", 26: \"JOIN\", 27: \"CHOICE\", 28: \"CONCURRENT\", 29: \"note\", 31: \"NOTE_TEXT\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 38: \"CLICK\", 39: \"STRING\", 40: \"HREF\", 41: \"classDef\", 42: \"CLASSDEF_ID\", 43: \"CLASSDEF_STYLEOPTS\", 44: \"DEFAULT\", 45: \"style\", 46: \"STYLE_IDS\", 47: \"STYLEDEF_STYLEOPTS\", 48: \"class\", 49: \"CLASSENTITY_IDS\", 50: \"STYLECLASS\", 51: \"direction_tb\", 52: \"direction_bt\", 53: \"direction_rl\", 54: \"direction_lr\", 56: \";\", 57: \"EDGE_STATE\", 58: \"STYLE_SEPARATOR\", 59: \"left_of\", 60: \"right_of\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 3], [9, 4], [9, 1], [9, 2], [9, 1], [9, 4], [9, 3], [9, 6], [9, 1], [9, 1], [9, 1], [9, 1], [9, 4], [9, 4], [9, 1], [9, 2], [9, 2], [9, 1], [9, 5], [9, 5], [10, 3], [10, 3], [11, 3], [12, 3], [32, 1], [32, 1], [32, 1], [32, 1], [55, 1], [55, 1], [13, 1], [13, 1], [13, 3], [13, 3], [30, 1], [30, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setRootDoc($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n          this.$ = [];\n          break;\n        case 5:\n          if ($$[$0] != \"nl\") {\n            $$[$0 - 1].push($$[$0]);\n            this.$ = $$[$0 - 1];\n          }\n          break;\n        case 6:\n        case 7:\n          this.$ = $$[$0];\n          break;\n        case 8:\n          this.$ = \"nl\";\n          break;\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 13:\n          const stateStmt = $$[$0 - 1];\n          stateStmt.description = yy.trimColon($$[$0]);\n          this.$ = stateStmt;\n          break;\n        case 14:\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 2], state2: $$[$0] };\n          break;\n        case 15:\n          const relDescription = yy.trimColon($$[$0]);\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 3], state2: $$[$0 - 1], description: relDescription };\n          break;\n        case 19:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: \"\", doc: $$[$0 - 1] };\n          break;\n        case 20:\n          var id = $$[$0];\n          var description = $$[$0 - 2].trim();\n          if ($$[$0].match(\":\")) {\n            var parts = $$[$0].split(\":\");\n            id = parts[0];\n            description = [description, parts[1]];\n          }\n          this.$ = { stmt: \"state\", id, type: \"default\", description };\n          break;\n        case 21:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: $$[$0 - 5], doc: $$[$0 - 1] };\n          break;\n        case 22:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"fork\" };\n          break;\n        case 23:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"join\" };\n          break;\n        case 24:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"choice\" };\n          break;\n        case 25:\n          this.$ = { stmt: \"state\", id: yy.getDividerId(), type: \"divider\" };\n          break;\n        case 26:\n          this.$ = { stmt: \"state\", id: $$[$0 - 1].trim(), note: { position: $$[$0 - 2].trim(), text: $$[$0].trim() } };\n          break;\n        case 29:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 30:\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n          this.$ = {\n            stmt: \"click\",\n            id: $$[$0 - 3],\n            url: $$[$0 - 2],\n            tooltip: $$[$0 - 1]\n          };\n          break;\n        case 33:\n          this.$ = {\n            stmt: \"click\",\n            id: $$[$0 - 3],\n            url: $$[$0 - 1],\n            tooltip: \"\"\n          };\n          break;\n        case 34:\n        case 35:\n          this.$ = { stmt: \"classDef\", id: $$[$0 - 1].trim(), classes: $$[$0].trim() };\n          break;\n        case 36:\n          this.$ = { stmt: \"style\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 37:\n          this.$ = { stmt: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 38:\n          yy.setDirection(\"TB\");\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 39:\n          yy.setDirection(\"BT\");\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 40:\n          yy.setDirection(\"RL\");\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 41:\n          yy.setDirection(\"LR\");\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n        case 44:\n        case 45:\n          this.$ = { stmt: \"state\", id: $$[$0].trim(), type: \"default\", description: \"\" };\n          break;\n        case 46:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n        case 47:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 16, 17, 19, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 41, 45, 48, 51, 52, 53, 54, 57], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 5]), { 9: 39, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 7]), o($Vs, [2, 8]), o($Vs, [2, 9]), o($Vs, [2, 10]), o($Vs, [2, 11]), o($Vs, [2, 12], { 14: [1, 40], 15: [1, 41] }), o($Vs, [2, 16]), { 18: [1, 42] }, o($Vs, [2, 18], { 20: [1, 43] }), { 23: [1, 44] }, o($Vs, [2, 22]), o($Vs, [2, 23]), o($Vs, [2, 24]), o($Vs, [2, 25]), { 30: 45, 31: [1, 46], 59: [1, 47], 60: [1, 48] }, o($Vs, [2, 28]), { 34: [1, 49] }, { 36: [1, 50] }, o($Vs, [2, 31]), { 13: 51, 24: $Va, 57: $Vr }, { 42: [1, 52], 44: [1, 53] }, { 46: [1, 54] }, { 49: [1, 55] }, o($Vt, [2, 44], { 58: [1, 56] }), o($Vt, [2, 45], { 58: [1, 57] }), o($Vs, [2, 38]), o($Vs, [2, 39]), o($Vs, [2, 40]), o($Vs, [2, 41]), o($Vs, [2, 6]), o($Vs, [2, 13]), { 13: 58, 24: $Va, 57: $Vr }, o($Vs, [2, 17]), o($Vu, $V3, { 7: 59 }), { 24: [1, 60] }, { 24: [1, 61] }, { 23: [1, 62] }, { 24: [2, 48] }, { 24: [2, 49] }, o($Vs, [2, 29]), o($Vs, [2, 30]), { 39: [1, 63], 40: [1, 64] }, { 43: [1, 65] }, { 43: [1, 66] }, { 47: [1, 67] }, { 50: [1, 68] }, { 24: [1, 69] }, { 24: [1, 70] }, o($Vs, [2, 14], { 14: [1, 71] }), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 72], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 20], { 20: [1, 73] }), { 31: [1, 74] }, { 24: [1, 75] }, { 39: [1, 76] }, { 39: [1, 77] }, o($Vs, [2, 34]), o($Vs, [2, 35]), o($Vs, [2, 36]), o($Vs, [2, 37]), o($Vt, [2, 46]), o($Vt, [2, 47]), o($Vs, [2, 15]), o($Vs, [2, 19]), o($Vu, $V3, { 7: 78 }), o($Vs, [2, 26]), o($Vs, [2, 27]), { 5: [1, 79] }, { 5: [1, 80] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 81], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 41: $Vk, 45: $Vl, 48: $Vm, 51: $Vn, 52: $Vo, 53: $Vp, 54: $Vq, 57: $Vr }, o($Vs, [2, 32]), o($Vs, [2, 33]), o($Vs, [2, 21])],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 47: [2, 48], 48: [2, 49] },\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 38;\n            break;\n          case 1:\n            return 40;\n            break;\n          case 2:\n            return 39;\n            break;\n          case 3:\n            return 44;\n            break;\n          case 4:\n            return 51;\n            break;\n          case 5:\n            return 52;\n            break;\n          case 6:\n            return 53;\n            break;\n          case 7:\n            return 54;\n            break;\n          case 8:\n            break;\n          case 9:\n            {\n            }\n            break;\n          case 10:\n            return 5;\n            break;\n          case 11:\n            break;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            break;\n          case 15:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 16:\n            return 18;\n            break;\n          case 17:\n            this.popState();\n            break;\n          case 18:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 19:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 20:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 21:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 22:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 25:\n            this.pushState(\"CLASSDEF\");\n            return 41;\n            break;\n          case 26:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 27:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 42;\n            break;\n          case 28:\n            this.popState();\n            return 43;\n            break;\n          case 29:\n            this.pushState(\"CLASS\");\n            return 48;\n            break;\n          case 30:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 49;\n            break;\n          case 31:\n            this.popState();\n            return 50;\n            break;\n          case 32:\n            this.pushState(\"STYLE\");\n            return 45;\n            break;\n          case 33:\n            this.popState();\n            this.pushState(\"STYLEDEF_STYLES\");\n            return 46;\n            break;\n          case 34:\n            this.popState();\n            return 47;\n            break;\n          case 35:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 36:\n            return 18;\n            break;\n          case 37:\n            this.popState();\n            break;\n          case 38:\n            this.pushState(\"STATE\");\n            break;\n          case 39:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 40:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 41:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 42:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 43:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 44:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 45:\n            return 51;\n            break;\n          case 46:\n            return 52;\n            break;\n          case 47:\n            return 53;\n            break;\n          case 48:\n            return 54;\n            break;\n          case 49:\n            this.pushState(\"STATE_STRING\");\n            break;\n          case 50:\n            this.pushState(\"STATE_ID\");\n            return \"AS\";\n            break;\n          case 51:\n            this.popState();\n            return \"ID\";\n            break;\n          case 52:\n            this.popState();\n            break;\n          case 53:\n            return \"STATE_DESCR\";\n            break;\n          case 54:\n            return 19;\n            break;\n          case 55:\n            this.popState();\n            break;\n          case 56:\n            this.popState();\n            this.pushState(\"struct\");\n            return 20;\n            break;\n          case 57:\n            break;\n          case 58:\n            this.popState();\n            return 21;\n            break;\n          case 59:\n            break;\n          case 60:\n            this.begin(\"NOTE\");\n            return 29;\n            break;\n          case 61:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 59;\n            break;\n          case 62:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 60;\n            break;\n          case 63:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE\");\n            break;\n          case 64:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE_ID\");\n            return \"AS\";\n            break;\n          case 65:\n            break;\n          case 66:\n            return \"NOTE_TEXT\";\n            break;\n          case 67:\n            this.popState();\n            return \"ID\";\n            break;\n          case 68:\n            this.popState();\n            this.pushState(\"NOTE_TEXT\");\n            return 24;\n            break;\n          case 69:\n            this.popState();\n            yy_.yytext = yy_.yytext.substr(2).trim();\n            return 31;\n            break;\n          case 70:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 31;\n            break;\n          case 71:\n            return 6;\n            break;\n          case 72:\n            return 6;\n            break;\n          case 73:\n            return 16;\n            break;\n          case 74:\n            return 57;\n            break;\n          case 75:\n            return 24;\n            break;\n          case 76:\n            yy_.yytext = yy_.yytext.trim();\n            return 14;\n            break;\n          case 77:\n            return 15;\n            break;\n          case 78:\n            return 28;\n            break;\n          case 79:\n            return 58;\n            break;\n          case 80:\n            return 5;\n            break;\n          case 81:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:click\\b)/i, /^(?:href\\b)/i, /^(?:\"[^\"]*\")/i, /^(?:default\\b)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:[\\s]+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:classDef\\s+)/i, /^(?:DEFAULT\\s+)/i, /^(?:\\w+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:class\\s+)/i, /^(?:(\\w+)+((,\\s*\\w+)*))/i, /^(?:[^\\n]*)/i, /^(?:style\\s+)/i, /^(?:[\\w,]+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:state\\s+)/i, /^(?:.*<<fork>>)/i, /^(?:.*<<join>>)/i, /^(?:.*<<choice>>)/i, /^(?:.*\\[\\[fork\\]\\])/i, /^(?:.*\\[\\[join\\]\\])/i, /^(?:.*\\[\\[choice\\]\\])/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\"])/i, /^(?:\\s*as\\s+)/i, /^(?:[^\\n\\{]*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n\\s\\{]+)/i, /^(?:\\n)/i, /^(?:\\{)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:\\})/i, /^(?:[\\n])/i, /^(?:note\\s+)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:\")/i, /^(?:\\s*as\\s*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n]*)/i, /^(?:\\s*[^:\\n\\s\\-]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:[\\s\\S]*?end note\\b)/i, /^(?:stateDiagram\\s+)/i, /^(?:stateDiagram-v2\\s+)/i, /^(?:hide empty description\\b)/i, /^(?:\\[\\*\\])/i, /^(?:[^:\\n\\s\\-\\{]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:-->)/i, /^(?:--)/i, /^(?::::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"LINE\": { \"rules\": [12, 13], \"inclusive\": false }, \"struct\": { \"rules\": [12, 13, 25, 29, 32, 38, 45, 46, 47, 48, 57, 58, 59, 60, 74, 75, 76, 77, 78], \"inclusive\": false }, \"FLOATING_NOTE_ID\": { \"rules\": [67], \"inclusive\": false }, \"FLOATING_NOTE\": { \"rules\": [64, 65, 66], \"inclusive\": false }, \"NOTE_TEXT\": { \"rules\": [69, 70], \"inclusive\": false }, \"NOTE_ID\": { \"rules\": [68], \"inclusive\": false }, \"NOTE\": { \"rules\": [61, 62, 63], \"inclusive\": false }, \"STYLEDEF_STYLEOPTS\": { \"rules\": [], \"inclusive\": false }, \"STYLEDEF_STYLES\": { \"rules\": [34], \"inclusive\": false }, \"STYLE_IDS\": { \"rules\": [], \"inclusive\": false }, \"STYLE\": { \"rules\": [33], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [31], \"inclusive\": false }, \"CLASS\": { \"rules\": [30], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [28], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [26, 27], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [23, 24], \"inclusive\": false }, \"acc_descr\": { \"rules\": [21], \"inclusive\": false }, \"acc_title\": { \"rules\": [19], \"inclusive\": false }, \"SCALE\": { \"rules\": [16, 17, 36, 37], \"inclusive\": false }, \"ALIAS\": { \"rules\": [], \"inclusive\": false }, \"STATE_ID\": { \"rules\": [51], \"inclusive\": false }, \"STATE_STRING\": { \"rules\": [52, 53], \"inclusive\": false }, \"FORK_STATE\": { \"rules\": [], \"inclusive\": false }, \"STATE\": { \"rules\": [12, 13, 39, 40, 41, 42, 43, 44, 49, 50, 54, 55, 56], \"inclusive\": false }, \"ID\": { \"rules\": [12, 13], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 18, 20, 22, 25, 29, 32, 35, 38, 56, 60, 71, 72, 73, 74, 75, 76, 77, 79, 80, 81], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar stateDiagram_default = parser;\n\n// src/diagrams/state/stateCommon.ts\nvar DEFAULT_DIAGRAM_DIRECTION = \"TB\";\nvar DEFAULT_NESTED_DOC_DIR = \"TB\";\nvar STMT_DIRECTION = \"dir\";\nvar STMT_STATE = \"state\";\nvar STMT_ROOT = \"root\";\nvar STMT_RELATION = \"relation\";\nvar STMT_CLASSDEF = \"classDef\";\nvar STMT_STYLEDEF = \"style\";\nvar STMT_APPLYCLASS = \"applyClass\";\nvar DEFAULT_STATE_TYPE = \"default\";\nvar DIVIDER_TYPE = \"divider\";\nvar G_EDGE_STYLE = \"fill:none\";\nvar G_EDGE_ARROWHEADSTYLE = \"fill: #333\";\nvar G_EDGE_LABELPOS = \"c\";\nvar G_EDGE_LABELTYPE = \"text\";\nvar G_EDGE_THICKNESS = \"normal\";\nvar SHAPE_STATE = \"rect\";\nvar SHAPE_STATE_WITH_DESC = \"rectWithTitle\";\nvar SHAPE_START = \"stateStart\";\nvar SHAPE_END = \"stateEnd\";\nvar SHAPE_DIVIDER = \"divider\";\nvar SHAPE_GROUP = \"roundedWithTitle\";\nvar SHAPE_NOTE = \"note\";\nvar SHAPE_NOTEGROUP = \"noteGroup\";\nvar CSS_DIAGRAM = \"statediagram\";\nvar CSS_STATE = \"state\";\nvar CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nvar CSS_EDGE = \"transition\";\nvar CSS_NOTE = \"note\";\nvar CSS_NOTE_EDGE = \"note-edge\";\nvar CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nvar CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nvar CSS_CLUSTER = \"cluster\";\nvar CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nvar CSS_CLUSTER_ALT = \"cluster-alt\";\nvar CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\nvar PARENT = \"parent\";\nvar NOTE = \"note\";\nvar DOMID_STATE = \"state\";\nvar DOMID_TYPE_SPACER = \"----\";\nvar NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nvar PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n\n// src/diagrams/state/stateRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(async function(text, id, _version, diag) {\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"REF0:\");\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, state: conf, layout } = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)();\n  diag.db.extract(diag.db.getRootDocV2());\n  const data4Layout = diag.db.getData();\n  const svg = (0,_chunk_BFAMUDN2_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramElement)(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"barb\"];\n  data4Layout.diagramId = id;\n  await (0,_chunk_IWUHOULB_mjs__WEBPACK_IMPORTED_MODULE_2__.render)(data4Layout, svg);\n  const padding = 8;\n  try {\n    const links = typeof diag.db.getLinks === \"function\" ? diag.db.getLinks() : /* @__PURE__ */ new Map();\n    links.forEach((linkInfo, key) => {\n      const stateId = typeof key === \"string\" ? key : typeof key?.id === \"string\" ? key.id : \"\";\n      if (!stateId) {\n        _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.warn(\"\\u26A0\\uFE0F Invalid or missing stateId from key:\", JSON.stringify(key));\n        return;\n      }\n      const allNodes = svg.node()?.querySelectorAll(\"g\");\n      let matchedElem;\n      allNodes?.forEach((g) => {\n        const text2 = g.textContent?.trim();\n        if (text2 === stateId) {\n          matchedElem = g;\n        }\n      });\n      if (!matchedElem) {\n        _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.warn(\"\\u26A0\\uFE0F Could not find node matching text:\", stateId);\n        return;\n      }\n      const parent = matchedElem.parentNode;\n      if (!parent) {\n        _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.warn(\"\\u26A0\\uFE0F Node has no parent, cannot wrap:\", stateId);\n        return;\n      }\n      const a = document.createElementNS(\"http://www.w3.org/2000/svg\", \"a\");\n      const cleanedUrl = linkInfo.url.replace(/^\"+|\"+$/g, \"\");\n      a.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"xlink:href\", cleanedUrl);\n      a.setAttribute(\"target\", \"_blank\");\n      if (linkInfo.tooltip) {\n        const tooltip = linkInfo.tooltip.replace(/^\"+|\"+$/g, \"\");\n        a.setAttribute(\"title\", tooltip);\n      }\n      parent.replaceChild(a, matchedElem);\n      a.appendChild(matchedElem);\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"\\u{1F517} Wrapped node in <a> tag for:\", stateId, linkInfo.url);\n    });\n  } catch (err) {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.error(\"\\u274C Error injecting clickable links:\", err);\n  }\n  _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_3__.utils_default.insertTitle(\n    svg,\n    \"statediagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  (0,_chunk_SKB7J2MH_mjs__WEBPACK_IMPORTED_MODULE_1__.setupViewPortForSVG)(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar stateRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\n// src/diagrams/state/dataFetcher.ts\nvar nodeDb = /* @__PURE__ */ new Map();\nvar graphItemCount = 0;\nfunction stateDomId(itemId = \"\", counter = 0, type = \"\", typeSpacer = DOMID_TYPE_SPACER) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : \"\";\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(stateDomId, \"stateDomId\");\nvar setupDoc = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)((parentParsedItem, doc, diagramStates, nodes, edges, altFlag, look, classes) => {\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.trace(\"items\", doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: \"edge\" + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: \"normal\",\n            arrowTypeEnd: \"arrow_barb\",\n            style: G_EDGE_STYLE,\n            labelStyle: \"\",\n            label: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeText(item.description ?? \"\", (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n}, \"setupDoc\");\nvar getDir2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === \"dir\") {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n}, \"getDir\");\nfunction insertOrUpdateNode(nodes, nodeData, classes) {\n  if (!nodeData.id || nodeData.id === \"</join></fork>\" || nodeData.id === \"</choice>\") {\n    return;\n  }\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n    nodeData.cssClasses.split(\" \").forEach((cssClass) => {\n      const classDef = classes.get(cssClass);\n      if (classDef) {\n        nodeData.cssCompiledStyles = [...nodeData.cssCompiledStyles ?? [], ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(insertOrUpdateNode, \"insertOrUpdateNode\");\nfunction getClassesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.classes?.join(\" \") ?? \"\";\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(getClassesFromDbInfo, \"getClassesFromDbInfo\");\nfunction getStylesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.styles ?? [];\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(getStylesFromDbInfo, \"getStylesFromDbInfo\");\nvar dataFetcher = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)((parent, parsedItem, diagramStates, nodes, edges, altFlag, look, classes) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)();\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"dataFetcher parsedItem\", parsedItem, dbState, style);\n  if (itemId !== \"root\") {\n    let shape = SHAPE_STATE;\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeText(itemId, config),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style\n      });\n    }\n    const newNode = nodeDb.get(itemId);\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length && newNode.description.length > 0) {\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeTextOrArray(newNode.description, config);\n    }\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === \"group\") {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n    if (!newNode.type && parsedItem.doc) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Setting cluster for XCX\", itemId, getDir2(parsedItem));\n      newNode.type = \"group\";\n      newNode.isGroup = true;\n      newNode.dir = getDir2(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : \"\"}`;\n    }\n    const nodeData = {\n      labelStyle: \"\",\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === \"group\",\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look\n    };\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = \"\";\n    }\n    if (parent && parent.id !== \"root\") {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.trace(\"Setting node \", itemId, \" to be child of its parent \", parent.id);\n      nodeData.parentId = parent.id;\n    }\n    nodeData.centerLabel = true;\n    if (parsedItem.note) {\n      const noteData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompiledStyles: [],\n        id: itemId + NOTE_ID + \"-\" + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === \"group\",\n        padding: config.flowchart?.padding,\n        look,\n        position: parsedItem.note.position\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: \"group\",\n        isGroup: true,\n        padding: 16,\n        //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position\n      };\n      graphItemCount++;\n      groupData.id = parentNodeId;\n      noteData.parentId = parentNodeId;\n      insertOrUpdateNode(nodes, groupData, classes);\n      insertOrUpdateNode(nodes, noteData, classes);\n      insertOrUpdateNode(nodes, nodeData, classes);\n      let from = itemId;\n      let to = noteData.id;\n      if (parsedItem.note.position === \"left of\") {\n        from = noteData.id;\n        to = itemId;\n      }\n      edges.push({\n        id: from + \"-\" + to,\n        start: from,\n        end: to,\n        arrowhead: \"none\",\n        arrowTypeEnd: \"\",\n        style: G_EDGE_STYLE,\n        labelStyle: \"\",\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.trace(\"Adding nodes children \");\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n}, \"dataFetcher\");\nvar reset = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(() => {\n  nodeDb.clear();\n  graphItemCount = 0;\n}, \"reset\");\n\n// src/diagrams/state/stateDb.ts\nvar CONSTANTS = {\n  START_NODE: \"[*]\",\n  START_TYPE: \"start\",\n  END_NODE: \"[*]\",\n  END_TYPE: \"end\",\n  COLOR_KEYWORD: \"color\",\n  FILL_KEYWORD: \"fill\",\n  BG_FILL: \"bgFill\",\n  STYLECLASS_SEP: \",\"\n};\nvar newClassesList = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(() => /* @__PURE__ */ new Map(), \"newClassesList\");\nvar newDoc = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(() => ({\n  relations: [],\n  states: /* @__PURE__ */ new Map(),\n  documents: {}\n}), \"newDoc\");\nvar clone = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)((o) => JSON.parse(JSON.stringify(o)), \"clone\");\nvar StateDB = class {\n  constructor(version) {\n    this.version = version;\n    this.nodes = [];\n    this.edges = [];\n    this.rootDoc = [];\n    this.classes = newClassesList();\n    this.documents = { root: newDoc() };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.dividerCnt = 0;\n    this.links = /* @__PURE__ */ new Map();\n    this.getAccTitle = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getAccTitle;\n    this.setAccTitle = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.setAccTitle;\n    this.getAccDescription = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getAccDescription;\n    this.setAccDescription = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.setAccDescription;\n    this.setDiagramTitle = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.setDiagramTitle;\n    this.getDiagramTitle = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getDiagramTitle;\n    this.clear();\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)(this, \"StateDB\");\n  }\n  static {\n    this.relationType = {\n      AGGREGATION: 0,\n      EXTENSION: 1,\n      COMPOSITION: 2,\n      DEPENDENCY: 3\n    };\n  }\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   */\n  extract(statements) {\n    this.clear(true);\n    for (const item of Array.isArray(statements) ? statements : statements.doc) {\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(item.id.trim(), item.type, item.doc, item.description, item.note);\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          this.handleStyleDef(item);\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n        case \"click\":\n          this.addLink(item.id, item.url, item.tooltip);\n          break;\n      }\n    }\n    const diagramStates = this.getStates();\n    const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)();\n    reset();\n    dataFetcher(\n      void 0,\n      this.getRootDocV2(),\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      config.look,\n      this.classes\n    );\n    for (const node of this.nodes) {\n      if (!Array.isArray(node.label)) {\n        continue;\n      }\n      node.description = node.label.slice(1);\n      if (node.isGroup && node.description.length > 0) {\n        throw new Error(\n          `Group nodes can only have label. Remove the additional description for node [${node.id}]`\n        );\n      }\n      node.label = node.label[0];\n    }\n  }\n  handleStyleDef(item) {\n    const ids = item.id.trim().split(\",\");\n    const styles = item.styleClass.split(\",\");\n    for (const id of ids) {\n      let state = this.getState(id);\n      if (!state) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        state = this.getState(trimmedId);\n      }\n      if (state) {\n        state.styles = styles.map((s) => s.replace(/;/g, \"\")?.trim());\n      }\n    }\n  }\n  setRootDoc(o) {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Setting root doc\", o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n  docTranslator(parent, node, first) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n      return;\n    }\n    if (node.stmt === STMT_STATE) {\n      if (node.id === CONSTANTS.START_NODE) {\n        node.id = parent.id + (first ? \"_start\" : \"_end\");\n        node.start = first;\n      } else {\n        node.id = node.id.trim();\n      }\n    }\n    if (node.stmt !== STMT_ROOT && node.stmt !== STMT_STATE || !node.doc) {\n      return;\n    }\n    const doc = [];\n    let currentDoc = [];\n    for (const stmt of node.doc) {\n      if (stmt.type === DIVIDER_TYPE) {\n        const newNode = clone(stmt);\n        newNode.doc = clone(currentDoc);\n        doc.push(newNode);\n        currentDoc = [];\n      } else {\n        currentDoc.push(stmt);\n      }\n    }\n    if (doc.length > 0 && currentDoc.length > 0) {\n      const newNode = {\n        stmt: STMT_STATE,\n        id: (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_3__.generateId)(),\n        type: \"divider\",\n        doc: clone(currentDoc)\n      };\n      doc.push(clone(newNode));\n      node.doc = doc;\n    }\n    node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n  }\n  getRootDocV2() {\n    this.docTranslator(\n      { id: STMT_ROOT, stmt: STMT_ROOT },\n      { id: STMT_ROOT, stmt: STMT_ROOT, doc: this.rootDoc },\n      true\n    );\n    return { id: STMT_ROOT, doc: this.rootDoc };\n  }\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param descr - description for the state. Can be a string or a list or strings\n   * @param classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(id, type = DEFAULT_STATE_TYPE, doc = void 0, descr = void 0, note = void 0, classes = void 0, styles = void 0, textStyles = void 0) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Adding state \", trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        stmt: STMT_STATE,\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: []\n      });\n    } else {\n      const state = this.currentDocument.states.get(trimmedId);\n      if (!state) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      if (!state.doc) {\n        state.doc = doc;\n      }\n      if (!state.type) {\n        state.type = type;\n      }\n    }\n    if (descr) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Setting state description\", trimmedId, descr);\n      const descriptions = Array.isArray(descr) ? descr : [descr];\n      descriptions.forEach((des) => this.addDescription(trimmedId, des.trim()));\n    }\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      if (!doc2) {\n        throw new Error(`State not found: ${trimmedId}`);\n      }\n      doc2.note = note;\n      doc2.note.text = _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeText(doc2.note.text, (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)());\n    }\n    if (classes) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Setting state classes\", trimmedId, classes);\n      const classesList = Array.isArray(classes) ? classes : [classes];\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n    if (styles) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Setting state styles\", trimmedId, styles);\n      const stylesList = Array.isArray(styles) ? styles : [styles];\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n    if (textStyles) {\n      _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Setting state styles\", trimmedId, styles);\n      const textStylesList = Array.isArray(textStyles) ? textStyles : [textStyles];\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n  clear(saveCommon) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = { root: newDoc() };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      this.links = /* @__PURE__ */ new Map();\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.clear)();\n    }\n  }\n  getState(id) {\n    return this.currentDocument.states.get(id);\n  }\n  getStates() {\n    return this.currentDocument.states;\n  }\n  logDocuments() {\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.info(\"Documents = \", this.documents);\n  }\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n  /**\n   * Adds a clickable link to a state.\n   */\n  addLink(stateId, url, tooltip) {\n    this.links.set(stateId, { url, tooltip });\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.log.warn(\"Adding link\", stateId, url, tooltip);\n  }\n  /**\n   * Get all registered links.\n   */\n  getLinks() {\n    return this.links;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   */\n  startIdIfNeeded(id = \"\") {\n    if (id === CONSTANTS.START_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.START_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   */\n  startTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.START_NODE ? CONSTANTS.START_TYPE : type;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   */\n  endIdIfNeeded(id = \"\") {\n    if (id === CONSTANTS.END_NODE) {\n      this.startEndCount++;\n      return `${CONSTANTS.END_TYPE}${this.startEndCount}`;\n    }\n    return id;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   */\n  endTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === CONSTANTS.END_NODE ? CONSTANTS.END_TYPE : type;\n  }\n  addRelationObjs(item1, item2, relationTitle = \"\") {\n    const id1 = this.startIdIfNeeded(item1.id.trim());\n    const type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    const id2 = this.startIdIfNeeded(item2.id.trim());\n    const type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeText(relationTitle, (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)())\n    });\n  }\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   */\n  addRelation(item1, item2, title) {\n    if (typeof item1 === \"object\" && typeof item2 === \"object\") {\n      this.addRelationObjs(item1, item2, title);\n    } else if (typeof item1 === \"string\" && typeof item2 === \"string\") {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        relationTitle: title ? _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeText(title, (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)()) : void 0\n      });\n    }\n  }\n  addDescription(id, descr) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(\":\") ? descr.replace(\":\", \"\").trim() : descr;\n    theState?.descriptions?.push(_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.common_default.sanitizeText(_descr, (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)()));\n  }\n  cleanupLabel(label) {\n    return label.startsWith(\":\") ? label.slice(2).trim() : label.trim();\n  }\n  getDividerId() {\n    this.dividerCnt++;\n    return `divider-id-${this.dividerCnt}`;\n  }\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param id - the id of this (style) class\n   * @param styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id, styleAttributes = \"\") {\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes && foundClass) {\n      styleAttributes.split(CONSTANTS.STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n        if (RegExp(CONSTANTS.COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(CONSTANTS.FILL_KEYWORD, CONSTANTS.BG_FILL);\n          const newStyle2 = newStyle1.replace(CONSTANTS.COLOR_KEYWORD, CONSTANTS.FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n  getClasses() {\n    return this.classes;\n  }\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param itemIds - The id or a list of ids of the item(s) to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setCssClass(itemIds, cssClassName) {\n    itemIds.split(\",\").forEach((id) => {\n      let foundState = this.getState(id);\n      if (!foundState) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState?.classes?.push(cssClassName);\n    });\n  }\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId - The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId, styleText) {\n    this.getState(itemId)?.styles?.push(styleText);\n  }\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId - The id of item to apply the css class to\n   * @param cssClassName - CSS class name\n   */\n  setTextStyle(itemId, cssClassName) {\n    this.getState(itemId)?.textStyles?.push(cssClassName);\n  }\n  /**\n   * Finds the direction statement in the root document.\n   * @returns the direction statement if present\n   */\n  getDirectionStatement() {\n    return this.rootDoc.find((doc) => doc.stmt === STMT_DIRECTION);\n  }\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n  setDirection(dir) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n  trimColon(str) {\n    return str.startsWith(\":\") ? str.slice(1).trim() : str.trim();\n  }\n  getData() {\n    const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2())\n    };\n  }\n  getConfig() {\n    return (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.getConfig2)().state;\n  }\n};\n\n// src/diagrams/state/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_4__.__name)((options) => `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-OW32GOEJ.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-SKB7J2MH.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-SKB7J2MH.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupViewPortForSVG: function() { return /* binding */ setupViewPortForSVG; }\n/* harmony export */ });\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-SKB7J2MH.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-5AN5P6BG.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-5AN5P6BG.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_OW32GOEJ_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-OW32GOEJ.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-OW32GOEJ.mjs\");\n/* harmony import */ var _chunk_BFAMUDN2_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-BFAMUDN2.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-BFAMUDN2.mjs\");\n/* harmony import */ var _chunk_SKB7J2MH_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-SKB7J2MH.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-SKB7J2MH.mjs\");\n/* harmony import */ var _chunk_IWUHOULB_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-IWUHOULB.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-IWUHOULB.mjs\");\n/* harmony import */ var _chunk_M6DAPIYF_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-M6DAPIYF.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-M6DAPIYF.mjs\");\n/* harmony import */ var _chunk_MXNHSMXR_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-MXNHSMXR.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-MXNHSMXR.mjs\");\n/* harmony import */ var _chunk_JW4RIYDF_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-JW4RIYDF.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-JW4RIYDF.mjs\");\n/* harmony import */ var _chunk_AC5SNWB5_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-AC5SNWB5.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-AC5SNWB5.mjs\");\n/* harmony import */ var _chunk_UWXLY5YG_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-UWXLY5YG.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-UWXLY5YG.mjs\");\n/* harmony import */ var _chunk_QESNASVV_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-QESNASVV.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-QESNASVV.mjs\");\n/* harmony import */ var _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-55PJQP7W.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: _chunk_OW32GOEJ_mjs__WEBPACK_IMPORTED_MODULE_0__.stateDiagram_default,\n  get db() {\n    return new _chunk_OW32GOEJ_mjs__WEBPACK_IMPORTED_MODULE_0__.StateDB(2);\n  },\n  renderer: _chunk_OW32GOEJ_mjs__WEBPACK_IMPORTED_MODULE_0__.stateRenderer_v3_unified_default,\n  styles: _chunk_OW32GOEJ_mjs__WEBPACK_IMPORTED_MODULE_0__.styles_default,\n  init: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_11__.__name)((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9zdGF0ZURpYWdyYW0tdjItNUFONVA2QkcubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLOEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdBOztBQUU5QjtBQUNBO0FBQ0EsVUFBVSxxRUFBb0I7QUFDOUI7QUFDQSxlQUFlLHdEQUFPO0FBQ3RCLEdBQUc7QUFDSCxZQUFZLGlGQUFnQztBQUM1QyxVQUFVLCtEQUFjO0FBQ3hCLHdCQUF3Qiw0REFBTTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9zdGF0ZURpYWdyYW0tdjItNUFONVA2QkcubWpzP2FkMzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgU3RhdGVEQixcbiAgc3RhdGVEaWFncmFtX2RlZmF1bHQsXG4gIHN0YXRlUmVuZGVyZXJfdjNfdW5pZmllZF9kZWZhdWx0LFxuICBzdHlsZXNfZGVmYXVsdFxufSBmcm9tIFwiLi9jaHVuay1PVzMyR09FSi5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstQkZBTVVETjIubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLVNLQjdKMk1ILm1qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1JV1VIT1VMQi5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstTTZEQVBJWUYubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLU1YTkhTTVhSLm1qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1KVzRSSVlERi5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstQUM1U05XQjUubWpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLVVXWExZNVlHLm1qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1RRVNOQVNWVi5tanNcIjtcbmltcG9ydCBcIi4vY2h1bmstNTVQSlFQN1cubWpzXCI7XG5pbXBvcnQge1xuICBfX25hbWVcbn0gZnJvbSBcIi4vY2h1bmstM1hZUkg1QVAubWpzXCI7XG5cbi8vIHNyYy9kaWFncmFtcy9zdGF0ZS9zdGF0ZURpYWdyYW0tdjIudHNcbnZhciBkaWFncmFtID0ge1xuICBwYXJzZXI6IHN0YXRlRGlhZ3JhbV9kZWZhdWx0LFxuICBnZXQgZGIoKSB7XG4gICAgcmV0dXJuIG5ldyBTdGF0ZURCKDIpO1xuICB9LFxuICByZW5kZXJlcjogc3RhdGVSZW5kZXJlcl92M191bmlmaWVkX2RlZmF1bHQsXG4gIHN0eWxlczogc3R5bGVzX2RlZmF1bHQsXG4gIGluaXQ6IC8qIEBfX1BVUkVfXyAqLyBfX25hbWUoKGNuZikgPT4ge1xuICAgIGlmICghY25mLnN0YXRlKSB7XG4gICAgICBjbmYuc3RhdGUgPSB7fTtcbiAgICB9XG4gICAgY25mLnN0YXRlLmFycm93TWFya2VyQWJzb2x1dGUgPSBjbmYuYXJyb3dNYXJrZXJBYnNvbHV0ZTtcbiAgfSwgXCJpbml0XCIpXG59O1xuZXhwb3J0IHtcbiAgZGlhZ3JhbVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-5AN5P6BG.mjs\n"));

/***/ })

}]);