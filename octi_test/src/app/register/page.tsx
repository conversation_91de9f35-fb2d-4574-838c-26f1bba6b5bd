/**
 * OCTI智能评估系统 - 注册页面
 * 
 * 用户注册界面
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthLayout, AuthDivider, AuthError, AuthSuccess } from '@/components/layout/auth-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// ============================================================================
// 注册页面组件
// ============================================================================

export default function RegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    organizationType: '',
    organizationName: '',
    role: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // 清除错误信息
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    if (error) setError('');
    if (success) setSuccess('');
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('请输入姓名');
      return false;
    }
    if (!formData.email.trim()) {
      setError('请输入邮箱地址');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('请输入有效的邮箱地址');
      return false;
    }
    if (formData.password.length < 6) {
      setError('密码长度至少6位');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致');
      return false;
    }
    if (!formData.organizationType) {
      setError('请选择组织类型');
      return false;
    }
    if (!formData.organizationName.trim()) {
      setError('请输入组织名称');
      return false;
    }
    if (!formData.role) {
      setError('请选择您的角色');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // TODO: 实现实际的注册逻辑
      // 这里模拟注册过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess('注册成功！正在跳转到登录页面...');
      
      // 延迟跳转到登录页面
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    } catch (err) {
      setError('注册失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout
      title="注册OCTI账户"
      description="创建您的OCTI智能评估系统账户"
      footer={
        <p className="text-muted-foreground">
          已有账户？{' '}
          <Link href="/login" className="text-primary hover:underline">
            立即登录
          </Link>
        </p>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* 错误和成功消息 */}
        {error && <AuthError message={error} />}
        {success && <AuthSuccess message={success} />}

        {/* 基本信息 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-muted-foreground">基本信息</h3>
          
          <div className="space-y-2">
            <Label htmlFor="name">姓名</Label>
            <Input
              id="name"
              name="name"
              type="text"
              placeholder="请输入您的姓名"
              value={formData.name}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">邮箱地址</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              value={formData.email}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="至少6位密码"
                value={formData.password}
                onChange={handleInputChange}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">确认密码</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="再次输入密码"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                disabled={isLoading}
              />
            </div>
          </div>
        </div>

        <AuthDivider />

        {/* 组织信息 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-muted-foreground">组织信息</h3>
          
          <div className="space-y-2">
            <Label htmlFor="organizationName">组织名称</Label>
            <Input
              id="organizationName"
              name="organizationName"
              type="text"
              placeholder="请输入您所在的组织名称"
              value={formData.organizationName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="organizationType">组织类型</Label>
              <Select
                value={formData.organizationType}
                onValueChange={(value) => handleSelectChange('organizationType', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择组织类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NGO">公益组织/NGO</SelectItem>
                  <SelectItem value="FOUNDATION">基金会</SelectItem>
                  <SelectItem value="SOCIAL_ENTERPRISE">社会企业</SelectItem>
                  <SelectItem value="CHARITY">慈善机构</SelectItem>
                  <SelectItem value="COMMUNITY_ORG">社区组织</SelectItem>
                  <SelectItem value="GOVERNMENT_AGENCY">政府机构</SelectItem>
                  <SelectItem value="OTHER">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">您的角色</Label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleSelectChange('role', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择您的角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="founder">创始人/理事长</SelectItem>
                  <SelectItem value="director">执行主任</SelectItem>
                  <SelectItem value="manager">项目经理</SelectItem>
                  <SelectItem value="coordinator">协调员</SelectItem>
                  <SelectItem value="volunteer">志愿者</SelectItem>
                  <SelectItem value="consultant">顾问</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 注册按钮 */}
        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? '注册中...' : '创建账户'}
        </Button>

        {/* 服务条款 */}
        <p className="text-xs text-muted-foreground text-center">
          点击"创建账户"即表示您同意我们的{' '}
          <Link href="/terms" className="text-primary hover:underline">
            服务条款
          </Link>{' '}
          和{' '}
          <Link href="/privacy" className="text-primary hover:underline">
            隐私政策
          </Link>
        </p>
      </form>

      {/* 系统特性介绍 */}
      <div className="mt-6 rounded-lg bg-muted/50 p-4">
        <h3 className="text-sm font-medium mb-2">为什么选择OCTI？</h3>
        <ul className="space-y-1 text-xs text-muted-foreground">
          <li>• 专为公益机构设计的能力评估体系</li>
          <li>• AI驱动的个性化问卷生成</li>
          <li>• 双模型协作提供精准分析</li>
          <li>• 专业的发展建议和改进路径</li>
        </ul>
      </div>
    </AuthLayout>
  );
}
