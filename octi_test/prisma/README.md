# Prisma 数据库配置说明

## 数据库设置

### PostgreSQL 安装和配置

1. 安装 PostgreSQL（如果尚未安装）：
   ```bash
   # macOS
   brew install postgresql
   
   # 启动 PostgreSQL 服务
   brew services start postgresql
   ```

2. 创建数据库：
   ```bash
   # 连接到 PostgreSQL
   psql postgres
   
   # 创建数据库
   CREATE DATABASE octi_test;
   
   # 创建用户（可选）
   CREATE USER octi_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE octi_test TO octi_user;
   
   # 退出
   \q
   ```

3. 更新 `.env` 文件中的 `DATABASE_URL`：
   ```
   DATABASE_URL="postgresql://octi_user:your_password@localhost:5432/octi_test?schema=public"
   ```

### 运行迁移

1. 生成 Prisma 客户端：
   ```bash
   npx prisma generate
   ```

2. 运行数据库迁移：
   ```bash
   npx prisma migrate dev --name "initial_migration"
   ```

3. 查看数据库状态：
   ```bash
   npx prisma studio
   ```

## 数据库架构说明

根据 `docs/5_database_schema.md` 设计文档，数据库包含以下主要模块：

### 核心模块
- **用户管理**：User, Organization, OrganizationMember
- **非营利组织**：NonprofitProfile
- **对话系统**：DialogueSession, DialogueMessage
- **智能问卷**：IntelligentQuestionnaire, Assessment, PresetQuestion
- **AI代理配置**：AIAgentConfig, ConfigVersion, ConfigUsageStats
- **数据处理**：ExternalDataSource, ProcessedData
- **安全管理**：ApiKey, SecurityEvent

### 关键特性
- 支持多租户架构
- 完整的审计日志
- 灵活的AI代理配置系统
- 智能问卷评估机制
- 外部数据源集成

## 开发注意事项

1. 在修改 schema 后，务必运行 `npx prisma generate` 重新生成客户端
2. 使用 `npx prisma migrate dev` 创建新的迁移文件
3. 生产环境部署时使用 `npx prisma migrate deploy`
4. 使用 `npx prisma studio` 可视化管理数据库