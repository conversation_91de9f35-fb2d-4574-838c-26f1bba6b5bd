/**
 * OCTI智能评估系统 - 问卷向导组件
 * 
 * 提供完整的问卷填写流程，包括导航、进度管理、数据保存等
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { QuestionRenderer, Question, QuestionResponse } from './question-renderer';
import { cn } from '@/lib/utils';

// ============================================================================
// 类型定义
// ============================================================================

interface QuestionnaireWizardProps {
  questionnaire: {
    id: string;
    title: string;
    description?: string;
    totalQuestions: number;
    estimatedDuration: number;
  };
  questions: Question[];
  initialResponses?: QuestionResponse[];
  onSave?: (responses: QuestionResponse[]) => void;
  onSubmit?: (responses: QuestionResponse[]) => void;
  onExit?: () => void;
  autoSave?: boolean;
  className?: string;
}

// ============================================================================
// 问卷向导组件
// ============================================================================

export function QuestionnaireWizard({
  questionnaire,
  questions,
  initialResponses = [],
  onSave,
  onSubmit,
  onExit,
  autoSave = true,
  className,
}: QuestionnaireWizardProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Map<string, QuestionResponse>>(new Map());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // 初始化已有回答
  useEffect(() => {
    const responseMap = new Map<string, QuestionResponse>();
    initialResponses.forEach(response => {
      responseMap.set(response.questionId, response);
    });
    setResponses(responseMap);
  }, [initialResponses]);

  // 自动保存
  useEffect(() => {
    if (autoSave && responses.size > 0) {
      const timer = setTimeout(() => {
        handleSave();
      }, 5000); // 5秒后自动保存

      return () => clearTimeout(timer);
    }
  }, [responses, autoSave]);

  const currentQuestion = questions[currentQuestionIndex];
  const currentResponse = responses.get(currentQuestion?.id);
  const totalQuestions = questions.length;
  const answeredQuestions = Array.from(responses.values()).filter(r => 
    r.answer !== null && r.answer !== undefined && r.answer !== ''
  ).length;
  const completionRate = (answeredQuestions / totalQuestions) * 100;

  const handleAnswerChange = (response: QuestionResponse) => {
    setResponses(prev => new Map(prev.set(response.questionId, response)));
  };

  const handleSave = async () => {
    if (onSave) {
      try {
        await onSave(Array.from(responses.values()));
        setLastSaved(new Date());
      } catch (error) {
        console.error('保存失败:', error);
      }
    }
  };

  const handleSubmit = async () => {
    console.log('🚀 问卷向导：开始提交...');
    console.log('📊 当前回答数量:', responses.size);
    console.log('📝 未完成的必答题:', getRequiredUnansweredQuestions().length);
    console.log('✅ 可以提交:', canSubmit());

    if (onSubmit) {
      setIsSubmitting(true);
      try {
        const responsesArray = Array.from(responses.values());
        console.log('📤 提交的回答:', responsesArray);
        await onSubmit(responsesArray);
      } catch (error) {
        console.error('❌ 提交失败:', error);
        alert('提交失败，请重试');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      console.warn('⚠️ 没有提供onSubmit回调函数');
    }
  };

  const goToQuestion = (index: number) => {
    if (index >= 0 && index < totalQuestions) {
      setCurrentQuestionIndex(index);
    }
  };

  const goToPrevious = () => {
    goToQuestion(currentQuestionIndex - 1);
  };

  const goToNext = () => {
    if (currentQuestion?.required && !isCurrentQuestionAnswered()) {
      return;
    }
    goToQuestion(currentQuestionIndex + 1);
  };

  const isCurrentQuestionAnswered = () => {
    const response = responses.get(currentQuestion?.id);

    if (!response) {
      return false;
    }

    const answer = response.answer;

    // 根据题目类型判断是否已回答
    switch (currentQuestion?.type) {
      case 'SINGLE_CHOICE':
      case 'SCALE':
      case 'BOOLEAN':
        return answer !== null && answer !== undefined && answer !== '';
      case 'MULTIPLE_CHOICE':
        return Array.isArray(answer) && answer.length > 0;
      case 'TEXT':
        return typeof answer === 'string' && answer.trim() !== '';
      default:
        return answer !== null && answer !== undefined;
    }
  };

  const getRequiredUnansweredQuestions = () => {
    return questions.filter(q => {
      if (!q.required) return false;

      const response = responses.get(q.id);
      if (!response) return true;

      const answer = response.answer;

      // 根据题目类型判断是否已回答
      switch (q.type) {
        case 'SINGLE_CHOICE':
        case 'SCALE':
        case 'BOOLEAN':
          return answer === null || answer === undefined || answer === '';
        case 'MULTIPLE_CHOICE':
          return !Array.isArray(answer) || answer.length === 0;
        case 'TEXT':
          return typeof answer !== 'string' || answer.trim() === '';
        default:
          return answer === null || answer === undefined;
      }
    });
  };

  const canSubmit = () => {
    return getRequiredUnansweredQuestions().length === 0;
  };

  if (!currentQuestion) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">没有找到问题</p>
      </div>
    );
  }

  return (
    <div className={cn('max-w-4xl mx-auto space-y-6', className)}>
      {/* 问卷头部信息 */}
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold">{questionnaire.title}</h1>
          {questionnaire.description && (
            <p className="text-muted-foreground mt-2">{questionnaire.description}</p>
          )}
        </div>

        {/* 整体进度 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>整体进度</span>
            <span>{answeredQuestions}/{totalQuestions} 已完成</span>
          </div>
          <Progress value={completionRate} className="h-2" />
        </div>

        {/* 状态信息 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Badge variant="outline">
              预计用时: {questionnaire.estimatedDuration}分钟
            </Badge>
            <Badge variant={completionRate >= 80 ? 'default' : 'secondary'}>
              完成度: {Math.round(completionRate)}%
            </Badge>
          </div>
          
          {lastSaved && (
            <div className="text-xs text-muted-foreground">
              最后保存: {lastSaved.toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>

      {/* 问题导航 */}
      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        {questions.map((question, index) => {
          const isAnswered = responses.has(question.id) && 
            responses.get(question.id)?.answer !== null &&
            responses.get(question.id)?.answer !== undefined &&
            responses.get(question.id)?.answer !== '';
          const isCurrent = index === currentQuestionIndex;

          return (
            <button
              key={question.id}
              className={cn(
                'flex-shrink-0 h-8 w-8 rounded-full border-2 text-xs font-medium transition-colors',
                isCurrent
                  ? 'border-primary bg-primary text-white'
                  : isAnswered
                  ? 'border-green-500 bg-green-500 text-white'
                  : question.required
                  ? 'border-red-200 text-red-600 hover:border-red-300'
                  : 'border-muted-foreground text-muted-foreground hover:border-primary'
              )}
              onClick={() => goToQuestion(index)}
            >
              {index + 1}
            </button>
          );
        })}
      </div>

      {/* 当前问题 */}
      <div className="bg-card border rounded-lg p-6">
        <QuestionRenderer
          question={currentQuestion}
          response={currentResponse}
          onAnswerChange={handleAnswerChange}
          showProgress={true}
          currentIndex={currentQuestionIndex}
          totalQuestions={totalQuestions}
        />
      </div>

      {/* 导航按钮 */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={goToPrevious}
          disabled={currentQuestionIndex === 0}
        >
          上一题
        </Button>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleSave}>
            保存草稿
          </Button>
          
          {currentQuestionIndex === totalQuestions - 1 ? (
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit() || isSubmitting}
              className="min-w-24"
            >
              {isSubmitting ? '提交中...' : '提交问卷'}
            </Button>
          ) : (
            <Button
              onClick={goToNext}
              disabled={currentQuestion.required && !isCurrentQuestionAnswered()}
            >
              下一题
            </Button>
          )}
        </div>
      </div>

      {/* 提示信息 */}
      {currentQuestion.required && !isCurrentQuestionAnswered() && (
        <Alert>
          <AlertDescription>
            这是一道必答题，请完成回答后继续。
          </AlertDescription>
        </Alert>
      )}

      {currentQuestionIndex === totalQuestions - 1 && !canSubmit() && (
        <Alert>
          <AlertDescription>
            还有 {getRequiredUnansweredQuestions().length} 道必答题未完成，请完成后提交问卷。
          </AlertDescription>
        </Alert>
      )}

      {/* 退出确认 */}
      {onExit && (
        <div className="text-center pt-4 border-t">
          <Button variant="ghost" onClick={onExit} className="text-muted-foreground">
            退出问卷
          </Button>
        </div>
      )}
    </div>
  );
}
