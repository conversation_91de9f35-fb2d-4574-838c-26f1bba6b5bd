@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    
    /* OCTI 品牌色彩变量 */
    --octi-primary: 221.2 83.2% 53.3%;
    --octi-secondary: 210 40% 96%;
    --octi-accent: 142.1 76.2% 36.3%;
    --octi-warning: 47.9 95.8% 53.1%;
    --octi-error: 0 84.2% 60.2%;
    --octi-success: 142.1 76.2% 36.3%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* OCTI 专用组件样式 */
  .octi-card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }
  
  .octi-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors;
  }
  
  .octi-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-4 py-2 rounded-md font-medium transition-colors;
  }
  
  .octi-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .octi-textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .octi-badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .octi-progress-bar {
    @apply relative h-4 w-full overflow-hidden rounded-full bg-secondary;
  }
  
  .octi-progress-indicator {
    @apply h-full w-full flex-1 bg-primary transition-all;
  }
}

@layer utilities {
  /* OCTI 专用工具类 */
  .octi-gradient-bg {
    background: linear-gradient(135deg, hsl(var(--octi-primary)) 0%, hsl(var(--octi-accent)) 100%);
  }
  
  .octi-text-gradient {
    background: linear-gradient(135deg, hsl(var(--octi-primary)) 0%, hsl(var(--octi-accent)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .octi-shadow-soft {
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1), 0 4px 16px -4px rgba(0, 0, 0, 0.1);
  }
  
  .octi-shadow-medium {
    box-shadow: 0 4px 16px -4px rgba(0, 0, 0, 0.15), 0 8px 32px -8px rgba(0, 0, 0, 0.15);
  }
  
  .octi-animation-fade-in {
    animation: fadeIn 0.5s ease-out;
  }
  
  .octi-animation-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .octi-animation-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计辅助 */
@media (max-width: 640px) {
  .octi-mobile-padding {
    @apply px-4;
  }
}

@media (min-width: 768px) {
  .octi-desktop-padding {
    @apply px-8;
  }
}

/* 打印样式 */
@media print {
  .octi-no-print {
    display: none !important;
  }
  
  .octi-print-only {
    display: block !important;
  }
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
  .octi-animation-fade-in,
  .octi-animation-slide-up,
  .octi-animation-scale-in {
    animation: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .octi-card {
    @apply border-2;
  }
  
  .octi-button-primary,
  .octi-button-secondary {
    @apply border-2 border-current;
  }
}
