# OCTI智能评估系统 - 生产环境Docker Compose配置
version: '3.8'

services:
  # ============================================================================
  # 应用服务
  # ============================================================================
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: octi-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - MINIMAX_API_KEY=${MINIMAX_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - octi-network
    volumes:
      - app-uploads:/app/public/uploads
      - app-logs:/app/logs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.octi-app.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.octi-app.tls=true"
      - "traefik.http.routers.octi-app.tls.certresolver=letsencrypt"

  # ============================================================================
  # 数据库服务
  # ============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: octi-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - octi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # ============================================================================
  # 缓存服务
  # ============================================================================
  redis:
    image: redis:7-alpine
    container_name: octi-redis
    restart: unless-stopped
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - octi-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    command: redis-server /usr/local/etc/redis/redis.conf

  # ============================================================================
  # 反向代理服务
  # ============================================================================
  nginx:
    image: nginx:alpine
    container_name: octi-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx-logs:/var/log/nginx
      - ssl-certs:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - octi-network

  # ============================================================================
  # 监控服务
  # ============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: octi-prometheus
    restart: unless-stopped
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./docker/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus-data:/prometheus
    networks:
      - octi-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: octi-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - octi-network
    depends_on:
      - prometheus

# ============================================================================
# 网络配置
# ============================================================================
networks:
  octi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ============================================================================
# 数据卷配置
# ============================================================================
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  app-uploads:
    driver: local
  app-logs:
    driver: local
  nginx-logs:
    driver: local
  ssl-certs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
