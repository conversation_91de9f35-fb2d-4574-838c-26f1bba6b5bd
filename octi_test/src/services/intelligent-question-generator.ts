/**
 * OCTI智能问题生成器
 * 
 * 基于组织画像和question_design_prompt.json配置，
 * 真正动态生成个性化的28道智能题目
 */

import { Question } from '@/components/questionnaire/question-renderer';

// 组织画像接口
export interface OrganizationProfile {
  organizationType: string;
  serviceArea: string[];
  organizationScale: string;
  developmentStage: string;
  operatingModel: string;
  impactPositioning: string;
  organizationalCulture: string;
  mission?: string;
  challenges?: string[];
  goals?: string[];
}

// 智能生成配置
interface IntelligentGenerationConfig {
  version: string;
  questionsPerDimension: number;
  totalIntelligentQuestions: number;
  contextualAdaptation: Record<string, Record<string, string>>;
  nonprofitFocusAreas: string[];
}

// 维度类型
type OCTIDimension = 'SF' | 'IT' | 'MV' | 'AD';

/**
 * 智能问题生成器类
 */
export class IntelligentQuestionGenerator {
  private config: IntelligentGenerationConfig;

  constructor() {
    // 从question_design_prompt.json加载配置
    this.config = {
      version: '4.0.0',
      questionsPerDimension: 7,
      totalIntelligentQuestions: 28,
      contextualAdaptation: {
        serviceArea: {
          '教育': '关注教育公平、学习成果、师资发展、教育创新',
          '环保': '关注环境保护、可持续发展、生态修复、绿色倡导',
          '扶贫': '关注贫困减缓、能力建设、可持续脱贫、社区发展',
          '医疗': '关注健康促进、医疗可及性、疾病预防、健康教育',
          '养老': '关注老年关怀、养老服务、代际关系、老龄化应对',
          '儿童': '关注儿童保护、儿童发展、教育支持、权益维护',
        },
        developmentStage: {
          '初创期': '关注基础能力建设、团队组建、资源获取、项目启动',
          '成长期': '关注规模扩张、流程优化、品牌建设、影响力提升',
          '成熟期': '关注可持续发展、创新转型、深度影响、行业引领',
          '转型期': '关注战略调整、模式创新、能力重构、风险管控',
        },
        operatingModel: {
          '直接服务': '关注服务质量、受益者满意度、服务创新、规模效应',
          '资助型': '关注资助策略、项目筛选、监督评估、资源配置',
          '倡导型': '关注政策影响、公众参与、议题设置、联盟建设',
        }
      },
      nonprofitFocusAreas: [
        '使命驱动特性', '利益相关者管理', '社会影响力测量', 
        '透明度与问责', '志愿者管理', '资源筹集能力', 
        '可持续发展', '治理结构'
      ]
    };
  }

  /**
   * 基于组织画像生成28道智能题目
   */
  async generateIntelligentQuestions(profile: OrganizationProfile): Promise<Question[]> {
    const dimensions: OCTIDimension[] = ['SF', 'IT', 'MV', 'AD'];
    const allQuestions: Question[] = [];

    for (const dimension of dimensions) {
      const dimensionQuestions = await this.generateDimensionQuestions(
        dimension, 
        profile, 
        this.config.questionsPerDimension
      );
      allQuestions.push(...dimensionQuestions);
    }

    return allQuestions;
  }

  /**
   * 为特定维度生成题目
   */
  private async generateDimensionQuestions(
    dimension: OCTIDimension,
    profile: OrganizationProfile,
    count: number
  ): Promise<Question[]> {
    const contextualPrompt = this.buildContextualPrompt(dimension, profile);

    // 直接尝试LLM生成，如果失败则降级到模板生成
    try {
      console.log(`🤖 为${dimension}维度调用LLM生成${count}道智能题目...`);
      return await this.generateQuestionsWithLLM(dimension, profile, count, contextualPrompt);
    } catch (error) {
      console.warn(`⚠️ ${dimension}维度LLM生成失败，降级到模板生成:`, error);
      return this.generateQuestionsFromTemplate(dimension, profile, count);
    }
  }

  /**
   * 使用LLM生成题目（通过API调用）
   */
  private async generateQuestionsWithLLM(
    dimension: OCTIDimension,
    profile: OrganizationProfile,
    count: number,
    contextualPrompt: string
  ): Promise<Question[]> {
    const response = await fetch('/api/questionnaire/generate-intelligent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        dimension,
        profile,
        count,
        contextualPrompt
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API调用失败: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || '智能问题生成失败');
    }

    console.log(`✅ ${dimension}维度API调用成功:`, result.data.questions.length, '道题目');
    return result.data.questions;
  }

  /**
   * 构建上下文化提示词
   */
  private buildContextualPrompt(dimension: OCTIDimension, profile: OrganizationProfile): string {
    // 安全获取数据，避免undefined错误
    const serviceArea = Array.isArray(profile.serviceArea) ? profile.serviceArea : [profile.serviceArea || '社会服务'];
    const serviceAreaContext = this.config.contextualAdaptation?.serviceArea?.[serviceArea[0]] || '综合性社会服务';
    const stageContext = this.config.contextualAdaptation?.developmentStage?.[profile.developmentStage] || '组织发展';
    const modelContext = this.config.contextualAdaptation?.operatingModel?.[profile.operatingModel] || '综合运营';

    return `
你是一位专业的公益机构组织能力评估专家，正在为以下公益机构设计${dimension}维度的评估问题：

机构背景：
- 组织类型：${profile.organizationType || '公益组织'}
- 服务领域：${serviceArea.join('、')} (${serviceAreaContext})
- 组织规模：${profile.organizationScale || '中型'}
- 发展阶段：${profile.developmentStage || '成长期'} (${stageContext})
- 运营模式：${profile.operatingModel || '综合服务型'} (${modelContext})
- 影响力定位：${profile.impactPositioning || '区域影响'}
- 组织文化：${profile.organizationalCulture || '使命驱动'}
- 使命：${profile.mission || '致力于社会公益事业'}

请基于以上背景，生成针对性的${dimension}维度评估问题。
问题应该：
1. 体现公益机构的特点和专业术语
2. 符合该发展阶段的关注重点和能力要求
3. 适应该组织规模的管理复杂度和资源约束
4. 针对具体的公益挑战和社会目标
5. 考虑公益机构的使命驱动特性和社会责任
6. 反映该运营模式的独特性和影响力测量需求
`;
  }

  /**
   * 基于模板生成题目（模拟LLM生成）
   */
  private generateQuestionsFromTemplate(
    dimension: OCTIDimension, 
    profile: OrganizationProfile, 
    count: number
  ): Question[] {
    const templates = this.getQuestionTemplates(dimension);
    const questions: Question[] = [];

    for (let i = 0; i < count; i++) {
      const template = templates[i % templates.length];
      const question = this.personalizeQuestion(template, profile, dimension, i + 1);
      questions.push(question);
    }

    return questions;
  }

  /**
   * 获取维度特定的题目模板
   */
  private getQuestionTemplates(dimension: OCTIDimension): any[] {
    const templates = {
      SF: [
        {
          title: '作为{organizationType}，您的组织在{serviceArea}领域的专业化程度如何？',
          description: '评估组织在特定服务领域的专业深度和聚焦程度。',
          type: 'SCALE',
          options: { min: 1, max: 5, labels: ['很低', '较低', '一般', '较高', '很高'] }
        },
        {
          title: '在{developmentStage}阶段，您的组织如何平衡资源投入与社会影响力？',
          description: '了解组织在当前发展阶段的资源配置策略。',
          type: 'SINGLE_CHOICE',
          options: [
            { text: '优先投入核心项目，确保深度影响', value: 'depth_focus' },
            { text: '适度分散投入，扩大服务覆盖', value: 'breadth_focus' },
            { text: '根据资源状况灵活调整', value: 'flexible' },
            { text: '主要依据捐赠者意愿决定', value: 'donor_driven' },
            { text: '还没有明确的配置策略', value: 'unclear' },
          ]
        }
      ],
      IT: [
        {
          title: '作为{operatingModel}的公益机构，您如何协调不同利益相关者的需求？',
          description: '评估组织在多利益相关者环境中的协调能力。',
          type: 'MULTIPLE_CHOICE',
          options: [
            { text: '建立定期沟通机制', value: 'regular_communication' },
            { text: '设立利益相关者代表制度', value: 'representation_system' },
            { text: '通过透明的决策流程', value: 'transparent_process' },
            { text: '依靠个人关系维护', value: 'personal_relations' },
            { text: '主要关注主要资助方', value: 'major_funders' },
          ]
        }
      ],
      MV: [
        {
          title: '您的组织如何确保{serviceArea}服务始终与公益使命保持一致？',
          description: '了解组织在使命驱动方面的实践和机制。',
          type: 'SINGLE_CHOICE',
          options: [
            { text: '建立了使命导向的决策框架', value: 'mission_framework' },
            { text: '定期评估项目与使命的契合度', value: 'regular_assessment' },
            { text: '通过培训强化使命认知', value: 'training' },
            { text: '主要依靠领导者的价值引导', value: 'leadership' },
            { text: '还没有系统的保障机制', value: 'no_system' },
          ]
        }
      ],
      AD: [
        {
          title: '面对{serviceArea}领域的政策变化，您的组织通常如何应对？',
          description: '评估组织对外部环境变化的适应能力。',
          type: 'SINGLE_CHOICE',
          options: [
            { text: '提前研究政策趋势，主动调整', value: 'proactive' },
            { text: '密切关注变化，快速响应', value: 'responsive' },
            { text: '等待明确信号后再调整', value: 'reactive' },
            { text: '主要依靠合作伙伴指导', value: 'partner_guided' },
            { text: '较少关注政策变化', value: 'limited_attention' },
          ]
        }
      ]
    };

    return templates[dimension] || [];
  }

  /**
   * 个性化题目内容
   */
  private personalizeQuestion(
    template: any, 
    profile: OrganizationProfile, 
    dimension: OCTIDimension, 
    order: number
  ): Question {
    let title = template.title;
    let description = template.description;

    // 安全获取服务领域
    const serviceArea = Array.isArray(profile.serviceArea)
      ? profile.serviceArea[0]
      : profile.serviceArea || '公益';

    // 替换占位符
    const replacements = {
      '{organizationType}': profile.organizationType || '公益组织',
      '{serviceArea}': serviceArea,
      '{organizationScale}': profile.organizationScale || '中型',
      '{developmentStage}': profile.developmentStage || '成长期',
      '{operatingModel}': profile.operatingModel || '直接服务',
      '{impactPositioning}': profile.impactPositioning || '区域影响',
      '{organizationalCulture}': profile.organizationalCulture || '使命驱动',
    };

    Object.entries(replacements).forEach(([key, value]) => {
      title = title.replace(new RegExp(key, 'g'), value);
      description = description.replace(new RegExp(key, 'g'), value);
    });

    return {
      id: `${dimension}_I${order.toString().padStart(3, '0')}`,
      type: template.type,
      source: 'AI_GENERATED',
      category: `${dimension} - ${this.getDimensionName(dimension)}`,
      subCategory: '智能生成',
      title,
      description: `基于您的${profile.organizationType}背景生成：${description}`,
      options: template.options,
      required: true,
      order: this.getBaseOrder(dimension) + order,
    };
  }

  /**
   * 获取维度名称
   */
  private getDimensionName(dimension: OCTIDimension): string {
    const names = {
      SF: '战略与财务',
      IT: '影响力与透明度', 
      MV: '使命与价值观',
      AD: '适应性与发展'
    };
    return names[dimension];
  }

  /**
   * 获取维度基础序号
   */
  private getBaseOrder(dimension: OCTIDimension): number {
    const baseOrders = { SF: 100, IT: 200, MV: 300, AD: 400 };
    return baseOrders[dimension];
  }
}

/**
 * 创建智能问题生成器实例
 */
export const intelligentQuestionGenerator = new IntelligentQuestionGenerator();
