"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_xychartDiagram-H2YORKM3_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-H2YORKM3.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-H2YORKM3.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: function() { return /* binding */ diagram; }\n/* harmony export */ });\n/* harmony import */ var _chunk_QESNASVV_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-QESNASVV.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-QESNASVV.mjs\");\n/* harmony import */ var _chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-55PJQP7W.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs\");\n/* harmony import */ var _chunk_P3VETL53_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-P3VETL53.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-P3VETL53.mjs\");\n/* harmony import */ var _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-3XYRH5AP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n\n\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23], $V1 = [2, 6], $V2 = [1, 3], $V3 = [1, 5], $V4 = [1, 6], $V5 = [1, 7], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $V7 = [1, 25], $V8 = [1, 26], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 30], $Vc = [1, 31], $Vd = [1, 32], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [1, 43], $Vk = [1, 42], $Vl = [1, 47], $Vm = [1, 50], $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36], $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"XYCHART\": 5, \"chartConfig\": 6, \"document\": 7, \"CHART_ORIENTATION\": 8, \"statement\": 9, \"title\": 10, \"text\": 11, \"X_AXIS\": 12, \"parseXAxis\": 13, \"Y_AXIS\": 14, \"parseYAxis\": 15, \"LINE\": 16, \"plotData\": 17, \"BAR\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"SQUARE_BRACES_START\": 24, \"commaSeparatedNumbers\": 25, \"SQUARE_BRACES_END\": 26, \"NUMBER_WITH_DECIMAL\": 27, \"COMMA\": 28, \"xAxisData\": 29, \"bandData\": 30, \"ARROW_DELIMITER\": 31, \"commaSeparatedTexts\": 32, \"yAxisData\": 33, \"NEWLINE\": 34, \"SEMI\": 35, \"EOF\": 36, \"alphaNum\": 37, \"STR\": 38, \"MD_STR\": 39, \"alphaNumToken\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"PLUS\": 44, \"EQUALS\": 45, \"MULT\": 46, \"DOT\": 47, \"BRKT\": 48, \"MINUS\": 49, \"UNDERSCORE\": 50, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"XYCHART\", 8: \"CHART_ORIENTATION\", 10: \"title\", 12: \"X_AXIS\", 14: \"Y_AXIS\", 16: \"LINE\", 18: \"BAR\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"SQUARE_BRACES_START\", 26: \"SQUARE_BRACES_END\", 27: \"NUMBER_WITH_DECIMAL\", 28: \"COMMA\", 31: \"ARROW_DELIMITER\", 34: \"NEWLINE\", 35: \"SEMI\", 36: \"EOF\", 38: \"STR\", 39: \"MD_STR\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"PLUS\", 45: \"EQUALS\", 46: \"MULT\", 47: \"DOT\", 48: \"BRKT\", 49: \"MINUS\", 50: \"UNDERSCORE\" },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 38:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 39:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, { 3: 1, 4: 2, 7: 4, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [3] }, o($V0, $V1, { 4: 2, 7: 4, 3: 8, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), o($V0, $V1, { 4: 2, 7: 4, 6: 9, 3: 10, 5: $V2, 8: [1, 11], 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 4], 9: 12, 10: [1, 13], 12: [1, 14], 14: [1, 15], 16: [1, 16], 18: [1, 17], 19: [1, 18], 21: [1, 19], 23: [1, 20] }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), { 1: [2, 1] }, o($V0, $V1, { 4: 2, 7: 4, 3: 21, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 3] }, o($V6, [2, 5]), o($V0, [2, 7], { 4: 22, 34: $V3, 35: $V4, 36: $V5 }), { 11: 23, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 39, 13: 38, 24: $Vj, 27: $Vk, 29: 40, 30: 41, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 45, 15: 44, 27: $Vl, 33: 46, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 49, 17: 48, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 52, 17: 51, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 20: [1, 53] }, { 22: [1, 54] }, o($Vn, [2, 18]), { 1: [2, 2] }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], { 40: 55, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], { 30: 41, 29: 56, 24: $Vj, 27: $Vk }), o($Vn, [2, 24]), o($Vn, [2, 25]), { 31: [1, 57] }, { 11: 59, 32: 58, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 11]), o($Vn, [2, 30], { 33: 60, 27: $Vl }), o($Vn, [2, 32]), { 31: [1, 61] }, o($Vn, [2, 12]), { 17: 62, 24: $Vm }, { 25: 63, 27: $Vq }, o($Vn, [2, 14]), { 17: 65, 24: $Vm }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), { 27: [1, 66] }, { 26: [1, 67] }, { 26: [2, 29], 28: [1, 68] }, o($Vn, [2, 31]), { 27: [1, 69] }, o($Vn, [2, 13]), { 26: [1, 70] }, { 26: [2, 21], 28: [1, 71] }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), { 11: 59, 32: 72, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 33]), o($Vn, [2, 19]), { 25: 73, 27: $Vq }, { 26: [2, 28] }, { 26: [2, 20] }],\n    defaultActions: { 8: [2, 1], 10: [2, 3], 21: [2, 2], 72: [2, 28], 73: [2, 20] },\n    parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: { \"data_inner\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"data\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_band_data\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_data\": { \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [], \"inclusive\": false }, \"md_string\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [27, 28], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = (0,_chunk_QESNASVV_mjs__WEBPACK_IMPORTED_MODULE_0__.computeDimensionOfText)(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\n\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleBand)().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleBand)().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\n\nvar LinearAxis = class extends BaseAxis {\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleLinear)().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = (0,d3__WEBPACK_IMPORTED_MODULE_4__.scaleLinear)().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: \"middle\",\n            horizontalPos: \"center\",\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0\n          }\n        ]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\n\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = (0,d3__WEBPACK_IMPORTED_MODULE_4__.line)().y((d) => d[0]).x((d) => d[1])(finalData);\n    } else {\n      path = (0,d3__WEBPACK_IMPORTED_MODULE_4__.line)().x((d) => d[0]).y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n        type: \"path\",\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth\n          }\n        ]\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [\n        {\n          groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n          type: \"rect\",\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill\n          }))\n        }\n      ];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor\n        },\n        tmpSVGGroup2\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor\n        },\n        tmpSVGGroup2\n      )\n    };\n  }\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getThemeVariables)();\n  const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)();\n  return (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.cleanAndMerge)(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)();\n  return (0,_chunk_55PJQP7W_mjs__WEBPACK_IMPORTED_MODULE_1__.cleanAndMerge)(\n    _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.defaultConfig_default.xyChart,\n    config.xyChart\n  );\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)();\n  return (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(text.trim(), config);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = { type: \"linear\", title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = { type: \"linear\", title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle)();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getChartConfig, \"getChartConfig\");\nfunction getXYChartData() {\n  return xyChartData;\n}\n(0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getXYChartData, \"getXYChartData\");\nvar clear2 = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.clear)();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccTitle,\n  getAccTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccTitle,\n  setDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.setDiagramTitle,\n  getDiagramTitle: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle,\n  getAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccDescription,\n  setAccDescription: _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig,\n  getXYChartData\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */ (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  const labelData = db.getXYChartData().plots[0].data.map((data) => data[1]);\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getTextTransformation, \"getTextTransformation\");\n  _chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = (0,_chunk_P3VETL53_mjs__WEBPACK_IMPORTED_MODULE_2__.selectSvgElement)(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.configureSvgSize)(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        if (chartConfig.showDataLabel) {\n          if (chartConfig.chartOrientation === \"horizontal\") {\n            let fitsHorizontally2 = function(item, fontSize) {\n              const { data, label } = item;\n              const textWidth = fontSize * label.length * charWidthFactor;\n              return textWidth <= data.width - 10;\n            };\n            var fitsHorizontally = fitsHorizontally2;\n            (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(fitsHorizontally2, \"fitsHorizontally\");\n            const charWidthFactor = 0.7;\n            const validItems = shape.data.map((d, i) => ({ data: d, label: labelData[i].toString() })).filter((item) => item.data.width > 0 && item.data.height > 0);\n            const candidateFontSizes = validItems.map((item) => {\n              const { data } = item;\n              let fontSize = data.height * 0.7;\n              while (!fitsHorizontally2(item, fontSize) && fontSize > 0) {\n                fontSize -= 1;\n              }\n              return fontSize;\n            });\n            const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));\n            shapeGroup.selectAll(\"text\").data(validItems).enter().append(\"text\").attr(\"x\", (item) => item.data.x + item.data.width - 10).attr(\"y\", (item) => item.data.y + item.data.height / 2).attr(\"text-anchor\", \"end\").attr(\"dominant-baseline\", \"middle\").attr(\"fill\", \"black\").attr(\"font-size\", `${uniformFontSize}px`).text((item) => item.label);\n          } else {\n            let fitsInBar2 = function(item, fontSize, yOffset2) {\n              const { data, label } = item;\n              const charWidthFactor = 0.7;\n              const textWidth = fontSize * label.length * charWidthFactor;\n              const centerX = data.x + data.width / 2;\n              const leftEdge = centerX - textWidth / 2;\n              const rightEdge = centerX + textWidth / 2;\n              const horizontalFits = leftEdge >= data.x && rightEdge <= data.x + data.width;\n              const verticalFits = data.y + yOffset2 + fontSize <= data.y + data.height;\n              return horizontalFits && verticalFits;\n            };\n            var fitsInBar = fitsInBar2;\n            (0,_chunk_3XYRH5AP_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(fitsInBar2, \"fitsInBar\");\n            const yOffset = 10;\n            const validItems = shape.data.map((d, i) => ({ data: d, label: labelData[i].toString() })).filter((item) => item.data.width > 0 && item.data.height > 0);\n            const candidateFontSizes = validItems.map((item) => {\n              const { data, label } = item;\n              let fontSize = data.width / (label.length * 0.7);\n              while (!fitsInBar2(item, fontSize, yOffset) && fontSize > 0) {\n                fontSize -= 1;\n              }\n              return fontSize;\n            });\n            const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));\n            shapeGroup.selectAll(\"text\").data(validItems).enter().append(\"text\").attr(\"x\", (item) => item.data.x + item.data.width / 2).attr(\"y\", (item) => item.data.y + yOffset).attr(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"hanging\").attr(\"fill\", \"black\").attr(\"font-size\", `${uniformFontSize}px`).text((item) => item.label);\n          }\n        }\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.horizontalPos)).attr(\"transform\", (data) => getTextTransformation(data)).text((data) => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", (data) => data.path).attr(\"fill\", (data) => data.fill ? data.fill : \"none\").attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-H2YORKM3.mjs\n"));

/***/ })

}]);