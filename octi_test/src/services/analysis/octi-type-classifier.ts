/**
 * OCTI四维八极组织类型分类器
 * 基于四维八极理论的16种组织类型识别
 * 维度：战略聚焦度(S/F)、团队协同度(I/T)、价值导向度(M/V)、能力发展度(A/D)
 */

export interface OCTITypeResult {
  code: string;
  name: string;
  description: string;
  characteristics: string[];
  strengths: string[];
  challenges: string[];
  category: 'success' | 'risk' | 'growth';
  developmentFocus: string[];
}

/**
 * OCTI四维八极16种组织类型定义
 */
const OCTI_TYPES: Record<string, OCTITypeResult> = {
  // ========== 经典成功型组织（4种） ==========
  'FTMD': {
    code: 'FTMD',
    name: '战略大师型',
    description: '战略聚焦、团队协同、使命导向、能力完善的成熟组织典型代表',
    characteristics: [
      '战略定位清晰明确',
      '团队协作机制完善',
      '使命驱动力强',
      '组织能力体系成熟'
    ],
    strengths: [
      '战略清晰',
      '团队强大',
      '使命明确',
      '能力完善'
    ],
    challenges: [
      '创新活力',
      '灵活性',
      '成本控制',
      '持续发展'
    ],
    category: 'success',
    developmentFocus: ['保持优势', '创新发展', '影响力扩大', '行业引领']
  },
  'FTMA': {
    code: 'FTMA',
    name: '创新先锋型',
    description: '战略聚焦、团队协同、使命导向、适应成长的创新型组织',
    characteristics: [
      '战略方向明确',
      '团队协作良好',
      '使命驱动强烈',
      '适应能力突出'
    ],
    strengths: [
      '创新能力',
      '团队协作',
      '使命驱动',
      '适应性强'
    ],
    challenges: [
      '能力建设',
      '标准化',
      '可持续性',
      '规模化'
    ],
    category: 'success',
    developmentFocus: ['能力完善', '规模扩张', '标准化建设', '可持续发展']
  },
  'FTVD': {
    code: 'FTVD',
    name: '专业服务型',
    description: '战略聚焦、团队协同、价值导向、能力完善的专业化服务组织',
    characteristics: [
      '专业领域聚焦',
      '团队专业化程度高',
      '注重实际价值创造',
      '服务体系完善'
    ],
    strengths: [
      '专业能力',
      '服务质量',
      '价值创造',
      '体系完善'
    ],
    challenges: [
      '使命感',
      '创新动力',
      '社会影响',
      '公益属性'
    ],
    category: 'success',
    developmentFocus: ['服务创新', '影响力提升', '品牌建设', '行业标准制定']
  },
  'FTVA': {
    code: 'FTVA',
    name: '价值创新型',
    description: '战略聚焦、团队协同、价值导向、适应成长的价值创新组织',
    characteristics: [
      '战略定位清晰',
      '团队协作有效',
      '价值创造导向',
      '创新适应能力强'
    ],
    strengths: [
      '价值创新',
      '团队协作',
      '战略聚焦',
      '成长潜力'
    ],
    challenges: [
      '使命深度',
      '能力建设',
      '可持续性',
      '社会认同'
    ],
    category: 'success',
    developmentFocus: ['模式创新', '价值提升', '能力建设', '可持续发展']
  },

  // ========== 风险挑战型组织（4种） ==========
  'SIMD': {
    code: 'SIMD',
    name: '个人英雄型',
    description: '战略模糊、个体驱动、使命导向、能力完善，过度依赖核心人员',
    characteristics: [
      '战略边界不清',
      '高度依赖核心人物',
      '使命感强烈',
      '某些能力突出'
    ],
    strengths: [
      '核心能力突出',
      '使命驱动强',
      '执行效率高',
      '专业深度好'
    ],
    challenges: [
      '核心人员风险高',
      '继任者问题',
      '决策集中',
      '团队依赖'
    ],
    category: 'risk',
    developmentFocus: ['团队建设', '权力分散', '制度建设', '人才培养']
  },
  'SIVD': {
    code: 'SIVD',
    name: '机会主义型',
    description: '战略模糊、个体驱动、价值导向、能力完善，缺乏内在使命驱动',
    characteristics: [
      '战略机会导向',
      '个人决策主导',
      '注重外在价值',
      '执行能力较强'
    ],
    strengths: [
      '机会敏感度高',
      '执行能力强',
      '资源整合好',
      '适应性强'
    ],
    challenges: [
      '缺乏内在驱动力',
      '使命感不足',
      '价值观模糊',
      '可持续性差'
    ],
    category: 'risk',
    developmentFocus: ['使命重塑', '价值澄清', '战略聚焦', '文化建设']
  },
  'SIMA': {
    code: 'SIMA',
    name: '创始人驱动型',
    description: '战略模糊、个体驱动、使命导向、适应成长，过度依赖创始人',
    characteristics: [
      '战略方向不够清晰',
      '创始人主导一切',
      '使命感强烈',
      '适应性和学习能力强'
    ],
    strengths: [
      '使命驱动强',
      '学习能力好',
      '适应性强',
      '创新意识强'
    ],
    challenges: [
      '过度依赖创始人',
      '战略不清晰',
      '制度不完善',
      '团队建设滞后'
    ],
    category: 'risk',
    developmentFocus: ['战略澄清', '团队建设', '制度完善', '权力分散']
  },
  'SIVA': {
    code: 'SIVA',
    name: '资源导向型',
    description: '战略模糊、个体驱动、价值导向、适应成长，主要依靠资源机会发展',
    characteristics: [
      '资源机会导向',
      '个人网络依赖',
      '注重实际价值',
      '灵活适应环境'
    ],
    strengths: [
      '资源整合能力',
      '灵活适应性',
      '实用主义',
      '机会把握好'
    ],
    challenges: [
      '缺乏战略方向',
      '资源依赖',
      '使命模糊',
      '可持续性差'
    ],
    category: 'risk',
    developmentFocus: ['战略规划', '使命澄清', '团队建设', '能力提升']
  },

  // ========== 成长潜力型组织（8种典型代表） ==========
  'STMA': {
    code: 'STMA',
    name: '创业萌芽型',
    description: '战略模糊、团队协同、使命导向、适应成长，高成长潜力的萌芽组织',
    characteristics: [
      '战略模糊但团队协作好',
      '使命导向强',
      '适应性高',
      '学习能力强'
    ],
    strengths: [
      '团队协作好',
      '使命驱动强',
      '适应性强',
      '成长潜力大'
    ],
    challenges: [
      '战略不够聚焦',
      '能力体系待完善',
      '资源有限',
      '经验不足'
    ],
    category: 'growth',
    developmentFocus: ['战略聚焦', '能力建设', '资源整合', '经验积累']
  },
  'STMD': {
    code: 'STMD',
    name: '使命驱动型',
    description: '战略模糊、团队协同、使命导向、能力完善，使命感强但战略待聚焦',
    characteristics: [
      '团队协作好',
      '使命感强',
      '能力较完善',
      '战略不够聚焦'
    ],
    strengths: [
      '使命驱动强',
      '团队协作好',
      '能力基础好',
      '价值认同高'
    ],
    challenges: [
      '战略聚焦不足',
      '资源分散',
      '效率待提升',
      '影响力有限'
    ],
    category: 'growth',
    developmentFocus: ['战略聚焦', '资源整合', '效率提升', '影响力扩大']
  }
};

/**
 * OCTI四维八极组织类型分类器
 */
export class OCTITypeClassifier {

  /**
   * 根据问卷结果判断OCTI组织类型（16种类型之一）
   */
  static classifyOrganizationType(responses: any[]): OCTITypeResult {
    const scores = this.calculateDimensionScores(responses);
    const polarities = this.determinePolarities(scores);
    const typeCode = this.buildTypeCode(polarities);

    console.log('🔍 OCTI类型识别过程:');
    console.log('📊 维度得分:', scores);
    console.log('⚖️ 极性判断:', polarities);
    console.log('🏷️ 类型代码:', typeCode);

    return OCTI_TYPES[typeCode] || this.getDefaultType(polarities);
  }

  /**
   * 计算四个维度的得分
   */
  private static calculateDimensionScores(responses: any[]): {
    SF: number;
    IT: number;
    MV: number;
    AD: number;
  } {
    const scores = { SF: 0, IT: 0, MV: 0, AD: 0 };
    const counts = { SF: 0, IT: 0, MV: 0, AD: 0 };

    responses.forEach(response => {
      const dimension = this.getDimensionFromQuestionId(response.questionId);
      if (dimension) {
        const score = this.convertAnswerToScore(response.answer);
        scores[dimension] += score;
        counts[dimension]++;
      }
    });

    // 计算平均分
    Object.keys(scores).forEach(dim => {
      const dimension = dim as keyof typeof scores;
      if (counts[dimension] > 0) {
        scores[dimension] = scores[dimension] / counts[dimension];
      } else {
        scores[dimension] = 3; // 默认中等分数
      }
    });

    return scores;
  }

  /**
   * 基于得分确定四维极性
   * S/F: 战略模糊(S) vs 战略聚焦(F)
   * I/T: 个体驱动(I) vs 团队协同(T)
   * M/V: 使命导向(M) vs 价值导向(V)
   * A/D: 适应成长(A) vs 能力完善(D)
   */
  private static determinePolarities(scores: {
    SF: number;
    IT: number;
    MV: number;
    AD: number;
  }): {
    SF: 'S' | 'F';
    IT: 'I' | 'T';
    MV: 'M' | 'V';
    AD: 'A' | 'D';
  } {
    const threshold = 3.0; // 极性判断阈值

    return {
      SF: scores.SF >= threshold ? 'F' : 'S', // 高分=聚焦(F)，低分=模糊(S)
      IT: scores.IT >= threshold ? 'T' : 'I', // 高分=团队(T)，低分=个体(I)
      MV: scores.MV >= threshold ? 'V' : 'M', // 高分=价值(V)，低分=使命(M)
      AD: scores.AD >= threshold ? 'D' : 'A'  // 高分=完善(D)，低分=适应(A)
    };
  }

  /**
   * 基于四维极性构建类型代码
   */
  private static buildTypeCode(polarities: {
    SF: 'S' | 'F';
    IT: 'I' | 'T';
    MV: 'M' | 'V';
    AD: 'A' | 'D';
  }): string {
    return `${polarities.SF}${polarities.IT}${polarities.MV}${polarities.AD}`;
  }

  /**
   * 获取默认类型（当类型代码不存在时）
   */
  private static getDefaultType(polarities: {
    SF: 'S' | 'F';
    IT: 'I' | 'T';
    MV: 'M' | 'V';
    AD: 'A' | 'D';
  }): OCTITypeResult {
    // 如果找不到精确匹配，返回一个成长潜力型
    return {
      code: `${polarities.SF}${polarities.IT}${polarities.MV}${polarities.AD}`,
      name: '成长潜力型',
      description: '具有独特组合特征的发展型组织，需要针对性的发展策略',
      characteristics: [
        '组合特征独特',
        '发展潜力较大',
        '需要针对性指导',
        '适应性较强'
      ],
      strengths: [
        '特色鲜明',
        '发展空间大',
        '灵活性好',
        '创新潜力'
      ],
      challenges: [
        '发展路径不明确',
        '需要专业指导',
        '资源整合待优化',
        '能力建设需加强'
      ],
      category: 'growth',
      developmentFocus: ['明确发展方向', '整合优势资源', '建设核心能力', '制定发展策略']
    };
  }
  
  /**
   * 从问题ID判断维度
   */
  private static getDimensionFromQuestionId(questionId: string): 'SF' | 'IT' | 'MV' | 'AD' | null {
    if (questionId.startsWith('SF_')) return 'SF';
    if (questionId.startsWith('IT_')) return 'IT';
    if (questionId.startsWith('MV_')) return 'MV';
    if (questionId.startsWith('AD_')) return 'AD';
    return null;
  }

  /**
   * 将答案转换为分数（1-5分制）
   */
  private static convertAnswerToScore(answer: any): number {
    if (typeof answer === 'number') {
      return Math.max(1, Math.min(5, answer));
    }

    if (typeof answer === 'string') {
      const lowerAnswer = answer.toLowerCase();

      // 5分：非常好/非常清晰
      if (lowerAnswer.includes('excellent') ||
          lowerAnswer.includes('very_clear') ||
          lowerAnswer.includes('outstanding') ||
          lowerAnswer.includes('exceptional')) return 5;

      // 4分：好/清晰
      if (lowerAnswer.includes('good') ||
          lowerAnswer.includes('clear') ||
          lowerAnswer.includes('strong') ||
          lowerAnswer.includes('effective')) return 4;

      // 3分：一般/中等
      if (lowerAnswer.includes('average') ||
          lowerAnswer.includes('moderate') ||
          lowerAnswer.includes('fair') ||
          lowerAnswer.includes('adequate')) return 3;

      // 2分：差/不清晰
      if (lowerAnswer.includes('poor') ||
          lowerAnswer.includes('unclear') ||
          lowerAnswer.includes('weak') ||
          lowerAnswer.includes('limited')) return 2;

      // 1分：非常差/非常不清晰
      if (lowerAnswer.includes('very_poor') ||
          lowerAnswer.includes('very_unclear') ||
          lowerAnswer.includes('terrible') ||
          lowerAnswer.includes('none')) return 1;
    }

    if (Array.isArray(answer)) {
      // 多选题按选择数量评分，但限制在1-5分范围内
      return Math.min(5, Math.max(1, answer.length));
    }

    return 3; // 默认中等分数
  }
}
}
