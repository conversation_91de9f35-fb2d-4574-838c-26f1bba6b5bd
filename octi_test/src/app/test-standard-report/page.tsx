'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, FileText, ArrowLeft, Download } from 'lucide-react';
import Link from 'next/link';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import jsPDF from 'jspdf';

/**
 * 标准版分析报告样式测试页面
 */
export default function TestStandardReportPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  // 生成标准版分析报告
  const generateStandardReport = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 使用模拟数据生成标准版报告
      const mockProfile = {
        organizationType: '社会服务机构',
        serviceArea: '教育支持',
        resourceStructure: '基金会支持型',
        developmentStage: '成长期',
        teamSize: '中型（21-50人）',
        operatingModel: '直接服务型',
        impactScope: '区域影响',
        organizationCulture: '使命驱动',
        challengesPriorities: '资金筹集',
        futureVision: '成为区域领先的教育支持机构'
      };

      // 生成模拟的60道题目答案
      const mockResponses = [];
      for (let i = 1; i <= 60; i++) {
        mockResponses.push({
          questionId: `Q${i.toString().padStart(3, '0')}`,
          answer: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
          confidence: Math.floor(Math.random() * 3) + 3,
          timeSpent: Math.floor(Math.random() * 30) + 10
        });
      }

      console.log('🚀 开始生成标准版分析报告...');

      const response = await fetch('/api/assessment/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          profile: mockProfile,
          responses: mockResponses,
          version: 'standard'
        }),
      });

      if (!response.ok) {
        throw new Error(`分析失败: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || '分析失败');
      }

      setAnalysisResult(result.data);
      console.log('✅ 标准版分析报告生成成功:', result.data);

    } catch (err) {
      console.error('❌ 生成标准版报告失败:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 下载PDF报告功能
  const handleDownloadReport = async () => {
    if (!analysisResult) return;

    setIsDownloading(true);
    try {
      // 生成PDF报告
      const pdf = generatePDFReport(analysisResult);

      // 下载PDF文件
      const fileName = `OCTI组织能力评估报告_${new Date().toLocaleDateString().replace(/\//g, '-')}.pdf`;
      pdf.save(fileName);

      console.log('✅ PDF报告下载成功');
    } catch (error) {
      console.error('❌ PDF报告下载失败:', error);
      alert('报告下载失败，请重试');
    } finally {
      setIsDownloading(false);
    }
  };

  // 生成PDF报告
  const generatePDFReport = (result: any): jsPDF => {
    const pdf = new jsPDF();
    const timestamp = new Date().toLocaleString();
    let yPosition = 20;

    // 设置字体
    pdf.setFont('helvetica');

    // 标题
    pdf.setFontSize(20);
    pdf.text('OCTI组织能力评估报告', 20, yPosition);
    yPosition += 15;

    // 分隔线
    pdf.setLineWidth(0.5);
    pdf.line(20, yPosition, 190, yPosition);
    yPosition += 15;

    // 基本信息
    pdf.setFontSize(12);
    pdf.text(`生成时间: ${timestamp}`, 20, yPosition);
    yPosition += 10;
    pdf.text(`总体得分: ${result.overallScore || 'N/A'}`, 20, yPosition);
    yPosition += 10;
    pdf.text(`能力等级: ${result.level || 'N/A'}`, 20, yPosition);
    yPosition += 15;

    // 维度分析
    if (result.dimensions && result.dimensions.length > 0) {
      pdf.setFontSize(14);
      pdf.text('维度分析', 20, yPosition);
      yPosition += 10;

      result.dimensions.forEach((dimension: any) => {
        if (yPosition > 270) {
          pdf.addPage();
          yPosition = 20;
        }

        pdf.setFontSize(12);
        pdf.text(`${dimension.name}: ${dimension.score} (${dimension.level})`, 25, yPosition);
        yPosition += 8;

        if (dimension.description) {
          pdf.setFontSize(10);
          const descLines = pdf.splitTextToSize(dimension.description, 160);
          pdf.text(descLines, 30, yPosition);
          yPosition += descLines.length * 5 + 5;
        }
      });
      yPosition += 10;
    }

    // 改进建议
    if (result.recommendations && result.recommendations.length > 0) {
      if (yPosition > 250) {
        pdf.addPage();
        yPosition = 20;
      }

      pdf.setFontSize(14);
      pdf.text('改进建议', 20, yPosition);
      yPosition += 10;

      result.recommendations.forEach((rec: any, index: number) => {
        if (yPosition > 270) {
          pdf.addPage();
          yPosition = 20;
        }

        pdf.setFontSize(12);
        pdf.text(`${index + 1}. ${rec.title || rec.description}`, 25, yPosition);
        yPosition += 8;

        if (rec.actions && rec.actions.length > 0) {
          pdf.setFontSize(10);
          rec.actions.forEach((action: string) => {
            if (yPosition > 270) {
              pdf.addPage();
              yPosition = 20;
            }
            pdf.text(`• ${action}`, 30, yPosition);
            yPosition += 6;
          });
        }
        yPosition += 5;
      });
    }

    // 页脚
    const pageCount = pdf.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.text('报告由OCTI智能评估系统生成', 20, 285);
      pdf.text(`第 ${i} 页，共 ${pageCount} 页`, 170, 290);
    }

    return pdf;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <Link href="/test-full-flow">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回测试页面
          </Button>
        </Link>
        <Link href="/">
          <Button variant="outline" size="sm">
            返回首页
          </Button>
        </Link>
      </div>

      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">📊 标准版分析报告样式预览</h1>
        <p className="text-muted-foreground">
          生成并查看OCTI标准版分析报告的完整样式和内容结构
        </p>
      </div>

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            报告生成控制
          </CardTitle>
          <CardDescription>
            点击按钮生成标准版分析报告并查看样式
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button
              onClick={generateStandardReport}
              disabled={isLoading}
              size="lg"
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  生成标准版分析报告
                </>
              )}
            </Button>

            {analysisResult && (
              <Button
                onClick={handleDownloadReport}
                disabled={isDownloading}
                size="lg"
                className="w-full"
                variant="outline"
              >
                {isDownloading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    下载中...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    下载PDF报告
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 错误信息 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">生成失败</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* 分析结果展示 */}
      {analysisResult && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>📋 分析结果概览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {analysisResult.overallScore || 'N/A'}
                  </div>
                  <div className="text-sm text-muted-foreground">总体得分</div>
                </div>
                <div className="text-center">
                  <Badge variant="outline" className="text-sm">
                    {analysisResult.level || 'N/A'}
                  </Badge>
                  <div className="text-sm text-muted-foreground mt-1">能力等级</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {analysisResult.dimensions?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">评估维度</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {analysisResult.recommendations?.length || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">改进建议</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 维度分析 */}
          {analysisResult.dimensions && analysisResult.dimensions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>🎯 维度分析详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analysisResult.dimensions.map((dimension: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">{dimension.name}</h3>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-blue-600">
                            {dimension.score}
                          </span>
                          <Badge variant="outline">{dimension.level}</Badge>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {dimension.description}
                      </p>
                      
                      {dimension.strengths && dimension.strengths.length > 0 && (
                        <div className="mb-2">
                          <h4 className="text-sm font-medium text-green-700 mb-1">优势</h4>
                          <ul className="text-sm text-green-600 list-disc list-inside">
                            {dimension.strengths.map((strength: string, i: number) => (
                              <li key={i}>{strength}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {dimension.improvements && dimension.improvements.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-orange-700 mb-1">改进点</h4>
                          <ul className="text-sm text-orange-600 list-disc list-inside">
                            {dimension.improvements.map((improvement: string, i: number) => (
                              <li key={i}>{improvement}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 改进建议 */}
          {analysisResult.recommendations && analysisResult.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>💡 改进建议</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analysisResult.recommendations.map((rec: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">{rec.title}</h3>
                        <Badge 
                          variant={rec.priority === 'high' ? 'destructive' : 
                                  rec.priority === 'medium' ? 'default' : 'secondary'}
                        >
                          {rec.priority === 'high' ? '高优先级' : 
                           rec.priority === 'medium' ? '中优先级' : '低优先级'}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {rec.description}
                      </p>
                      
                      {rec.actions && rec.actions.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium mb-1">具体行动</h4>
                          <ul className="text-sm list-disc list-inside space-y-1">
                            {rec.actions.map((action: string, i: number) => (
                              <li key={i}>{action}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 原始数据 */}
          <Card>
            <CardHeader>
              <CardTitle>🔍 原始数据结构</CardTitle>
              <CardDescription>
                用于开发调试的完整数据结构
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-96">
                {JSON.stringify(analysisResult, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
