"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standard-report/page",{

/***/ "(app-pages-browser)/./src/app/test-standard-report/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/test-standard-report/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestStandardReportPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n/**\n * 标准版分析报告样式测试页面\n */ function TestStandardReportPage() {\n    var _analysisResult_dimensions, _analysisResult_recommendations;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 生成标准版分析报告\n    const generateStandardReport = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 使用模拟数据生成标准版报告\n            const mockProfile = {\n                organizationType: \"社会服务机构\",\n                serviceArea: \"教育支持\",\n                resourceStructure: \"基金会支持型\",\n                developmentStage: \"成长期\",\n                teamSize: \"中型（21-50人）\",\n                operatingModel: \"直接服务型\",\n                impactScope: \"区域影响\",\n                organizationCulture: \"使命驱动\",\n                challengesPriorities: \"资金筹集\",\n                futureVision: \"成为区域领先的教育支持机构\"\n            };\n            // 生成模拟的60道题目答案\n            const mockResponses = [];\n            for(let i = 1; i <= 60; i++){\n                mockResponses.push({\n                    questionId: \"Q\".concat(i.toString().padStart(3, \"0\")),\n                    answer: [\n                        \"A\",\n                        \"B\",\n                        \"C\",\n                        \"D\"\n                    ][Math.floor(Math.random() * 4)],\n                    confidence: Math.floor(Math.random() * 3) + 3,\n                    timeSpent: Math.floor(Math.random() * 30) + 10\n                });\n            }\n            console.log(\"\\uD83D\\uDE80 开始生成标准版分析报告...\");\n            const response = await fetch(\"/api/assessment/analyze\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profile: mockProfile,\n                    responses: mockResponses,\n                    version: \"standard\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"分析失败: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error || \"分析失败\");\n            }\n            setAnalysisResult(result.data);\n            console.log(\"✅ 标准版分析报告生成成功:\", result.data);\n        } catch (err) {\n            console.error(\"❌ 生成标准版报告失败:\", err);\n            setError(err instanceof Error ? err.message : \"未知错误\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/test-full-flow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                \"返回测试页面\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"\\uD83D\\uDCCA 标准版分析报告样式预览\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"生成并查看OCTI标准版分析报告的完整样式和内容结构\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"报告生成控制\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"点击按钮生成标准版分析报告并查看样式\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: generateStandardReport,\n                            disabled: isLoading,\n                            size: \"lg\",\n                            className: \"w-full\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"生成中...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"生成标准版分析报告\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-red-800\",\n                            children: \"生成失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this),\n            analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83D\\uDCCB 分析结果概览\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: analysisResult.overallScore || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"总体得分\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-sm\",\n                                                    children: analysisResult.level || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"能力等级\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: ((_analysisResult_dimensions = analysisResult.dimensions) === null || _analysisResult_dimensions === void 0 ? void 0 : _analysisResult_dimensions.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"评估维度\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: ((_analysisResult_recommendations = analysisResult.recommendations) === null || _analysisResult_recommendations === void 0 ? void 0 : _analysisResult_recommendations.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"改进建议\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    analysisResult.dimensions && analysisResult.dimensions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83C\\uDFAF 维度分析详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: analysisResult.dimensions.map((dimension, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: dimension.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-blue-600\",\n                                                                    children: dimension.score\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: dimension.level\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mb-3\",\n                                                    children: dimension.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 23\n                                                }, this),\n                                                dimension.strengths && dimension.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-green-700 mb-1\",\n                                                            children: \"优势\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-green-600 list-disc list-inside\",\n                                                            children: dimension.strengths.map((strength, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: strength\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 25\n                                                }, this),\n                                                dimension.improvements && dimension.improvements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-orange-700 mb-1\",\n                                                            children: \"改进点\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-orange-600 list-disc list-inside\",\n                                                            children: dimension.improvements.map((improvement, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: improvement\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 13\n                    }, this),\n                    analysisResult.recommendations && analysisResult.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83D\\uDCA1 改进建议\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: analysisResult.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: rec.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: rec.priority === \"high\" ? \"destructive\" : rec.priority === \"medium\" ? \"default\" : \"secondary\",\n                                                            children: rec.priority === \"high\" ? \"高优先级\" : rec.priority === \"medium\" ? \"中优先级\" : \"低优先级\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mb-3\",\n                                                    children: rec.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 23\n                                                }, this),\n                                                rec.actions && rec.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium mb-1\",\n                                                            children: \"具体行动\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm list-disc list-inside space-y-1\",\n                                                            children: rec.actions.map((action, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: action\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"\\uD83D\\uDD0D 原始数据结构\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"用于开发调试的完整数据结构\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-96\",\n                                    children: JSON.stringify(analysisResult, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(TestStandardReportPage, \"gfKDQ+FyMMUSt7XswdaxwbLgOIk=\");\n_c = TestStandardReportPage;\nvar _c;\n$RefreshReg$(_c, \"TestStandardReportPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-standard-report/page.tsx\n"));

/***/ })

});