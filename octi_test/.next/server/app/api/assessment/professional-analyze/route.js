"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/assessment/professional-analyze/route";
exports.ids = ["app/api/assessment/professional-analyze/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&page=%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&page=%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_assessment_professional_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/assessment/professional-analyze/route.ts */ \"(rsc)/./src/app/api/assessment/professional-analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/assessment/professional-analyze/route\",\n        pathname: \"/api/assessment/professional-analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/assessment/professional-analyze/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/professional-analyze/route.ts\",\n    nextConfigOutput,\n    userland: _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_assessment_professional_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/assessment/professional-analyze/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&page=%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/assessment/professional-analyze/route.ts":
/*!**************************************************************!*\
  !*** ./src/app/api/assessment/professional-analyze/route.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_analysis_professional_analysis_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/analysis/professional-analysis-service */ \"(rsc)/./src/services/analysis/professional-analysis-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/**\n * 专业版双模型OCTI分析API路由\n * MiniMax基础分析 + DeepSeek-Reasoner深度升华\n */ \n\n\n// 请求数据验证schema\nconst ProfessionalAnalysisRequestSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    profile: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        organizationType: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        serviceArea: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        resourceStructure: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        developmentStage: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        teamSize: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        operatingModel: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        impactScope: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        organizationCulture: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        challengesPriorities: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional(),\n        futureVision: zod__WEBPACK_IMPORTED_MODULE_2__.string().optional()\n    }),\n    responses: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        questionId: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n        answer: zod__WEBPACK_IMPORTED_MODULE_2__.union([\n            zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n            zod__WEBPACK_IMPORTED_MODULE_2__.number(),\n            zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string())\n        ])\n    })),\n    version: zod__WEBPACK_IMPORTED_MODULE_2__.literal(\"professional\")\n});\nasync function POST(request) {\n    try {\n        console.log(\"\\uD83D\\uDE80 收到专业版双模型分析请求\");\n        // 解析请求数据\n        const body = await request.json();\n        // 验证数据格式\n        const validatedData = ProfessionalAnalysisRequestSchema.parse(body);\n        console.log(\"\\uD83D\\uDCCA 专业版数据验证通过\");\n        console.log(\"\\uD83D\\uDC64 组织画像:\", validatedData.profile);\n        console.log(\"\\uD83D\\uDCDD 回答数量:\", validatedData.responses.length);\n        console.log(\"\\uD83C\\uDFAF 分析版本:\", validatedData.version);\n        // 执行专业版双模型分析\n        const analysisService = new _services_analysis_professional_analysis_service__WEBPACK_IMPORTED_MODULE_1__.ProfessionalAnalysisService();\n        const result = await analysisService.analyzeProfessional(validatedData);\n        if (result.success) {\n            console.log(\"✅ 专业版双模型分析完成\");\n            console.log(\"\\uD83D\\uDCCB 处理步骤:\", result.data.processingSteps);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n        } else {\n            console.error(\"❌ 专业版分析失败:\", result.error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ 专业版API处理失败:\", error);\n        // 返回友好的错误信息\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"请求数据格式不正确\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"服务器内部错误\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"OCTI专业版双模型分析API\",\n        version: \"1.0.0\",\n        models: {\n            step1: \"MiniMax-M1 (基础分析)\",\n            step2: \"DeepSeek-Reasoner (深度升华)\"\n        },\n        endpoints: {\n            POST: \"/api/assessment/professional-analyze - 执行专业版双模型OCTI分析\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/assessment/professional-analyze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/octi-type-classifier.ts":
/*!*******************************************************!*\
  !*** ./src/services/analysis/octi-type-classifier.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OCTITypeClassifier: () => (/* binding */ OCTITypeClassifier)\n/* harmony export */ });\n/**\n * OCTI四维八极组织类型分类器\n * 基于问卷结果快速判断组织类型，无需复杂的LLM调用\n */ /**\n * OCTI组织类型定义\n */ const OCTI_TYPES = {\n    \"SFMV\": {\n        code: \"SFMV\",\n        name: \"战略价值型\",\n        description: \"具有清晰战略定位和强烈价值导向的组织，在使命驱动和专业聚焦方面表现突出。\",\n        characteristics: [\n            \"使命陈述清晰明确\",\n            \"价值观深入人心\",\n            \"专业领域聚焦\",\n            \"社会影响力导向\"\n        ],\n        strengths: [\n            \"战略方向明确\",\n            \"团队价值认同度高\",\n            \"专业能力突出\",\n            \"社会公信力强\"\n        ],\n        challenges: [\n            \"团队协作机制需完善\",\n            \"适应性发展能力待提升\",\n            \"资源整合效率可优化\"\n        ]\n    },\n    \"ITAD\": {\n        code: \"ITAD\",\n        name: \"协同发展型\",\n        description: \"注重团队协作和能力建设的组织，在内部管理和学习发展方面具有优势。\",\n        characteristics: [\n            \"团队协作机制完善\",\n            \"学习发展氛围浓厚\",\n            \"内部沟通顺畅\",\n            \"适应变化能力强\"\n        ],\n        strengths: [\n            \"团队凝聚力强\",\n            \"组织学习能力突出\",\n            \"变革适应性好\",\n            \"内部管理规范\"\n        ],\n        challenges: [\n            \"战略聚焦度需加强\",\n            \"价值传播影响力待提升\",\n            \"外部资源整合能力需完善\"\n        ]\n    },\n    \"SFAD\": {\n        code: \"SFAD\",\n        name: \"战略发展型\",\n        description: \"战略清晰且具备强适应能力的组织，能够在变化中保持发展方向。\",\n        characteristics: [\n            \"战略规划完善\",\n            \"发展目标明确\",\n            \"创新能力较强\",\n            \"资源配置合理\"\n        ],\n        strengths: [\n            \"长期发展规划清晰\",\n            \"创新发展能力强\",\n            \"资源利用效率高\",\n            \"市场适应性好\"\n        ],\n        challenges: [\n            \"团队协同效率需提升\",\n            \"价值文化建设待加强\",\n            \"利益相关者参与度可提高\"\n        ]\n    },\n    \"MVIT\": {\n        code: \"MVIT\",\n        name: \"价值协同型\",\n        description: \"价值导向明确且团队协作良好的组织，注重文化建设和团队凝聚。\",\n        characteristics: [\n            \"组织文化浓厚\",\n            \"价值观统一\",\n            \"团队合作默契\",\n            \"志愿者参与度高\"\n        ],\n        strengths: [\n            \"组织文化凝聚力强\",\n            \"团队协作效率高\",\n            \"价值传播能力突出\",\n            \"社会认同度高\"\n        ],\n        challenges: [\n            \"战略执行力需加强\",\n            \"创新发展能力待提升\",\n            \"专业技能建设需完善\"\n        ]\n    },\n    \"BALANCED\": {\n        code: \"BALANCED\",\n        name: \"均衡发展型\",\n        description: \"四个维度发展相对均衡的组织，具有全面但不突出的特征。\",\n        characteristics: [\n            \"各维度发展均衡\",\n            \"综合能力较强\",\n            \"发展潜力较大\",\n            \"适应性较好\"\n        ],\n        strengths: [\n            \"综合发展能力强\",\n            \"各方面基础扎实\",\n            \"发展潜力大\",\n            \"风险抵御能力较强\"\n        ],\n        challenges: [\n            \"缺乏突出优势\",\n            \"特色不够鲜明\",\n            \"需要找到发展重点\",\n            \"资源分散风险\"\n        ]\n    }\n};\n/**\n * 基于问卷结果分类OCTI组织类型\n */ class OCTITypeClassifier {\n    /**\n   * 根据问卷结果判断组织类型\n   */ static classifyOrganizationType(responses) {\n        const scores = this.calculateDimensionScores(responses);\n        const typeCode = this.determineTypeCode(scores);\n        return OCTI_TYPES[typeCode] || OCTI_TYPES[\"BALANCED\"];\n    }\n    /**\n   * 计算四个维度的得分\n   */ static calculateDimensionScores(responses) {\n        const scores = {\n            SF: 0,\n            IT: 0,\n            MV: 0,\n            AD: 0\n        };\n        const counts = {\n            SF: 0,\n            IT: 0,\n            MV: 0,\n            AD: 0\n        };\n        responses.forEach((response)=>{\n            const dimension = this.getDimensionFromQuestionId(response.questionId);\n            if (dimension) {\n                const score = this.convertAnswerToScore(response.answer);\n                scores[dimension] += score;\n                counts[dimension]++;\n            }\n        });\n        // 计算平均分\n        Object.keys(scores).forEach((dim)=>{\n            const dimension = dim;\n            if (counts[dimension] > 0) {\n                scores[dimension] = scores[dimension] / counts[dimension];\n            } else {\n                scores[dimension] = 3; // 默认中等分数\n            }\n        });\n        return scores;\n    }\n    /**\n   * 从问题ID判断维度\n   */ static getDimensionFromQuestionId(questionId) {\n        if (questionId.startsWith(\"SF_\")) return \"SF\";\n        if (questionId.startsWith(\"IT_\")) return \"IT\";\n        if (questionId.startsWith(\"MV_\")) return \"MV\";\n        if (questionId.startsWith(\"AD_\")) return \"AD\";\n        return null;\n    }\n    /**\n   * 将答案转换为分数\n   */ static convertAnswerToScore(answer) {\n        if (typeof answer === \"number\") {\n            return Math.max(1, Math.min(5, answer));\n        }\n        if (typeof answer === \"string\") {\n            const lowerAnswer = answer.toLowerCase();\n            if (lowerAnswer.includes(\"excellent\") || lowerAnswer.includes(\"very_clear\")) return 5;\n            if (lowerAnswer.includes(\"good\") || lowerAnswer.includes(\"clear\")) return 4;\n            if (lowerAnswer.includes(\"average\") || lowerAnswer.includes(\"moderate\")) return 3;\n            if (lowerAnswer.includes(\"poor\") || lowerAnswer.includes(\"unclear\")) return 2;\n            if (lowerAnswer.includes(\"very_poor\") || lowerAnswer.includes(\"very_unclear\")) return 1;\n        }\n        if (Array.isArray(answer)) {\n            // 多选题按选择数量评分\n            return Math.min(5, Math.max(1, answer.length));\n        }\n        return 3; // 默认中等分数\n    }\n    /**\n   * 根据维度得分确定组织类型代码\n   */ static determineTypeCode(scores) {\n        const threshold = 3.5; // 高分阈值\n        const highDimensions = [];\n        if (scores.SF >= threshold) highDimensions.push(\"SF\");\n        if (scores.IT >= threshold) highDimensions.push(\"IT\");\n        if (scores.MV >= threshold) highDimensions.push(\"MV\");\n        if (scores.AD >= threshold) highDimensions.push(\"AD\");\n        // 根据高分维度组合确定类型\n        if (highDimensions.includes(\"SF\") && highDimensions.includes(\"MV\")) {\n            return \"SFMV\";\n        }\n        if (highDimensions.includes(\"IT\") && highDimensions.includes(\"AD\")) {\n            return \"ITAD\";\n        }\n        if (highDimensions.includes(\"SF\") && highDimensions.includes(\"AD\")) {\n            return \"SFAD\";\n        }\n        if (highDimensions.includes(\"MV\") && highDimensions.includes(\"IT\")) {\n            return \"MVIT\";\n        }\n        return \"BALANCED\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/octi-type-classifier.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/professional-analysis-service.ts":
/*!****************************************************************!*\
  !*** ./src/services/analysis/professional-analysis-service.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfessionalAnalysisService: () => (/* binding */ ProfessionalAnalysisService)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _octi_type_classifier__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./octi-type-classifier */ \"(rsc)/./src/services/analysis/octi-type-classifier.ts\");\n/* harmony import */ var _llm_llm_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../llm/llm-client */ \"(rsc)/./src/services/llm/llm-client.ts\");\n/**\n * 专业版双模型OCTI分析服务\n * 第一步：MiniMax基础分析 -> 第二步：DeepSeek-Reasoner深度升华\n */ \n\n\n\n/**\n * 专业版双模型OCTI分析服务\n */ class ProfessionalAnalysisService {\n    constructor(){\n        this.minimaxClient = new _llm_llm_client__WEBPACK_IMPORTED_MODULE_3__.LLMClient(\"minimax\");\n        this.deepseekClient = new _llm_llm_client__WEBPACK_IMPORTED_MODULE_3__.LLMClient(\"deepseek\");\n        this.loadPromptConfigs();\n    }\n    /**\n   * 加载提示词配置\n   */ loadPromptConfigs() {\n        try {\n            // 基础分析使用精简配置\n            const basicConfigPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"configs\", \"octi_analysis_prompt.json\");\n            const basicConfigContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(basicConfigPath, \"utf-8\");\n            this.basicPromptConfig = JSON.parse(basicConfigContent);\n            // 深度分析使用完整配置\n            const enhancementConfigPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"configs\", \"organization_tutor_prompt.json\");\n            const enhancementConfigContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(enhancementConfigPath, \"utf-8\");\n            this.enhancementPromptConfig = JSON.parse(enhancementConfigContent);\n            console.log(\"✅ 专业版双模型提示词配置加载成功\");\n            console.log(`📏 基础配置长度: ${basicConfigContent.length}字符`);\n            console.log(`📏 增强配置长度: ${enhancementConfigContent.length}字符`);\n        } catch (error) {\n            console.error(\"❌ 加载专业版提示词配置失败:\", error);\n            throw new Error(\"无法加载专业版分析配置\");\n        }\n    }\n    /**\n   * 执行专业版双模型分析\n   */ async analyzeProfessional(request) {\n        const processingSteps = [];\n        try {\n            console.log(\"\\uD83D\\uDE80 开始专业版双模型OCTI分析...\");\n            processingSteps.push(\"开始专业版分析\");\n            // 第一步：快速组织类型判断\n            console.log(\"\\uD83D\\uDCCA 步骤1: 组织类型判断...\");\n            const organizationType = _octi_type_classifier__WEBPACK_IMPORTED_MODULE_2__.OCTITypeClassifier.classifyOrganizationType(request.responses);\n            console.log(`✅ 组织类型: ${organizationType.code} - ${organizationType.name}`);\n            processingSteps.push(`组织类型判断完成: ${organizationType.name}`);\n            // 第二步：MiniMax基础分析\n            console.log(\"\\uD83E\\uDD16 步骤2: MiniMax基础分析...\");\n            const basicAnalysis = await this.generateBasicAnalysis(request);\n            console.log(\"✅ MiniMax基础分析完成\");\n            processingSteps.push(\"MiniMax基础分析完成\");\n            // 第三步：DeepSeek深度升华\n            console.log(\"\\uD83E\\uDDE0 步骤3: DeepSeek深度升华...\");\n            const enhancedAnalysis = await this.generateEnhancedAnalysis(request, basicAnalysis, organizationType);\n            console.log(\"✅ DeepSeek深度升华完成\");\n            processingSteps.push(\"DeepSeek深度升华完成\");\n            return {\n                success: true,\n                data: {\n                    organizationType,\n                    basicAnalysis,\n                    enhancedAnalysis,\n                    timestamp: new Date().toISOString(),\n                    version: request.version,\n                    processingSteps\n                }\n            };\n        } catch (error) {\n            console.error(\"❌ 专业版分析失败:\", error);\n            processingSteps.push(`分析失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n            // 容错处理：至少返回组织类型判断\n            try {\n                const organizationType = _octi_type_classifier__WEBPACK_IMPORTED_MODULE_2__.OCTITypeClassifier.classifyOrganizationType(request.responses);\n                return {\n                    success: false,\n                    error: `专业版分析失败: ${error instanceof Error ? error.message : \"未知错误\"}`,\n                    data: {\n                        organizationType,\n                        basicAnalysis: \"基础分析失败，请稍后重试。\",\n                        enhancedAnalysis: \"深度分析失败，请稍后重试。\",\n                        timestamp: new Date().toISOString(),\n                        version: request.version,\n                        processingSteps\n                    }\n                };\n            } catch (fallbackError) {\n                return {\n                    success: false,\n                    error: `完全分析失败: ${error instanceof Error ? error.message : \"未知错误\"}`,\n                    data: {\n                        organizationType: {\n                            code: \"ERROR\",\n                            name: \"分析失败\",\n                            description: \"分析过程中出现错误\",\n                            characteristics: [],\n                            strengths: [],\n                            challenges: []\n                        },\n                        basicAnalysis: \"分析失败，请检查系统配置。\",\n                        enhancedAnalysis: \"分析失败，请检查系统配置。\",\n                        timestamp: new Date().toISOString(),\n                        version: request.version,\n                        processingSteps\n                    }\n                };\n            }\n        }\n    }\n    /**\n   * 第二步：MiniMax基础分析\n   */ async generateBasicAnalysis(request) {\n        // 构建基础分析提示词\n        const systemPrompt = this.buildBasicSystemPrompt();\n        const userPrompt = this.buildBasicUserPrompt(request);\n        console.log(\"\\uD83D\\uDCCF MiniMax提示词长度:\", systemPrompt.length + userPrompt.length, \"字符\");\n        // 调用MiniMax\n        const rawResponse = await this.minimaxClient.call({\n            model: \"minimax-M1\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 3000\n        });\n        // 提取文本内容\n        let responseText = \"\";\n        if (rawResponse?.choices?.[0]?.message?.content) {\n            responseText = rawResponse.choices[0].message.content;\n        } else if (typeof rawResponse === \"string\") {\n            responseText = rawResponse;\n        } else {\n            console.warn(\"⚠️ MiniMax响应格式异常:\", rawResponse);\n            responseText = \"\";\n        }\n        // 确保是字符串类型\n        if (typeof responseText !== \"string\") {\n            console.warn(\"⚠️ MiniMax响应内容不是字符串:\", typeof responseText, responseText);\n            responseText = String(responseText || \"\");\n        }\n        // 清理文本格式\n        return this.cleanAnalysisText(responseText);\n    }\n    /**\n   * 第三步：DeepSeek深度升华分析\n   */ async generateEnhancedAnalysis(request, basicAnalysis, organizationType) {\n        // 构建深度分析提示词\n        const systemPrompt = this.buildEnhancementSystemPrompt();\n        const userPrompt = this.buildEnhancementUserPrompt(request, basicAnalysis, organizationType);\n        console.log(\"\\uD83D\\uDCCF DeepSeek提示词长度:\", systemPrompt.length + userPrompt.length, \"字符\");\n        // 调用DeepSeek-Reasoner\n        const rawResponse = await this.deepseekClient.call({\n            model: \"deepseek-reasoner\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.3,\n            max_tokens: 4000\n        });\n        // 提取文本内容\n        let responseText = \"\";\n        if (rawResponse?.choices?.[0]?.message?.content) {\n            responseText = rawResponse.choices[0].message.content;\n        } else if (typeof rawResponse === \"string\") {\n            responseText = rawResponse;\n        } else {\n            console.warn(\"⚠️ DeepSeek响应格式异常:\", rawResponse);\n            responseText = \"\";\n        }\n        // 确保是字符串类型\n        if (typeof responseText !== \"string\") {\n            console.warn(\"⚠️ DeepSeek响应内容不是字符串:\", typeof responseText, responseText);\n            responseText = String(responseText || \"\");\n        }\n        // 清理文本格式\n        return this.cleanAnalysisText(responseText);\n    }\n    /**\n   * 构建MiniMax基础分析系统提示词\n   */ buildBasicSystemPrompt() {\n        return `你是OCTI公益组织能力评估专家，负责进行基础的组织能力分析。\n\n你的任务是基于组织画像和问卷结果，提供清晰、实用的基础分析，为后续的深度分析提供基础。\n\n分析要求：\n1. 语言通俗易懂，避免过于专业的术语\n2. 重点关注组织的实际能力表现\n3. 识别明显的优势和挑战\n4. 保持客观中肯的分析态度\n5. 为深度分析提供扎实的基础\n\n输出格式：自然文本，结构清晰，不使用markdown符号。`;\n    }\n    /**\n   * 构建MiniMax基础分析用户提示词\n   */ buildBasicUserPrompt(request) {\n        const { profile, responses } = request;\n        const organizationProfile = this.formatOrganizationProfile(profile);\n        const questionnaireResults = this.formatQuestionnaireResults(responses);\n        return `请对以下公益组织进行基础能力分析：\n\n【组织基本情况】\n${organizationProfile}\n\n【问卷评估结果】\n${questionnaireResults}\n\n请按照OCTI四维模型进行基础分析，严格采用以下格式：\n\n一、组织能力概览\n简要总结组织的整体能力水平和发展阶段\n\n二、四维能力分析\n\n1. 战略聚焦度\n当前表现：\n- [具体表现点1]\n- [具体表现点2]\n- [具体表现点3]\n\n主要优势：\n- [优势点1]\n- [优势点2]\n- [优势点3]\n\n改进空间：\n- [改进建议1]\n- [改进建议2]\n\n2. 团队协同度\n当前表现：\n- [具体表现点1]\n- [具体表现点2]\n- [具体表现点3]\n\n主要优势：\n- [优势点1]\n- [优势点2]\n- [优势点3]\n\n改进空间：\n- [改进建议1]\n- [改进建议2]\n\n3. 价值导向度\n当前表现：\n- [具体表现点1]\n- [具体表现点2]\n- [具体表现点3]\n\n主要优势：\n- [优势点1]\n- [优势点2]\n- [优势点3]\n\n改进空间：\n- [改进建议1]\n- [改进建议2]\n\n4. 能力发展度\n当前表现：\n- [具体表现点1]\n- [具体表现点2]\n- [具体表现点3]\n\n主要优势：\n- [优势点1]\n- [优势点2]\n- [优势点3]\n\n改进空间：\n- [改进建议1]\n- [改进建议2]\n\n三、核心建议\n1. [具体建议1]\n2. [具体建议2]\n3. [具体建议3]\n\n**重要要求：**\n- 严格按照上述格式输出，保持层次清晰\n- 全部使用中文，绝对不要出现任何英文单词（如infrastructure等）\n- 不要出现任何问题ID、调试信息或代码（如MV_P001、SF_P003等）\n- 不要使用括号，特别是连续括号（（或空括号（）\n- 每个要点独占一行，便于阅读\n- 用自然流畅的中文描述，避免技术术语\n- 如果需要表达技术概念，请用中文词汇替代英文`;\n    }\n    /**\n   * 构建DeepSeek深度分析系统提示词\n   */ buildEnhancementSystemPrompt() {\n        // 使用完整的organization_tutor_prompt.json配置\n        const fullPrompt = this.enhancementPromptConfig.prompt_template?.system_message || this.enhancementPromptConfig.systemPrompt?.role || \"你是资深的公益组织发展顾问和OCTI评估专家\";\n        return `${fullPrompt}\n\n你的特殊任务是基于已有的基础分析，进行深度升华和专业指导：\n\n1. 深化分析深度：从基础分析中提炼更深层的洞察\n2. 提供战略指导：结合行业最佳实践，给出战略性建议\n3. 系统性思考：将各个维度整合为系统性的发展方案\n4. 前瞻性规划：考虑组织的长期发展和行业趋势\n5. 可操作性：确保建议具体可行，有明确的实施路径\n\n你要在基础分析的基础上，提供更有价值、更具指导性的专业分析。`;\n    }\n    /**\n   * 构建DeepSeek深度分析用户提示词\n   */ buildEnhancementUserPrompt(request, basicAnalysis, organizationType) {\n        const { profile } = request;\n        return `请基于以下信息，对这个公益组织进行深度升华分析：\n\n【组织类型】\n${organizationType.code} - ${organizationType.name}\n${organizationType.description}\n\n【组织基本情况】\n${this.formatOrganizationProfile(profile)}\n\n【基础分析结果】\n${basicAnalysis}\n\n请在基础分析的基础上，进行深度升华，提供：\n\n1. 深层能力洞察：挖掘基础分析中的深层含义和潜在机会\n2. 战略发展建议：结合组织类型特征，提供战略性发展方向\n3. 系统性改进方案：整合各维度，形成系统性的能力提升方案\n4. 行业对标分析：与同类型优秀组织对比，找出差距和机会\n5. 长期发展规划：考虑3-5年的发展路径和里程碑\n6. 风险预警和应对：识别潜在风险并提供应对策略\n\n**重要要求：**\n- 请提供更有深度、更具指导价值的专业分析，帮助组织实现跨越式发展\n- 全部使用中文，绝对不要出现任何英文单词（如infrastructure、framework等）\n- 不要在分析结果中包含任何调试信息、问题ID或评分代码（如IT_P002=4、MV_P001等）\n- 不要使用括号，特别是连续括号（（或空括号（）\n- 确保输出内容完全面向最终用户，不包含后台运算过程\n- 如果需要表达技术概念，请用中文词汇替代英文\n- 如果需要使用表格，请使用标准的Markdown表格格式，确保前端能正确渲染`;\n    }\n    /**\n   * 格式化组织画像\n   */ formatOrganizationProfile(profile) {\n        return `\n组织类型: ${profile.organizationType || \"未知\"}\n服务领域: ${profile.serviceArea || \"未知\"}\n发展阶段: ${profile.developmentStage || \"未知\"}\n团队规模: ${profile.teamSize || \"未知\"}\n运营模式: ${profile.operatingModel || \"未知\"}\n影响范围: ${profile.impactScope || \"未知\"}\n组织文化: ${profile.organizationCulture || \"未知\"}\n主要挑战: ${profile.challengesPriorities || \"未知\"}\n未来愿景: ${profile.futureVision || \"未知\"}\n    `.trim();\n    }\n    /**\n   * 格式化问卷结果\n   */ formatQuestionnaireResults(responses) {\n        return responses.map((response, index)=>{\n            const answer = Array.isArray(response.answer) ? response.answer.join(\", \") : response.answer;\n            return `${index + 1}. ${response.questionId}: ${answer}`;\n        }).join(\"\\n\");\n    }\n    /**\n   * 清理分析文本（保留格式结构）\n   */ cleanAnalysisText(text) {\n        // 确保输入是字符串\n        if (typeof text !== \"string\") {\n            console.warn(\"⚠️ cleanAnalysisText 接收到非字符串类型:\", typeof text);\n            return \"\";\n        }\n        // 先保存原始的换行结构\n        let cleanedText = text;\n        // 第一步：移除调试信息（保留换行）\n        cleanedText = cleanedText// 移除各种格式的调试代码\n        .replace(/\\([A-Z]{2,}_[PI]\\d+\\s*[=:]\\s*[^)]+\\)/g, \"\") // 移除 (IT_P002=4) 或 (IT_I002: 2分低) 类型\n        .replace(/\\([A-Z]{2,}_\\w+\\s*[=:]\\s*[^)]+\\)/g, \"\") // 移除其他类似的调试信息\n        .replace(/[A-Z]{2,}_[PI]\\d+\\s*[=:]\\s*[^,，\\s\\n)]+/g, \"\") // 移除 SF_P001: very_clear 类型\n        .replace(/（[A-Z]{2,}_[PI]\\d+\\s*[=:]\\s*[^）]+）/g, \"\") // 移除中文括号的调试信息\n        .replace(/[A-Z]{2,}_[PI]\\d+\\s*:\\s*\\d+分[低中高]/g, \"\") // 移除 \"IT_I002: 2分低\" 类型\n        .replace(/[A-Z]{2,}_[PI]\\d+/g, \"\") // 移除单独的调试代码如MV_P001\n        .replace(/（[A-Z]{2,}_[PI]\\d+）/g, \"\") // 移除括号中的调试代码\n        .replace(/数据提示：[^\\n]*/g, \"\") // 移除\"数据提示：\"开头的调试行\n        .replace(/但类似于[^\\n]*不应该出现[^\\n]*/g, \"\") // 移除特定的调试说明\n        // 新增：移除更多调试信息格式\n        .replace(/\\d+分[低中高等]/g, \"\") // 移除 \"2分低\"、\"4分中等\" 类型\n        .replace(/[A-Z]{2,}_[A-Z]\\d+\\s*:\\s*\\d+分[低中高等]/g, \"\") // 移除完整的评分调试信息\n        .replace(/评分[：:]\\s*\\d+分/g, \"\") // 移除 \"评分：4分\" 类型\n        .replace(/得分[：:]\\s*\\d+/g, \"\"); // 移除 \"得分：4\" 类型\n        // 第二步：移除英文内容（保留换行）\n        cleanedText = cleanedText.replace(/（问卷显示\"[^\"]*\"）/g, \"\") // 移除（问卷显示\"very_clear\"）类型的调试信息\n        .replace(/（\"[^\"]*\"）/g, \"\") // 移除（\"excellent\"）类型的调试信息\n        .replace(/问卷显示\"[^\"]*\"/g, \"\") // 移除问卷显示\"very_clear\"\n        .replace(/\"[a-zA-Z_]+\"/g, \"\") // 移除英文引号内容\n        .replace(/\\([a-zA-Z_]+\\)/g, \"\") // 移除英文括号内容\n        .replace(/\\b[a-zA-Z]{4,}\\b/g, \"\") // 移除4个字母以上的英文单词\n        .replace(/\\([^)]*[a-zA-Z]{3,}[^)]*\\)/g, \"\"); // 移除包含英文的括号内容\n        // 第三步：修复括号问题（保留换行）\n        cleanedText = cleanedText.replace(/（+/g, \"\") // 清理连续的左括号\n        .replace(/）+/g, \"\") // 清理连续的右括号\n        .replace(/（\\s*）/g, \"\") // 清理空括号\n        .replace(/\\(\\s*\\)/g, \"\"); // 清理英文空括号\n        // 第四步：清理多余的空格和空行，但保留段落结构\n        cleanedText = cleanedText.replace(/[ \\t]+/g, \" \") // 合并连续的空格和制表符\n        .replace(/\\n[ \\t]+/g, \"\\n\") // 清理行首的空格\n        .replace(/[ \\t]+\\n/g, \"\\n\") // 清理行尾的空格\n        .replace(/\\n{3,}/g, \"\\n\\n\") // 合并多余的空行，但保留段落分隔\n        .trim();\n        return cleanedText;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/professional-analysis-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/llm-client.ts":
/*!****************************************!*\
  !*** ./src/services/llm/llm-client.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLMClient: () => (/* binding */ LLMClient),\n/* harmony export */   createLLMClient: () => (/* binding */ createLLMClient)\n/* harmony export */ });\n/**\n * OCTI智能评估系统 - LLM API客户端\n * \n * 支持多个LLM服务提供商的统一接口\n */ /**\n * LLM API客户端\n */ class LLMClient {\n    constructor(provider = \"minimax\"){\n        this.provider = provider;\n        if (provider === \"deepseek\") {\n            this.apiKey = process.env.DEEPSEEK_API_KEY || \"\";\n            this.baseUrl = \"https://api.deepseek.com/v1\";\n        } else {\n            this.apiKey = process.env.MINIMAX_API_KEY || \"\";\n            this.baseUrl = \"https://api.minimaxi.com/v1\";\n        }\n        if (!this.apiKey) {\n            throw new Error(`缺少${provider.toUpperCase()}_API_KEY环境变量`);\n        }\n    }\n    /**\n   * 调用LLM API\n   */ async call(request) {\n        try {\n            console.log(\"\\uD83E\\uDD16 调用LLM API:\", {\n                model: request.model,\n                messages: request.messages.length,\n                temperature: request.temperature\n            });\n            // 根据提供商调整请求格式\n            const requestBody = this.provider === \"minimax\" ? {\n                model: request.model || \"MiniMax-M1\",\n                messages: request.messages,\n                temperature: request.temperature || 0.7,\n                max_tokens: request.max_tokens || 12000,\n                top_p: 0.9,\n                stream: false,\n                // MiniMax不支持response_format参数，移除它\n                ...request.response_format ? {} : {}\n            } : {\n                model: request.model || \"deepseek-reasoner\",\n                messages: request.messages,\n                temperature: request.temperature || 0.3,\n                max_tokens: request.max_tokens || 8000,\n                // DeepSeek支持response_format参数\n                ...request.response_format && {\n                    response_format: request.response_format\n                }\n            };\n            // 根据提供商使用不同的端点\n            const endpoint = this.provider === \"minimax\" ? `${this.baseUrl}/text/chatcompletion_v2` : `${this.baseUrl}/chat/completions`;\n            // 创建带超时的fetch请求\n            const controller = new AbortController();\n            // 根据模型设置不同的超时时间（智能问卷增加了分析复杂度）\n            const timeout = this.provider === \"deepseek\" ? 480000 : 180000; // DeepSeek 8分钟，MiniMax 3分钟\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            try {\n                const response = await fetch(endpoint, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.apiKey}`\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`LLM API错误 (${response.status}): ${errorText}`);\n                }\n                const result = await response.json();\n                console.log(\"✅ LLM API调用成功\");\n                return result;\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error.name === \"AbortError\") {\n                    throw new Error(\"LLM API调用超时（2分钟）\");\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"❌ LLM API调用失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 解析JSON响应（带容错处理）\n   */ static parseJSONResponse(content) {\n        try {\n            // 尝试直接解析\n            return JSON.parse(content);\n        } catch (e1) {\n            try {\n                // 尝试提取JSON代码块\n                const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n                if (jsonMatch && jsonMatch[1]) {\n                    return JSON.parse(jsonMatch[1]);\n                }\n            } catch (e2) {\n                // 尝试修复常见JSON错误\n                try {\n                    let fixed = content;\n                    // 移除尾随逗号\n                    fixed = fixed.replace(/,\\s*}/g, \"}\");\n                    fixed = fixed.replace(/,\\s*]/g, \"]\");\n                    // 修复未引用的键\n                    fixed = fixed.replace(/(\\w+):/g, '\"$1\":');\n                    return JSON.parse(fixed);\n                } catch (e3) {\n                    console.error(\"JSON解析失败:\", content);\n                    throw new Error(\"无法解析LLM返回的JSON格式\");\n                }\n            }\n        }\n        // 如果所有解析方法都失败，返回null\n        return null;\n    }\n}\n/**\n * 创建LLM客户端实例\n */ function createLLMClient(provider = \"deepseek\") {\n    return new LLMClient(provider);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/llm-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&page=%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassessment%2Fprofessional-analyze%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();