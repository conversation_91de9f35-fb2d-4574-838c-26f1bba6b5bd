/**
 * OCTI智能评估系统 - 提示词加载器
 * 
 * 加载和处理organization_tutor_prompt.json配置
 */

import fs from 'fs';
import path from 'path';

export interface OCTIPromptConfig {
  version: string;
  agent_type: string;
  prompt_template: {
    system_message: string;
    task_description: string;
    framework: {
      dimensions: {
        SF: any;
        IT: any;
        MV: any;
        AD: any;
      };
    };
  };
  configuration_parameters: any;
  version_differences: {
    standard: any;
    professional: any;
  };
  output_formats: {
    standard: any[];
    professional: any[];
  };
  analysis_instructions: {
    standard: any;
    professional: any;
  };
  api_integration: {
    model_config: {
      standard: {
        primary_model: string;
        model_parameters: {
          temperature: number;
          max_tokens: number;
          top_p: number;
        };
        questionnaire_processing: {
          hybrid_mode: boolean;
          preset_weight: number;
          intelligent_weight: number;
        };
      };
      professional: {
        primary_model: string;
        secondary_model: string;
        model_parameters: {
          temperature: number;
          max_tokens: number;
          top_p: number;
        };
        dual_model_strategy: string;
        questionnaire_processing: {
          hybrid_mode: boolean;
          preset_weight: number;
          intelligent_weight: number;
        };
        collaboration_config: {
          synthesis_strategy: string;
          confidence_threshold: number;
          disagreement_resolution: string;
        };
      };
    };
    error_handling: {
      retry_strategy: string;
      max_retries: number;
      timeout: number;
      fallback_behavior: string;
    };
  };
  quality_control: any;
}

/**
 * 提示词加载器类
 */
export class PromptLoader {
  private static instance: PromptLoader;
  private config: OCTIPromptConfig | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): PromptLoader {
    if (!PromptLoader.instance) {
      PromptLoader.instance = new PromptLoader();
    }
    return PromptLoader.instance;
  }

  /**
   * 加载OCTI提示词配置
   */
  async loadConfig(version: 'standard' | 'professional' = 'standard'): Promise<OCTIPromptConfig> {
    if (this.config) {
      return this.config;
    }

    try {
      // 根据版本选择配置文件
      const configFileName = version === 'standard'
        ? 'organization_tutor_prompt_compact.json'  // 标准版使用精简配置
        : 'organization_tutor_prompt.json';         // 专业版使用完整配置

      const configPath = path.join(process.cwd(), 'configs', configFileName);
      const configContent = fs.readFileSync(configPath, 'utf-8');
      this.config = JSON.parse(configContent);

      console.log(`✅ OCTI提示词配置加载成功 (${version}版，长度: ${configContent.length}字符)`);
      return this.config;
    } catch (error) {
      console.error('❌ 加载OCTI提示词配置失败:', error);
      throw new Error('无法加载OCTI评估配置');
    }
  }

  /**
   * 获取系统消息
   */
  getSystemMessage(): string {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.prompt_template.system_message;
  }

  /**
   * 获取任务描述
   */
  getTaskDescription(): string {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.prompt_template.task_description;
  }

  /**
   * 获取评估框架
   */
  getFramework() {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.prompt_template.framework;
  }

  /**
   * 获取版本配置
   */
  getVersionConfig(version: 'standard' | 'professional') {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.version_differences[version];
  }

  /**
   * 获取输出格式
   */
  getOutputFormat(version: 'standard' | 'professional') {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.output_formats[version];
  }

  /**
   * 获取分析指令
   */
  getAnalysisInstructions(version: 'standard' | 'professional') {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.analysis_instructions[version];
  }

  /**
   * 获取API集成配置
   */
  getApiConfig(version: 'standard' | 'professional') {
    if (!this.config) {
      throw new Error('配置未加载');
    }
    return this.config.api_integration.model_config[version];
  }

  /**
   * 构建完整的分析提示词
   */
  buildAnalysisPrompt(
    organizationProfile: any,
    questionnaireResults: any,
    version: 'standard' | 'professional' = 'standard'
  ): string {
    if (!this.config) {
      throw new Error('配置未加载');
    }

    const versionConfig = this.getVersionConfig(version);
    const outputFormat = this.getOutputFormat(version);
    const analysisInstructions = this.getAnalysisInstructions(version);
    const framework = this.getFramework();

    const prompt = `
# OCTI公益机构能力评估分析

## 评估版本
${versionConfig.name}

## 组织画像数据
${JSON.stringify(organizationProfile, null, 2)}

## 问卷评估结果
${JSON.stringify(questionnaireResults, null, 2)}

## 评估框架
${JSON.stringify(framework, null, 2)}

## 分析要求
版本: ${version}
分析深度: ${analysisInstructions.depth_level}
语言风格: ${analysisInstructions.language_style}
建议风格: ${analysisInstructions.recommendation_style}

重点关注领域:
${analysisInstructions.focus_areas.map((area: string) => `- ${area}`).join('\n')}

## 输出格式要求
请严格按照以下结构生成分析报告，确保包含所有必要的章节：

${outputFormat.map((section: any, index: number) => `
### ${index + 1}. ${section.section}
内容要求: ${section.content}
分析深度: ${section.analysis_depth}
数据需求: ${section.data_requirements?.join(', ') || '基础数据'}
`).join('\n')}

## 特别要求
1. 必须基于提供的组织画像和问卷数据进行分析
2. 分析结果必须符合公益机构特色
3. 建议必须具体可操作
4. 使用OCTI四维八极评估框架
5. 输出格式必须为结构化的JSON格式

请开始分析并生成专业的评估报告。
`;

    return prompt;
  }

  /**
   * 获取模型参数配置
   */
  getModelParameters(version: 'standard' | 'professional') {
    const apiConfig = this.getApiConfig(version);
    return apiConfig.model_parameters;
  }

  /**
   * 获取主要模型名称
   */
  getPrimaryModel(version: 'standard' | 'professional'): string {
    const apiConfig = this.getApiConfig(version);
    return apiConfig.primary_model;
  }
}
