/**
 * 单题测试页面
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { QuestionRenderer, Question, QuestionResponse } from '@/components/questionnaire/question-renderer';

export default function TestSingleQuestionPage() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Map<string, QuestionResponse>>(new Map());

  // 测试题目
  const testQuestions: Question[] = [
    {
      id: 'test_1',
      type: 'SINGLE_CHOICE',
      source: 'PRESET',
      category: '测试',
      subCategory: '单选题',
      title: '这是一道单选题测试',
      description: '请选择一个选项',
      options: [
        { text: '选项A', value: 'A' },
        { text: '选项B', value: 'B' },
        { text: '选项C', value: 'C' },
      ],
      required: true,
      order: 1,
    },
    {
      id: 'test_2',
      type: 'SCALE',
      source: 'PRESET',
      category: '测试',
      subCategory: '量表题',
      title: '这是一道量表题测试',
      description: '请在1-5之间选择',
      options: {
        min: 1,
        max: 5,
        labels: ['很差', '较差', '一般', '较好', '很好'],
      },
      required: true,
      order: 2,
    },
    {
      id: 'test_3',
      type: 'MULTIPLE_CHOICE',
      source: 'PRESET',
      category: '测试',
      subCategory: '多选题',
      title: '这是一道多选题测试',
      description: '请选择多个选项',
      options: [
        { text: '选项1', value: '1' },
        { text: '选项2', value: '2' },
        { text: '选项3', value: '3' },
        { text: '选项4', value: '4' },
      ],
      required: true,
      order: 3,
    },
  ];

  const currentQuestion = testQuestions[currentQuestionIndex];
  const currentResponse = responses.get(currentQuestion?.id);

  const handleAnswerChange = (response: QuestionResponse) => {
    console.log('📝 收到答案变化:', response);
    setResponses(prev => new Map(prev.set(response.questionId, response)));
  };

  const goToNext = () => {
    console.log('➡️ 下一题');
    if (currentQuestionIndex < testQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    console.log('⬅️ 上一题');
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const isAnswered = () => {
    return responses.has(currentQuestion?.id);
  };

  const canGoNext = () => {
    if (!currentQuestion) return false;
    if (currentQuestion.required && !isAnswered()) return false;
    return currentQuestionIndex < testQuestions.length - 1;
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>单题功能测试</CardTitle>
          <div className="text-sm text-muted-foreground">
            题目 {currentQuestionIndex + 1} / {testQuestions.length}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {currentQuestion && (
            <QuestionRenderer
              question={currentQuestion}
              response={currentResponse}
              onAnswerChange={handleAnswerChange}
              showProgress={true}
              currentIndex={currentQuestionIndex}
              totalQuestions={testQuestions.length}
            />
          )}

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={goToPrevious}
              disabled={currentQuestionIndex === 0}
            >
              上一题
            </Button>

            <div className="flex space-x-2">
              <Button
                onClick={goToNext}
                disabled={!canGoNext()}
              >
                下一题
              </Button>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded">
            <h3 className="font-semibold mb-2">调试信息:</h3>
            <div className="text-sm space-y-1">
              <div>当前题目ID: {currentQuestion?.id}</div>
              <div>题目类型: {currentQuestion?.type}</div>
              <div>是否必答: {currentQuestion?.required ? '是' : '否'}</div>
              <div>是否已回答: {isAnswered() ? '是' : '否'}</div>
              <div>可以下一题: {canGoNext() ? '是' : '否'}</div>
              <div>当前答案: {JSON.stringify(currentResponse?.answer || null)}</div>
              <div>总回答数: {responses.size}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
