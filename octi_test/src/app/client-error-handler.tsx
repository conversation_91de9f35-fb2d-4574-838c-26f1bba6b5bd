/**
 * OCTI智能评估系统 - 客户端错误处理组件
 * 
 * 在客户端设置全局错误处理器，忽略已知的外部错误
 */

'use client';

import { useEffect } from 'react';

export function ClientErrorHandler() {
  useEffect(() => {
    // 处理未捕获的Promise拒绝
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.warn('Unhandled promise rejection:', event.reason);
      
      // 忽略已知的外部错误
      const reason = event.reason;
      const reasonString = String(reason);
      
      if (
        reasonString.includes('MetaMask') ||
        reasonString.includes('chrome-extension') ||
        reasonString.includes('Failed to connect to MetaMask') ||
        (reason?.stack && reason.stack.includes('chrome-extension'))
      ) {
        event.preventDefault();
        return;
      }
    };

    // 处理全局JavaScript错误
    const handleGlobalError = (event: ErrorEvent) => {
      console.warn('Global error:', event.error);
      
      // 忽略已知的外部错误
      if (
        event.filename?.includes('chrome-extension') ||
        event.error?.stack?.includes('chrome-extension') ||
        event.message?.includes('MetaMask') ||
        event.message?.includes('Failed to connect to MetaMask')
      ) {
        return;
      }
    };

    // 添加事件监听器
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    // 清理函数
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleGlobalError);
    };
  }, []);

  return null;
}
