/**
 * OCTI智能评估系统 - 页面头部组件
 * 
 * 提供导航、用户菜单、系统状态等功能
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';

// ============================================================================
// 类型定义
// ============================================================================

interface HeaderProps {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  onLogin?: () => void;
  onLogout?: () => void;
}

// ============================================================================
// 导航菜单配置
// ============================================================================

const navigationItems = [
  {
    title: '评估管理',
    href: '/assessments',
    description: '创建和管理组织能力评估',
    items: [
      { title: '我的评估', href: '/assessments', description: '查看所有评估项目' },
      { title: '创建评估', href: '/assessments/create', description: '开始新的评估' },
      { title: '评估模板', href: '/assessments/templates', description: '管理评估模板' },
    ],
  },
  {
    title: '组织管理',
    href: '/organizations',
    description: '管理组织信息和画像',
    items: [
      { title: '组织列表', href: '/organizations', description: '查看所有组织' },
      { title: '添加组织', href: '/organizations/create', description: '添加新组织' },
      { title: '组织画像', href: '/organizations/profiles', description: '管理组织画像' },
    ],
  },
  {
    title: '报告中心',
    href: '/reports',
    description: '查看和下载评估报告',
    items: [
      { title: '评估报告', href: '/reports', description: '查看评估报告' },
      { title: '数据分析', href: '/reports/analytics', description: '深度数据分析' },
      { title: '对比分析', href: '/reports/comparison', description: '组织对比分析' },
    ],
  },
];

// ============================================================================
// 头部组件
// ============================================================================

export function Header({ user, onLogin, onLogout }: HeaderProps) {
  const pathname = usePathname();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Logo和品牌 */}
        <div className="mr-8 flex items-center space-x-3">
          <Link href="/" className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-green-600">
              <span className="text-sm font-bold text-white">O</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-bold text-foreground">OCTI</h1>
              <p className="text-xs text-muted-foreground">智能评估系统</p>
            </div>
          </Link>
          <Badge variant="secondary" className="hidden md:inline-flex">
            v4.0
          </Badge>
        </div>

        {/* 主导航菜单 */}
        <NavigationMenu className="hidden lg:flex">
          <NavigationMenuList>
            {navigationItems.map((item) => (
              <NavigationMenuItem key={item.href}>
                <NavigationMenuTrigger className="h-9">
                  {item.title}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    {item.items.map((subItem) => (
                      <li key={subItem.href}>
                        <NavigationMenuLink asChild>
                          <Link
                            href={subItem.href}
                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div className="text-sm font-medium leading-none">
                              {subItem.title}
                            </div>
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {subItem.description}
                            </p>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* 右侧操作区域 */}
        <div className="ml-auto flex items-center space-x-4">
          {/* 系统状态指示器 */}
          <div className="hidden md:flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span className="text-xs text-muted-foreground">系统正常</span>
            </div>
          </div>

          {/* 用户菜单或登录按钮 */}
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-green-600">
                    <span className="text-xs font-medium text-white">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile">个人资料</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings">系统设置</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/help">帮助中心</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onLogout}>
                  退出登录
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={onLogin}>
                登录
              </Button>
              <Button size="sm">开始评估</Button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}

// ============================================================================
// 移动端导航组件
// ============================================================================

export function MobileNav() {
  const pathname = usePathname();

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 border-t bg-background lg:hidden">
      <div className="grid grid-cols-4 gap-1 p-2">
        {[
          { title: '首页', href: '/', icon: '🏠' },
          { title: '评估', href: '/assessments', icon: '📊' },
          { title: '组织', href: '/organizations', icon: '🏢' },
          { title: '报告', href: '/reports', icon: '📋' },
        ].map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={`flex flex-col items-center justify-center space-y-1 rounded-md p-2 text-xs transition-colors ${
              pathname === item.href
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
            }`}
          >
            <span className="text-lg">{item.icon}</span>
            <span>{item.title}</span>
          </Link>
        ))}
      </div>
    </div>
  );
}
