# OCTI智能评估系统 - LLM模型实现文档 v4.0

## 1. 智能体配置架构 v4.0

系统采用**配置驱动的智能体架构**，通过JSON配置文件实现智能体的动态配置和热更新。架构支持混合问卷模式（32道预设+28道智能生成）、双模型协作分析（MiniMax + DeepSeek）和配置版本控制。

### 1.1 配置驱动架构概览

```
┌─────────────────────────────────────────────────────────┐
│           OCTI智能体配置系统架构 v4.0                     │
├─────────────────────────────────────────────────────────┤
│ 配置层                                                   │
│ ├── question_design_prompt.json (问卷设计师配置)         │
│ ├── organization_tutor_prompt.json (组织评估导师配置)    │
│ ├── preset_questions.json (预设题目库)                  │
│ └── version-control.json (版本管理)                     │
├─────────────────────────────────────────────────────────┤
│ 智能体层                                                 │
│ ├── 配置驱动问卷设计师 (混合问卷生成)                    │
│ ├── 配置驱动组织评估导师 (双模型协作分析)                │
│ ├── 配置引擎 (热更新、版本控制)                         │
│ └── 混合问卷融合器 (32预设+28智能生成)                  │
├─────────────────────────────────────────────────────────┤
│ LLM服务层                                               │
│ ├── MiniMax API (主分析模型)                            │
│ ├── DeepSeek API (推理增强模型)                         │
│ └── 双模型协作框架                                       │
└─────────────────────────────────────────────────────────┘
```

```mermaid
graph TD
    subgraph "智能体"
        A1[问卷设计师]
        A2[组织评估导师]
    end

    subgraph "提示词工程"
        B1[问卷提示词构建器]
        B2[分析提示词构建器]
    end

    subgraph "LLM API客户端 (可扩展)"
        C[LLMApiClient]
    end

    subgraph "外部LLM服务"
        D1[MiniMax API]
        D2[DeepSeek API]
    end

    A1 --> B1;
    A2 --> B2;
    B1 & B2 --> C;
    C -- "根据模型选择调用" --> D1;
    C -- "根据模型选择调用" --> D2;
```

### 1.2 配置文件结构

#### 问卷设计师配置 (question_design_prompt.json)
```json
{
  "version": "4.0.0",
  "agent_type": "QUESTION_DESIGNER",
  "system_message": "你是OCTI智能问卷设计师...",
  "task_description": "基于组织画像生成28道个性化问题...",
  "framework": {
    "SF": { "name": "S/F维度：战略聚焦度", "sub_dimensions": {...} },
    "IT": { "name": "I/T维度：团队协同度", "sub_dimensions": {...} },
    "MV": { "name": "M/V维度：价值导向度", "sub_dimensions": {...} },
    "AD": { "name": "A/D维度：能力发展度", "sub_dimensions": {...} }
  },
  "configuration_parameters": {
    "version": { "options": ["标准版", "专业版"], "default": "标准版" },
    "analysis_mode": { "options": ["单一评估", "双重评估"], "default": "单一评估" }
  }
}
```

#### 组织评估导师配置 (organization_tutor_prompt.json)
```json
{
  "version": "4.0.0",
  "agent_type": "ORGANIZATION_TUTOR",
  "system_message": "你是OCTI组织评估导师...",
  "task_description": "基于混合问卷结果进行双模型协作分析...",
  "version_differences": {
    "standard_version": {
      "model": "Minimax单一模型",
      "analysis_depth": "基础分析深度",
      "output_sections": 9
    },
    "professional_version": {
      "model": "Minimax + Deepseek-reasoner双重评估",
      "analysis_depth": "深度分析",
      "output_sections": 14
    }
  }
}
```

## 2. 核心组件实现

### 2.1 配置引擎 (`ConfigEngine`)

**【新增】** 配置驱动架构的核心组件，负责配置文件的加载、验证、版本控制和热更新。

```typescript
// src/services/llm/config-engine.ts
export class ConfigEngine {
  private configCache: Map<string, any> = new Map();
  private validators: Map<string, Function> = new Map();
  private subscribers: Set<Function> = new Set();
  private currentVersion: string = '4.0.0';
  
  constructor() {
    this.initializeValidators();
  }
  
  /**
   * 加载和验证配置
   */
  async loadConfig(configType: string, version: string = 'latest'): Promise<any> {
    const configKey = `${configType}:${version}`;
    
    if (this.configCache.has(configKey)) {
      return this.configCache.get(configKey);
    }
    
    const config = await this.fetchConfig(configType, version);
    const validatedConfig = await this.validateConfig(config, configType);
    
    this.configCache.set(configKey, validatedConfig);
    return validatedConfig;
  }
  
  /**
   * 热更新配置
   */
  async updateConfig(configType: string, newConfig: any): Promise<void> {
    const validatedConfig = await this.validateConfig(newConfig, configType);
    const version = this.generateVersion();
    
    await this.saveConfig(configType, validatedConfig, version);
    this.configCache.set(`${configType}:latest`, validatedConfig);
    
    // 通知订阅者
    this.notifySubscribers(configType, validatedConfig);
  }
  
  /**
   * 配置验证
   */
  private async validateConfig(config: any, configType: string): Promise<any> {
    const validator = this.validators.get(configType);
    if (!validator) {
      throw new Error(`未找到 ${configType} 的验证器`);
    }
    
    const isValid = await validator(config);
    if (!isValid) {
      throw new Error(`配置验证失败: ${configType}`);
    }
    
    return config;
  }
  
  /**
   * 初始化验证器
   */
  private initializeValidators(): void {
    this.validators.set('question_design_prompt', this.validateQuestionDesignConfig);
    this.validators.set('organization_tutor_prompt', this.validateTutorConfig);
    this.validators.set('preset_questions', this.validatePresetQuestionsConfig);
  }
  
  private validateQuestionDesignConfig(config: any): boolean {
    return config.version && config.agent_type === 'QUESTION_DESIGNER' && config.framework;
  }
  
  private validateTutorConfig(config: any): boolean {
    return config.version && config.agent_type === 'ORGANIZATION_TUTOR' && config.version_differences;
  }
  
  private validatePresetQuestionsConfig(config: any): boolean {
    return config.questions && Array.isArray(config.questions) && config.questions.length === 32;
  }
}
```

### 2.2 JSON稳定性处理器 (`JSONProcessor`)

**【新增】** 专门处理LLM输出的JSON格式稳定性问题，确保系统的可靠性。

```typescript
// src/services/llm/json-processor.ts
export class JSONProcessor {
  /**
   * 多重JSON解析策略
   */
  static parseWithFallback(content: string): any {
    // 策略1: 直接解析
    try {
      return JSON.parse(content);
    } catch (e1) {
      // 策略2: 提取JSON块
      try {
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[1]);
        }
      } catch (e2) {
        // 策略3: 智能修复
        try {
          const fixed = this.fixCommonJSONErrors(content);
          return JSON.parse(fixed);
        } catch (e3) {
          // 策略4: 结构化提取
          return this.extractStructuredData(content);
        }
      }
    }
  }
  
  /**
   * 修复常见JSON错误
   */
  static fixCommonJSONErrors(content: string): string {
    let fixed = content;
    // 修复尾随逗号
    fixed = fixed.replace(/,\s*}/g, '}');
    fixed = fixed.replace(/,\s*]/g, ']');
    // 修复未转义的引号
    fixed = fixed.replace(/"([^"]*?)"([^,}\]]*?)"([^"]*?)"/g, '"$1\\"$2\\"$3"');
    // 修复缺失的引号
    fixed = fixed.replace(/(\w+):/g, '"$1":');
    return fixed;
  }
  
  /**
   * 结构化数据提取（最后的回退策略）
   */
  static extractStructuredData(content: string): any {
    // 实现基于正则表达式的结构化数据提取
    const result = {};
    // 提取关键字段的逻辑
    return result;
  }
}
```

### 2.3 混合问卷融合器 (`HybridQuestionnaireFuser`)

**【新增】** 实现32道预设题目与28道智能生成题目的融合，确保问卷的标准性与个性化并重。

```typescript
// src/services/llm/hybrid-questionnaire-fuser.ts
export class HybridQuestionnaireFuser {
  private configEngine: ConfigEngine;
  private presetQuestions: any[] = [];
  
  constructor() {
    this.configEngine = new ConfigEngine();
    this.loadPresetQuestions();
  }
  
  /**
   * 生成混合问卷（32道预设 + 28道智能生成）
   */
  async generateHybridQuestionnaire(
    organizationProfile: any,
    version: 'standard' | 'professional'
  ): Promise<any> {
    // 1. 加载32道预设题目
    const presetQuestions = await this.getPresetQuestions();
    
    // 2. 基于组织画像生成28道智能题目
    const intelligentQuestions = await this.generateIntelligentQuestions(
      organizationProfile, 
      version
    );
    
    // 3. 融合问卷
    const hybridQuestionnaire = this.fuseQuestionnaires(
      presetQuestions, 
      intelligentQuestions
    );
    
    // 4. 验证问卷质量
    this.validateQuestionnaireQuality(hybridQuestionnaire);
    
    return hybridQuestionnaire;
  }
  
  /**
   * 获取32道预设题目（四维度各8题）
   */
  private async getPresetQuestions(): Promise<any[]> {
    const config = await this.configEngine.loadConfig('preset_questions');
    return config.questions.filter(q => q.is_active);
  }
  
  /**
   * 生成28道智能题目（四维度各7题）
   */
  private async generateIntelligentQuestions(
    profile: any, 
    version: string
  ): Promise<any[]> {
    const questionDesigner = new ConfigDrivenQuestionDesigner();
    return await questionDesigner.generatePersonalizedQuestions(profile, version, 28);
  }
  
  /**
   * 融合预设题目和智能生成题目
   */
  private fuseQuestionnaires(preset: any[], intelligent: any[]): any {
    const dimensions = ['SF', 'IT', 'MV', 'AD'];
    const fusedQuestionnaire = {
      total_questions: 60,
      preset_count: 32,
      intelligent_count: 28,
      dimensions: {}
    };
    
    dimensions.forEach(dim => {
      const presetForDim = preset.filter(q => q.dimension === dim);
      const intelligentForDim = intelligent.filter(q => q.dimension === dim);
      
      fusedQuestionnaire.dimensions[dim] = {
        preset_questions: presetForDim,
        intelligent_questions: intelligentForDim,
        total_questions: presetForDim.length + intelligentForDim.length
      };
    });
    
    return fusedQuestionnaire;
  }
  
  /**
   * 验证问卷质量
   */
  private validateQuestionnaireQuality(questionnaire: any): void {
    // 验证题目数量
    if (questionnaire.total_questions !== 60) {
      throw new Error('混合问卷题目数量不正确');
    }
    
    // 验证维度平衡
    Object.keys(questionnaire.dimensions).forEach(dim => {
      const dimQuestions = questionnaire.dimensions[dim];
      if (dimQuestions.total_questions !== 15) {
        throw new Error(`${dim}维度题目数量不正确`);
      }
    });
    
    // 验证重复性
    this.checkQuestionDuplication(questionnaire);
  }
  
  private checkQuestionDuplication(questionnaire: any): void {
    const allQuestions = [];
    Object.values(questionnaire.dimensions).forEach((dim: any) => {
      allQuestions.push(...dim.preset_questions, ...dim.intelligent_questions);
    });
    
    const questionTexts = allQuestions.map(q => q.text.toLowerCase());
    const uniqueTexts = new Set(questionTexts);
    
    if (questionTexts.length !== uniqueTexts.size) {
      console.warn('检测到重复题目，建议重新生成智能题目');
    }
  }
}
```

### 2.4 双模型协作框架 (`DualModelCollaborationFramework`)

**【新增】** 实现MiniMax和DeepSeek模型的协作分析，提供专业版的深度评估能力。

```typescript
// src/services/llm/dual-model-collaboration.ts
export class DualModelCollaborationFramework {
  private llmClient: LLMApiClient;
  private configEngine: ConfigEngine;
  
  constructor() {
    this.llmClient = new LLMApiClient();
    this.configEngine = new ConfigEngine();
  }
  
  /**
   * 双模型协作分析
   */
  async collaborativeAnalysis(
    questionnaireData: any,
    organizationProfile: any
  ): Promise<any> {
    // 1. MiniMax主分析
    const primaryAnalysis = await this.primaryAnalysis(questionnaireData, organizationProfile);
    
    // 2. DeepSeek推理增强
    const enhancedAnalysis = await this.enhancedReasoning(primaryAnalysis, questionnaireData);
    
    // 3. 结果融合
    const fusedAnalysis = await this.fuseAnalysisResults(primaryAnalysis, enhancedAnalysis);
    
    // 4. 质量验证
    this.validateAnalysisQuality(fusedAnalysis);
    
    return fusedAnalysis;
  }
  
  /**
   * MiniMax主分析
   */
  private async primaryAnalysis(data: any, profile: any): Promise<any> {
    const config = await this.configEngine.loadConfig('organization_tutor_prompt');
    const prompt = this.buildPrimaryAnalysisPrompt(data, profile, config);
    
    const response = await this.llmClient.call('minimax', prompt);
    return this.parseAnalysisResult(response, 'primary');
  }
  
  /**
   * DeepSeek推理增强
   */
  private async enhancedReasoning(primaryAnalysis: any, data: any): Promise<any> {
    const prompt = this.buildEnhancedReasoningPrompt(primaryAnalysis, data);
    
    const response = await this.llmClient.call('deepseek', prompt);
    return this.parseAnalysisResult(response, 'enhanced');
  }
  
  /**
   * 分析结果融合
   */
  private async fuseAnalysisResults(primary: any, enhanced: any): Promise<any> {
    return {
      primary_analysis: primary,
      enhanced_analysis: enhanced,
      fused_insights: await this.generateFusedInsights(primary, enhanced),
      confidence_scores: this.calculateConfidenceScores(primary, enhanced),
      model_agreement: this.assessModelAgreement(primary, enhanced)
    };
  }
  
  /**
   * 生成融合洞察
   */
  private async generateFusedInsights(primary: any, enhanced: any): Promise<any> {
    const fusionPrompt = this.buildFusionPrompt(primary, enhanced);
    const response = await this.llmClient.call('deepseek', fusionPrompt);
    return this.parseAnalysisResult(response, 'fused');
  }
  
  /**
   * 计算置信度分数
   */
  private calculateConfidenceScores(primary: any, enhanced: any): any {
    return {
      primary_confidence: this.assessAnalysisConfidence(primary),
      enhanced_confidence: this.assessAnalysisConfidence(enhanced),
      consensus_confidence: this.assessConsensusConfidence(primary, enhanced)
    };
  }
  
  /**
   * 评估模型一致性
   */
  private assessModelAgreement(primary: any, enhanced: any): any {
    return {
      dimension_agreement: this.compareDimensionScores(primary, enhanced),
      insight_alignment: this.compareInsights(primary, enhanced),
      recommendation_consistency: this.compareRecommendations(primary, enhanced)
    };
  }
}
```

### 2.5 公益机构组织画像处理器 (`NonprofitProfileProcessor`)

**【优化】** 专门处理公益机构组织画像的智能分析和上下文构建，支持配置驱动的画像处理。

```typescript
// src/services/llm/nonprofit-profile-processor.ts
export interface NonprofitOrganizationProfile {
  organizationType: '基金会' | '公益组织' | '社会团体' | '民办非企业' | '国际NGO' | '政府机构';
  serviceArea: string[];
  organizationScale: '微型' | '小型' | '中型' | '大型' | '超大型';
  developmentStage: '初创期' | '成长期' | '成熟期' | '转型期' | '扩张期';
  operatingModel: '直接服务' | '资助型' | '倡导型' | '研究型' | '平台型' | '混合型';
  impactPositioning: '本地影响' | '区域影响' | '全国影响' | '国际影响';
  organizationalCulture: '使命驱动' | '创新导向' | '协作共享' | '专业严谨' | '草根活力';
  missionVision: {
    mission: string;
    vision: string;
    values: string[];
    theory: string;
  };
  // ... 其他字段
}

export class NonprofitProfileProcessor {
  /**
   * 构建公益机构上下文提示词
   */
  static buildContextualPrompt(profile: NonprofitOrganizationProfile): string {
    const serviceAreaContext = this.getServiceAreaContext(profile.serviceArea);
    const stageContext = this.getDevelopmentStageContext(profile.developmentStage);
    const operatingContext = this.getOperatingModelContext(profile.operatingModel);
    
    return `
公益机构背景：
- 组织类型：${profile.organizationType}
- 服务领域：${profile.serviceArea.join('、')} (${serviceAreaContext})
- 发展阶段：${profile.developmentStage} (${stageContext})
- 运营模式：${profile.operatingModel} (${operatingContext})
- 影响定位：${profile.impactPositioning}
- 组织文化：${profile.organizationalCulture}
`;
  }
  
  private static getServiceAreaContext(areas: string[]): string {
    const contextMap = {
      '教育': '关注教育公平、学习成果、师资发展、教育创新',
      '环保': '关注环境保护、可持续发展、生态修复、绿色倡导',
      '扶贫': '关注贫困减缓、能力建设、可持续脱贫、社区发展',
      '医疗': '关注健康促进、医疗可及性、疾病预防、健康教育'
    };
    return areas.map(area => contextMap[area] || '').filter(Boolean).join('；');
  }
}
```

### 2.6 配置驱动智能问卷设计器 (`ConfigDrivenQuestionDesigner`)

**【新增】** 基于JSON配置文件驱动的智能问卷设计器，支持版本控制和热更新。

```typescript
// src/services/llm/config-driven-question-designer.ts
export class ConfigDrivenQuestionDesigner {
  private configEngine: ConfigEngine;
  private llmClient: LLMApiClient;
  
  constructor() {
    this.configEngine = new ConfigEngine();
    this.llmClient = new LLMApiClient();
  }
  
  /**
   * 生成个性化问卷题目
   */
  async generatePersonalizedQuestions(
    organizationProfile: any,
    version: 'standard' | 'professional',
    questionCount: number = 28
  ): Promise<any[]> {
    // 1. 加载配置
    const config = await this.configEngine.loadConfig('question_design_prompt');
    
    // 2. 构建提示词
    const prompt = this.buildQuestionGenerationPrompt(
      organizationProfile, 
      version, 
      questionCount, 
      config
    );
    
    // 3. 调用LLM生成题目
    const response = await this.llmClient.call('deepseek', prompt);
    
    // 4. 解析和验证题目
    const questions = this.parseAndValidateQuestions(response, questionCount);
    
    // 5. 质量控制
    return this.applyQualityControl(questions, organizationProfile);
  }
  
  /**
   * 构建问卷生成提示词
   */
  private buildQuestionGenerationPrompt(
    profile: any, 
    version: string, 
    count: number, 
    config: any
  ): string {
    const basePrompt = config.templates[version];
    const contextualPrompt = this.buildContextualPrompt(profile, config);
    
    return `${basePrompt}\n\n${contextualPrompt}\n\n请生成${count}道题目，确保四个维度平衡分布。`;
  }
  
  /**
   * 构建上下文化提示词
   */
  private buildContextualPrompt(profile: any, config: any): string {
    const contexts = [];
    
    // 服务领域上下文
    if (profile.service_area) {
      contexts.push(config.contexts.service_area[profile.service_area] || '');
    }
    
    // 发展阶段上下文
    if (profile.development_stage) {
      contexts.push(config.contexts.development_stage[profile.development_stage] || '');
    }
    
    // 组织规模上下文
    if (profile.organization_scale) {
      contexts.push(config.contexts.organization_scale[profile.organization_scale] || '');
    }
    
    return contexts.join('\n');
  }
  
  /**
   * 解析和验证题目
   */
  private parseAndValidateQuestions(response: string, expectedCount: number): any[] {
    try {
      const questions = JSON.parse(response);
      
      if (!Array.isArray(questions) || questions.length !== expectedCount) {
        throw new Error(`题目数量不正确，期望${expectedCount}道，实际${questions.length}道`);
      }
      
      // 验证题目结构
      questions.forEach((q, index) => {
        if (!q.text || !q.dimension || !q.type) {
          throw new Error(`第${index + 1}道题目结构不完整`);
        }
      });
      
      return questions;
    } catch (error) {
      throw new Error(`题目解析失败: ${error.message}`);
    }
  }
  
  /**
   * 质量控制
   */
  private applyQualityControl(questions: any[], profile: any): any[] {
    // 1. 维度平衡检查
    this.checkDimensionBalance(questions);
    
    // 2. 重复性检查
    this.checkQuestionDuplication(questions);
    
    // 3. 相关性检查
    this.checkRelevanceToProfile(questions, profile);
    
    return questions;
  }
  
  private checkDimensionBalance(questions: any[]): void {
    const dimensionCounts = questions.reduce((acc, q) => {
      acc[q.dimension] = (acc[q.dimension] || 0) + 1;
      return acc;
    }, {});
    
    const expectedPerDimension = questions.length / 4;
    Object.entries(dimensionCounts).forEach(([dim, count]) => {
      if (Math.abs(count - expectedPerDimension) > 1) {
        console.warn(`维度${dim}题目数量不平衡: ${count}`);
      }
    });
  }
}
```

### 2.7 基础智能体类 (`BaseAgent`)

**【新增】** 所有智能体的基础类，提供配置监听和热更新能力。

```typescript
// src/services/llm/base-agent.ts
export abstract class BaseAgent {
  protected configEngine: ConfigEngine;
  protected config: any;
  protected configType: string;
  
  constructor(configType: string) {
    this.configType = configType;
    this.configEngine = new ConfigEngine();
    this.initializeConfig();
    this.watchConfigChanges();
  }
  
  /**
   * 初始化配置
   */
  private async initializeConfig(): Promise<void> {
    try {
      this.config = await this.configEngine.loadConfig(this.configType);
      await this.onConfigLoaded(this.config);
    } catch (error) {
      console.error(`配置加载失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 监听配置变化
   */
  private watchConfigChanges(): void {
    this.configEngine.watchConfig(this.configType, async (newConfig) => {
      const oldConfig = this.config;
      this.config = newConfig;
      await this.onConfigUpdated(newConfig, oldConfig);
    });
  }
  
  /**
   * 配置加载完成回调
   */
  protected abstract onConfigLoaded(config: any): Promise<void>;
  
  /**
   * 配置更新回调
   */
  protected abstract onConfigUpdated(newConfig: any, oldConfig: any): Promise<void>;
  
  /**
   * 获取当前配置
   */
  protected getConfig(): any {
    return this.config;
  }
  
  /**
   * 验证配置有效性
   */
  protected validateConfig(config: any): boolean {
    // 子类可以重写此方法进行特定验证
    return config !== null && config !== undefined;
  }
}
```

### 2.8 多轮对话管理器 (`MultiRoundDialogueManager`)

**【优化】** 用于收集组织画像数据的多轮对话管理，现在支持配置驱动。

```typescript
// src/services/llm/dialogue-manager.ts
export class MultiRoundDialogueManager {
  private dialogueState: any = {};
  private currentRound: number = 0;
  private maxRounds: number = 5;
  
  /**
   * 开始组织画像对话
   */
  async startProfileDialogue(): Promise<string> {
    this.currentRound = 1;
    this.dialogueState = { profile: {}, responses: [] };
    
    return this.generateNextQuestion();
  }
  
  /**
   * 处理用户回答并生成下一个问题
   */
  async processAnswer(answer: string): Promise<{ question?: string; completed: boolean; profile?: NonprofitOrganizationProfile }> {
    this.dialogueState.responses.push({
      round: this.currentRound,
      answer: answer,
      timestamp: new Date()
    });
    
    // 更新组织画像
    await this.updateProfile(answer);
    
    this.currentRound++;
    
    if (this.currentRound > this.maxRounds || this.isProfileComplete()) {
      return {
        completed: true,
        profile: this.dialogueState.profile
      };
    }
    
    const nextQuestion = await this.generateNextQuestion();
    return {
      question: nextQuestion,
      completed: false
    };
  }
  
  private async generateNextQuestion(): Promise<string> {
    // 基于当前画像状态生成下一个问题
    const prompt = this.buildQuestionPrompt();
    // 调用LLM生成问题
    return "生成的问题";
  }
  
  private async updateProfile(answer: string): Promise<void> {
    // 基于回答更新组织画像
    // 使用LLM提取结构化信息
  }
}
```

### 2.4 LLM API客户端 (`LLMApiClient`)

**【已修复】** 此客户端经过重构，以支持调用多个不同的LLM API。它不再是硬编码的，而是通过策略模式动态选择与特定模型匹配的处理器。

```typescript
// src/services/llm/client/LLMApiClient.ts

import { getApiKey } from '@/services/llm/api-key-manager';
import { MiniMaxApiHandler } from './handlers/minimax-handler';
import { DeepSeekApiHandler } from './handlers/deepseek-handler';

// API处理器接口
interface ApiHandler {
  call(prompt: any, apiKey: string): Promise<any>;
}

export class LLMApiClient {
  private handlers: Map<string, ApiHandler>;

  constructor() {
    this.handlers = new Map();
    this.handlers.set('minimax', new MiniMaxApiHandler());
    this.handlers.set('deepseek', new DeepSeekApiHandler());
  }

  /**
   * 根据模型名称调用相应的LLM API
   * @param modelName - 'minimax' or 'deepseek'
   * @param prompt - 为目标API格式化好的提示词对象
   * @returns LLM的响应
   */
  async call(modelName: string, prompt: any): Promise<any> {
    const handler = this.handlers.get(modelName);
    if (!handler) {
      throw new Error(`不支持的模型: ${modelName}`);
    }

    const apiKey = await getApiKey(modelName);
    if (!apiKey) {
      throw new Error(`缺少 ${modelName} 的API密钥`);
    }

    return handler.call(prompt, apiKey);
  }
}

// 示例处理器：
// src/services/llm/client/handlers/deepseek-handler.ts
export class DeepSeekApiHandler implements ApiHandler {
  async call(prompt: any, apiKey: string): Promise<any> {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(prompt),
    });
    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.statusText}`);
    }
    return response.json();
  }
}
```

### 2.2 提示词构建器 (`PromptBuilder`)

提示词构建器负责根据配置文件和用户输入，动态生成符合特定LLM API格式要求的提示词。

```typescript
// src/services/llm/prompt/AnalysisPromptBuilder.ts

export class AnalysisPromptBuilder {
  private config: any;

  constructor(config: any) {
    this.config = config;
  }

  /**
   * 构建分析报告的提示词
   * @param answers - 用户问卷答案
   * @param version - 'standard' or 'professional'
   * @param externalData - 【新增】处理后的外部数据
   * @returns 为LLM API格式化好的提示词
   */
  build(answers: any, version: 'standard' | 'professional', externalData: any = null): any {
    const systemMessage = this.config.prompt_template.system_message;
    const framework = JSON.stringify(this.config.framework, null, 2);
    const answersText = JSON.stringify(answers, null, 2);
    
    let taskDescription = `请基于以下问卷答案进行分析:\n${answersText}\n`;

    // 【新增】如果存在外部数据，则加入到提示词中
    if (version === 'professional' && externalData) {
      const externalDataText = JSON.stringify(externalData, null, 2);
      taskDescription += `\n请结合以下补充材料进行深度融合分析:\n${externalDataText}\n`;
    }

    const reportStructure = this.config.output_formats[version]
      .map(s => `- ${s.section}: ${s.content}`)
      .join('\n');

    taskDescription += `\n请严格按照以下结构生成JSON格式的报告:\n${reportStructure}`;

    // 返回适用于特定模型的提示词格式
    return {
      model: "deepseek-chat", // 示例
      messages: [
        { role: "system", content: `${systemMessage}\n分析框架:\n${framework}` },
        { role: "user", content: taskDescription },
      ],
      response_format: { type: "json_object" }
    };
  }
}
```

## 3. 智能体实现

### 3.1 公益机构评估导师 (`NonprofitAssessmentTutor`)

**【优化】** 专门针对公益机构的评估导师，集成了公益机构特有的评估维度和分析框架。

```typescript
// src/agents/nonprofit-assessment-tutor.ts
export class NonprofitAssessmentTutor {
  private llmClient: LLMApiClient;
  private promptBuilder: PromptBuilder;
  private jsonProcessor: JSONProcessor;
  private profileProcessor: NonprofitProfileProcessor;
  
  constructor() {
    this.llmClient = new LLMApiClient();
    this.promptBuilder = new PromptBuilder();
    this.jsonProcessor = new JSONProcessor();
    this.profileProcessor = new NonprofitProfileProcessor();
  }
  
  /**
   * 生成公益机构评估报告
   */
  async generateAssessmentReport(
    organizationData: any,
    profile: NonprofitOrganizationProfile,
    version: 'standard' | 'professional' = 'standard'
  ): Promise<any> {
    try {
      // 构建公益机构上下文
      const contextPrompt = this.profileProcessor.buildContextualPrompt(profile);
      
      // 加载公益机构评估提示词模板
      const promptTemplate = await this.loadNonprofitPromptTemplate(version);
      
      // 构建完整提示词
      const fullPrompt = this.promptBuilder.build({
        template: promptTemplate,
        context: contextPrompt,
        data: organizationData,
        nonprofit_indicators: this.getNonprofitSpecificIndicators(profile)
      });
      
      // 调用LLM生成评估
      const response = await this.llmClient.call(
        version === 'professional' ? 'deepseek' : 'minimax',
        {
          prompt: fullPrompt,
          temperature: 0.3,
          max_tokens: version === 'professional' ? 4000 : 2000
        }
      );
      
      // 使用JSON稳定性处理器解析结果
      const assessmentResult = this.jsonProcessor.parseWithFallback(response.content);
      
      // 添加公益机构特有的分析
      if (version === 'professional') {
        assessmentResult.nonprofit_specific_analysis = await this.generateNonprofitSpecificAnalysis(
          organizationData, 
          profile
        );
      }
      
      return assessmentResult;
      
    } catch (error) {
      console.error('公益机构评估生成失败:', error);
      throw new Error(`评估生成失败: ${error.message}`);
    }
  }
  
  /**
   * 生成公益机构特有的深度分析
   */
  private async generateNonprofitSpecificAnalysis(
    data: any, 
    profile: NonprofitOrganizationProfile
  ): Promise<any> {
    return {
      stakeholder_impact_analysis: await this.analyzeStakeholderImpact(data, profile),
      social_impact_quantification: await this.quantifySocialImpact(data, profile),
      sustainability_risk_assessment: await this.assessSustainabilityRisk(data, profile),
      mission_alignment_analysis: await this.analyzeMissionAlignment(data, profile)
    };
  }
  
  private getNonprofitSpecificIndicators(profile: NonprofitOrganizationProfile): any {
    // 根据组织画像返回特定的评估指标
    const indicators = {
      mission_driven_indicators: [],
      social_impact_indicators: [],
      stakeholder_indicators: [],
      sustainability_indicators: []
    };
    
    // 基于服务领域添加特定指标
    if (profile.serviceArea.includes('教育')) {
      indicators.social_impact_indicators.push('教育成果测量', '受益人群覆盖');
    }
    
    if (profile.serviceArea.includes('环保')) {
      indicators.social_impact_indicators.push('环境影响测量', '生态效益评估');
    }
    
    return indicators;
  }
}
```

### 3.2 问卷设计师 (`QuestionnaireAgent`)

负责生成问卷。**【已增强】** 现在支持根据版本类型（标准版/专业版）生成不同深度的问题。

```typescript
// src/services/agents/QuestionnaireAgent.ts

import { LLMApiClient } from '../llm/client/LLMApiClient';
import { QuestionnairePromptBuilder } from '../llm/prompt/QuestionnairePromptBuilder';

export class QuestionnaireAgent {
  private llmClient: LLMApiClient;
  private config: any;

  constructor(config: any) {
    this.llmClient = new LLMApiClient();
    this.config = config;
  }

  /**
   * 生成问卷
   * @param version - 'standard' 或 'professional'
   * @param organizationInfo - 组织基本信息（可选）
   * @returns 生成的问卷JSON
   */
  async generate(version: 'standard' | 'professional', organizationInfo?: any): Promise<any> {
    const builder = new QuestionnairePromptBuilder(this.config);
    const prompt = builder.build(version, organizationInfo);
    
    // 问卷生成使用DeepSeek模型（智能生成能力强）
    const response = await this.llmClient.call('deepseek', prompt);
    
    // 此处应有对response的验证和解析逻辑
    const questionnaire = JSON.parse(response.choices[0].message.content);
    
    // 验证生成的问卷是否符合深度要求
    this.validateQuestionDepth(questionnaire, version);
    
    return questionnaire;
  }

  /**
   * 验证问卷深度是否符合版本要求
   * @param questionnaire - 生成的问卷
   * @param version - 版本类型
   */
  private validateQuestionDepth(questionnaire: any, version: 'standard' | 'professional'): void {
    const versionSettings = this.config.configuration_parameters.version_specific_settings[version];
    const complexityKeywords = versionSettings.complexity_keywords;
    
    // 简单验证：检查问题文本中是否包含相应的复杂度关键词
    let keywordMatchCount = 0;
    questionnaire.questions?.forEach((question: any) => {
      const questionText = question.text.toLowerCase();
      if (complexityKeywords.some((keyword: string) => questionText.includes(keyword))) {
        keywordMatchCount++;
      }
    });
    
    // 至少30%的问题应该体现相应的深度特征
    const expectedMinMatches = Math.floor((questionnaire.questions?.length || 0) * 0.3);
    if (keywordMatchCount < expectedMinMatches) {
      console.warn(`问卷深度验证警告: ${version}版本问题深度可能不足`);
    }
  }
}
```

#### 3.1.1 问卷提示词构建器增强

```typescript
// src/services/llm/prompt/QuestionnairePromptBuilder.ts

export class QuestionnairePromptBuilder {
  private config: any;

  constructor(config: any) {
    this.config = config;
  }

  /**
   * 构建问卷生成的提示词
   * @param version - 'standard' 或 'professional'
   * @param organizationInfo - 组织信息（可选）
   * @returns 格式化的提示词
   */
  build(version: 'standard' | 'professional', organizationInfo?: any): any {
    const systemMessage = this.config.prompt_template.system_message;
    const framework = JSON.stringify(this.config.prompt_template.framework, null, 2);
    const versionSettings = this.config.configuration_parameters.version_specific_settings[version];
    const depthInstructions = this.config.configuration_parameters.depth_control_instructions[version];
    
    let taskDescription = `请生成一套OCTI组织能力评估问卷，包含60道题目（每个维度15题）。\n\n`;
    
    // 添加版本特定的深度控制指令
    taskDescription += `**问题深度要求（${version === 'standard' ? '标准版' : '专业版'}）**:\n`;
    taskDescription += `- 深度级别: ${versionSettings.question_depth_level}\n`;
    taskDescription += `- 复杂度关键词: ${versionSettings.complexity_keywords.join('、')}\n`;
    taskDescription += `- 情境复杂度: ${versionSettings.scenario_complexity}\n`;
    taskDescription += `- 认知负荷: ${versionSettings.cognitive_load}\n`;
    taskDescription += `- 问题风格: ${versionSettings.question_style}\n\n`;
    
    taskDescription += `**具体要求**:\n${depthInstructions}\n\n`;
    
    if (organizationInfo) {
      taskDescription += `**组织背景信息**:\n${JSON.stringify(organizationInfo, null, 2)}\n\n`;
    }
    
    taskDescription += `请严格按照以下JSON格式返回问卷:\n`;
    taskDescription += `{
      "questionnaire": {
        "version": "${version}",
        "total_questions": 60,
        "dimensions": {
          "SF": { "questions": [...] },
          "IT": { "questions": [...] },
          "MV": { "questions": [...] },
          "AD": { "questions": [...] }
        }
      }
    }`;

    return {
      model: "abab6.5s-chat",
      messages: [
        { role: "system", content: `${systemMessage}\n\n评估框架:\n${framework}` },
        { role: "user", content: taskDescription },
      ],
      response_format: { type: "json_object" }
    };
  }
}
```

### 3.3 智能问卷设计师 (`IntelligentQuestionnaireDesigner`)

**【优化】** 专门针对公益机构的智能问卷设计师，集成了多轮对话机制和公益机构特有的问题生成逻辑。

```typescript
// src/agents/intelligent-questionnaire-designer.ts
export class IntelligentQuestionnaireDesigner {
  private llmClient: LLMApiClient;
  private promptBuilder: PromptBuilder;
  private jsonProcessor: JSONProcessor;
  private dialogueManager: MultiRoundDialogueManager;
  private profileProcessor: NonprofitProfileProcessor;
  
  constructor() {
    this.llmClient = new LLMApiClient();
    this.promptBuilder = new PromptBuilder();
    this.jsonProcessor = new JSONProcessor();
    this.dialogueManager = new MultiRoundDialogueManager();
    this.profileProcessor = new NonprofitProfileProcessor();
  }
  
  /**
   * 开始公益机构组织画像收集对话
   */
  async startProfileCollection(): Promise<{ question: string; sessionId: string }> {
    const sessionId = this.generateSessionId();
    const firstQuestion = await this.dialogueManager.startProfileDialogue();
    
    return {
      question: firstQuestion,
      sessionId: sessionId
    };
  }
  
  /**
   * 处理对话回答并继续收集画像
   */
  async processDialogueAnswer(
    sessionId: string, 
    answer: string
  ): Promise<{ question?: string; completed: boolean; profile?: NonprofitOrganizationProfile }> {
    return await this.dialogueManager.processAnswer(answer);
  }
  
  /**
   * 基于公益机构画像生成个性化问卷
   */
  async generateNonprofitQuestionnaire(
    profile: NonprofitOrganizationProfile,
    requirements: QuestionnaireRequirements
  ): Promise<Questionnaire> {
    try {
      // 构建公益机构上下文
      const contextPrompt = this.profileProcessor.buildContextualPrompt(profile);
      
      // 加载公益机构问卷设计提示词
      const promptTemplate = await this.loadNonprofitQuestionnairePrompt();
      
      // 构建完整提示词
      const prompt = this.promptBuilder.build({
        template: promptTemplate,
        context: contextPrompt,
        requirements: requirements,
        nonprofit_profile: profile,
        intelligent_prompts: this.getIntelligentPrompts(profile)
      });
      
      const response = await this.llmClient.call('deepseek', {
        prompt,
        temperature: 0.7,
        max_tokens: 3000
      });
      
      // 使用JSON稳定性处理器解析结果
      const questionnaire = this.jsonProcessor.parseWithFallback(response.content);
      
      // 应用公益机构特有的质量控制
      return this.applyNonprofitQualityControl(questionnaire, profile);
      
    } catch (error) {
      console.error('公益机构问卷生成失败:', error);
      throw new Error(`问卷生成失败: ${error.message}`);
    }
  }
  
  /**
   * 获取智能提示词
   */
  private getIntelligentPrompts(profile: NonprofitOrganizationProfile): any {
    const prompts = {
      service_area_prompts: {},
      development_stage_prompts: {},
      operating_model_prompts: {}
    };
    
    // 基于服务领域的智能提示
    if (profile.serviceArea.includes('教育')) {
      prompts.service_area_prompts['教育'] = {
        focus_areas: ['教学质量', '学生成果', '教师发展', '课程创新'],
        key_metrics: ['学习成果提升率', '师生比例', '课程覆盖率']
      };
    }
    
    // 基于发展阶段的智能提示
    if (profile.developmentStage === '初创期') {
      prompts.development_stage_prompts['初创期'] = {
        focus_areas: ['团队建设', '资源获取', '项目启动', '品牌建立'],
        assessment_priorities: ['团队稳定性', '资金可持续性', '项目可行性']
      };
    }
    
    return prompts;
  }
  
  /**
   * 应用公益机构特有的质量控制
   */
  private applyNonprofitQualityControl(
    questionnaire: Questionnaire, 
    profile: NonprofitOrganizationProfile
  ): Questionnaire {
    // 验证使命相关性
    this.validateMissionRelevance(questionnaire, profile);
    
    // 验证利益相关者视角
    this.validateStakeholderPerspective(questionnaire, profile);
    
    // 验证社会影响力测量
    this.validateSocialImpactMeasurement(questionnaire, profile);
    
    return questionnaire;
  }
  
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

### 3.4 组织评估导师 (`AnalysisAgent`)

**【已修复】** 负责生成分析报告。专业版逻辑中加入了**数据融合**步骤，并实现了**双模型协作**。

```typescript
// src/services/agents/AnalysisAgent.ts

import { LLMApiClient } from '../llm/client/LLMApiClient';
import { AnalysisPromptBuilder } from '../llm/prompt/AnalysisPromptBuilder';
import { DataFusionEngine } from '../data/DataFusionEngine'; // 【新增】

export class AnalysisAgent {
  private llmClient: LLMApiClient;
  private config: any;
  private dataFusionEngine: DataFusionEngine; // 【新增】

  constructor(config: any) {
    this.llmClient = new LLMApiClient();
    this.config = config;
    this.dataFusionEngine = new DataFusionEngine(); // 【新增】
  }

  async analyze(assessmentId: string, answers: any, version: 'standard' | 'professional'): Promise<any> {
    if (version === 'standard') {
      return this.runStandardAnalysis(answers);
    } else {
      return this.runProfessionalAnalysis(assessmentId, answers);
    }
  }

  private async runStandardAnalysis(answers: any): Promise<any> {
    const builder = new AnalysisPromptBuilder(this.config);
    const prompt = builder.build(answers, 'standard');
    
    const response = await this.llmClient.call('minimax', prompt);
    return JSON.parse(response.choices[0].message.content);
  }

  private async runProfessionalAnalysis(assessmentId: string, answers: any): Promise<any> {
    // 步骤1: 【新增】调用数据融合引擎获取外部数据
    const externalData = await this.dataFusionEngine.getFusedData(assessmentId);

    // 步骤2: 使用主模型（如DeepSeek）进行深度分析
    const builder = new AnalysisPromptBuilder(this.config);
    const deepseekPrompt = builder.build(answers, 'professional', externalData);
    const deepseekResponse = await this.llmClient.call('deepseek', deepseekPrompt);
    const mainReport = JSON.parse(deepseekResponse.choices[0].message.content);

    // 步骤3: 【新增】使用次模型（如MiniMax）进行交叉验证或生成摘要（双模型协作）
    const summaryPrompt = this.createSummaryPrompt(mainReport);
    const minimaxResponse = await this.llmClient.call('minimax', summaryPrompt);
    const summary = JSON.parse(minimaxResponse.choices[0].message.content);

    // 步骤4: 合并最终报告
    mainReport.executive_summary = summary; // 将摘要合并到主报告中
    return mainReport;
  }

  private createSummaryPrompt(report: any): any {
    // ... 此处省略为生成摘要而创建新提示词的逻辑 ...
    return { /* ... */ };
  }
}
```

### 3.3 【新增】数据融合引擎 (`DataFusionEngine`)

这是一个新的服务，负责从数据库中检索和准备用于分析的外部数据。

```typescript
// src/services/data/DataFusionEngine.ts
import { prisma } from '@/prisma/client';

export class DataFusionEngine {
  /**
   * 根据评估ID，获取所有已处理完成的外部数据源内容
   * @param assessmentId 
   * @returns 包含所有外部数据文本的对象，或null
   */
  async getFusedData(assessmentId: string): Promise<any | null> {
    const dataSources = await prisma.externalDataSource.findMany({
      where: {
        assessmentId: assessmentId,
        status: 'COMPLETED',
      },
      include: {
        processedData: true,
      },
    });

    if (dataSources.length === 0) {
      return null;
    }

    const fusedData = {
      uploaded_files: [],
      scraped_urls: [],
    };

    for (const source of dataSources) {
      if (source.type === 'FILE') {
        fusedData.uploaded_files.push({
          fileName: source.source,
          content: source.processedData?.extractedContent,
        });
      } else if (source.type === 'URL') {
        fusedData.scraped_urls.push({
          url: source.source,
          content: source.processedData?.extractedContent,
        });
      }
    }

    return fusedData;
  }
}

## 4. 公益机构专用架构优化

### 4.1 架构升级要点

**【新增】** 针对公益机构评估的专用架构优化，确保系统能够准确理解和评估公益机构的独特性。

```typescript
// src/core/nonprofit-architecture.ts
export class NonprofitArchitecture {
  /**
   * 公益机构评估流程优化
   */
  static getOptimizedFlow(): AssessmentFlow {
    return {
      // 第一阶段：组织画像收集（多轮对话）
      phase1: {
        name: '组织画像收集',
        agent: 'IntelligentQuestionnaireDesigner',
        method: 'startProfileCollection',
        duration: '5-10分钟',
        output: 'NonprofitOrganizationProfile'
      },
      
      // 第二阶段：个性化问卷生成
      phase2: {
        name: '智能问卷生成',
        agent: 'IntelligentQuestionnaireDesigner',
        method: 'generateNonprofitQuestionnaire',
        input: 'NonprofitOrganizationProfile',
        output: 'PersonalizedQuestionnaire'
      },
      
      // 第三阶段：问卷填写
      phase3: {
        name: '问卷填写',
        interface: 'AdaptiveQuestionnaireUI',
        features: ['智能提示', '上下文帮助', '进度保存']
      },
      
      // 第四阶段：公益机构专用评估
      phase4: {
        name: '专业评估分析',
        agent: 'NonprofitAssessmentTutor',
        method: 'generateAssessmentReport',
        features: ['四维八极框架', '公益特色指标', '利益相关者分析']
      }
    };
  }
  
  /**
   * 公益机构数据融合策略
   */
  static getDataFusionStrategy(): DataFusionStrategy {
    return {
      // 基础数据源
      basic_sources: ['问卷数据', '组织画像', '历史评估'],
      
      // 公益机构特有数据源
      nonprofit_sources: {
        impact_data: {
          description: '社会影响力数据',
          sources: ['项目成果', '受益人反馈', '社会效益测量'],
          weight: 0.3
        },
        stakeholder_data: {
          description: '利益相关者数据',
          sources: ['捐赠人反馈', '合作伙伴评价', '受益人满意度'],
          weight: 0.25
        },
        mission_data: {
          description: '使命一致性数据',
          sources: ['战略文档', '项目设计', '决策记录'],
          weight: 0.2
        },
        sustainability_data: {
          description: '可持续性数据',
          sources: ['财务报告', '资源配置', '风险评估'],
          weight: 0.25
        }
      },
      
      // 融合算法
      fusion_algorithm: 'WeightedNonprofitFusion'
    };
  }
}
```

### 4.2 JSON稳定性保障机制

**【新增】** 多层次的JSON稳定性保障，确保LLM输出的可靠性。

```typescript
// src/core/json-stability.ts
export class JSONStabilityManager {
  /**
   * 四层JSON稳定性保障
   */
  static async ensureStability(llmOutput: string): Promise<any> {
    const strategies = [
      this.directParse,
      this.extractJSONBlock,
      this.intelligentRepair,
      this.structuredExtraction,
      this.fallbackGeneration
    ];
    
    for (const strategy of strategies) {
      try {
        const result = await strategy(llmOutput);
        if (this.validateResult(result)) {
          return result;
        }
      } catch (error) {
        console.warn(`JSON解析策略失败: ${strategy.name}`, error);
        continue;
      }
    }
    
    throw new Error('所有JSON解析策略均失败');
  }
  
  /**
   * 智能JSON修复
   */
  static intelligentRepair(content: string): any {
    let repaired = content;
    
    // 修复常见格式问题
    const repairs = [
      { pattern: /,\s*}/g, replacement: '}' },
      { pattern: /,\s*]/g, replacement: ']' },
      { pattern: /([{,]\s*)(\w+):/g, replacement: '$1"$2":' },
      { pattern: /:\s*([^"\[\{\d][^,}\]]*)/g, replacement: ': "$1"' }
    ];
    
    repairs.forEach(repair => {
      repaired = repaired.replace(repair.pattern, repair.replacement);
    });
    
    return JSON.parse(repaired);
  }
}
```

### 4.3 可扩展性设计

**【优化】** 针对公益机构评估的可扩展性设计，支持不同类型公益机构的个性化需求。

```typescript
// src/core/nonprofit-extensibility.ts
export class NonprofitExtensibilityFramework {
  /**
   * 公益机构类型扩展点
   */
  static getExtensionPoints(): ExtensionPoints {
    return {
      // 组织类型扩展
      organization_types: {
        current: ['基金会', '公益组织', '社会团体', '民办非企业', '国际NGO', '政府机构'],
        extension_interface: 'OrganizationTypeHandler',
        custom_indicators: 'getTypeSpecificIndicators',
        custom_analysis: 'generateTypeSpecificAnalysis'
      },
      
      // 服务领域扩展
      service_areas: {
        current: ['教育', '环保', '扶贫', '医疗', '文化', '科技'],
        extension_interface: 'ServiceAreaHandler',
        domain_knowledge: 'getDomainSpecificKnowledge',
        impact_metrics: 'getImpactMeasurementFramework'
      },
      
      // 评估维度扩展
      assessment_dimensions: {
        base_framework: '四维八极',
        extension_interface: 'DimensionExtension',
        custom_subdimensions: 'addSubDimensions',
        weight_adjustment: 'adjustDimensionWeights'
      }
    };
  }
}
```

## 5. 部署和监控

### 5.1 公益机构专用监控指标

**【新增】** 针对公益机构评估的专用监控指标，确保系统质量。

```typescript
// src/monitoring/nonprofit-metrics.ts
export class NonprofitMonitoringMetrics {
  static getMetrics(): MonitoringMetrics {
    return {
      // 评估质量指标
      assessment_quality: {
        mission_alignment_accuracy: '使命一致性评估准确率',
        stakeholder_coverage: '利益相关者覆盖完整性',
        impact_measurement_validity: '社会影响力测量有效性'
      },
      
      // 用户体验指标
      user_experience: {
        dialogue_completion_rate: '组织画像对话完成率',
        questionnaire_relevance_score: '问卷相关性评分',
        report_satisfaction_rating: '报告满意度评级'
      },
      
      // 技术性能指标
      technical_performance: {
        json_parsing_success_rate: 'JSON解析成功率',
        llm_response_stability: 'LLM响应稳定性',
        multi_round_dialogue_efficiency: '多轮对话效率'
      }
    };
  }
}
```

## 6. 总结

本文档详细描述了OCTI智能评估系统针对公益机构的LLM集成实现。通过引入公益机构组织画像、多轮对话机制、JSON稳定性处理和专用评估框架，系统能够为公益机构提供更加精准、专业的组织评估服务。

**核心优化点：**
1. **公益机构专用化**：从通用组织评估转向公益机构专用评估
2. **智能化流程**：多轮对话收集组织画像，智能生成个性化问卷
3. **稳定性保障**：多层次JSON解析策略，确保系统可靠性
4. **可扩展架构**：支持不同类型公益机构的个性化需求
5. **专业监控**：公益机构特有的质量监控指标体系