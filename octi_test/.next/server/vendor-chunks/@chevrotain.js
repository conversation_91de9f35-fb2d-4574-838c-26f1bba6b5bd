"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@chevrotain";
exports.ids = ["vendor-chunks/@chevrotain"];
exports.modules = {

/***/ "(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/api.js":
/*!*************************************************************!*\
  !*** ./node_modules/@chevrotain/cst-dts-gen/lib/src/api.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCstDts: () => (/* binding */ generateCstDts)\n/* harmony export */ });\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js\");\n/* harmony import */ var _generate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./generate.js */ \"(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js\");\n\n\nconst defaultOptions = {\n    includeVisitorInterface: true,\n    visitorInterfaceName: \"ICstNodeVisitor\",\n};\nfunction generateCstDts(productions, options) {\n    const effectiveOptions = Object.assign(Object.assign({}, defaultOptions), options);\n    const model = (0,_model_js__WEBPACK_IMPORTED_MODULE_0__.buildModel)(productions);\n    return (0,_generate_js__WEBPACK_IMPORTED_MODULE_1__.genDts)(model, effectiveOptions);\n}\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vY3N0LWR0cy1nZW4vbGliL3NyYy9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdDO0FBQ0Q7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLDJEQUEyRDtBQUMzRCxrQkFBa0IscURBQVU7QUFDNUIsV0FBVyxvREFBTTtBQUNqQjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AY2hldnJvdGFpbi9jc3QtZHRzLWdlbi9saWIvc3JjL2FwaS5qcz8zYjIzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJ1aWxkTW9kZWwgfSBmcm9tIFwiLi9tb2RlbC5qc1wiO1xuaW1wb3J0IHsgZ2VuRHRzIH0gZnJvbSBcIi4vZ2VuZXJhdGUuanNcIjtcbmNvbnN0IGRlZmF1bHRPcHRpb25zID0ge1xuICAgIGluY2x1ZGVWaXNpdG9ySW50ZXJmYWNlOiB0cnVlLFxuICAgIHZpc2l0b3JJbnRlcmZhY2VOYW1lOiBcIklDc3ROb2RlVmlzaXRvclwiLFxufTtcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNzdER0cyhwcm9kdWN0aW9ucywgb3B0aW9ucykge1xuICAgIGNvbnN0IGVmZmVjdGl2ZU9wdGlvbnMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIGRlZmF1bHRPcHRpb25zKSwgb3B0aW9ucyk7XG4gICAgY29uc3QgbW9kZWwgPSBidWlsZE1vZGVsKHByb2R1Y3Rpb25zKTtcbiAgICByZXR1cm4gZ2VuRHRzKG1vZGVsLCBlZmZlY3RpdmVPcHRpb25zKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js":
/*!******************************************************************!*\
  !*** ./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genDts: () => (/* binding */ genDts)\n/* harmony export */ });\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/flatten.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/isArray.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/uniq.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/reduce.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/upperFirst.js\");\n\nfunction genDts(model, options) {\n    let contentParts = [];\n    contentParts = contentParts.concat(`import type { CstNode, ICstVisitor, IToken } from \"chevrotain\";`);\n    contentParts = contentParts.concat((0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(model, (node) => genCstNodeTypes(node))));\n    if (options.includeVisitorInterface) {\n        contentParts = contentParts.concat(genVisitor(options.visitorInterfaceName, model));\n    }\n    return contentParts.join(\"\\n\\n\") + \"\\n\";\n}\nfunction genCstNodeTypes(node) {\n    const nodeCstInterface = genNodeInterface(node);\n    const nodeChildrenInterface = genNodeChildrenType(node);\n    return [nodeCstInterface, nodeChildrenInterface];\n}\nfunction genNodeInterface(node) {\n    const nodeInterfaceName = getNodeInterfaceName(node.name);\n    const childrenTypeName = getNodeChildrenTypeName(node.name);\n    return `export interface ${nodeInterfaceName} extends CstNode {\n  name: \"${node.name}\";\n  children: ${childrenTypeName};\n}`;\n}\nfunction genNodeChildrenType(node) {\n    const typeName = getNodeChildrenTypeName(node.name);\n    return `export type ${typeName} = {\n  ${(0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node.properties, (property) => genChildProperty(property)).join(\"\\n  \")}\n};`;\n}\nfunction genChildProperty(prop) {\n    const typeName = buildTypeString(prop.type);\n    return `${prop.name}${prop.optional ? \"?\" : \"\"}: ${typeName}[];`;\n}\nfunction genVisitor(name, nodes) {\n    return `export interface ${name}<IN, OUT> extends ICstVisitor<IN, OUT> {\n  ${(0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(nodes, (node) => genVisitorFunction(node)).join(\"\\n  \")}\n}`;\n}\nfunction genVisitorFunction(node) {\n    const childrenTypeName = getNodeChildrenTypeName(node.name);\n    return `${node.name}(children: ${childrenTypeName}, param?: IN): OUT;`;\n}\nfunction buildTypeString(type) {\n    if ((0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(type)) {\n        const typeNames = (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(type, (t) => getTypeString(t)));\n        const typeString = (0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(typeNames, (sum, t) => sum + \" | \" + t);\n        return \"(\" + typeString + \")\";\n    }\n    else {\n        return getTypeString(type);\n    }\n}\nfunction getTypeString(type) {\n    if (type.kind === \"token\") {\n        return \"IToken\";\n    }\n    return getNodeInterfaceName(type.name);\n}\nfunction getNodeInterfaceName(ruleName) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ruleName) + \"CstNode\";\n}\nfunction getNodeChildrenTypeName(ruleName) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ruleName) + \"CstChildren\";\n}\n//# sourceMappingURL=generate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js":
/*!***************************************************************!*\
  !*** ./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildModel: () => (/* binding */ buildModel)\n/* harmony export */ });\n/* harmony import */ var _chevrotain_gast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @chevrotain/gast */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/api.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/values.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/groupBy.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/some.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/assign.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/flatten.js\");\n\n\nfunction buildModel(productions) {\n    const generator = new CstNodeDefinitionGenerator();\n    const allRules = (0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(productions);\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(allRules, (rule) => generator.visitRule(rule));\n}\nclass CstNodeDefinitionGenerator extends _chevrotain_gast__WEBPACK_IMPORTED_MODULE_0__.GAstVisitor {\n    visitRule(node) {\n        const rawElements = this.visitEach(node.definition);\n        const grouped = (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(rawElements, (el) => el.propertyName);\n        const properties = (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(grouped, (group, propertyName) => {\n            const allNullable = !(0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(group, (el) => !el.canBeNull);\n            // In an alternation with a label a property name can have\n            // multiple types.\n            let propertyType = group[0].type;\n            if (group.length > 1) {\n                propertyType = (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(group, (g) => g.type);\n            }\n            return {\n                name: propertyName,\n                type: propertyType,\n                optional: allNullable,\n            };\n        });\n        return {\n            name: node.name,\n            properties: properties,\n        };\n    }\n    visitAlternative(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitOption(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitRepetition(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitRepetitionMandatory(node) {\n        return this.visitEach(node.definition);\n    }\n    visitRepetitionMandatoryWithSeparator(node) {\n        return this.visitEach(node.definition).concat({\n            propertyName: node.separator.name,\n            canBeNull: true,\n            type: getType(node.separator),\n        });\n    }\n    visitRepetitionWithSeparator(node) {\n        return this.visitEachAndOverrideWith(node.definition, {\n            canBeNull: true,\n        }).concat({\n            propertyName: node.separator.name,\n            canBeNull: true,\n            type: getType(node.separator),\n        });\n    }\n    visitAlternation(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitTerminal(node) {\n        return [\n            {\n                propertyName: node.label || node.terminalType.name,\n                canBeNull: false,\n                type: getType(node),\n            },\n        ];\n    }\n    visitNonTerminal(node) {\n        return [\n            {\n                propertyName: node.label || node.nonTerminalName,\n                canBeNull: false,\n                type: getType(node),\n            },\n        ];\n    }\n    visitEachAndOverrideWith(definition, override) {\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this.visitEach(definition), (definition) => (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({}, definition, override));\n    }\n    visitEach(definition) {\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(definition, (definition) => this.visit(definition)));\n    }\n}\nfunction getType(production) {\n    if (production instanceof _chevrotain_gast__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return {\n            kind: \"rule\",\n            name: production.referencedRule.name,\n        };\n    }\n    return { kind: \"token\" };\n}\n//# sourceMappingURL=model.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/api.js":
/*!******************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/api.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alternation: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation),\n/* harmony export */   Alternative: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternative),\n/* harmony export */   GAstVisitor: () => (/* reexport safe */ _visitor_js__WEBPACK_IMPORTED_MODULE_1__.GAstVisitor),\n/* harmony export */   NonTerminal: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal),\n/* harmony export */   Option: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Option),\n/* harmony export */   Repetition: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition),\n/* harmony export */   RepetitionMandatory: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory),\n/* harmony export */   RepetitionMandatoryWithSeparator: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator),\n/* harmony export */   RepetitionWithSeparator: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator),\n/* harmony export */   Rule: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Rule),\n/* harmony export */   Terminal: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal),\n/* harmony export */   getProductionDslName: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.getProductionDslName),\n/* harmony export */   isBranchingProd: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.isBranchingProd),\n/* harmony export */   isOptionalProd: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.isOptionalProd),\n/* harmony export */   isSequenceProd: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.isSequenceProd),\n/* harmony export */   serializeGrammar: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.serializeGrammar),\n/* harmony export */   serializeProduction: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.serializeProduction)\n/* harmony export */ });\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\");\n/* harmony import */ var _visitor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./visitor.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/visitor.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/helpers.js\");\n\n\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vZ2FzdC9saWIvc3JjL2FwaS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErTjtBQUNwTDtBQUMyRDtBQUN0RyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vZ2FzdC9saWIvc3JjL2FwaS5qcz9lZjhhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IFJ1bGUsIFRlcm1pbmFsLCBOb25UZXJtaW5hbCwgT3B0aW9uLCBSZXBldGl0aW9uLCBSZXBldGl0aW9uTWFuZGF0b3J5LCBSZXBldGl0aW9uTWFuZGF0b3J5V2l0aFNlcGFyYXRvciwgUmVwZXRpdGlvbldpdGhTZXBhcmF0b3IsIEFsdGVybmF0aW9uLCBBbHRlcm5hdGl2ZSwgc2VyaWFsaXplR3JhbW1hciwgc2VyaWFsaXplUHJvZHVjdGlvbiwgfSBmcm9tIFwiLi9tb2RlbC5qc1wiO1xuZXhwb3J0IHsgR0FzdFZpc2l0b3IgfSBmcm9tIFwiLi92aXNpdG9yLmpzXCI7XG5leHBvcnQgeyBnZXRQcm9kdWN0aW9uRHNsTmFtZSwgaXNPcHRpb25hbFByb2QsIGlzQnJhbmNoaW5nUHJvZCwgaXNTZXF1ZW5jZVByb2QsIH0gZnJvbSBcIi4vaGVscGVycy5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/helpers.js":
/*!**********************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/helpers.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getProductionDslName: () => (/* binding */ getProductionDslName),\n/* harmony export */   isBranchingProd: () => (/* binding */ isBranchingProd),\n/* harmony export */   isOptionalProd: () => (/* binding */ isOptionalProd),\n/* harmony export */   isSequenceProd: () => (/* binding */ isSequenceProd)\n/* harmony export */ });\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/some.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/includes.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/every.js\");\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\");\n\n\nfunction isSequenceProd(prod) {\n    return (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternative ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Option ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Rule);\n}\nfunction isOptionalProd(prod, alreadyVisited = []) {\n    const isDirectlyOptional = prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Option ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator;\n    if (isDirectlyOptional) {\n        return true;\n    }\n    // note that this can cause infinite loop if one optional empty TOP production has a cyclic dependency with another\n    // empty optional top rule\n    // may be indirectly optional ((A?B?C?) | (D?E?F?))\n    if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        // for OR its enough for just one of the alternatives to be optional\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prod.definition, (subProd) => {\n            return isOptionalProd(subProd, alreadyVisited);\n        });\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal && (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alreadyVisited, prod)) {\n        // avoiding stack overflow due to infinite recursion\n        return false;\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.AbstractProduction) {\n        if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n            alreadyVisited.push(prod);\n        }\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(prod.definition, (subProd) => {\n            return isOptionalProd(subProd, alreadyVisited);\n        });\n    }\n    else {\n        return false;\n    }\n}\nfunction isBranchingProd(prod) {\n    return prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation;\n}\nfunction getProductionDslName(prod) {\n    /* istanbul ignore else */\n    if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return \"SUBRULE\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return \"OPTION\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return \"OR\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return \"AT_LEAST_ONE\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return \"AT_LEAST_ONE_SEP\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return \"MANY_SEP\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return \"MANY\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal) {\n        return \"CONSUME\";\n        /* c8 ignore next 3 */\n    }\n    else {\n        throw Error(\"non exhaustive match\");\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js":
/*!********************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/model.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractProduction: () => (/* binding */ AbstractProduction),\n/* harmony export */   Alternation: () => (/* binding */ Alternation),\n/* harmony export */   Alternative: () => (/* binding */ Alternative),\n/* harmony export */   NonTerminal: () => (/* binding */ NonTerminal),\n/* harmony export */   Option: () => (/* binding */ Option),\n/* harmony export */   Repetition: () => (/* binding */ Repetition),\n/* harmony export */   RepetitionMandatory: () => (/* binding */ RepetitionMandatory),\n/* harmony export */   RepetitionMandatoryWithSeparator: () => (/* binding */ RepetitionMandatoryWithSeparator),\n/* harmony export */   RepetitionWithSeparator: () => (/* binding */ RepetitionWithSeparator),\n/* harmony export */   Rule: () => (/* binding */ Rule),\n/* harmony export */   Terminal: () => (/* binding */ Terminal),\n/* harmony export */   serializeGrammar: () => (/* binding */ serializeGrammar),\n/* harmony export */   serializeProduction: () => (/* binding */ serializeProduction)\n/* harmony export */ });\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/isString.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/forEach.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/assign.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/pickBy.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/isRegExp.js\");\n\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction tokenLabel(tokType) {\n    if (hasTokenLabel(tokType)) {\n        return tokType.LABEL;\n    }\n    else {\n        return tokType.name;\n    }\n}\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction hasTokenLabel(obj) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(obj.LABEL) && obj.LABEL !== \"\";\n}\nclass AbstractProduction {\n    get definition() {\n        return this._definition;\n    }\n    set definition(value) {\n        this._definition = value;\n    }\n    constructor(_definition) {\n        this._definition = _definition;\n    }\n    accept(visitor) {\n        visitor.visit(this);\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this.definition, (prod) => {\n            prod.accept(visitor);\n        });\n    }\n}\nclass NonTerminal extends AbstractProduction {\n    constructor(options) {\n        super([]);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n    set definition(definition) {\n        // immutable\n    }\n    get definition() {\n        if (this.referencedRule !== undefined) {\n            return this.referencedRule.definition;\n        }\n        return [];\n    }\n    accept(visitor) {\n        visitor.visit(this);\n        // don't visit children of a reference, we will get cyclic infinite loops if we do so\n    }\n}\nclass Rule extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.orgText = \"\";\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Alternative extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.ignoreAmbiguities = false;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Option extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass RepetitionMandatory extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass RepetitionMandatoryWithSeparator extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Repetition extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass RepetitionWithSeparator extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Alternation extends AbstractProduction {\n    get definition() {\n        return this._definition;\n    }\n    set definition(value) {\n        this._definition = value;\n    }\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        this.ignoreAmbiguities = false;\n        this.hasPredicates = false;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Terminal {\n    constructor(options) {\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n    accept(visitor) {\n        visitor.visit(this);\n    }\n}\nfunction serializeGrammar(topRules) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(topRules, serializeProduction);\n}\nfunction serializeProduction(node) {\n    function convertDefinition(definition) {\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(definition, serializeProduction);\n    }\n    /* istanbul ignore else */\n    if (node instanceof NonTerminal) {\n        const serializedNonTerminal = {\n            type: \"NonTerminal\",\n            name: node.nonTerminalName,\n            idx: node.idx,\n        };\n        if ((0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node.label)) {\n            serializedNonTerminal.label = node.label;\n        }\n        return serializedNonTerminal;\n    }\n    else if (node instanceof Alternative) {\n        return {\n            type: \"Alternative\",\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Option) {\n        return {\n            type: \"Option\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof RepetitionMandatory) {\n        return {\n            type: \"RepetitionMandatory\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof RepetitionMandatoryWithSeparator) {\n        return {\n            type: \"RepetitionMandatoryWithSeparator\",\n            idx: node.idx,\n            separator: (serializeProduction(new Terminal({ terminalType: node.separator }))),\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof RepetitionWithSeparator) {\n        return {\n            type: \"RepetitionWithSeparator\",\n            idx: node.idx,\n            separator: (serializeProduction(new Terminal({ terminalType: node.separator }))),\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Repetition) {\n        return {\n            type: \"Repetition\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Alternation) {\n        return {\n            type: \"Alternation\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Terminal) {\n        const serializedTerminal = {\n            type: \"Terminal\",\n            name: node.terminalType.name,\n            label: tokenLabel(node.terminalType),\n            idx: node.idx,\n        };\n        if ((0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node.label)) {\n            serializedTerminal.terminalLabel = node.label;\n        }\n        const pattern = node.terminalType.PATTERN;\n        if (node.terminalType.PATTERN) {\n            serializedTerminal.pattern = (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(pattern)\n                ? pattern.source\n                : pattern;\n        }\n        return serializedTerminal;\n    }\n    else if (node instanceof Rule) {\n        return {\n            type: \"Rule\",\n            name: node.name,\n            orgText: node.orgText,\n            definition: convertDefinition(node.definition),\n        };\n        /* c8 ignore next 3 */\n    }\n    else {\n        throw Error(\"non exhaustive match\");\n    }\n}\n//# sourceMappingURL=model.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/visitor.js":
/*!**********************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/visitor.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GAstVisitor: () => (/* binding */ GAstVisitor)\n/* harmony export */ });\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\");\n\nclass GAstVisitor {\n    visit(node) {\n        const nodeAny = node;\n        switch (nodeAny.constructor) {\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal:\n                return this.visitNonTerminal(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternative:\n                return this.visitAlternative(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Option:\n                return this.visitOption(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory:\n                return this.visitRepetitionMandatory(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator:\n                return this.visitRepetitionMandatoryWithSeparator(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator:\n                return this.visitRepetitionWithSeparator(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition:\n                return this.visitRepetition(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation:\n                return this.visitAlternation(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal:\n                return this.visitTerminal(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Rule:\n                return this.visitRule(nodeAny);\n            /* c8 ignore next 2 */\n            default:\n                throw Error(\"non exhaustive match\");\n        }\n    }\n    /* c8 ignore next */\n    visitNonTerminal(node) { }\n    /* c8 ignore next */\n    visitAlternative(node) { }\n    /* c8 ignore next */\n    visitOption(node) { }\n    /* c8 ignore next */\n    visitRepetition(node) { }\n    /* c8 ignore next */\n    visitRepetitionMandatory(node) { }\n    /* c8 ignore next 3 */\n    visitRepetitionMandatoryWithSeparator(node) { }\n    /* c8 ignore next */\n    visitRepetitionWithSeparator(node) { }\n    /* c8 ignore next */\n    visitAlternation(node) { }\n    /* c8 ignore next */\n    visitTerminal(node) { }\n    /* c8 ignore next */\n    visitRule(node) { }\n}\n//# sourceMappingURL=visitor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/visitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/api.js":
/*!***************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/api.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseRegExpVisitor: () => (/* reexport safe */ _base_regexp_visitor_js__WEBPACK_IMPORTED_MODULE_1__.BaseRegExpVisitor),\n/* harmony export */   RegExpParser: () => (/* reexport safe */ _regexp_parser_js__WEBPACK_IMPORTED_MODULE_0__.RegExpParser)\n/* harmony export */ });\n/* harmony import */ var _regexp_parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regexp-parser.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js\");\n/* harmony import */ var _base_regexp_visitor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base-regexp-visitor.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js\");\n\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vcmVnZXhwLXRvLWFzdC9saWIvc3JjL2FwaS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ1c7QUFDN0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BjaGV2cm90YWluL3JlZ2V4cC10by1hc3QvbGliL3NyYy9hcGkuanM/NzUxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBSZWdFeHBQYXJzZXIgfSBmcm9tIFwiLi9yZWdleHAtcGFyc2VyLmpzXCI7XG5leHBvcnQgeyBCYXNlUmVnRXhwVmlzaXRvciB9IGZyb20gXCIuL2Jhc2UtcmVnZXhwLXZpc2l0b3IuanNcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseRegExpVisitor: () => (/* binding */ BaseRegExpVisitor)\n/* harmony export */ });\nclass BaseRegExpVisitor {\n    visitChildren(node) {\n        for (const key in node) {\n            const child = node[key];\n            /* istanbul ignore else */\n            if (node.hasOwnProperty(key)) {\n                if (child.type !== undefined) {\n                    this.visit(child);\n                }\n                else if (Array.isArray(child)) {\n                    child.forEach((subChild) => {\n                        this.visit(subChild);\n                    }, this);\n                }\n            }\n        }\n    }\n    visit(node) {\n        switch (node.type) {\n            case \"Pattern\":\n                this.visitPattern(node);\n                break;\n            case \"Flags\":\n                this.visitFlags(node);\n                break;\n            case \"Disjunction\":\n                this.visitDisjunction(node);\n                break;\n            case \"Alternative\":\n                this.visitAlternative(node);\n                break;\n            case \"StartAnchor\":\n                this.visitStartAnchor(node);\n                break;\n            case \"EndAnchor\":\n                this.visitEndAnchor(node);\n                break;\n            case \"WordBoundary\":\n                this.visitWordBoundary(node);\n                break;\n            case \"NonWordBoundary\":\n                this.visitNonWordBoundary(node);\n                break;\n            case \"Lookahead\":\n                this.visitLookahead(node);\n                break;\n            case \"NegativeLookahead\":\n                this.visitNegativeLookahead(node);\n                break;\n            case \"Character\":\n                this.visitCharacter(node);\n                break;\n            case \"Set\":\n                this.visitSet(node);\n                break;\n            case \"Group\":\n                this.visitGroup(node);\n                break;\n            case \"GroupBackReference\":\n                this.visitGroupBackReference(node);\n                break;\n            case \"Quantifier\":\n                this.visitQuantifier(node);\n                break;\n        }\n        this.visitChildren(node);\n    }\n    visitPattern(node) { }\n    visitFlags(node) { }\n    visitDisjunction(node) { }\n    visitAlternative(node) { }\n    // Assertion\n    visitStartAnchor(node) { }\n    visitEndAnchor(node) { }\n    visitWordBoundary(node) { }\n    visitNonWordBoundary(node) { }\n    visitLookahead(node) { }\n    visitNegativeLookahead(node) { }\n    // atoms\n    visitCharacter(node) { }\n    visitSet(node) { }\n    visitGroup(node) { }\n    visitGroupBackReference(node) { }\n    visitQuantifier(node) { }\n}\n//# sourceMappingURL=base-regexp-visitor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   digitsCharCodes: () => (/* binding */ digitsCharCodes),\n/* harmony export */   whitespaceCodes: () => (/* binding */ whitespaceCodes),\n/* harmony export */   wordCharCodes: () => (/* binding */ wordCharCodes)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js\");\n\nconst digitsCharCodes = [];\nfor (let i = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"0\"); i <= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"9\"); i++) {\n    digitsCharCodes.push(i);\n}\nconst wordCharCodes = [(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"_\")].concat(digitsCharCodes);\nfor (let i = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"a\"); i <= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"z\"); i++) {\n    wordCharCodes.push(i);\n}\nfor (let i = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"A\"); i <= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"Z\"); i++) {\n    wordCharCodes.push(i);\n}\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#character-classes\nconst whitespaceCodes = [\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\" \"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\f\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\n\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\r\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\t\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\v\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\t\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u00a0\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u1680\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2000\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2001\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2002\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2003\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2004\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2005\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2006\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2007\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2008\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2009\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u200a\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2028\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2029\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u202f\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u205f\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u3000\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\ufeff\"),\n];\n//# sourceMappingURL=character-classes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegExpParser: () => (/* binding */ RegExpParser)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js\");\n/* harmony import */ var _character_classes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./character-classes.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js\");\n\n\n// consts and utilities\nconst hexDigitPattern = /[0-9a-fA-F]/;\nconst decimalPattern = /[0-9]/;\nconst decimalPatternNoZero = /[1-9]/;\n// https://hackernoon.com/the-madness-of-parsing-real-world-javascript-regexps-d9ee336df983\n// https://www.ecma-international.org/ecma-262/8.0/index.html#prod-Pattern\nclass RegExpParser {\n    constructor() {\n        this.idx = 0;\n        this.input = \"\";\n        this.groupIdx = 0;\n    }\n    saveState() {\n        return {\n            idx: this.idx,\n            input: this.input,\n            groupIdx: this.groupIdx,\n        };\n    }\n    restoreState(newState) {\n        this.idx = newState.idx;\n        this.input = newState.input;\n        this.groupIdx = newState.groupIdx;\n    }\n    pattern(input) {\n        // parser state\n        this.idx = 0;\n        this.input = input;\n        this.groupIdx = 0;\n        this.consumeChar(\"/\");\n        const value = this.disjunction();\n        this.consumeChar(\"/\");\n        const flags = {\n            type: \"Flags\",\n            loc: { begin: this.idx, end: input.length },\n            global: false,\n            ignoreCase: false,\n            multiLine: false,\n            unicode: false,\n            sticky: false,\n        };\n        while (this.isRegExpFlag()) {\n            switch (this.popChar()) {\n                case \"g\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"global\");\n                    break;\n                case \"i\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"ignoreCase\");\n                    break;\n                case \"m\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"multiLine\");\n                    break;\n                case \"u\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"unicode\");\n                    break;\n                case \"y\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"sticky\");\n                    break;\n            }\n        }\n        if (this.idx !== this.input.length) {\n            throw Error(\"Redundant input: \" + this.input.substring(this.idx));\n        }\n        return {\n            type: \"Pattern\",\n            flags: flags,\n            value: value,\n            loc: this.loc(0),\n        };\n    }\n    disjunction() {\n        const alts = [];\n        const begin = this.idx;\n        alts.push(this.alternative());\n        while (this.peekChar() === \"|\") {\n            this.consumeChar(\"|\");\n            alts.push(this.alternative());\n        }\n        return { type: \"Disjunction\", value: alts, loc: this.loc(begin) };\n    }\n    alternative() {\n        const terms = [];\n        const begin = this.idx;\n        while (this.isTerm()) {\n            terms.push(this.term());\n        }\n        return { type: \"Alternative\", value: terms, loc: this.loc(begin) };\n    }\n    term() {\n        if (this.isAssertion()) {\n            return this.assertion();\n        }\n        else {\n            return this.atom();\n        }\n    }\n    assertion() {\n        const begin = this.idx;\n        switch (this.popChar()) {\n            case \"^\":\n                return {\n                    type: \"StartAnchor\",\n                    loc: this.loc(begin),\n                };\n            case \"$\":\n                return { type: \"EndAnchor\", loc: this.loc(begin) };\n            // '\\b' or '\\B'\n            case \"\\\\\":\n                switch (this.popChar()) {\n                    case \"b\":\n                        return {\n                            type: \"WordBoundary\",\n                            loc: this.loc(begin),\n                        };\n                    case \"B\":\n                        return {\n                            type: \"NonWordBoundary\",\n                            loc: this.loc(begin),\n                        };\n                }\n                // istanbul ignore next\n                throw Error(\"Invalid Assertion Escape\");\n            // '(?=' or '(?!'\n            case \"(\":\n                this.consumeChar(\"?\");\n                let type;\n                switch (this.popChar()) {\n                    case \"=\":\n                        type = \"Lookahead\";\n                        break;\n                    case \"!\":\n                        type = \"NegativeLookahead\";\n                        break;\n                }\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(type);\n                const disjunction = this.disjunction();\n                this.consumeChar(\")\");\n                return {\n                    type: type,\n                    value: disjunction,\n                    loc: this.loc(begin),\n                };\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    quantifier(isBacktracking = false) {\n        let range = undefined;\n        const begin = this.idx;\n        switch (this.popChar()) {\n            case \"*\":\n                range = {\n                    atLeast: 0,\n                    atMost: Infinity,\n                };\n                break;\n            case \"+\":\n                range = {\n                    atLeast: 1,\n                    atMost: Infinity,\n                };\n                break;\n            case \"?\":\n                range = {\n                    atLeast: 0,\n                    atMost: 1,\n                };\n                break;\n            case \"{\":\n                const atLeast = this.integerIncludingZero();\n                switch (this.popChar()) {\n                    case \"}\":\n                        range = {\n                            atLeast: atLeast,\n                            atMost: atLeast,\n                        };\n                        break;\n                    case \",\":\n                        let atMost;\n                        if (this.isDigit()) {\n                            atMost = this.integerIncludingZero();\n                            range = {\n                                atLeast: atLeast,\n                                atMost: atMost,\n                            };\n                        }\n                        else {\n                            range = {\n                                atLeast: atLeast,\n                                atMost: Infinity,\n                            };\n                        }\n                        this.consumeChar(\"}\");\n                        break;\n                }\n                // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n                // causes severe performance degradations\n                if (isBacktracking === true && range === undefined) {\n                    return undefined;\n                }\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(range);\n                break;\n        }\n        // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n        // causes severe performance degradations\n        if (isBacktracking === true && range === undefined) {\n            return undefined;\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(range)) {\n            if (this.peekChar(0) === \"?\") {\n                this.consumeChar(\"?\");\n                range.greedy = false;\n            }\n            else {\n                range.greedy = true;\n            }\n            range.type = \"Quantifier\";\n            range.loc = this.loc(begin);\n            return range;\n        }\n    }\n    atom() {\n        let atom;\n        const begin = this.idx;\n        switch (this.peekChar()) {\n            case \".\":\n                atom = this.dotAll();\n                break;\n            case \"\\\\\":\n                atom = this.atomEscape();\n                break;\n            case \"[\":\n                atom = this.characterClass();\n                break;\n            case \"(\":\n                atom = this.group();\n                break;\n        }\n        if (atom === undefined && this.isPatternCharacter()) {\n            atom = this.patternCharacter();\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(atom)) {\n            atom.loc = this.loc(begin);\n            if (this.isQuantifier()) {\n                atom.quantifier = this.quantifier();\n            }\n            return atom;\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    dotAll() {\n        this.consumeChar(\".\");\n        return {\n            type: \"Set\",\n            complement: true,\n            value: [(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\n\"), (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\r\"), (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2028\"), (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2029\")],\n        };\n    }\n    atomEscape() {\n        this.consumeChar(\"\\\\\");\n        switch (this.peekChar()) {\n            case \"1\":\n            case \"2\":\n            case \"3\":\n            case \"4\":\n            case \"5\":\n            case \"6\":\n            case \"7\":\n            case \"8\":\n            case \"9\":\n                return this.decimalEscapeAtom();\n            case \"d\":\n            case \"D\":\n            case \"s\":\n            case \"S\":\n            case \"w\":\n            case \"W\":\n                return this.characterClassEscape();\n            case \"f\":\n            case \"n\":\n            case \"r\":\n            case \"t\":\n            case \"v\":\n                return this.controlEscapeAtom();\n            case \"c\":\n                return this.controlLetterEscapeAtom();\n            case \"0\":\n                return this.nulCharacterAtom();\n            case \"x\":\n                return this.hexEscapeSequenceAtom();\n            case \"u\":\n                return this.regExpUnicodeEscapeSequenceAtom();\n            default:\n                return this.identityEscapeAtom();\n        }\n    }\n    decimalEscapeAtom() {\n        const value = this.positiveInteger();\n        return { type: \"GroupBackReference\", value: value };\n    }\n    characterClassEscape() {\n        let set;\n        let complement = false;\n        switch (this.popChar()) {\n            case \"d\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.digitsCharCodes;\n                break;\n            case \"D\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.digitsCharCodes;\n                complement = true;\n                break;\n            case \"s\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.whitespaceCodes;\n                break;\n            case \"S\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.whitespaceCodes;\n                complement = true;\n                break;\n            case \"w\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.wordCharCodes;\n                break;\n            case \"W\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.wordCharCodes;\n                complement = true;\n                break;\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(set)) {\n            return { type: \"Set\", value: set, complement: complement };\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    controlEscapeAtom() {\n        let escapeCode;\n        switch (this.popChar()) {\n            case \"f\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\f\");\n                break;\n            case \"n\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\n\");\n                break;\n            case \"r\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\r\");\n                break;\n            case \"t\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\t\");\n                break;\n            case \"v\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\v\");\n                break;\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(escapeCode)) {\n            return { type: \"Character\", value: escapeCode };\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    controlLetterEscapeAtom() {\n        this.consumeChar(\"c\");\n        const letter = this.popChar();\n        if (/[a-zA-Z]/.test(letter) === false) {\n            throw Error(\"Invalid \");\n        }\n        const letterCode = letter.toUpperCase().charCodeAt(0) - 64;\n        return { type: \"Character\", value: letterCode };\n    }\n    nulCharacterAtom() {\n        // TODO implement '[lookahead ∉ DecimalDigit]'\n        // TODO: for the deprecated octal escape sequence\n        this.consumeChar(\"0\");\n        return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\0\") };\n    }\n    hexEscapeSequenceAtom() {\n        this.consumeChar(\"x\");\n        return this.parseHexDigits(2);\n    }\n    regExpUnicodeEscapeSequenceAtom() {\n        this.consumeChar(\"u\");\n        return this.parseHexDigits(4);\n    }\n    identityEscapeAtom() {\n        // TODO: implement \"SourceCharacter but not UnicodeIDContinue\"\n        // // http://unicode.org/reports/tr31/#Specific_Character_Adjustments\n        const escapedChar = this.popChar();\n        return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(escapedChar) };\n    }\n    classPatternCharacterAtom() {\n        switch (this.peekChar()) {\n            // istanbul ignore next\n            case \"\\n\":\n            // istanbul ignore next\n            case \"\\r\":\n            // istanbul ignore next\n            case \"\\u2028\":\n            // istanbul ignore next\n            case \"\\u2029\":\n            // istanbul ignore next\n            case \"\\\\\":\n            // istanbul ignore next\n            case \"]\":\n                throw Error(\"TBD\");\n            default:\n                const nextChar = this.popChar();\n                return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(nextChar) };\n        }\n    }\n    characterClass() {\n        const set = [];\n        let complement = false;\n        this.consumeChar(\"[\");\n        if (this.peekChar(0) === \"^\") {\n            this.consumeChar(\"^\");\n            complement = true;\n        }\n        while (this.isClassAtom()) {\n            const from = this.classAtom();\n            const isFromSingleChar = from.type === \"Character\";\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isCharacter)(from) && this.isRangeDash()) {\n                this.consumeChar(\"-\");\n                const to = this.classAtom();\n                const isToSingleChar = to.type === \"Character\";\n                // a range can only be used when both sides are single characters\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isCharacter)(to)) {\n                    if (to.value < from.value) {\n                        throw Error(\"Range out of order in character class\");\n                    }\n                    set.push({ from: from.value, to: to.value });\n                }\n                else {\n                    // literal dash\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.insertToSet)(from.value, set);\n                    set.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"-\"));\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.insertToSet)(to.value, set);\n                }\n            }\n            else {\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.insertToSet)(from.value, set);\n            }\n        }\n        this.consumeChar(\"]\");\n        return { type: \"Set\", complement: complement, value: set };\n    }\n    classAtom() {\n        switch (this.peekChar()) {\n            // istanbul ignore next\n            case \"]\":\n            // istanbul ignore next\n            case \"\\n\":\n            // istanbul ignore next\n            case \"\\r\":\n            // istanbul ignore next\n            case \"\\u2028\":\n            // istanbul ignore next\n            case \"\\u2029\":\n                throw Error(\"TBD\");\n            case \"\\\\\":\n                return this.classEscape();\n            default:\n                return this.classPatternCharacterAtom();\n        }\n    }\n    classEscape() {\n        this.consumeChar(\"\\\\\");\n        switch (this.peekChar()) {\n            // Matches a backspace.\n            // (Not to be confused with \\b word boundary outside characterClass)\n            case \"b\":\n                this.consumeChar(\"b\");\n                return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u0008\") };\n            case \"d\":\n            case \"D\":\n            case \"s\":\n            case \"S\":\n            case \"w\":\n            case \"W\":\n                return this.characterClassEscape();\n            case \"f\":\n            case \"n\":\n            case \"r\":\n            case \"t\":\n            case \"v\":\n                return this.controlEscapeAtom();\n            case \"c\":\n                return this.controlLetterEscapeAtom();\n            case \"0\":\n                return this.nulCharacterAtom();\n            case \"x\":\n                return this.hexEscapeSequenceAtom();\n            case \"u\":\n                return this.regExpUnicodeEscapeSequenceAtom();\n            default:\n                return this.identityEscapeAtom();\n        }\n    }\n    group() {\n        let capturing = true;\n        this.consumeChar(\"(\");\n        switch (this.peekChar(0)) {\n            case \"?\":\n                this.consumeChar(\"?\");\n                this.consumeChar(\":\");\n                capturing = false;\n                break;\n            default:\n                this.groupIdx++;\n                break;\n        }\n        const value = this.disjunction();\n        this.consumeChar(\")\");\n        const groupAst = {\n            type: \"Group\",\n            capturing: capturing,\n            value: value,\n        };\n        if (capturing) {\n            groupAst[\"idx\"] = this.groupIdx;\n        }\n        return groupAst;\n    }\n    positiveInteger() {\n        let number = this.popChar();\n        // istanbul ignore next - can't ever get here due to previous lookahead checks\n        // still implementing this error checking in case this ever changes.\n        if (decimalPatternNoZero.test(number) === false) {\n            throw Error(\"Expecting a positive integer\");\n        }\n        while (decimalPattern.test(this.peekChar(0))) {\n            number += this.popChar();\n        }\n        return parseInt(number, 10);\n    }\n    integerIncludingZero() {\n        let number = this.popChar();\n        if (decimalPattern.test(number) === false) {\n            throw Error(\"Expecting an integer\");\n        }\n        while (decimalPattern.test(this.peekChar(0))) {\n            number += this.popChar();\n        }\n        return parseInt(number, 10);\n    }\n    patternCharacter() {\n        const nextChar = this.popChar();\n        switch (nextChar) {\n            // istanbul ignore next\n            case \"\\n\":\n            // istanbul ignore next\n            case \"\\r\":\n            // istanbul ignore next\n            case \"\\u2028\":\n            // istanbul ignore next\n            case \"\\u2029\":\n            // istanbul ignore next\n            case \"^\":\n            // istanbul ignore next\n            case \"$\":\n            // istanbul ignore next\n            case \"\\\\\":\n            // istanbul ignore next\n            case \".\":\n            // istanbul ignore next\n            case \"*\":\n            // istanbul ignore next\n            case \"+\":\n            // istanbul ignore next\n            case \"?\":\n            // istanbul ignore next\n            case \"(\":\n            // istanbul ignore next\n            case \")\":\n            // istanbul ignore next\n            case \"[\":\n            // istanbul ignore next\n            case \"|\":\n                // istanbul ignore next\n                throw Error(\"TBD\");\n            default:\n                return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(nextChar) };\n        }\n    }\n    isRegExpFlag() {\n        switch (this.peekChar(0)) {\n            case \"g\":\n            case \"i\":\n            case \"m\":\n            case \"u\":\n            case \"y\":\n                return true;\n            default:\n                return false;\n        }\n    }\n    isRangeDash() {\n        return this.peekChar() === \"-\" && this.isClassAtom(1);\n    }\n    isDigit() {\n        return decimalPattern.test(this.peekChar(0));\n    }\n    isClassAtom(howMuch = 0) {\n        switch (this.peekChar(howMuch)) {\n            case \"]\":\n            case \"\\n\":\n            case \"\\r\":\n            case \"\\u2028\":\n            case \"\\u2029\":\n                return false;\n            default:\n                return true;\n        }\n    }\n    isTerm() {\n        return this.isAtom() || this.isAssertion();\n    }\n    isAtom() {\n        if (this.isPatternCharacter()) {\n            return true;\n        }\n        switch (this.peekChar(0)) {\n            case \".\":\n            case \"\\\\\": // atomEscape\n            case \"[\": // characterClass\n            // TODO: isAtom must be called before isAssertion - disambiguate\n            case \"(\": // group\n                return true;\n            default:\n                return false;\n        }\n    }\n    isAssertion() {\n        switch (this.peekChar(0)) {\n            case \"^\":\n            case \"$\":\n                return true;\n            // '\\b' or '\\B'\n            case \"\\\\\":\n                switch (this.peekChar(1)) {\n                    case \"b\":\n                    case \"B\":\n                        return true;\n                    default:\n                        return false;\n                }\n            // '(?=' or '(?!'\n            case \"(\":\n                return (this.peekChar(1) === \"?\" &&\n                    (this.peekChar(2) === \"=\" || this.peekChar(2) === \"!\"));\n            default:\n                return false;\n        }\n    }\n    isQuantifier() {\n        const prevState = this.saveState();\n        try {\n            return this.quantifier(true) !== undefined;\n        }\n        catch (e) {\n            return false;\n        }\n        finally {\n            this.restoreState(prevState);\n        }\n    }\n    isPatternCharacter() {\n        switch (this.peekChar()) {\n            case \"^\":\n            case \"$\":\n            case \"\\\\\":\n            case \".\":\n            case \"*\":\n            case \"+\":\n            case \"?\":\n            case \"(\":\n            case \")\":\n            case \"[\":\n            case \"|\":\n            case \"/\":\n            case \"\\n\":\n            case \"\\r\":\n            case \"\\u2028\":\n            case \"\\u2029\":\n                return false;\n            default:\n                return true;\n        }\n    }\n    parseHexDigits(howMany) {\n        let hexString = \"\";\n        for (let i = 0; i < howMany; i++) {\n            const hexChar = this.popChar();\n            if (hexDigitPattern.test(hexChar) === false) {\n                throw Error(\"Expecting a HexDecimal digits\");\n            }\n            hexString += hexChar;\n        }\n        const charCode = parseInt(hexString, 16);\n        return { type: \"Character\", value: charCode };\n    }\n    peekChar(howMuch = 0) {\n        return this.input[this.idx + howMuch];\n    }\n    popChar() {\n        const nextChar = this.peekChar(0);\n        this.consumeChar(undefined);\n        return nextChar;\n    }\n    consumeChar(char) {\n        if (char !== undefined && this.input[this.idx] !== char) {\n            throw Error(\"Expected: '\" +\n                char +\n                \"' but found: '\" +\n                this.input[this.idx] +\n                \"' at offset: \" +\n                this.idx);\n        }\n        if (this.idx >= this.input.length) {\n            throw Error(\"Unexpected end of input\");\n        }\n        this.idx++;\n    }\n    loc(begin) {\n        return { begin: begin, end: this.idx };\n    }\n}\n//# sourceMappingURL=regexp-parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ASSERT_EXISTS: () => (/* binding */ ASSERT_EXISTS),\n/* harmony export */   ASSERT_NEVER_REACH_HERE: () => (/* binding */ ASSERT_NEVER_REACH_HERE),\n/* harmony export */   addFlag: () => (/* binding */ addFlag),\n/* harmony export */   cc: () => (/* binding */ cc),\n/* harmony export */   insertToSet: () => (/* binding */ insertToSet),\n/* harmony export */   isCharacter: () => (/* binding */ isCharacter)\n/* harmony export */ });\nfunction cc(char) {\n    return char.charCodeAt(0);\n}\nfunction insertToSet(item, set) {\n    if (Array.isArray(item)) {\n        item.forEach(function (subItem) {\n            set.push(subItem);\n        });\n    }\n    else {\n        set.push(item);\n    }\n}\nfunction addFlag(flagObj, flagKey) {\n    if (flagObj[flagKey] === true) {\n        throw \"duplicate flag \" + flagKey;\n    }\n    const x = flagObj[flagKey];\n    flagObj[flagKey] = true;\n}\nfunction ASSERT_EXISTS(obj) {\n    // istanbul ignore next\n    if (obj === undefined) {\n        throw Error(\"Internal Error - Should never get here!\");\n    }\n    return true;\n}\n// istanbul ignore next\nfunction ASSERT_NEVER_REACH_HERE() {\n    throw Error(\"Internal Error - Should never get here!\");\n}\nfunction isCharacter(obj) {\n    return obj[\"type\"] === \"Character\";\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vcmVnZXhwLXRvLWFzdC9saWIvc3JjL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vcmVnZXhwLXRvLWFzdC9saWIvc3JjL3V0aWxzLmpzP2JmNmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGNjKGNoYXIpIHtcbiAgICByZXR1cm4gY2hhci5jaGFyQ29kZUF0KDApO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGluc2VydFRvU2V0KGl0ZW0sIHNldCkge1xuICAgIGlmIChBcnJheS5pc0FycmF5KGl0ZW0pKSB7XG4gICAgICAgIGl0ZW0uZm9yRWFjaChmdW5jdGlvbiAoc3ViSXRlbSkge1xuICAgICAgICAgICAgc2V0LnB1c2goc3ViSXRlbSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgc2V0LnB1c2goaXRlbSk7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIGFkZEZsYWcoZmxhZ09iaiwgZmxhZ0tleSkge1xuICAgIGlmIChmbGFnT2JqW2ZsYWdLZXldID09PSB0cnVlKSB7XG4gICAgICAgIHRocm93IFwiZHVwbGljYXRlIGZsYWcgXCIgKyBmbGFnS2V5O1xuICAgIH1cbiAgICBjb25zdCB4ID0gZmxhZ09ialtmbGFnS2V5XTtcbiAgICBmbGFnT2JqW2ZsYWdLZXldID0gdHJ1ZTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBBU1NFUlRfRVhJU1RTKG9iaikge1xuICAgIC8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG4gICAgaWYgKG9iaiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHRocm93IEVycm9yKFwiSW50ZXJuYWwgRXJyb3IgLSBTaG91bGQgbmV2ZXIgZ2V0IGhlcmUhXCIpO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5leHBvcnQgZnVuY3Rpb24gQVNTRVJUX05FVkVSX1JFQUNIX0hFUkUoKSB7XG4gICAgdGhyb3cgRXJyb3IoXCJJbnRlcm5hbCBFcnJvciAtIFNob3VsZCBuZXZlciBnZXQgaGVyZSFcIik7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNDaGFyYWN0ZXIob2JqKSB7XG4gICAgcmV0dXJuIG9ialtcInR5cGVcIl0gPT09IFwiQ2hhcmFjdGVyXCI7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/api.js":
/*!*******************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/api.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRINT_ERROR: () => (/* reexport safe */ _print_js__WEBPACK_IMPORTED_MODULE_0__.PRINT_ERROR),\n/* harmony export */   PRINT_WARNING: () => (/* reexport safe */ _print_js__WEBPACK_IMPORTED_MODULE_0__.PRINT_WARNING),\n/* harmony export */   timer: () => (/* reexport safe */ _timer_js__WEBPACK_IMPORTED_MODULE_1__.timer),\n/* harmony export */   toFastProperties: () => (/* reexport safe */ _to_fast_properties_js__WEBPACK_IMPORTED_MODULE_2__.toFastProperties)\n/* harmony export */ });\n/* harmony import */ var _print_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./print.js */ \"(ssr)/./node_modules/@chevrotain/utils/lib/src/print.js\");\n/* harmony import */ var _timer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timer.js */ \"(ssr)/./node_modules/@chevrotain/utils/lib/src/timer.js\");\n/* harmony import */ var _to_fast_properties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./to-fast-properties.js */ \"(ssr)/./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js\");\n\n\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RDtBQUNyQjtBQUN3QjtBQUMzRCIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy9hcGkuanM/OGYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBQUklOVF9XQVJOSU5HLCBQUklOVF9FUlJPUiB9IGZyb20gXCIuL3ByaW50LmpzXCI7XG5leHBvcnQgeyB0aW1lciB9IGZyb20gXCIuL3RpbWVyLmpzXCI7XG5leHBvcnQgeyB0b0Zhc3RQcm9wZXJ0aWVzIH0gZnJvbSBcIi4vdG8tZmFzdC1wcm9wZXJ0aWVzLmpzXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/print.js":
/*!*********************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/print.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRINT_ERROR: () => (/* binding */ PRINT_ERROR),\n/* harmony export */   PRINT_WARNING: () => (/* binding */ PRINT_WARNING)\n/* harmony export */ });\nfunction PRINT_ERROR(msg) {\n    /* istanbul ignore else - can't override global.console in node.js */\n    if (console && console.error) {\n        console.error(`Error: ${msg}`);\n    }\n}\nfunction PRINT_WARNING(msg) {\n    /* istanbul ignore else - can't override global.console in node.js*/\n    if (console && console.warn) {\n        // TODO: modify docs accordingly\n        console.warn(`Warning: ${msg}`);\n    }\n}\n//# sourceMappingURL=print.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy9wcmludC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLGdDQUFnQyxJQUFJO0FBQ3BDO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxJQUFJO0FBQ3JDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy9wcmludC5qcz8zMWRiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBQUklOVF9FUlJPUihtc2cpIHtcbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAtIGNhbid0IG92ZXJyaWRlIGdsb2JhbC5jb25zb2xlIGluIG5vZGUuanMgKi9cbiAgICBpZiAoY29uc29sZSAmJiBjb25zb2xlLmVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yOiAke21zZ31gKTtcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gUFJJTlRfV0FSTklORyhtc2cpIHtcbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAtIGNhbid0IG92ZXJyaWRlIGdsb2JhbC5jb25zb2xlIGluIG5vZGUuanMqL1xuICAgIGlmIChjb25zb2xlICYmIGNvbnNvbGUud2Fybikge1xuICAgICAgICAvLyBUT0RPOiBtb2RpZnkgZG9jcyBhY2NvcmRpbmdseVxuICAgICAgICBjb25zb2xlLndhcm4oYFdhcm5pbmc6ICR7bXNnfWApO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByaW50LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/print.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/timer.js":
/*!*********************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/timer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timer: () => (/* binding */ timer)\n/* harmony export */ });\nfunction timer(func) {\n    const start = new Date().getTime();\n    const val = func();\n    const end = new Date().getTime();\n    const total = end - start;\n    return { time: total, value: val };\n}\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy90aW1lci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9AY2hldnJvdGFpbi91dGlscy9saWIvc3JjL3RpbWVyLmpzP2Q0YjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHRpbWVyKGZ1bmMpIHtcbiAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkuZ2V0VGltZSgpO1xuICAgIGNvbnN0IHZhbCA9IGZ1bmMoKTtcbiAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTtcbiAgICBjb25zdCB0b3RhbCA9IGVuZCAtIHN0YXJ0O1xuICAgIHJldHVybiB7IHRpbWU6IHRvdGFsLCB2YWx1ZTogdmFsIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD10aW1lci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/timer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toFastProperties: () => (/* binding */ toFastProperties)\n/* harmony export */ });\n// based on: https://github.com/petkaantonov/bluebird/blob/b97c0d2d487e8c5076e8bd897e0dcd4622d31846/src/util.js#L201-L216\nfunction toFastProperties(toBecomeFast) {\n    function FakeConstructor() { }\n    // If our object is used as a constructor, it would receive\n    FakeConstructor.prototype = toBecomeFast;\n    const fakeInstance = new FakeConstructor();\n    function fakeAccess() {\n        return typeof fakeInstance.bar;\n    }\n    // help V8 understand this is a \"real\" prototype by actually using\n    // the fake instance.\n    fakeAccess();\n    fakeAccess();\n    // Always true condition to suppress the Firefox warning of unreachable\n    // code after a return statement.\n    if (true)\n        return toBecomeFast;\n    // Eval prevents optimization of this method (even though this is dead code)\n    // - https://esbuild.github.io/content-types/#direct-eval\n    /* istanbul ignore next */\n    // tslint:disable-next-line\n    (0, eval)(toBecomeFast);\n}\n//# sourceMappingURL=to-fast-properties.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy90by1mYXN0LXByb3BlcnRpZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsSUFBQztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BjaGV2cm90YWluL3V0aWxzL2xpYi9zcmMvdG8tZmFzdC1wcm9wZXJ0aWVzLmpzP2RhZTgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gYmFzZWQgb246IGh0dHBzOi8vZ2l0aHViLmNvbS9wZXRrYWFudG9ub3YvYmx1ZWJpcmQvYmxvYi9iOTdjMGQyZDQ4N2U4YzUwNzZlOGJkODk3ZTBkY2Q0NjIyZDMxODQ2L3NyYy91dGlsLmpzI0wyMDEtTDIxNlxuZXhwb3J0IGZ1bmN0aW9uIHRvRmFzdFByb3BlcnRpZXModG9CZWNvbWVGYXN0KSB7XG4gICAgZnVuY3Rpb24gRmFrZUNvbnN0cnVjdG9yKCkgeyB9XG4gICAgLy8gSWYgb3VyIG9iamVjdCBpcyB1c2VkIGFzIGEgY29uc3RydWN0b3IsIGl0IHdvdWxkIHJlY2VpdmVcbiAgICBGYWtlQ29uc3RydWN0b3IucHJvdG90eXBlID0gdG9CZWNvbWVGYXN0O1xuICAgIGNvbnN0IGZha2VJbnN0YW5jZSA9IG5ldyBGYWtlQ29uc3RydWN0b3IoKTtcbiAgICBmdW5jdGlvbiBmYWtlQWNjZXNzKCkge1xuICAgICAgICByZXR1cm4gdHlwZW9mIGZha2VJbnN0YW5jZS5iYXI7XG4gICAgfVxuICAgIC8vIGhlbHAgVjggdW5kZXJzdGFuZCB0aGlzIGlzIGEgXCJyZWFsXCIgcHJvdG90eXBlIGJ5IGFjdHVhbGx5IHVzaW5nXG4gICAgLy8gdGhlIGZha2UgaW5zdGFuY2UuXG4gICAgZmFrZUFjY2VzcygpO1xuICAgIGZha2VBY2Nlc3MoKTtcbiAgICAvLyBBbHdheXMgdHJ1ZSBjb25kaXRpb24gdG8gc3VwcHJlc3MgdGhlIEZpcmVmb3ggd2FybmluZyBvZiB1bnJlYWNoYWJsZVxuICAgIC8vIGNvZGUgYWZ0ZXIgYSByZXR1cm4gc3RhdGVtZW50LlxuICAgIGlmICgxKVxuICAgICAgICByZXR1cm4gdG9CZWNvbWVGYXN0O1xuICAgIC8vIEV2YWwgcHJldmVudHMgb3B0aW1pemF0aW9uIG9mIHRoaXMgbWV0aG9kIChldmVuIHRob3VnaCB0aGlzIGlzIGRlYWQgY29kZSlcbiAgICAvLyAtIGh0dHBzOi8vZXNidWlsZC5naXRodWIuaW8vY29udGVudC10eXBlcy8jZGlyZWN0LWV2YWxcbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICAgIC8vIHRzbGludDpkaXNhYmxlLW5leHQtbGluZVxuICAgICgwLCBldmFsKSh0b0JlY29tZUZhc3QpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dG8tZmFzdC1wcm9wZXJ0aWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js\n");

/***/ })

};
;