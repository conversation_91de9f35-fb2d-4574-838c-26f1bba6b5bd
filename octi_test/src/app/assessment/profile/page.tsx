/**
 * OCTI智能评估系统 - 公益机构画像收集页面
 *
 * 通过10道选择题收集组织画像信息
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { UserLayout, UserPageContainer, StepIndicator } from '@/components/layouts/user-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle2, Circle } from 'lucide-react';

// ============================================================================
// 类型定义
// ============================================================================

interface QuestionOption {
  value: string;
  label: string;
  description: string;
}

interface ProfileQuestion {
  id: number;
  title: string;
  description: string;
  options: QuestionOption[];
}

interface ProfileAnswers {
  [questionId: number]: string;
}

// ============================================================================
// 10道公益机构画像题目数据
// ============================================================================

const profileQuestions: ProfileQuestion[] = [
  {
    id: 1,
    title: "组织发展阶段与团队规模",
    description: "描述您的组织目前的发展状态和团队构成：",
    options: [
      {
        value: "A",
        label: "初创期",
        description: "1-2年，创始人主导，核心团队3-5人，主要靠热情驱动"
      },
      {
        value: "B",
        label: "探索期",
        description: "2-3年，项目初具规模，团队5-10人，寻找稳定模式"
      },
      {
        value: "C",
        label: "成长期",
        description: "3-5年，有固定项目运作，团队10-20人，寻求扩大影响"
      },
      {
        value: "D",
        label: "成熟期",
        description: "5年以上，多项目运作，团队20人以上，行业有知名度"
      },
      {
        value: "E",
        label: "转型期",
        description: "成立多年，知名机构重新定位，或正在战略调整"
      },
      {
        value: "F",
        label: "分化期",
        description: "从其他组织独立，具备一定基础但需重新整合资源"
      }
    ]
  },
  {
    id: 2,
    title: "核心业务模式与价值创造",
    description: "描述您组织创造社会价值的主要方式：",
    options: [
      {
        value: "A",
        label: "直接服务型",
        description: "为特定群体提供教育、医疗、救助等直接服务"
      },
      {
        value: "B",
        label: "倡导推动型",
        description: "通过研究、政策倡导推动制度或观念改变"
      },
      {
        value: "C",
        label: "平台连接型",
        description: "搭建合作平台，连接资源，促进多方协作"
      },
      {
        value: "D",
        label: "能力建设型",
        description: "为其他组织或个人提供培训、咨询、赋能服务"
      },
      {
        value: "E",
        label: "资源配置型",
        description: "从事资金募集、物资调配和资源分配"
      },
      {
        value: "F",
        label: "创新孵化型",
        description: "探索新模式、新技术解决社会问题的创新方案"
      }
    ]
  },
  {
    id: 3,
    title: "资源获取与资金结构",
    description: "您的组织主要通过哪些渠道获得运营资源：",
    options: [
      {
        value: "A",
        label: "政府主导型",
        description: "主要依靠政府购买服务、财政资助或政府项目"
      },
      {
        value: "B",
        label: "基金会支持型",
        description: "主要依靠基金会资助和大型项目资金"
      },
      {
        value: "C",
        label: "公众募捐型",
        description: "通过公开募捐、会员费、个人小额捐赠"
      },
      {
        value: "D",
        label: "企业合作型",
        description: "与企业建立CSR合作关系或商业合作模式"
      },
      {
        value: "E",
        label: "混合收入型",
        description: "多元化资源来源，各种方式均有涉及"
      },
      {
        value: "F",
        label: "自造血型",
        description: "具备稳定的服务收费或社会企业收入模式"
      }
    ]
  },
  {
    id: 4,
    title: "治理结构与决策机制",
    description: "描述您组织的治理结构和决策特点：",
    options: [
      {
        value: "A",
        label: "创始人中心制",
        description: "主要由创始人或核心领导者决策，执行效率高"
      },
      {
        value: "B",
        label: "理事会监督制",
        description: "设有理事会，但日常决策主要由执行团队负责"
      },
      {
        value: "C",
        label: "集体决策制",
        description: "建立完善治理结构，重大决策通过集体讨论决定"
      },
      {
        value: "D",
        label: "扁平化管理制",
        description: "采用扁平化管理，团队成员共同参与各项决策"
      },
      {
        value: "E",
        label: "专业管理制",
        description: "引入现代企业管理模式，分工明确、职责清晰"
      },
      {
        value: "F",
        label: "制度建设期",
        description: "正在完善管理制度，决策方式还在探索优化中"
      }
    ]
  },
  {
    id: 5,
    title: "目标客户与服务对象结构",
    description: "描述您组织服务的客户结构特点：",
    options: [
      {
        value: "A",
        label: "单一主客户",
        description: "直接服务对象即为主要客户，关系相对简单"
      },
      {
        value: "B",
        label: "主辅客户分离",
        description: "服务对象和付费方分离，需平衡双方需求"
      },
      {
        value: "C",
        label: "多元客户结构",
        description: "存在受益人、决策者、影响者等多重客户角色"
      },
      {
        value: "D",
        label: "代理客户模式",
        description: "服务无自主意识群体（如儿童、动物），需通过责任人"
      },
      {
        value: "E",
        label: "生态系统服务",
        description: "服务整个行业或生态系统，客户边界相对模糊"
      },
      {
        value: "F",
        label: "平台型客户",
        description: "同时服务供需双方，扮演中介平台角色"
      }
    ]
  },
  {
    id: 6,
    title: "专业化程度与战略聚焦度",
    description: "描述您组织的专业化特征和战略定位：",
    options: [
      {
        value: "A",
        label: "垂直深耕型",
        description: "专注单一领域深度耕耘，具备专业影响力和话语权"
      },
      {
        value: "B",
        label: "水平整合型",
        description: "围绕核心使命，在相关多个领域开展综合性项目"
      },
      {
        value: "C",
        label: "机会导向型",
        description: "根据资源状况和外部机会，灵活调整业务方向"
      },
      {
        value: "D",
        label: "战略聚焦型",
        description: "正在明确核心定位，逐步聚焦主营业务领域"
      },
      {
        value: "E",
        label: "创新探索型",
        description: "具有创新属性，探索新解决方案和业务模式"
      },
      {
        value: "F",
        label: "生态构建型",
        description: "致力于构建行业生态，扮演平台或枢纽角色"
      }
    ]
  },
  {
    id: 7,
    title: "能力建设与人才发展",
    description: "描述您组织在能力建设和人才发展方面的特点：",
    options: [
      {
        value: "A",
        label: "个人能力依赖",
        description: "主要依靠核心人员的个人能力和经验"
      },
      {
        value: "B",
        label: "团队协作驱动",
        description: "注重团队建设，通过集体智慧解决问题"
      },
      {
        value: "C",
        label: "系统能力建设",
        description: "建立培训体系，有计划地提升组织整体能力"
      },
      {
        value: "D",
        label: "外部智力引入",
        description: "通过顾问、志愿专家等方式获得专业支持"
      },
      {
        value: "E",
        label: "学习型组织",
        description: "建立学习机制，持续学习和改进工作方法"
      },
      {
        value: "F",
        label: "能力输出型",
        description: "不仅自身能力强，还能向其他组织输出能力"
      }
    ]
  },
  {
    id: 8,
    title: "品牌建设与社会影响力",
    description: "描述您组织的品牌特征和社会认知度：",
    options: [
      {
        value: "A",
        label: "默默耕耘型",
        description: "专注做事，较少对外宣传，知名度相对较低"
      },
      {
        value: "B",
        label: "专业口碑型",
        description: "在专业领域有良好口碑，但公众知名度一般"
      },
      {
        value: "C",
        label: "区域知名型",
        description: "在特定地区或社群中有较高知名度和影响力"
      },
      {
        value: "D",
        label: "行业领军型",
        description: "在所属行业具备一定的领军地位和话语权"
      },
      {
        value: "E",
        label: "媒体活跃型",
        description: "善于传播，经常出现在媒体报道中"
      },
      {
        value: "F",
        label: "品牌建设型",
        description: "有意识进行品牌建设，注重形象和传播策略"
      }
    ]
  },
  {
    id: 9,
    title: "创新能力与适应性",
    description: "描述您组织面对变化和创新的态度与能力：",
    options: [
      {
        value: "A",
        label: "传统稳健型",
        description: "坚持既定模式，变化谨慎，注重稳定性"
      },
      {
        value: "B",
        label: "渐进改良型",
        description: "在现有基础上持续优化和改进工作方式"
      },
      {
        value: "C",
        label: "快速响应型",
        description: "能够快速响应外部变化，及时调整策略"
      },
      {
        value: "D",
        label: "主动创新型",
        description: "主动探索新方法、新技术、新模式"
      },
      {
        value: "E",
        label: "颠覆式创新",
        description: "敢于突破传统，尝试根本性的模式创新"
      },
      {
        value: "F",
        label: "技术驱动型",
        description: "积极拥抱新技术，用技术提升工作效率"
      }
    ]
  },
  {
    id: 10,
    title: "可持续发展与长期愿景",
    description: "描述您组织对可持续发展的规划和长期愿景：",
    options: [
      {
        value: "A",
        label: "生存导向",
        description: "主要关注组织生存，暂时较少考虑长远发展"
      },
      {
        value: "B",
        label: "项目导向",
        description: "以项目为中心，根据项目周期规划发展"
      },
      {
        value: "C",
        label: "机构导向",
        description: "注重机构可持续发展，有中长期发展规划"
      },
      {
        value: "D",
        label: "影响导向",
        description: "以社会影响最大化为目标，愿景相对清晰"
      },
      {
        value: "E",
        label: "生态导向",
        description: "考虑在更大生态系统中的作用和长期价值"
      },
      {
        value: "F",
        label: "使命导向",
        description: "有明确使命驱动，长期愿景清晰且坚定"
      }
    ]
  }
];

// ============================================================================
// 公益机构画像收集页面组件
// ============================================================================

export default function NonprofitProfilePage() {
  const router = useRouter();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<ProfileAnswers>({});
  const [isCompleted, setIsCompleted] = useState(false);

  // 计算进度
  const progress = (Object.keys(answers).length / profileQuestions.length) * 100;
  const answeredCount = Object.keys(answers).length;

  // 选择答案
  const handleSelectAnswer = (questionId: number, value: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // 下一题
  const handleNext = () => {
    if (currentQuestion < profileQuestions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  // 上一题
  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  // 完成问卷
  const handleComplete = () => {
    if (answeredCount === profileQuestions.length) {
      setIsCompleted(true);
      // 保存答案到本地存储或发送到服务器
      localStorage.setItem('profileAnswers', JSON.stringify(answers));
    }
  };

  // 跳转到能力评估
  const handleContinue = () => {
    router.push('/assessment/questionnaire/assessment-1');
  };

  const currentQ = profileQuestions[currentQuestion];
  const currentAnswer = answers[currentQ?.id];

  return (
    <UserLayout
      showBackButton
      backHref="/assessment/start"
      title="组织画像收集"
      subtitle="通过10道选择题深度了解您的组织特征，为您生成个性化评估问卷"
    >
      <UserPageContainer>
        {/* 步骤指示器 */}
        <StepIndicator
          currentStep={1}
          totalSteps={4}
          steps={['组织画像', '能力评估', '结果分析', '发展建议']}
        />

        <div className="max-w-4xl mx-auto">
          {!isCompleted ? (
            <div className="space-y-6">
              {/* 进度条 */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>已完成 {answeredCount} / {profileQuestions.length} 题</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* 当前题目 */}
              {currentQ && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        问题 {currentQ.id} / {profileQuestions.length}
                      </CardTitle>
                      <div className="text-sm text-muted-foreground">
                        {currentAnswer ? (
                          <div className="flex items-center text-green-600">
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            已回答
                          </div>
                        ) : (
                          <div className="flex items-center text-gray-400">
                            <Circle className="h-4 w-4 mr-1" />
                            未回答
                          </div>
                        )}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold">{currentQ.title}</h3>
                    <p className="text-muted-foreground">{currentQ.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {currentQ.options.map((option) => (
                      <div
                        key={option.value}
                        className={`p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/50 ${
                          currentAnswer === option.value
                            ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                            : 'border-gray-200'
                        }`}
                        onClick={() => handleSelectAnswer(currentQ.id, option.value)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mt-0.5 ${
                            currentAnswer === option.value
                              ? 'border-primary bg-primary'
                              : 'border-gray-300'
                          }`}>
                            {currentAnswer === option.value && (
                              <div className="w-2 h-2 bg-white rounded-full" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-primary">{option.value}.</span>
                              <span className="font-semibold">{option.label}</span>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {option.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* 导航按钮 */}
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentQuestion === 0}
                >
                  上一题
                </Button>

                <div className="flex space-x-2">
                  {currentQuestion < profileQuestions.length - 1 ? (
                    <Button
                      onClick={handleNext}
                      disabled={!currentAnswer}
                    >
                      下一题
                    </Button>
                  ) : (
                    <Button
                      onClick={handleComplete}
                      disabled={answeredCount < profileQuestions.length}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      完成画像收集
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ) : (
            // 完成状态
            <div className="text-center space-y-6">
              <div className="text-6xl mb-4">🎉</div>
              <div>
                <h2 className="text-2xl font-bold mb-2">画像收集完成！</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  感谢您完成组织画像问卷！我们已经成功收集到您的组织特征信息。
                  现在将基于您的回答为您生成个性化的能力评估问卷。
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-center space-x-3">
                  <CheckCircle2 className="h-6 w-6 text-green-600" />
                  <div className="text-left">
                    <h3 className="font-semibold text-green-800">已完成 {answeredCount} 道题目</h3>
                    <p className="text-sm text-green-600">
                      系统将根据您的回答生成专属的能力评估问卷
                    </p>
                  </div>
                </div>
              </div>

              <Button size="lg" onClick={handleContinue} className="bg-primary hover:bg-primary/90">
                继续下一步
              </Button>
            </div>
          )}
        </div>
      </UserPageContainer>
    </UserLayout>
  );
}
