'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { UserLayout, UserPageContainer, StepIndicator } from '@/components/layouts/user-layout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Share2, Loader2, Brain } from 'lucide-react';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

// 标准版分析结果接口
interface StandardAnalysisResult {
  organizationType: {
    code: string;
    name: string;
    description: string;
    characteristics: string[];
    strengths: string[];
    challenges: string[];
  };
  detailedAnalysis: string;
  timestamp: string;
  version: string;
}

/**
 * 标准版评估结果页面
 */
export default function StandardResultsPage() {
  const [analysisResult, setAnalysisResult] = useState<StandardAnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);



  // 获取分析结果
  useEffect(() => {
    const fetchAnalysisResult = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 优先从localStorage获取数据
        let profileData = localStorage.getItem('profileAnswers');
        let responsesData = localStorage.getItem('assessmentResponses');

        // 如果本地没有数据，尝试从服务器恢复
        if (!profileData || !responsesData) {
          console.log('🔄 本地数据缺失，尝试从服务器恢复...');
          const sessionId = localStorage.getItem('assessmentSessionId') || 'default';

          try {
            const response = await fetch(`/api/assessment/responses?sessionId=${sessionId}`);
            if (response.ok) {
              const serverData = await response.json();
              if (serverData.success && serverData.data) {
                profileData = JSON.stringify(serverData.data.profile);
                responsesData = JSON.stringify(serverData.data.responses);
                console.log('✅ 从服务器恢复数据成功');

                // 更新本地存储
                localStorage.setItem('profileAnswers', profileData);
                localStorage.setItem('assessmentResponses', responsesData);
              }
            }
          } catch (serverError) {
            console.warn('⚠️ 服务器数据恢复失败:', serverError);
          }
        }

        if (!profileData || !responsesData) {
          throw new Error('缺少评估数据，请重新完成评估');
        }

        const profile = JSON.parse(profileData);
        const responses = JSON.parse(responsesData);

        console.log('📊 开始请求标准版分析结果...');

        // 调用标准版分析API
        const response = await fetch('/api/assessment/simple-analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            profile,
            responses,
            version: 'standard'
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '分析服务暂时不可用');
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || '分析失败');
        }

        setAnalysisResult(result.data);
        console.log('✅ 标准版分析结果获取成功:', result.data);

      } catch (err) {
        console.error('❌ 获取分析结果失败:', err);
        setError(err instanceof Error ? err.message : '获取分析结果失败');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalysisResult();
  }, []);

  // 下载PDF报告功能（使用浏览器打印功能）
  const handleDownloadReport = async () => {
    if (!analysisResult) return;

    setIsDownloading(true);
    try {
      // 生成HTML内容
      const htmlContent = generateHTMLReport(analysisResult);

      // 创建新窗口并打印
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(htmlContent);
        printWindow.document.close();

        // 等待内容加载完成后打印
        printWindow.onload = () => {
          printWindow.print();
          printWindow.close();
        };
      }

      console.log('✅ PDF报告打印窗口已打开');
    } catch (error) {
      console.error('❌ PDF报告生成失败:', error);
      alert('报告生成失败，请重试');
    } finally {
      setIsDownloading(false);
    }
  };

  // 生成HTML报告内容
  const generateHTMLReport = (result: StandardAnalysisResult): string => {
    const timestamp = new Date().toLocaleString();

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCTI组织能力评估报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            color: #2563eb;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 10px;
        }
        .info-section {
            margin-bottom: 25px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            border-left: 4px solid #2563eb;
            padding-left: 10px;
        }
        .content {
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        @media print {
            body { margin: 20px; }
            .header { page-break-after: avoid; }
            .section-title { page-break-after: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        OCTI组织能力评估报告
    </div>

    <div class="info-section">
        <strong>生成时间：</strong>${timestamp}<br>
        <strong>组织类型：</strong>${result.organizationType.name} (${result.organizationType.code})
    </div>

    <div class="content">
        <div class="section-title">组织类型描述</div>
        <p>${result.organizationType.description}</p>
    </div>

    <div class="content">
        <div class="section-title">组织特征</div>
        <ul>
            ${result.organizationType.characteristics.map(item => `<li>${item}</li>`).join('')}
        </ul>
    </div>

    <div class="content">
        <div class="section-title">组织优势</div>
        <ul>
            ${result.organizationType.strengths.map(item => `<li>${item}</li>`).join('')}
        </ul>
    </div>

    <div class="content">
        <div class="section-title">面临挑战</div>
        <ul>
            ${result.organizationType.challenges.map(item => `<li>${item}</li>`).join('')}
        </ul>
    </div>

    <div class="content">
        <div class="section-title">详细分析</div>
        <p>${result.detailedAnalysis}</p>
    </div>

    <div class="footer">
        报告由OCTI智能评估系统生成 | 版本：${result.version} | 生成时间：${timestamp}
    </div>
</body>
</html>
    `.trim();
  };

  // 加载状态
  if (isLoading) {
    return (
      <UserLayout
        showBackButton
        backHref="/assessment/questionnaire/assessment-1"
        title="标准版分析结果"
        subtitle="AI正在为您生成专业的组织能力分析报告..."
      >
        <UserPageContainer>
          <div className="flex items-center justify-center py-20">
            <div className="text-center space-y-4">
              <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">组织能力评估师正在分析您的数据</h3>
                <p className="text-muted-foreground">
                  这可能需要30-60秒，请耐心等待...
                </p>
              </div>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  // 错误状态
  if (error) {
    return (
      <UserLayout
        showBackButton
        backHref="/assessment/questionnaire/assessment-1"
        title="标准版分析结果"
        subtitle="获取分析结果时出现问题"
      >
        <UserPageContainer>
          <div className="text-center py-20">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold mb-2">分析失败</h3>
            <p className="text-muted-foreground mb-6">{error}</p>
            <div className="space-x-4">
              <Button onClick={() => window.location.reload()}>
                重试
              </Button>
              <Link href="/assessment/start">
                <Button variant="outline">重新开始评估</Button>
              </Link>
            </div>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  // 没有数据
  if (!analysisResult) {
    return (
      <UserLayout
        showBackButton
        backHref="/assessment/start"
        title="标准版分析结果"
        subtitle="没有找到分析结果"
      >
        <UserPageContainer>
          <div className="text-center py-20">
            <div className="text-gray-400 text-6xl mb-4">📊</div>
            <h3 className="text-lg font-semibold mb-2">暂无分析结果</h3>
            <p className="text-muted-foreground mb-6">请先完成组织画像和能力评估问卷</p>
            <Link href="/assessment/start">
              <Button>开始评估</Button>
            </Link>
          </div>
        </UserPageContainer>
      </UserLayout>
    );
  }

  return (
    <UserLayout 
      showBackButton 
      backHref="/assessment/questionnaire/assessment-1"
      title="标准版分析结果"
      subtitle="恭喜您完成评估！以下是您的组织能力分析报告"
    >
      <UserPageContainer>
        {/* 版本标识 */}
        <div className="mb-6">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold border border-blue-200">
            <Brain className="h-4 w-4 mr-2" />
            标准版分析 - 组织能力评估师
          </div>
        </div>

        {/* 步骤指示器 */}
        <StepIndicator 
          currentStep={4}
          totalSteps={4}
          steps={['组织信息', '能力评估', '结果分析', '发展建议']}
        />

        {/* 组织类型判断结果 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">组织类型识别</h2>
          
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-blue-600 font-bold text-lg">{analysisResult.organizationType.code}</span>
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">{analysisResult.organizationType.name}</h3>
                <p className="text-gray-600">{analysisResult.organizationType.description}</p>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">核心优势</h4>
                <ul className="space-y-1">
                  {analysisResult.organizationType.strengths.map((strength, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-green-500 mr-2">•</span>
                      {strength}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">发展挑战</h4>
                <ul className="space-y-1">
                  {analysisResult.organizationType.challenges.map((challenge, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-orange-500 mr-2">•</span>
                      {challenge}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 详细分析报告 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">AI详细分析报告</h2>
          <div className="prose max-w-none">
            <MarkdownRenderer
              content={analysisResult.detailedAnalysis}
              className="text-gray-700"
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
          <div className="flex justify-center space-x-4 mb-6">
            <Button
              className="flex items-center space-x-2"
              onClick={handleDownloadReport}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>{isDownloading ? '下载中...' : '下载报告'}</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2">
              <Share2 className="h-4 w-4" />
              <span>分享结果</span>
            </Button>
          </div>

          <p className="text-gray-600 text-sm">
            完成时间：{new Date(analysisResult.timestamp).toLocaleString('zh-CN')}
          </p>

          {/* 升级提示 */}
          <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
            <h3 className="font-semibold text-gray-900 mb-2">想要更深入的分析？</h3>
            <p className="text-sm text-gray-600 mb-3">
              专业版提供DeepSeek深度推理分析，获得更全面的战略洞察和发展建议
            </p>
            <Link href="/assessment/start?version=professional">
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                升级到专业版
              </Button>
            </Link>
          </div>
        </div>
      </UserPageContainer>
    </UserLayout>
  );
}
