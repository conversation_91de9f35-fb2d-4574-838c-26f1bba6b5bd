"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/questionnaire/generate-background/route";
exports.ids = ["app/api/questionnaire/generate-background/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&page=%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&page=%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_questionnaire_generate_background_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/questionnaire/generate-background/route.ts */ \"(rsc)/./src/app/api/questionnaire/generate-background/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/questionnaire/generate-background/route\",\n        pathname: \"/api/questionnaire/generate-background\",\n        filename: \"route\",\n        bundlePath: \"app/api/questionnaire/generate-background/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/questionnaire/generate-background/route.ts\",\n    nextConfigOutput,\n    userland: _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_questionnaire_generate_background_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/questionnaire/generate-background/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZxdWVzdGlvbm5haXJlJTJGZ2VuZXJhdGUtYmFja2dyb3VuZCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGcXVlc3Rpb25uYWlyZSUyRmdlbmVyYXRlLWJhY2tncm91bmQlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZxdWVzdGlvbm5haXJlJTJGZ2VuZXJhdGUtYmFja2dyb3VuZCUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRmFwcGxlJTJGRG9jdW1lbnRzJTJGMi4xJTIwQUklMjBKb3VybmV5JTJGQ3Vyc29yX3Byb2plY3RzJTJGb2N0aV90ZXN0JTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz1qcyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9bWQmcGFnZUV4dGVuc2lvbnM9bWR4JnJvb3REaXI9JTJGVXNlcnMlMkZhcHBsZSUyRkRvY3VtZW50cyUyRjIuMSUyMEFJJTIwSm91cm5leSUyRkN1cnNvcl9wcm9qZWN0cyUyRm9jdGlfdGVzdCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD1zdGFuZGFsb25lJnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ3NFO0FBQ25KO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWlFO0FBQ3pFO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDdUg7O0FBRXZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8/M2YwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIvVXNlcnMvYXBwbGUvRG9jdW1lbnRzLzIuMSBBSSBKb3VybmV5L0N1cnNvcl9wcm9qZWN0cy9vY3RpX3Rlc3Qvc3JjL2FwcC9hcGkvcXVlc3Rpb25uYWlyZS9nZW5lcmF0ZS1iYWNrZ3JvdW5kL3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcInN0YW5kYWxvbmVcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvcXVlc3Rpb25uYWlyZS9nZW5lcmF0ZS1iYWNrZ3JvdW5kL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcXVlc3Rpb25uYWlyZS9nZW5lcmF0ZS1iYWNrZ3JvdW5kXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9xdWVzdGlvbm5haXJlL2dlbmVyYXRlLWJhY2tncm91bmQvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvYXBwbGUvRG9jdW1lbnRzLzIuMSBBSSBKb3VybmV5L0N1cnNvcl9wcm9qZWN0cy9vY3RpX3Rlc3Qvc3JjL2FwcC9hcGkvcXVlc3Rpb25uYWlyZS9nZW5lcmF0ZS1iYWNrZ3JvdW5kL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9xdWVzdGlvbm5haXJlL2dlbmVyYXRlLWJhY2tncm91bmQvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&page=%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/questionnaire/generate-background/route.ts":
/*!****************************************************************!*\
  !*** ./src/app/api/questionnaire/generate-background/route.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_intelligent_question_generator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/intelligent-question-generator */ \"(rsc)/./src/services/intelligent-question-generator.ts\");\n/* harmony import */ var _services_analysis_profile_transformer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/analysis/profile-transformer */ \"(rsc)/./src/services/analysis/profile-transformer.ts\");\n/* harmony import */ var _services_llm_llm_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/llm/llm-client */ \"(rsc)/./src/services/llm/llm-client.ts\");\n/**\n * 后台智能问题生成API路由\n * 用于在用户答题过程中异步生成智能题目\n */ \n\n\n\n/**\n * 强化的LLM响应清理函数\n */ function cleanLLMResponse(text) {\n    return text.trim().replace(/^```json\\s*/i, \"\").replace(/^```\\s*/, \"\").replace(/\\s*```$/g, \"\").replace(/\\/\\*[\\s\\S]*?\\*\\//g, \"\").replace(/\\/\\/.*$/gm, \"\").replace(/,(\\s*[}\\]])/g, \"$1\").replace(/\\s+$/gm, \"\").replace(/\\n{3,}/g, \"\\n\\n\");\n}\n/**\n * XML解析函数 - 更宽松和可靠\n */ function parseXMLResponse(text, dimension) {\n    console.log(`🔍 ${dimension}原始响应前200字符:`, text.substring(0, 200));\n    try {\n        // 清理响应文本\n        let cleanedText = text.trim();\n        // 移除可能的markdown包装\n        cleanedText = cleanedText.replace(/^```xml\\s*/i, \"\").replace(/^```\\s*/, \"\").replace(/\\s*```$/g, \"\");\n        // 确保有根元素\n        if (!cleanedText.includes(\"<questions>\")) {\n            // 如果没有根元素，尝试包装\n            if (cleanedText.includes(\"<question\")) {\n                cleanedText = `<questions>${cleanedText}</questions>`;\n            }\n        }\n        console.log(`🧹 ${dimension}清理后前200字符:`, cleanedText.substring(0, 200));\n        // 使用正则表达式提取问题\n        const questions = [];\n        const questionRegex = /<question[^>]*id=\"([^\"]*)\"[^>]*>([\\s\\S]*?)<\\/question>/gi;\n        let match;\n        while((match = questionRegex.exec(cleanedText)) !== null){\n            const questionId = match[1];\n            const questionContent = match[2];\n            // 提取问题文本\n            const textMatch = questionContent.match(/<text>([\\s\\S]*?)<\\/text>/i);\n            const questionText = textMatch ? textMatch[1].trim() : \"\";\n            console.log(`🔍 ${dimension}解析问题 ${questionId}:`);\n            console.log(`  - 问题内容长度: ${questionContent.length}`);\n            console.log(`  - 文本匹配结果: ${textMatch ? \"成功\" : \"失败\"}`);\n            console.log(`  - 提取的文本: \"${questionText}\"`);\n            // 提取选项\n            const options = [];\n            const optionRegex = /<option>([\\s\\S]*?)<\\/option>/gi;\n            let optionMatch;\n            while((optionMatch = optionRegex.exec(questionContent)) !== null){\n                options.push(optionMatch[1].trim());\n            }\n            console.log(`  - 提取的选项数量: ${options.length}`);\n            console.log(`  - 选项内容: ${JSON.stringify(options)}`);\n            if (questionText && options.length > 0) {\n                // 转换选项格式为前端期望的格式\n                const formattedOptions = options.map((optionText, index)=>({\n                        value: String.fromCharCode(65 + index),\n                        text: optionText\n                    }));\n                questions.push({\n                    id: questionId,\n                    title: questionText,\n                    text: questionText,\n                    type: \"SINGLE_CHOICE\",\n                    options: formattedOptions\n                });\n            }\n        }\n        console.log(`✅ ${dimension}XML解析成功，提取到${questions.length}道题目`);\n        return {\n            questions\n        };\n    } catch (error) {\n        console.error(`❌ ${dimension}XML解析失败:`, error.message);\n        console.error(\"原始文本:\", text.substring(0, 500));\n        // 降级到简单的文本解析\n        return parseTextFallback(text, dimension);\n    }\n}\n/**\n * 文本降级解析 - 最后的备选方案\n */ function parseTextFallback(text, dimension) {\n    console.log(`🔄 ${dimension}尝试文本降级解析...`);\n    // 简单的文本模式匹配，生成基础题目\n    const questions = [\n        {\n            id: `${dimension}_FALLBACK_001`,\n            title: `请评估您的组织在${dimension}维度的整体表现`,\n            text: `请评估您的组织在${dimension}维度的整体表现`,\n            type: \"SINGLE_CHOICE\",\n            options: [\n                {\n                    value: \"A\",\n                    text: \"需要大幅改进\"\n                },\n                {\n                    value: \"B\",\n                    text: \"有待提升\"\n                },\n                {\n                    value: \"C\",\n                    text: \"基本满足要求\"\n                },\n                {\n                    value: \"D\",\n                    text: \"表现优秀\"\n                }\n            ]\n        }\n    ];\n    console.log(`⚠️ ${dimension}使用降级解析，生成1道基础题目`);\n    return {\n        questions\n    };\n}\n/**\n * 获取可用的API密钥列表\n */ function getAvailableApiKeys() {\n    const apiKeys = [\n        process.env.MINIMAX_API_KEY_1,\n        process.env.MINIMAX_API_KEY_2,\n        process.env.MINIMAX_API_KEY_3,\n        process.env.MINIMAX_API_KEY_4,\n        process.env.MINIMAX_API_KEY // 备用主密钥\n    ].filter((key)=>key && key.length > 50); // 过滤有效密钥\n    console.log(`🔑 发现${apiKeys.length}个有效API密钥，支持并发调用`);\n    return apiKeys;\n}\n/**\n * 直接在服务端生成智能题目 - 分批+多API Key并发版本\n */ async function generateIntelligentQuestionsDirectly(profile) {\n    const dimensions = [\n        \"SF\",\n        \"IT\",\n        \"MV\",\n        \"AD\"\n    ];\n    const batchSizes = [\n        2,\n        2,\n        2,\n        1\n    ]; // 2+2+2+1分批策略\n    const apiKeys = getAvailableApiKeys();\n    if (apiKeys.length === 0) {\n        throw new Error(\"没有可用的API密钥\");\n    }\n    console.log(`🚀 开始分批并发生成智能题目 - 4个维度 × 4批次 = 16个并发任务`);\n    const startTime = Date.now();\n    // 为每个维度创建分批任务\n    const allPromises = [];\n    let apiKeyIndex = 0;\n    dimensions.forEach((dimension, dimIndex)=>{\n        batchSizes.forEach((batchSize, batchIndex)=>{\n            const apiKey = apiKeys[apiKeyIndex % apiKeys.length];\n            const batchNumber = batchIndex + 1;\n            const totalBatches = batchSizes.length;\n            console.log(`🎯 ${dimension}维度第${batchNumber}批(${batchSize}题)使用API密钥${apiKeyIndex % apiKeys.length + 1}`);\n            const promise = generateBatchQuestionsDirectly(dimension, profile, batchSize, batchNumber, totalBatches, apiKey).then((questions)=>{\n                console.log(`✅ ${dimension}维度第${batchNumber}批生成成功:`, questions.length, \"道题目\");\n                return {\n                    dimension,\n                    batchNumber,\n                    questions,\n                    success: true\n                };\n            }).catch((error)=>{\n                console.warn(`⚠️ ${dimension}维度第${batchNumber}批生成失败，使用模板生成:`, error.message);\n                // 降级到模板生成\n                const generator = new _services_intelligent_question_generator__WEBPACK_IMPORTED_MODULE_1__.IntelligentQuestionGenerator();\n                const templateQuestions = generator.generateQuestionsFromTemplate(dimension, profile, batchSize);\n                return {\n                    dimension,\n                    batchNumber,\n                    questions: templateQuestions,\n                    success: false\n                };\n            });\n            allPromises.push(promise);\n            apiKeyIndex++;\n        });\n    });\n    // 等待所有分批任务完成\n    const results = await Promise.allSettled(allPromises);\n    const endTime = Date.now();\n    console.log(`⚡ 分批并发生成完成，总耗时: ${(endTime - startTime) / 1000}秒`);\n    // 按维度和批次整理结果\n    const dimensionQuestions = {};\n    let successCount = 0;\n    let failureCount = 0;\n    results.forEach((result, index)=>{\n        if (result.status === \"fulfilled\") {\n            const { dimension, batchNumber, questions, success } = result.value;\n            if (!dimensionQuestions[dimension]) {\n                dimensionQuestions[dimension] = [];\n            }\n            dimensionQuestions[dimension].push(...questions);\n            if (success) {\n                successCount++;\n            } else {\n                failureCount++;\n            }\n        } else {\n            console.error(`❌ 分批任务${index + 1}完全失败:`, result.reason);\n            failureCount++;\n        }\n    });\n    // 合并所有题目\n    const allQuestions = [];\n    Object.values(dimensionQuestions).forEach((questions)=>{\n        allQuestions.push(...questions);\n    });\n    console.log(`📊 分批生成统计: 成功${successCount}批, 失败${failureCount}批, 总题目${allQuestions.length}道`);\n    return allQuestions;\n}\n/**\n * 分批生成题目 - 针对性提示词 + 只生成单选题\n */ async function generateBatchQuestionsDirectly(dimension, profile, count, batchNumber, totalBatches, apiKey) {\n    // 使用指定的API密钥创建LLM客户端\n    const llmClient = new _services_llm_llm_client__WEBPACK_IMPORTED_MODULE_3__.LLMClient();\n    // 如果提供了特定的API密钥，临时设置环境变量\n    const originalApiKey = process.env.MINIMAX_API_KEY;\n    if (apiKey) {\n        process.env.MINIMAX_API_KEY = apiKey;\n        console.log(`🔑 ${dimension}维度第${batchNumber}批使用专用API密钥: ${apiKey.substring(0, 20)}...`);\n    }\n    // 根据批次生成不同角度的提示词\n    const batchFocus = getBatchFocus(dimension, batchNumber, totalBatches);\n    const systemPrompt = `你是一位专业的公益机构组织能力评估专家，专门设计个性化的单选题评估问题。\n\n🎯 本批次专注角度：${batchFocus}\n\n📋 严格格式要求：\n1. 只生成单选题\n2. 直接返回XML格式，不要任何包装\n3. 每道题必须有4个选项\n4. 选项要有明显的区分度（从低到高的能力层次）\n\n返回格式示例：\n<questions>\n  <question id=\"${dimension}_I${String(batchNumber).padStart(2, \"0\")}1\">\n    <text>问题内容</text>\n    <options>\n      <option>选项1</option>\n      <option>选项2</option>\n      <option>选项3</option>\n      <option>选项4</option>\n    </options>\n  </question>\n</questions>\n\n📝 题目要求：\n1. 针对公益机构的实际工作场景\n2. 体现${dimension}维度的${batchFocus}\n3. 选项从\"基础/被动\"到\"优秀/主动\"递进\n4. 问题具体、可操作、易理解\n\n⚠️ 重要：直接返回XML，不要任何额外文字或标记`;\n    const contextualPrompt = buildContextualPrompt(dimension, profile);\n    const userPrompt = `${contextualPrompt}\n\n请生成${count}道专注于\"${batchFocus}\"的${dimension}维度单选题。`;\n    console.log(`📏 ${dimension}维度第${batchNumber}批提示词长度:`, systemPrompt.length + userPrompt.length, \"字符\");\n    try {\n        const response = await llmClient.call({\n            model: \"minimax-M1\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 2000 // 减少token数量\n        });\n        let responseText = \"\";\n        if (response?.choices?.[0]?.message?.content) {\n            responseText = response.choices[0].message.content;\n        } else if (response?.reply) {\n            responseText = response.reply;\n        } else {\n            throw new Error(\"LLM响应格式异常\");\n        }\n        const parsedResponse = parseXMLResponse(responseText, `${dimension}第${batchNumber}批`);\n        const questions = parsedResponse.questions || [];\n        return questions.map((q, index)=>{\n            const questionId = `${dimension}_I${String(batchNumber).padStart(2, \"0\")}${String(index + 1)}`;\n            console.log(`🔧 ${dimension}第${batchNumber}批问题${index + 1}: id=\"${questionId}\", text=\"${q.text?.substring(0, 50)}...\"`);\n            return {\n                id: questionId,\n                title: q.text,\n                text: q.text,\n                type: \"SINGLE_CHOICE\",\n                options: q.options || [\n                    {\n                        value: \"A\",\n                        text: \"选项1\"\n                    },\n                    {\n                        value: \"B\",\n                        text: \"选项2\"\n                    },\n                    {\n                        value: \"C\",\n                        text: \"选项3\"\n                    },\n                    {\n                        value: \"D\",\n                        text: \"选项4\"\n                    }\n                ],\n                category: `${dimension}_INTELLIGENT`,\n                dimension: dimension,\n                weight: q.weight || 1,\n                source: \"AI_GENERATED\",\n                order: 1000 + (batchNumber - 1) * 10 + index,\n                required: true,\n                tags: [\n                    `${dimension.toLowerCase()}_dimension`,\n                    \"intelligent_generated\",\n                    `batch_${batchNumber}`\n                ],\n                metadata: {\n                    generatedAt: new Date().toISOString(),\n                    batchNumber,\n                    batchFocus,\n                    organizationType: profile.organizationType,\n                    serviceArea: profile.serviceArea,\n                    developmentStage: profile.developmentStage\n                }\n            };\n        });\n    } finally{\n        // 恢复原始API密钥\n        if (apiKey && originalApiKey) {\n            process.env.MINIMAX_API_KEY = originalApiKey;\n        }\n    }\n}\n/**\n * 根据批次获取专注角度\n */ function getBatchFocus(dimension, batchNumber, totalBatches) {\n    const focusMap = {\n        SF: [\n            \"战略规划与目标设定\",\n            \"资源配置与优化\",\n            \"服务创新与改进\",\n            \"可持续发展能力\"\n        ],\n        IT: [\n            \"信息系统建设\",\n            \"数据管理与分析\",\n            \"数字化工具应用\",\n            \"技术创新应用\"\n        ],\n        MV: [\n            \"使命愿景传达\",\n            \"价值观践行\",\n            \"文化建设\",\n            \"品牌影响力\"\n        ],\n        AD: [\n            \"组织架构设计\",\n            \"团队管理能力\",\n            \"决策机制\",\n            \"变革适应能力\"\n        ]\n    };\n    return focusMap[dimension]?.[batchNumber - 1] || \"综合能力评估\";\n}\n/**\n * 为特定维度生成题目 - 支持指定API密钥（保留兼容性）\n */ async function generateDimensionQuestionsDirectly(dimension, profile, count, apiKey) {\n    // 使用指定的API密钥创建LLM客户端\n    const llmClient = new _services_llm_llm_client__WEBPACK_IMPORTED_MODULE_3__.LLMClient();\n    // 如果提供了特定的API密钥，临时设置环境变量\n    const originalApiKey = process.env.MINIMAX_API_KEY;\n    if (apiKey) {\n        process.env.MINIMAX_API_KEY = apiKey;\n        console.log(`🔑 ${dimension}维度使用专用API密钥: ${apiKey.substring(0, 20)}...`);\n    }\n    const systemPrompt = `你是一位专业的公益机构组织能力评估专家，专门设计个性化的评估问题。\n\n🎯 JSON格式要求（严格遵守）：\n1. 直接返回JSON对象，不要使用markdown代码块包装\n2. 不要添加 \\`\\`\\`json 或 \\`\\`\\` 标记\n3. 使用双引号，不要使用单引号\n4. 最后一个元素后不要添加逗号\n5. 确保所有必需字段都存在且格式正确\n6. 字符串中的引号要正确转义\n\n📋 返回格式示例：\n{\n  \"questions\": [\n    {\n      \"id\": \"${dimension}_I001\",\n      \"text\": \"问题内容\",\n      \"type\": \"SINGLE_CHOICE\",\n      \"options\": [\"选项1\", \"选项2\", \"选项3\", \"选项4\"],\n      \"category\": \"${dimension}_INTELLIGENT\",\n      \"dimension\": \"${dimension}\",\n      \"weight\": 1\n    }\n  ]\n}\n\n📝 问题类型说明：\n- SINGLE_CHOICE: 单选题（最常用）\n- MULTIPLE_CHOICE: 多选题\n- SCALE: 量表题（1-5分）\n- TEXT: 文本题\n- BOOLEAN: 是非题\n\n📝 内容要求：\n1. 问题必须针对公益机构的特点和挑战\n2. 体现${dimension}维度的核心评估要素\n3. 根据组织画像个性化设计\n4. 选项设计要有区分度和专业性\n5. 问题ID格式：${dimension}_I001, ${dimension}_I002...\n\n⚠️ 重要提醒：\n- 直接返回JSON，不要任何额外的文字说明\n- 确保JSON格式完全正确，可以直接被JSON.parse()解析`;\n    const contextualPrompt = buildContextualPrompt(dimension, profile);\n    const userPrompt = `${contextualPrompt}\\n\\n请生成${count}道针对性的${dimension}维度评估问题。`;\n    console.log(`📏 ${dimension}维度提示词长度:`, systemPrompt.length + userPrompt.length, \"字符\");\n    try {\n        const response = await llmClient.call({\n            model: \"minimax-M1\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: userPrompt\n                }\n            ],\n            temperature: 0.8,\n            max_tokens: 3000,\n            response_format: {\n                type: \"json_object\"\n            }\n        });\n        let responseText = \"\";\n        if (response?.choices?.[0]?.message?.content) {\n            responseText = response.choices[0].message.content;\n        } else if (response?.reply) {\n            responseText = response.reply;\n        } else {\n            throw new Error(\"LLM响应格式异常\");\n        }\n        const parsedResponse = parseFlexibleJSON(responseText, dimension);\n        const questions = parsedResponse.questions || [];\n        return questions.map((q, index)=>{\n            // 确保问题类型格式正确\n            let questionType = q.type || \"SINGLE_CHOICE\";\n            // 转换小写格式到大写格式\n            const typeMapping = {\n                \"single_choice\": \"SINGLE_CHOICE\",\n                \"multiple_choice\": \"MULTIPLE_CHOICE\",\n                \"scale\": \"SCALE\",\n                \"text\": \"TEXT\",\n                \"boolean\": \"BOOLEAN\",\n                \"ranking\": \"RANKING\"\n            };\n            if (typeMapping[questionType.toLowerCase()]) {\n                questionType = typeMapping[questionType.toLowerCase()];\n            }\n            console.log(`🔧 ${dimension}维度问题${index + 1}: type=\"${questionType}\", text=\"${q.text?.substring(0, 50)}...\"`);\n            return {\n                id: q.id || `${dimension}_I${String(index + 1).padStart(3, \"0\")}`,\n                text: q.text,\n                type: questionType,\n                options: q.options || [],\n                category: `${dimension}_INTELLIGENT`,\n                dimension: dimension,\n                weight: q.weight || 1,\n                source: \"AI_GENERATED\",\n                order: 1000 + index,\n                required: true,\n                tags: [\n                    `${dimension.toLowerCase()}_dimension`,\n                    \"intelligent_generated\"\n                ],\n                metadata: {\n                    generatedAt: new Date().toISOString(),\n                    organizationType: profile.organizationType,\n                    serviceArea: profile.serviceArea,\n                    developmentStage: profile.developmentStage\n                }\n            };\n        });\n    } finally{\n        // 恢复原始API密钥\n        if (apiKey && originalApiKey) {\n            process.env.MINIMAX_API_KEY = originalApiKey;\n        }\n    }\n}\n/**\n * 构建上下文化提示词\n */ function buildContextualPrompt(dimension, profile) {\n    const serviceArea = Array.isArray(profile.serviceArea) ? profile.serviceArea : [\n        profile.serviceArea || \"社会服务\"\n    ];\n    return `\n你是一位专业的公益机构组织能力评估专家，正在为以下公益机构设计${dimension}维度的评估问题：\n\n机构背景：\n- 组织类型：${profile.organizationType || \"公益组织\"}\n- 服务领域：${serviceArea.join(\"、\")}\n- 组织规模：${profile.organizationScale || \"中型\"}\n- 发展阶段：${profile.developmentStage || \"成长期\"}\n- 运营模式：${profile.operatingModel || \"直接服务\"}\n- 影响力定位：${profile.impactPositioning || \"区域影响\"}\n- 组织文化：${profile.organizationalCulture || \"使命驱动\"}\n- 使命：${profile.mission || \"致力于社会公益事业\"}\n\n请基于以上背景，生成针对性的${dimension}维度评估问题。`;\n}\nasync function POST(request) {\n    try {\n        const { profile: rawProfile } = await request.json();\n        console.log(\"\\uD83E\\uDD16 后台API: 开始生成28道智能题目...\");\n        console.log(\"\\uD83D\\uDD0D 接收到的原始profile数据:\", JSON.stringify(rawProfile, null, 2));\n        // 转换组织画像数据为标准格式\n        const transformedProfile = _services_analysis_profile_transformer__WEBPACK_IMPORTED_MODULE_2__.ProfileTransformer.transformRawAnswers(rawProfile);\n        console.log(\"✅ 转换后的profile数据:\", JSON.stringify(transformedProfile, null, 2));\n        // 转换为智能生成器需要的格式\n        const profile = {\n            organizationType: transformedProfile.organizationType,\n            serviceArea: [\n                transformedProfile.serviceArea\n            ],\n            organizationScale: transformedProfile.teamSize,\n            developmentStage: transformedProfile.developmentStage,\n            operatingModel: transformedProfile.operatingModel,\n            impactPositioning: transformedProfile.impactScope,\n            organizationalCulture: transformedProfile.organizationCulture,\n            mission: \"致力于社会公益事业\",\n            challenges: [\n                transformedProfile.challengesPriorities\n            ],\n            goals: [\n                transformedProfile.futureVision\n            ]\n        };\n        console.log(\"\\uD83D\\uDD04 智能生成器格式的profile:\", JSON.stringify(profile, null, 2));\n        // 检查是否有有效的API密钥\n        const hasValidApiKey = process.env.MINIMAX_API_KEY && process.env.MINIMAX_API_KEY.length > 50;\n        if (!hasValidApiKey) {\n            console.warn(\"⚠️ 后台API: 未配置LLM API密钥，使用模板生成\");\n        }\n        // 直接在服务端生成智能题目，避免服务端到服务端的API调用\n        const intelligentQuestions = await generateIntelligentQuestionsDirectly(profile);\n        console.log(\"✅ 后台API: 智能题目生成完成:\", intelligentQuestions.length, \"道\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                questions: intelligentQuestions,\n                count: intelligentQuestions.length,\n                generatedAt: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ 后台API: 智能题目生成失败:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"后台智能题目生成失败\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/questionnaire/generate-background/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/analysis/profile-transformer.ts":
/*!******************************************************!*\
  !*** ./src/services/analysis/profile-transformer.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileTransformer: () => (/* binding */ ProfileTransformer)\n/* harmony export */ });\n/**\n * OCTI智能评估系统 - 组织画像数据转换器\n * \n * 将原始问卷答案转换为结构化的组织画像数据\n */ /**\n * 组织画像数据转换器\n */ class ProfileTransformer {\n    /**\n   * 将原始答案转换为结构化组织画像\n   */ static transformRawAnswers(rawAnswers) {\n        console.log(\"\\uD83D\\uDD04 转换组织画像数据:\", rawAnswers);\n        // 如果已经是结构化数据，直接返回\n        if (rawAnswers.organizationType) {\n            return rawAnswers;\n        }\n        // 转换原始答案（数字键值对）为结构化数据\n        const profile = {\n            organizationType: this.getOrganizationType(rawAnswers[\"1\"]),\n            serviceArea: this.getServiceArea(rawAnswers[\"2\"]),\n            resourceStructure: this.getResourceStructure(rawAnswers[\"3\"]),\n            developmentStage: this.getDevelopmentStage(rawAnswers[\"4\"]),\n            teamSize: this.getTeamSize(rawAnswers[\"5\"]),\n            operatingModel: this.getOperatingModel(rawAnswers[\"6\"]),\n            impactScope: this.getImpactScope(rawAnswers[\"7\"]),\n            organizationCulture: this.getOrganizationCulture(rawAnswers[\"8\"]),\n            challengesPriorities: this.getChallengesPriorities(rawAnswers[\"9\"]),\n            futureVision: this.getFutureVision(rawAnswers[\"10\"])\n        };\n        console.log(\"✅ 组织画像转换完成:\", profile);\n        return profile;\n    }\n    static getOrganizationType(value) {\n        const mapping = {\n            \"A\": \"初创期公益组织\",\n            \"B\": \"探索期公益组织\",\n            \"C\": \"成长期公益组织\",\n            \"D\": \"成熟期公益组织\",\n            \"E\": \"转型期公益组织\",\n            \"F\": \"分化期公益组织\"\n        };\n        return mapping[value] || \"公益组织\";\n    }\n    static getServiceArea(value) {\n        const mapping = {\n            \"A\": \"教育\",\n            \"B\": \"环保\",\n            \"C\": \"扶贫\",\n            \"D\": \"医疗健康\",\n            \"E\": \"文化艺术\",\n            \"F\": \"综合服务\"\n        };\n        return mapping[value] || \"综合服务\";\n    }\n    static getResourceStructure(value) {\n        const mapping = {\n            \"A\": \"政府主导型\",\n            \"B\": \"基金会支持型\",\n            \"C\": \"公众募捐型\",\n            \"D\": \"企业合作型\",\n            \"E\": \"混合收入型\",\n            \"F\": \"自主经营型\"\n        };\n        return mapping[value] || \"混合收入型\";\n    }\n    static getDevelopmentStage(value) {\n        const mapping = {\n            \"A\": \"初创期\",\n            \"B\": \"探索期\",\n            \"C\": \"成长期\",\n            \"D\": \"成熟期\",\n            \"E\": \"转型期\",\n            \"F\": \"分化期\"\n        };\n        return mapping[value] || \"成长期\";\n    }\n    static getTeamSize(value) {\n        const mapping = {\n            \"A\": \"微型（1-5人）\",\n            \"B\": \"小型（6-15人）\",\n            \"C\": \"中型（16-50人）\",\n            \"D\": \"大型（51-100人）\",\n            \"E\": \"超大型（100人以上）\",\n            \"F\": \"分布式团队\"\n        };\n        return mapping[value] || \"中型（16-50人）\";\n    }\n    static getOperatingModel(value) {\n        const mapping = {\n            \"A\": \"直接服务型\",\n            \"B\": \"倡导推动型\",\n            \"C\": \"平台连接型\",\n            \"D\": \"能力建设型\",\n            \"E\": \"资源配置型\",\n            \"F\": \"创新孵化型\"\n        };\n        return mapping[value] || \"直接服务型\";\n    }\n    static getImpactScope(value) {\n        const mapping = {\n            \"A\": \"社区影响\",\n            \"B\": \"城市影响\",\n            \"C\": \"区域影响\",\n            \"D\": \"全国影响\",\n            \"E\": \"国际影响\",\n            \"F\": \"行业影响\"\n        };\n        return mapping[value] || \"区域影响\";\n    }\n    static getOrganizationCulture(value) {\n        const mapping = {\n            \"A\": \"使命驱动\",\n            \"B\": \"创新导向\",\n            \"C\": \"协作共享\",\n            \"D\": \"专业严谨\",\n            \"E\": \"草根活力\",\n            \"F\": \"学习成长\"\n        };\n        return mapping[value] || \"使命驱动\";\n    }\n    static getChallengesPriorities(value) {\n        const mapping = {\n            \"A\": \"资金筹集\",\n            \"B\": \"团队建设\",\n            \"C\": \"项目管理\",\n            \"D\": \"影响力扩大\",\n            \"E\": \"制度建设\",\n            \"F\": \"创新发展\"\n        };\n        return mapping[value] || \"资金筹集和团队建设\";\n    }\n    static getFutureVision(value) {\n        const mapping = {\n            \"A\": \"成为行业领导者\",\n            \"B\": \"扩大服务规模\",\n            \"C\": \"提升专业能力\",\n            \"D\": \"建立品牌影响力\",\n            \"E\": \"实现可持续发展\",\n            \"F\": \"推动行业变革\"\n        };\n        return mapping[value] || \"实现可持续发展\";\n    }\n    /**\n   * 验证组织画像数据完整性\n   */ static validateProfile(profile) {\n        const requiredFields = [\n            \"organizationType\",\n            \"serviceArea\",\n            \"resourceStructure\",\n            \"developmentStage\",\n            \"teamSize\",\n            \"operatingModel\",\n            \"impactScope\",\n            \"organizationCulture\",\n            \"challengesPriorities\",\n            \"futureVision\"\n        ];\n        for (const field of requiredFields){\n            if (!profile[field] || profile[field].trim() === \"\") {\n                console.warn(`⚠️ 组织画像缺少字段: ${field}`);\n                return false;\n            }\n        }\n        return true;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvYW5hbHlzaXMvcHJvZmlsZS10cmFuc2Zvcm1lci50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQWVEOztDQUVDLEdBQ00sTUFBTUE7SUFDWDs7R0FFQyxHQUNELE9BQU9DLG9CQUFvQkMsVUFBZSxFQUF1QjtRQUMvREMsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQkY7UUFFNUIsa0JBQWtCO1FBQ2xCLElBQUlBLFdBQVdHLGdCQUFnQixFQUFFO1lBQy9CLE9BQU9IO1FBQ1Q7UUFFQSxzQkFBc0I7UUFDdEIsTUFBTUksVUFBK0I7WUFDbkNELGtCQUFrQixJQUFJLENBQUNFLG1CQUFtQixDQUFDTCxVQUFVLENBQUMsSUFBSTtZQUMxRE0sYUFBYSxJQUFJLENBQUNDLGNBQWMsQ0FBQ1AsVUFBVSxDQUFDLElBQUk7WUFDaERRLG1CQUFtQixJQUFJLENBQUNDLG9CQUFvQixDQUFDVCxVQUFVLENBQUMsSUFBSTtZQUM1RFUsa0JBQWtCLElBQUksQ0FBQ0MsbUJBQW1CLENBQUNYLFVBQVUsQ0FBQyxJQUFJO1lBQzFEWSxVQUFVLElBQUksQ0FBQ0MsV0FBVyxDQUFDYixVQUFVLENBQUMsSUFBSTtZQUMxQ2MsZ0JBQWdCLElBQUksQ0FBQ0MsaUJBQWlCLENBQUNmLFVBQVUsQ0FBQyxJQUFJO1lBQ3REZ0IsYUFBYSxJQUFJLENBQUNDLGNBQWMsQ0FBQ2pCLFVBQVUsQ0FBQyxJQUFJO1lBQ2hEa0IscUJBQXFCLElBQUksQ0FBQ0Msc0JBQXNCLENBQUNuQixVQUFVLENBQUMsSUFBSTtZQUNoRW9CLHNCQUFzQixJQUFJLENBQUNDLHVCQUF1QixDQUFDckIsVUFBVSxDQUFDLElBQUk7WUFDbEVzQixjQUFjLElBQUksQ0FBQ0MsZUFBZSxDQUFDdkIsVUFBVSxDQUFDLEtBQUs7UUFDckQ7UUFFQUMsUUFBUUMsR0FBRyxDQUFDLGVBQWVFO1FBQzNCLE9BQU9BO0lBQ1Q7SUFFQSxPQUFlQyxvQkFBb0JtQixLQUFhLEVBQVU7UUFDeEQsTUFBTUMsVUFBVTtZQUNkLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztRQUNQO1FBQ0EsT0FBT0EsT0FBTyxDQUFDRCxNQUFNLElBQUk7SUFDM0I7SUFFQSxPQUFlakIsZUFBZWlCLEtBQWEsRUFBVTtRQUNuRCxNQUFNQyxVQUFVO1lBQ2QsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1FBQ1A7UUFDQSxPQUFPQSxPQUFPLENBQUNELE1BQU0sSUFBSTtJQUMzQjtJQUVBLE9BQWVmLHFCQUFxQmUsS0FBYSxFQUFVO1FBQ3pELE1BQU1DLFVBQVU7WUFDZCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7UUFDUDtRQUNBLE9BQU9BLE9BQU8sQ0FBQ0QsTUFBTSxJQUFJO0lBQzNCO0lBRUEsT0FBZWIsb0JBQW9CYSxLQUFhLEVBQVU7UUFDeEQsTUFBTUMsVUFBVTtZQUNkLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztRQUNQO1FBQ0EsT0FBT0EsT0FBTyxDQUFDRCxNQUFNLElBQUk7SUFDM0I7SUFFQSxPQUFlWCxZQUFZVyxLQUFhLEVBQVU7UUFDaEQsTUFBTUMsVUFBVTtZQUNkLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztRQUNQO1FBQ0EsT0FBT0EsT0FBTyxDQUFDRCxNQUFNLElBQUk7SUFDM0I7SUFFQSxPQUFlVCxrQkFBa0JTLEtBQWEsRUFBVTtRQUN0RCxNQUFNQyxVQUFVO1lBQ2QsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1FBQ1A7UUFDQSxPQUFPQSxPQUFPLENBQUNELE1BQU0sSUFBSTtJQUMzQjtJQUVBLE9BQWVQLGVBQWVPLEtBQWEsRUFBVTtRQUNuRCxNQUFNQyxVQUFVO1lBQ2QsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1FBQ1A7UUFDQSxPQUFPQSxPQUFPLENBQUNELE1BQU0sSUFBSTtJQUMzQjtJQUVBLE9BQWVMLHVCQUF1QkssS0FBYSxFQUFVO1FBQzNELE1BQU1DLFVBQVU7WUFDZCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7UUFDUDtRQUNBLE9BQU9BLE9BQU8sQ0FBQ0QsTUFBTSxJQUFJO0lBQzNCO0lBRUEsT0FBZUgsd0JBQXdCRyxLQUFhLEVBQVU7UUFDNUQsTUFBTUMsVUFBVTtZQUNkLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztRQUNQO1FBQ0EsT0FBT0EsT0FBTyxDQUFDRCxNQUFNLElBQUk7SUFDM0I7SUFFQSxPQUFlRCxnQkFBZ0JDLEtBQWEsRUFBVTtRQUNwRCxNQUFNQyxVQUFVO1lBQ2QsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1FBQ1A7UUFDQSxPQUFPQSxPQUFPLENBQUNELE1BQU0sSUFBSTtJQUMzQjtJQUVBOztHQUVDLEdBQ0QsT0FBT0UsZ0JBQWdCdEIsT0FBNEIsRUFBVztRQUM1RCxNQUFNdUIsaUJBQWlCO1lBQ3JCO1lBQW9CO1lBQWU7WUFDbkM7WUFBb0I7WUFBWTtZQUNoQztZQUFlO1lBQXVCO1lBQXdCO1NBQy9EO1FBRUQsS0FBSyxNQUFNQyxTQUFTRCxlQUFnQjtZQUNsQyxJQUFJLENBQUN2QixPQUFPLENBQUN3QixNQUFNLElBQUl4QixPQUFPLENBQUN3QixNQUFNLENBQUNDLElBQUksT0FBTyxJQUFJO2dCQUNuRDVCLFFBQVE2QixJQUFJLENBQUMsQ0FBQyxhQUFhLEVBQUVGLE1BQU0sQ0FBQztnQkFDcEMsT0FBTztZQUNUO1FBQ0Y7UUFFQSxPQUFPO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9zcmMvc2VydmljZXMvYW5hbHlzaXMvcHJvZmlsZS10cmFuc2Zvcm1lci50cz8yYzUzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogT0NUSeaZuuiDveivhOS8sOezu+e7nyAtIOe7hOe7h+eUu+WDj+aVsOaNrui9rOaNouWZqFxuICogXG4gKiDlsIbljp/lp4vpl67ljbfnrZTmoYjovazmjaLkuLrnu5PmnoTljJbnmoTnu4Tnu4fnlLvlg4/mlbDmja5cbiAqL1xuXG5leHBvcnQgaW50ZXJmYWNlIE9yZ2FuaXphdGlvblByb2ZpbGUge1xuICBvcmdhbml6YXRpb25UeXBlOiBzdHJpbmc7XG4gIHNlcnZpY2VBcmVhOiBzdHJpbmc7XG4gIHJlc291cmNlU3RydWN0dXJlOiBzdHJpbmc7XG4gIGRldmVsb3BtZW50U3RhZ2U6IHN0cmluZztcbiAgdGVhbVNpemU6IHN0cmluZztcbiAgb3BlcmF0aW5nTW9kZWw6IHN0cmluZztcbiAgaW1wYWN0U2NvcGU6IHN0cmluZztcbiAgb3JnYW5pemF0aW9uQ3VsdHVyZTogc3RyaW5nO1xuICBjaGFsbGVuZ2VzUHJpb3JpdGllczogc3RyaW5nO1xuICBmdXR1cmVWaXNpb246IHN0cmluZztcbn1cblxuLyoqXG4gKiDnu4Tnu4fnlLvlg4/mlbDmja7ovazmjaLlmahcbiAqL1xuZXhwb3J0IGNsYXNzIFByb2ZpbGVUcmFuc2Zvcm1lciB7XG4gIC8qKlxuICAgKiDlsIbljp/lp4vnrZTmoYjovazmjaLkuLrnu5PmnoTljJbnu4Tnu4fnlLvlg49cbiAgICovXG4gIHN0YXRpYyB0cmFuc2Zvcm1SYXdBbnN3ZXJzKHJhd0Fuc3dlcnM6IGFueSk6IE9yZ2FuaXphdGlvblByb2ZpbGUge1xuICAgIGNvbnNvbGUubG9nKCfwn5SEIOi9rOaNoue7hOe7h+eUu+WDj+aVsOaNrjonLCByYXdBbnN3ZXJzKTtcblxuICAgIC8vIOWmguaenOW3sue7j+aYr+e7k+aehOWMluaVsOaNru+8jOebtOaOpei/lOWbnlxuICAgIGlmIChyYXdBbnN3ZXJzLm9yZ2FuaXphdGlvblR5cGUpIHtcbiAgICAgIHJldHVybiByYXdBbnN3ZXJzIGFzIE9yZ2FuaXphdGlvblByb2ZpbGU7XG4gICAgfVxuXG4gICAgLy8g6L2s5o2i5Y6f5aeL562U5qGI77yI5pWw5a2X6ZSu5YC85a+577yJ5Li657uT5p6E5YyW5pWw5o2uXG4gICAgY29uc3QgcHJvZmlsZTogT3JnYW5pemF0aW9uUHJvZmlsZSA9IHtcbiAgICAgIG9yZ2FuaXphdGlvblR5cGU6IHRoaXMuZ2V0T3JnYW5pemF0aW9uVHlwZShyYXdBbnN3ZXJzWycxJ10pLFxuICAgICAgc2VydmljZUFyZWE6IHRoaXMuZ2V0U2VydmljZUFyZWEocmF3QW5zd2Vyc1snMiddKSxcbiAgICAgIHJlc291cmNlU3RydWN0dXJlOiB0aGlzLmdldFJlc291cmNlU3RydWN0dXJlKHJhd0Fuc3dlcnNbJzMnXSksXG4gICAgICBkZXZlbG9wbWVudFN0YWdlOiB0aGlzLmdldERldmVsb3BtZW50U3RhZ2UocmF3QW5zd2Vyc1snNCddKSxcbiAgICAgIHRlYW1TaXplOiB0aGlzLmdldFRlYW1TaXplKHJhd0Fuc3dlcnNbJzUnXSksXG4gICAgICBvcGVyYXRpbmdNb2RlbDogdGhpcy5nZXRPcGVyYXRpbmdNb2RlbChyYXdBbnN3ZXJzWyc2J10pLFxuICAgICAgaW1wYWN0U2NvcGU6IHRoaXMuZ2V0SW1wYWN0U2NvcGUocmF3QW5zd2Vyc1snNyddKSxcbiAgICAgIG9yZ2FuaXphdGlvbkN1bHR1cmU6IHRoaXMuZ2V0T3JnYW5pemF0aW9uQ3VsdHVyZShyYXdBbnN3ZXJzWyc4J10pLFxuICAgICAgY2hhbGxlbmdlc1ByaW9yaXRpZXM6IHRoaXMuZ2V0Q2hhbGxlbmdlc1ByaW9yaXRpZXMocmF3QW5zd2Vyc1snOSddKSxcbiAgICAgIGZ1dHVyZVZpc2lvbjogdGhpcy5nZXRGdXR1cmVWaXNpb24ocmF3QW5zd2Vyc1snMTAnXSlcbiAgICB9O1xuXG4gICAgY29uc29sZS5sb2coJ+KchSDnu4Tnu4fnlLvlg4/ovazmjaLlrozmiJA6JywgcHJvZmlsZSk7XG4gICAgcmV0dXJuIHByb2ZpbGU7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZXRPcmdhbml6YXRpb25UeXBlKHZhbHVlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IG1hcHBpbmcgPSB7XG4gICAgICAnQSc6ICfliJ3liJvmnJ/lhaznm4rnu4Tnu4cnLFxuICAgICAgJ0InOiAn5o6i57Si5pyf5YWs55uK57uE57uHJywgXG4gICAgICAnQyc6ICfmiJDplb/mnJ/lhaznm4rnu4Tnu4cnLFxuICAgICAgJ0QnOiAn5oiQ54af5pyf5YWs55uK57uE57uHJyxcbiAgICAgICdFJzogJ+i9rOWei+acn+WFrOebiue7hOe7hycsXG4gICAgICAnRic6ICfliIbljJbmnJ/lhaznm4rnu4Tnu4cnXG4gICAgfTtcbiAgICByZXR1cm4gbWFwcGluZ1t2YWx1ZV0gfHwgJ+WFrOebiue7hOe7hyc7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZXRTZXJ2aWNlQXJlYSh2YWx1ZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBtYXBwaW5nID0ge1xuICAgICAgJ0EnOiAn5pWZ6IKyJyxcbiAgICAgICdCJzogJ+eOr+S/nScsXG4gICAgICAnQyc6ICfmibbotKsnLFxuICAgICAgJ0QnOiAn5Yy755aX5YGl5bq3JyxcbiAgICAgICdFJzogJ+aWh+WMluiJuuacrycsXG4gICAgICAnRic6ICfnu7zlkIjmnI3liqEnXG4gICAgfTtcbiAgICByZXR1cm4gbWFwcGluZ1t2YWx1ZV0gfHwgJ+e7vOWQiOacjeWKoSc7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZXRSZXNvdXJjZVN0cnVjdHVyZSh2YWx1ZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBtYXBwaW5nID0ge1xuICAgICAgJ0EnOiAn5pS/5bqc5Li75a+85Z6LJyxcbiAgICAgICdCJzogJ+WfuumHkeS8muaUr+aMgeWeiycsXG4gICAgICAnQyc6ICflhazkvJfli5/mjZDlnosnLFxuICAgICAgJ0QnOiAn5LyB5Lia5ZCI5L2c5Z6LJyxcbiAgICAgICdFJzogJ+a3t+WQiOaUtuWFpeWeiycsXG4gICAgICAnRic6ICfoh6rkuLvnu4/okKXlnosnXG4gICAgfTtcbiAgICByZXR1cm4gbWFwcGluZ1t2YWx1ZV0gfHwgJ+a3t+WQiOaUtuWFpeWeiyc7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZXREZXZlbG9wbWVudFN0YWdlKHZhbHVlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IG1hcHBpbmcgPSB7XG4gICAgICAnQSc6ICfliJ3liJvmnJ8nLFxuICAgICAgJ0InOiAn5o6i57Si5pyfJyxcbiAgICAgICdDJzogJ+aIkOmVv+acnycsXG4gICAgICAnRCc6ICfmiJDnhp/mnJ8nLFxuICAgICAgJ0UnOiAn6L2s5Z6L5pyfJyxcbiAgICAgICdGJzogJ+WIhuWMluacnydcbiAgICB9O1xuICAgIHJldHVybiBtYXBwaW5nW3ZhbHVlXSB8fCAn5oiQ6ZW/5pyfJztcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGdldFRlYW1TaXplKHZhbHVlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IG1hcHBpbmcgPSB7XG4gICAgICAnQSc6ICflvq7lnovvvIgxLTXkurrvvIknLFxuICAgICAgJ0InOiAn5bCP5Z6L77yINi0xNeS6uu+8iScsXG4gICAgICAnQyc6ICfkuK3lnovvvIgxNi01MOS6uu+8iScsXG4gICAgICAnRCc6ICflpKflnovvvIg1MS0xMDDkurrvvIknLFxuICAgICAgJ0UnOiAn6LaF5aSn5Z6L77yIMTAw5Lq65Lul5LiK77yJJyxcbiAgICAgICdGJzogJ+WIhuW4g+W8j+WboumYnydcbiAgICB9O1xuICAgIHJldHVybiBtYXBwaW5nW3ZhbHVlXSB8fCAn5Lit5Z6L77yIMTYtNTDkurrvvIknO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0T3BlcmF0aW5nTW9kZWwodmFsdWU6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3QgbWFwcGluZyA9IHtcbiAgICAgICdBJzogJ+ebtOaOpeacjeWKoeWeiycsXG4gICAgICAnQic6ICflgKHlr7zmjqjliqjlnosnLFxuICAgICAgJ0MnOiAn5bmz5Y+w6L+e5o6l5Z6LJyxcbiAgICAgICdEJzogJ+iDveWKm+W7uuiuvuWeiycsXG4gICAgICAnRSc6ICfotYTmupDphY3nva7lnosnLFxuICAgICAgJ0YnOiAn5Yib5paw5a215YyW5Z6LJ1xuICAgIH07XG4gICAgcmV0dXJuIG1hcHBpbmdbdmFsdWVdIHx8ICfnm7TmjqXmnI3liqHlnosnO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0SW1wYWN0U2NvcGUodmFsdWU6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3QgbWFwcGluZyA9IHtcbiAgICAgICdBJzogJ+ekvuWMuuW9seWTjScsXG4gICAgICAnQic6ICfln47luILlvbHlk40nLFxuICAgICAgJ0MnOiAn5Yy65Z+f5b2x5ZONJyxcbiAgICAgICdEJzogJ+WFqOWbveW9seWTjScsXG4gICAgICAnRSc6ICflm73pmYXlvbHlk40nLFxuICAgICAgJ0YnOiAn6KGM5Lia5b2x5ZONJ1xuICAgIH07XG4gICAgcmV0dXJuIG1hcHBpbmdbdmFsdWVdIHx8ICfljLrln5/lvbHlk40nO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0T3JnYW5pemF0aW9uQ3VsdHVyZSh2YWx1ZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBtYXBwaW5nID0ge1xuICAgICAgJ0EnOiAn5L2/5ZG96amx5YqoJyxcbiAgICAgICdCJzogJ+WIm+aWsOWvvOWQkScsXG4gICAgICAnQyc6ICfljY/kvZzlhbHkuqsnLFxuICAgICAgJ0QnOiAn5LiT5Lia5Lil6LCoJyxcbiAgICAgICdFJzogJ+iNieaguea0u+WKmycsXG4gICAgICAnRic6ICflrabkuaDmiJDplb8nXG4gICAgfTtcbiAgICByZXR1cm4gbWFwcGluZ1t2YWx1ZV0gfHwgJ+S9v+WRvempseWKqCc7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZXRDaGFsbGVuZ2VzUHJpb3JpdGllcyh2YWx1ZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBtYXBwaW5nID0ge1xuICAgICAgJ0EnOiAn6LWE6YeR56256ZuGJyxcbiAgICAgICdCJzogJ+WboumYn+W7uuiuvicsXG4gICAgICAnQyc6ICfpobnnm67nrqHnkIYnLFxuICAgICAgJ0QnOiAn5b2x5ZON5Yqb5omp5aSnJyxcbiAgICAgICdFJzogJ+WItuW6puW7uuiuvicsXG4gICAgICAnRic6ICfliJvmlrDlj5HlsZUnXG4gICAgfTtcbiAgICByZXR1cm4gbWFwcGluZ1t2YWx1ZV0gfHwgJ+i1hOmHkeetuembhuWSjOWboumYn+W7uuiuvic7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBnZXRGdXR1cmVWaXNpb24odmFsdWU6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3QgbWFwcGluZyA9IHtcbiAgICAgICdBJzogJ+aIkOS4uuihjOS4mumihuWvvOiAhScsXG4gICAgICAnQic6ICfmianlpKfmnI3liqHop4TmqKEnLFxuICAgICAgJ0MnOiAn5o+Q5Y2H5LiT5Lia6IO95YqbJyxcbiAgICAgICdEJzogJ+W7uueri+WTgeeJjOW9seWTjeWKmycsXG4gICAgICAnRSc6ICflrp7njrDlj6/mjIHnu63lj5HlsZUnLFxuICAgICAgJ0YnOiAn5o6o5Yqo6KGM5Lia5Y+Y6Z2pJ1xuICAgIH07XG4gICAgcmV0dXJuIG1hcHBpbmdbdmFsdWVdIHx8ICflrp7njrDlj6/mjIHnu63lj5HlsZUnO1xuICB9XG5cbiAgLyoqXG4gICAqIOmqjOivgee7hOe7h+eUu+WDj+aVsOaNruWujOaVtOaAp1xuICAgKi9cbiAgc3RhdGljIHZhbGlkYXRlUHJvZmlsZShwcm9maWxlOiBPcmdhbml6YXRpb25Qcm9maWxlKTogYm9vbGVhbiB7XG4gICAgY29uc3QgcmVxdWlyZWRGaWVsZHMgPSBbXG4gICAgICAnb3JnYW5pemF0aW9uVHlwZScsICdzZXJ2aWNlQXJlYScsICdyZXNvdXJjZVN0cnVjdHVyZScsIFxuICAgICAgJ2RldmVsb3BtZW50U3RhZ2UnLCAndGVhbVNpemUnLCAnb3BlcmF0aW5nTW9kZWwnLFxuICAgICAgJ2ltcGFjdFNjb3BlJywgJ29yZ2FuaXphdGlvbkN1bHR1cmUnLCAnY2hhbGxlbmdlc1ByaW9yaXRpZXMnLCAnZnV0dXJlVmlzaW9uJ1xuICAgIF07XG5cbiAgICBmb3IgKGNvbnN0IGZpZWxkIG9mIHJlcXVpcmVkRmllbGRzKSB7XG4gICAgICBpZiAoIXByb2ZpbGVbZmllbGRdIHx8IHByb2ZpbGVbZmllbGRdLnRyaW0oKSA9PT0gJycpIHtcbiAgICAgICAgY29uc29sZS53YXJuKGDimqDvuI8g57uE57uH55S75YOP57y65bCR5a2X5q61OiAke2ZpZWxkfWApO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJQcm9maWxlVHJhbnNmb3JtZXIiLCJ0cmFuc2Zvcm1SYXdBbnN3ZXJzIiwicmF3QW5zd2VycyIsImNvbnNvbGUiLCJsb2ciLCJvcmdhbml6YXRpb25UeXBlIiwicHJvZmlsZSIsImdldE9yZ2FuaXphdGlvblR5cGUiLCJzZXJ2aWNlQXJlYSIsImdldFNlcnZpY2VBcmVhIiwicmVzb3VyY2VTdHJ1Y3R1cmUiLCJnZXRSZXNvdXJjZVN0cnVjdHVyZSIsImRldmVsb3BtZW50U3RhZ2UiLCJnZXREZXZlbG9wbWVudFN0YWdlIiwidGVhbVNpemUiLCJnZXRUZWFtU2l6ZSIsIm9wZXJhdGluZ01vZGVsIiwiZ2V0T3BlcmF0aW5nTW9kZWwiLCJpbXBhY3RTY29wZSIsImdldEltcGFjdFNjb3BlIiwib3JnYW5pemF0aW9uQ3VsdHVyZSIsImdldE9yZ2FuaXphdGlvbkN1bHR1cmUiLCJjaGFsbGVuZ2VzUHJpb3JpdGllcyIsImdldENoYWxsZW5nZXNQcmlvcml0aWVzIiwiZnV0dXJlVmlzaW9uIiwiZ2V0RnV0dXJlVmlzaW9uIiwidmFsdWUiLCJtYXBwaW5nIiwidmFsaWRhdGVQcm9maWxlIiwicmVxdWlyZWRGaWVsZHMiLCJmaWVsZCIsInRyaW0iLCJ3YXJuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/services/analysis/profile-transformer.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/intelligent-question-generator.ts":
/*!********************************************************!*\
  !*** ./src/services/intelligent-question-generator.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntelligentQuestionGenerator: () => (/* binding */ IntelligentQuestionGenerator),\n/* harmony export */   intelligentQuestionGenerator: () => (/* binding */ intelligentQuestionGenerator)\n/* harmony export */ });\n/**\n * OCTI智能问题生成器\n * \n * 基于组织画像和question_design_prompt.json配置，\n * 真正动态生成个性化的28道智能题目\n */ /**\n * 智能问题生成器类\n */ class IntelligentQuestionGenerator {\n    constructor(){\n        // 从question_design_prompt.json加载配置\n        this.config = {\n            version: \"4.0.0\",\n            questionsPerDimension: 7,\n            totalIntelligentQuestions: 28,\n            contextualAdaptation: {\n                serviceArea: {\n                    \"教育\": \"关注教育公平、学习成果、师资发展、教育创新\",\n                    \"环保\": \"关注环境保护、可持续发展、生态修复、绿色倡导\",\n                    \"扶贫\": \"关注贫困减缓、能力建设、可持续脱贫、社区发展\",\n                    \"医疗\": \"关注健康促进、医疗可及性、疾病预防、健康教育\",\n                    \"养老\": \"关注老年关怀、养老服务、代际关系、老龄化应对\",\n                    \"儿童\": \"关注儿童保护、儿童发展、教育支持、权益维护\"\n                },\n                developmentStage: {\n                    \"初创期\": \"关注基础能力建设、团队组建、资源获取、项目启动\",\n                    \"成长期\": \"关注规模扩张、流程优化、品牌建设、影响力提升\",\n                    \"成熟期\": \"关注可持续发展、创新转型、深度影响、行业引领\",\n                    \"转型期\": \"关注战略调整、模式创新、能力重构、风险管控\"\n                },\n                operatingModel: {\n                    \"直接服务\": \"关注服务质量、受益者满意度、服务创新、规模效应\",\n                    \"资助型\": \"关注资助策略、项目筛选、监督评估、资源配置\",\n                    \"倡导型\": \"关注政策影响、公众参与、议题设置、联盟建设\"\n                }\n            },\n            nonprofitFocusAreas: [\n                \"使命驱动特性\",\n                \"利益相关者管理\",\n                \"社会影响力测量\",\n                \"透明度与问责\",\n                \"志愿者管理\",\n                \"资源筹集能力\",\n                \"可持续发展\",\n                \"治理结构\"\n            ]\n        };\n    }\n    /**\n   * 基于组织画像生成28道智能题目\n   */ async generateIntelligentQuestions(profile) {\n        const dimensions = [\n            \"SF\",\n            \"IT\",\n            \"MV\",\n            \"AD\"\n        ];\n        const allQuestions = [];\n        for (const dimension of dimensions){\n            const dimensionQuestions = await this.generateDimensionQuestions(dimension, profile, this.config.questionsPerDimension);\n            allQuestions.push(...dimensionQuestions);\n        }\n        return allQuestions;\n    }\n    /**\n   * 为特定维度生成题目\n   */ async generateDimensionQuestions(dimension, profile, count) {\n        const contextualPrompt = this.buildContextualPrompt(dimension, profile);\n        // 直接尝试LLM生成，如果失败则降级到模板生成\n        try {\n            console.log(`🤖 为${dimension}维度调用LLM生成${count}道智能题目...`);\n            return await this.generateQuestionsWithLLM(dimension, profile, count, contextualPrompt);\n        } catch (error) {\n            console.warn(`⚠️ ${dimension}维度LLM生成失败，降级到模板生成:`, error);\n            return this.generateQuestionsFromTemplate(dimension, profile, count);\n        }\n    }\n    /**\n   * 使用LLM生成题目（通过API调用）\n   */ async generateQuestionsWithLLM(dimension, profile, count, contextualPrompt) {\n        const response = await fetch(\"/api/questionnaire/generate-intelligent\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                dimension,\n                profile,\n                count,\n                contextualPrompt\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || `API调用失败: ${response.status}`);\n        }\n        const result = await response.json();\n        if (!result.success) {\n            throw new Error(result.error || \"智能问题生成失败\");\n        }\n        console.log(`✅ ${dimension}维度API调用成功:`, result.data.questions.length, \"道题目\");\n        return result.data.questions;\n    }\n    /**\n   * 构建上下文化提示词\n   */ buildContextualPrompt(dimension, profile) {\n        // 安全获取数据，避免undefined错误\n        const serviceArea = Array.isArray(profile.serviceArea) ? profile.serviceArea : [\n            profile.serviceArea || \"社会服务\"\n        ];\n        const serviceAreaContext = this.config.contextualAdaptation?.serviceArea?.[serviceArea[0]] || \"综合性社会服务\";\n        const stageContext = this.config.contextualAdaptation?.developmentStage?.[profile.developmentStage] || \"组织发展\";\n        const modelContext = this.config.contextualAdaptation?.operatingModel?.[profile.operatingModel] || \"综合运营\";\n        return `\n你是一位专业的公益机构组织能力评估专家，正在为以下公益机构设计${dimension}维度的评估问题：\n\n机构背景：\n- 组织类型：${profile.organizationType || \"公益组织\"}\n- 服务领域：${serviceArea.join(\"、\")} (${serviceAreaContext})\n- 组织规模：${profile.organizationScale || \"中型\"}\n- 发展阶段：${profile.developmentStage || \"成长期\"} (${stageContext})\n- 运营模式：${profile.operatingModel || \"综合服务型\"} (${modelContext})\n- 影响力定位：${profile.impactPositioning || \"区域影响\"}\n- 组织文化：${profile.organizationalCulture || \"使命驱动\"}\n- 使命：${profile.mission || \"致力于社会公益事业\"}\n\n请基于以上背景，生成针对性的${dimension}维度评估问题。\n问题应该：\n1. 体现公益机构的特点和专业术语\n2. 符合该发展阶段的关注重点和能力要求\n3. 适应该组织规模的管理复杂度和资源约束\n4. 针对具体的公益挑战和社会目标\n5. 考虑公益机构的使命驱动特性和社会责任\n6. 反映该运营模式的独特性和影响力测量需求\n`;\n    }\n    /**\n   * 基于模板生成题目（模拟LLM生成）\n   */ generateQuestionsFromTemplate(dimension, profile, count) {\n        const templates = this.getQuestionTemplates(dimension);\n        const questions = [];\n        for(let i = 0; i < count; i++){\n            const template = templates[i % templates.length];\n            const question = this.personalizeQuestion(template, profile, dimension, i + 1);\n            questions.push(question);\n        }\n        return questions;\n    }\n    /**\n   * 获取维度特定的题目模板\n   */ getQuestionTemplates(dimension) {\n        const templates = {\n            SF: [\n                {\n                    title: \"作为{organizationType}，您的组织在{serviceArea}领域的专业化程度如何？\",\n                    description: \"评估组织在特定服务领域的专业深度和聚焦程度。\",\n                    type: \"SCALE\",\n                    options: {\n                        min: 1,\n                        max: 5,\n                        labels: [\n                            \"很低\",\n                            \"较低\",\n                            \"一般\",\n                            \"较高\",\n                            \"很高\"\n                        ]\n                    }\n                },\n                {\n                    title: \"在{developmentStage}阶段，您的组织如何平衡资源投入与社会影响力？\",\n                    description: \"了解组织在当前发展阶段的资源配置策略。\",\n                    type: \"SINGLE_CHOICE\",\n                    options: [\n                        {\n                            text: \"优先投入核心项目，确保深度影响\",\n                            value: \"depth_focus\"\n                        },\n                        {\n                            text: \"适度分散投入，扩大服务覆盖\",\n                            value: \"breadth_focus\"\n                        },\n                        {\n                            text: \"根据资源状况灵活调整\",\n                            value: \"flexible\"\n                        },\n                        {\n                            text: \"主要依据捐赠者意愿决定\",\n                            value: \"donor_driven\"\n                        },\n                        {\n                            text: \"还没有明确的配置策略\",\n                            value: \"unclear\"\n                        }\n                    ]\n                }\n            ],\n            IT: [\n                {\n                    title: \"作为{operatingModel}的公益机构，您如何协调不同利益相关者的需求？\",\n                    description: \"评估组织在多利益相关者环境中的协调能力。\",\n                    type: \"MULTIPLE_CHOICE\",\n                    options: [\n                        {\n                            text: \"建立定期沟通机制\",\n                            value: \"regular_communication\"\n                        },\n                        {\n                            text: \"设立利益相关者代表制度\",\n                            value: \"representation_system\"\n                        },\n                        {\n                            text: \"通过透明的决策流程\",\n                            value: \"transparent_process\"\n                        },\n                        {\n                            text: \"依靠个人关系维护\",\n                            value: \"personal_relations\"\n                        },\n                        {\n                            text: \"主要关注主要资助方\",\n                            value: \"major_funders\"\n                        }\n                    ]\n                }\n            ],\n            MV: [\n                {\n                    title: \"您的组织如何确保{serviceArea}服务始终与公益使命保持一致？\",\n                    description: \"了解组织在使命驱动方面的实践和机制。\",\n                    type: \"SINGLE_CHOICE\",\n                    options: [\n                        {\n                            text: \"建立了使命导向的决策框架\",\n                            value: \"mission_framework\"\n                        },\n                        {\n                            text: \"定期评估项目与使命的契合度\",\n                            value: \"regular_assessment\"\n                        },\n                        {\n                            text: \"通过培训强化使命认知\",\n                            value: \"training\"\n                        },\n                        {\n                            text: \"主要依靠领导者的价值引导\",\n                            value: \"leadership\"\n                        },\n                        {\n                            text: \"还没有系统的保障机制\",\n                            value: \"no_system\"\n                        }\n                    ]\n                }\n            ],\n            AD: [\n                {\n                    title: \"面对{serviceArea}领域的政策变化，您的组织通常如何应对？\",\n                    description: \"评估组织对外部环境变化的适应能力。\",\n                    type: \"SINGLE_CHOICE\",\n                    options: [\n                        {\n                            text: \"提前研究政策趋势，主动调整\",\n                            value: \"proactive\"\n                        },\n                        {\n                            text: \"密切关注变化，快速响应\",\n                            value: \"responsive\"\n                        },\n                        {\n                            text: \"等待明确信号后再调整\",\n                            value: \"reactive\"\n                        },\n                        {\n                            text: \"主要依靠合作伙伴指导\",\n                            value: \"partner_guided\"\n                        },\n                        {\n                            text: \"较少关注政策变化\",\n                            value: \"limited_attention\"\n                        }\n                    ]\n                }\n            ]\n        };\n        return templates[dimension] || [];\n    }\n    /**\n   * 个性化题目内容\n   */ personalizeQuestion(template, profile, dimension, order) {\n        let title = template.title;\n        let description = template.description;\n        // 安全获取服务领域\n        const serviceArea = Array.isArray(profile.serviceArea) ? profile.serviceArea[0] : profile.serviceArea || \"公益\";\n        // 替换占位符\n        const replacements = {\n            \"{organizationType}\": profile.organizationType || \"公益组织\",\n            \"{serviceArea}\": serviceArea,\n            \"{organizationScale}\": profile.organizationScale || \"中型\",\n            \"{developmentStage}\": profile.developmentStage || \"成长期\",\n            \"{operatingModel}\": profile.operatingModel || \"直接服务\",\n            \"{impactPositioning}\": profile.impactPositioning || \"区域影响\",\n            \"{organizationalCulture}\": profile.organizationalCulture || \"使命驱动\"\n        };\n        Object.entries(replacements).forEach(([key, value])=>{\n            title = title.replace(new RegExp(key, \"g\"), value);\n            description = description.replace(new RegExp(key, \"g\"), value);\n        });\n        return {\n            id: `${dimension}_I${order.toString().padStart(3, \"0\")}`,\n            type: template.type,\n            source: \"AI_GENERATED\",\n            category: `${dimension} - ${this.getDimensionName(dimension)}`,\n            subCategory: \"智能生成\",\n            title,\n            description: `基于您的${profile.organizationType}背景生成：${description}`,\n            options: template.options,\n            required: true,\n            order: this.getBaseOrder(dimension) + order\n        };\n    }\n    /**\n   * 获取维度名称\n   */ getDimensionName(dimension) {\n        const names = {\n            SF: \"战略与财务\",\n            IT: \"影响力与透明度\",\n            MV: \"使命与价值观\",\n            AD: \"适应性与发展\"\n        };\n        return names[dimension];\n    }\n    /**\n   * 获取维度基础序号\n   */ getBaseOrder(dimension) {\n        const baseOrders = {\n            SF: 100,\n            IT: 200,\n            MV: 300,\n            AD: 400\n        };\n        return baseOrders[dimension];\n    }\n}\n/**\n * 创建智能问题生成器实例\n */ const intelligentQuestionGenerator = new IntelligentQuestionGenerator();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/intelligent-question-generator.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/llm-client.ts":
/*!****************************************!*\
  !*** ./src/services/llm/llm-client.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLMClient: () => (/* binding */ LLMClient),\n/* harmony export */   createLLMClient: () => (/* binding */ createLLMClient)\n/* harmony export */ });\n/**\n * OCTI智能评估系统 - LLM API客户端\n * \n * 支持多个LLM服务提供商的统一接口\n */ /**\n * LLM API客户端\n */ class LLMClient {\n    constructor(provider = \"minimax\"){\n        this.provider = provider;\n        if (provider === \"deepseek\") {\n            this.apiKey = process.env.DEEPSEEK_API_KEY || \"\";\n            this.baseUrl = \"https://api.deepseek.com/v1\";\n        } else {\n            this.apiKey = process.env.MINIMAX_API_KEY || \"\";\n            this.baseUrl = \"https://api.minimaxi.com/v1\";\n        }\n        if (!this.apiKey) {\n            throw new Error(`缺少${provider.toUpperCase()}_API_KEY环境变量`);\n        }\n    }\n    /**\n   * 调用LLM API\n   */ async call(request) {\n        try {\n            console.log(\"\\uD83E\\uDD16 调用LLM API:\", {\n                model: request.model,\n                messages: request.messages.length,\n                temperature: request.temperature\n            });\n            // 根据提供商调整请求格式\n            const requestBody = this.provider === \"minimax\" ? {\n                model: request.model || \"MiniMax-M1\",\n                messages: request.messages,\n                temperature: request.temperature || 0.7,\n                max_tokens: request.max_tokens || 12000,\n                top_p: 0.9,\n                stream: false,\n                // MiniMax不支持response_format参数，移除它\n                ...request.response_format ? {} : {}\n            } : {\n                model: request.model || \"deepseek-reasoner\",\n                messages: request.messages,\n                temperature: request.temperature || 0.3,\n                max_tokens: request.max_tokens || 8000,\n                // DeepSeek支持response_format参数\n                ...request.response_format && {\n                    response_format: request.response_format\n                }\n            };\n            // 根据提供商使用不同的端点\n            const endpoint = this.provider === \"minimax\" ? `${this.baseUrl}/text/chatcompletion_v2` : `${this.baseUrl}/chat/completions`;\n            // 创建带超时的fetch请求\n            const controller = new AbortController();\n            // 根据模型设置不同的超时时间（智能问卷增加了分析复杂度）\n            const timeout = this.provider === \"deepseek\" ? 480000 : 180000; // DeepSeek 8分钟，MiniMax 3分钟\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            try {\n                const response = await fetch(endpoint, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.apiKey}`\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`LLM API错误 (${response.status}): ${errorText}`);\n                }\n                const result = await response.json();\n                console.log(\"✅ LLM API调用成功\");\n                return result;\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error.name === \"AbortError\") {\n                    throw new Error(\"LLM API调用超时（2分钟）\");\n                }\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"❌ LLM API调用失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 解析JSON响应（带容错处理）\n   */ static parseJSONResponse(content) {\n        try {\n            // 尝试直接解析\n            return JSON.parse(content);\n        } catch (e1) {\n            try {\n                // 尝试提取JSON代码块\n                const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n                if (jsonMatch && jsonMatch[1]) {\n                    return JSON.parse(jsonMatch[1]);\n                }\n            } catch (e2) {\n                // 尝试修复常见JSON错误\n                try {\n                    let fixed = content;\n                    // 移除尾随逗号\n                    fixed = fixed.replace(/,\\s*}/g, \"}\");\n                    fixed = fixed.replace(/,\\s*]/g, \"]\");\n                    // 修复未引用的键\n                    fixed = fixed.replace(/(\\w+):/g, '\"$1\":');\n                    return JSON.parse(fixed);\n                } catch (e3) {\n                    console.error(\"JSON解析失败:\", content);\n                    throw new Error(\"无法解析LLM返回的JSON格式\");\n                }\n            }\n        }\n        // 如果所有解析方法都失败，返回null\n        return null;\n    }\n}\n/**\n * 创建LLM客户端实例\n */ function createLLMClient(provider = \"deepseek\") {\n    return new LLMClient(provider);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/llm-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&page=%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fgenerate-background%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();