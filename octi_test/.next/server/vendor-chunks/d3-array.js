"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-array";
exports.ids = ["vendor-chunks/d3-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-array/src/array.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/array.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\n\nvar slice = array.slice;\nvar map = array.map;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FycmF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7O0FBRU87QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FycmF5LmpzP2MyNGQiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGFycmF5ID0gQXJyYXkucHJvdG90eXBlO1xuXG5leHBvcnQgdmFyIHNsaWNlID0gYXJyYXkuc2xpY2U7XG5leHBvcnQgdmFyIG1hcCA9IGFycmF5Lm1hcDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ascending.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/ascending.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ascending)\n/* harmony export */ });\nfunction ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FzY2VuZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvYXNjZW5kaW5nLmpzP2YxNDYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gYXNjZW5kaW5nKGEsIGIpIHtcbiAgcmV0dXJuIGEgPT0gbnVsbCB8fCBiID09IG51bGwgPyBOYU4gOiBhIDwgYiA/IC0xIDogYSA+IGIgPyAxIDogYSA+PSBiID8gMCA6IE5hTjtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bin.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/bin.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bin)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-array/src/array.js\");\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-array/src/constant.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-array/src/identity.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n\n\n\n\n\n\n\n\n\nfunction bin() {\n  var value = _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n      domain = _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n      threshold = _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) [x0, x1] = (0,_nice_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(x0, x1, tn);\n      tz = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n          const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length, a = 0, b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[(0,_bisect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(Array.isArray(_) ? _array_js__WEBPACK_IMPORTED_MODULE_7__.slice.call(_) : _), histogram) : threshold;\n  };\n\n  return histogram;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisect.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/bisect.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bisectCenter: () => (/* binding */ bisectCenter),\n/* harmony export */   bisectLeft: () => (/* binding */ bisectLeft),\n/* harmony export */   bisectRight: () => (/* binding */ bisectRight),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n\n\n\n\nconst ascendingBisect = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst bisectRight = ascendingBisect.right;\nconst bisectLeft = ascendingBisect.left;\nconst bisectCenter = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_number_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).center;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bisectRight);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Jpc2VjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVDO0FBQ0Y7QUFDSjs7QUFFakMsd0JBQXdCLHdEQUFRLENBQUMscURBQVM7QUFDbkM7QUFDQTtBQUNBLHFCQUFxQix3REFBUSxDQUFDLGtEQUFNO0FBQzNDLGlFQUFlLFdBQVcsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Jpc2VjdC5qcz83OWVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhc2NlbmRpbmcgZnJvbSBcIi4vYXNjZW5kaW5nLmpzXCI7XG5pbXBvcnQgYmlzZWN0b3IgZnJvbSBcIi4vYmlzZWN0b3IuanNcIjtcbmltcG9ydCBudW1iZXIgZnJvbSBcIi4vbnVtYmVyLmpzXCI7XG5cbmNvbnN0IGFzY2VuZGluZ0Jpc2VjdCA9IGJpc2VjdG9yKGFzY2VuZGluZyk7XG5leHBvcnQgY29uc3QgYmlzZWN0UmlnaHQgPSBhc2NlbmRpbmdCaXNlY3QucmlnaHQ7XG5leHBvcnQgY29uc3QgYmlzZWN0TGVmdCA9IGFzY2VuZGluZ0Jpc2VjdC5sZWZ0O1xuZXhwb3J0IGNvbnN0IGJpc2VjdENlbnRlciA9IGJpc2VjdG9yKG51bWJlcikuY2VudGVyO1xuZXhwb3J0IGRlZmF1bHQgYmlzZWN0UmlnaHQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisector.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/bisector.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bisector)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n\n\n\nfunction bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    compare2 = (d, x) => (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || f === _descending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"] ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/blur.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/blur.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blur: () => (/* binding */ blur),\n/* harmony export */   blur2: () => (/* binding */ blur2),\n/* harmony export */   blurImage: () => (/* binding */ blurImage)\n/* harmony export */ });\nfunction blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\n\nconst blur2 = Blur2(blurf);\n\nconst blurImage = Blur2(blurfImage);\n\nfunction Blur2(blur) {\n  return function(data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {data: values, width, height} = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || (!rx && !ry)) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\n\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\n\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\n\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/blur.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/constant.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/constant.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ constant)\n/* harmony export */ });\nfunction constant(x) {\n  return () => x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9jb25zdGFudC5qcz8xM2YyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNvbnN0YW50KHgpIHtcbiAgcmV0dXJuICgpID0+IHg7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/count.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/count.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ count)\n/* harmony export */ });\nfunction count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvdW50LmpzP2RiZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY291bnQodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBjb3VudCA9IDA7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgKytjb3VudDtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQ7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBjb3VudDtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/count.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/cross.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/cross.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cross)\n/* harmony export */ });\nfunction length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nfunction cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Nyb3NzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Nyb3NzLmpzP2MwZTUiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gbGVuZ3RoKGFycmF5KSB7XG4gIHJldHVybiBhcnJheS5sZW5ndGggfCAwO1xufVxuXG5mdW5jdGlvbiBlbXB0eShsZW5ndGgpIHtcbiAgcmV0dXJuICEobGVuZ3RoID4gMCk7XG59XG5cbmZ1bmN0aW9uIGFycmF5aWZ5KHZhbHVlcykge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlcyAhPT0gXCJvYmplY3RcIiB8fCBcImxlbmd0aFwiIGluIHZhbHVlcyA/IHZhbHVlcyA6IEFycmF5LmZyb20odmFsdWVzKTtcbn1cblxuZnVuY3Rpb24gcmVkdWNlcihyZWR1Y2UpIHtcbiAgcmV0dXJuIHZhbHVlcyA9PiByZWR1Y2UoLi4udmFsdWVzKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY3Jvc3MoLi4udmFsdWVzKSB7XG4gIGNvbnN0IHJlZHVjZSA9IHR5cGVvZiB2YWx1ZXNbdmFsdWVzLmxlbmd0aCAtIDFdID09PSBcImZ1bmN0aW9uXCIgJiYgcmVkdWNlcih2YWx1ZXMucG9wKCkpO1xuICB2YWx1ZXMgPSB2YWx1ZXMubWFwKGFycmF5aWZ5KTtcbiAgY29uc3QgbGVuZ3RocyA9IHZhbHVlcy5tYXAobGVuZ3RoKTtcbiAgY29uc3QgaiA9IHZhbHVlcy5sZW5ndGggLSAxO1xuICBjb25zdCBpbmRleCA9IG5ldyBBcnJheShqICsgMSkuZmlsbCgwKTtcbiAgY29uc3QgcHJvZHVjdCA9IFtdO1xuICBpZiAoaiA8IDAgfHwgbGVuZ3Rocy5zb21lKGVtcHR5KSkgcmV0dXJuIHByb2R1Y3Q7XG4gIHdoaWxlICh0cnVlKSB7XG4gICAgcHJvZHVjdC5wdXNoKGluZGV4Lm1hcCgoaiwgaSkgPT4gdmFsdWVzW2ldW2pdKSk7XG4gICAgbGV0IGkgPSBqO1xuICAgIHdoaWxlICgrK2luZGV4W2ldID09PSBsZW5ndGhzW2ldKSB7XG4gICAgICBpZiAoaSA9PT0gMCkgcmV0dXJuIHJlZHVjZSA/IHByb2R1Y3QubWFwKHJlZHVjZSkgOiBwcm9kdWN0O1xuICAgICAgaW5kZXhbaS0tXSA9IDA7XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/cross.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/cumsum.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/cumsum.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cumsum)\n/* harmony export */ });\nfunction cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2N1bXN1bS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvY3Vtc3VtLmpzPzA5NmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY3Vtc3VtKHZhbHVlcywgdmFsdWVvZikge1xuICB2YXIgc3VtID0gMCwgaW5kZXggPSAwO1xuICByZXR1cm4gRmxvYXQ2NEFycmF5LmZyb20odmFsdWVzLCB2YWx1ZW9mID09PSB1bmRlZmluZWRcbiAgICA/IHYgPT4gKHN1bSArPSArdiB8fCAwKVxuICAgIDogdiA9PiAoc3VtICs9ICt2YWx1ZW9mKHYsIGluZGV4KyssIHZhbHVlcykgfHwgMCkpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/cumsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/descending.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/descending.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ descending)\n/* harmony export */ });\nfunction descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rlc2NlbmRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZGVzY2VuZGluZy5qcz9hYzAwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRlc2NlbmRpbmcoYSwgYikge1xuICByZXR1cm4gYSA9PSBudWxsIHx8IGIgPT0gbnVsbCA/IE5hTlxuICAgIDogYiA8IGEgPyAtMVxuICAgIDogYiA+IGEgPyAxXG4gICAgOiBiID49IGEgPyAwXG4gICAgOiBOYU47XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/descending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/deviation.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/deviation.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ deviation)\n/* harmony export */ });\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/./node_modules/d3-array/src/variance.js\");\n\n\nfunction deviation(values, valueof) {\n  const v = (0,_variance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RldmlhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQzs7QUFFdEI7QUFDZixZQUFZLHdEQUFRO0FBQ3BCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kZXZpYXRpb24uanM/MmQ1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdmFyaWFuY2UgZnJvbSBcIi4vdmFyaWFuY2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGV2aWF0aW9uKHZhbHVlcywgdmFsdWVvZikge1xuICBjb25zdCB2ID0gdmFyaWFuY2UodmFsdWVzLCB2YWx1ZW9mKTtcbiAgcmV0dXJuIHYgPyBNYXRoLnNxcnQodikgOiB2O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/deviation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/difference.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/difference.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ difference)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction difference(values, ...others) {\n  values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RpZmZlcmVuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXJCO0FBQ2YsZUFBZSxnREFBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RpZmZlcmVuY2UuanM/MmMyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0ludGVyblNldH0gZnJvbSBcImludGVybm1hcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBkaWZmZXJlbmNlKHZhbHVlcywgLi4ub3RoZXJzKSB7XG4gIHZhbHVlcyA9IG5ldyBJbnRlcm5TZXQodmFsdWVzKTtcbiAgZm9yIChjb25zdCBvdGhlciBvZiBvdGhlcnMpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIG90aGVyKSB7XG4gICAgICB2YWx1ZXMuZGVsZXRlKHZhbHVlKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlcztcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/difference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/disjoint.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/disjoint.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ disjoint)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rpc2pvaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DOztBQUVyQjtBQUNmLHVEQUF1RCxnREFBUztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGFBQWE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9kaXNqb2ludC5qcz8zYTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuU2V0fSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRpc2pvaW50KHZhbHVlcywgb3RoZXIpIHtcbiAgY29uc3QgaXRlcmF0b3IgPSBvdGhlcltTeW1ib2wuaXRlcmF0b3JdKCksIHNldCA9IG5ldyBJbnRlcm5TZXQoKTtcbiAgZm9yIChjb25zdCB2IG9mIHZhbHVlcykge1xuICAgIGlmIChzZXQuaGFzKHYpKSByZXR1cm4gZmFsc2U7XG4gICAgbGV0IHZhbHVlLCBkb25lO1xuICAgIHdoaWxlICgoe3ZhbHVlLCBkb25lfSA9IGl0ZXJhdG9yLm5leHQoKSkpIHtcbiAgICAgIGlmIChkb25lKSBicmVhaztcbiAgICAgIGlmIChPYmplY3QuaXModiwgdmFsdWUpKSByZXR1cm4gZmFsc2U7XG4gICAgICBzZXQuYWRkKHZhbHVlKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/disjoint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/every.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/every.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ every)\n/* harmony export */ });\nfunction every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V2ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V2ZXJ5LmpzPzk4ZDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZXZlcnkodmFsdWVzLCB0ZXN0KSB7XG4gIGlmICh0eXBlb2YgdGVzdCAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidGVzdCBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgaWYgKCF0ZXN0KHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG4gIHJldHVybiB0cnVlO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/every.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/extent.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/extent.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ extent)\n/* harmony export */ });\nfunction extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V4dGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V4dGVudC5qcz84ODBlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGV4dGVudCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1pbjtcbiAgbGV0IG1heDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCkge1xuICAgICAgICBpZiAobWluID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICBpZiAodmFsdWUgPj0gdmFsdWUpIG1pbiA9IG1heCA9IHZhbHVlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGlmIChtaW4gPiB2YWx1ZSkgbWluID0gdmFsdWU7XG4gICAgICAgICAgaWYgKG1heCA8IHZhbHVlKSBtYXggPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsKSB7XG4gICAgICAgIGlmIChtaW4gPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgIGlmICh2YWx1ZSA+PSB2YWx1ZSkgbWluID0gbWF4ID0gdmFsdWU7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaWYgKG1pbiA+IHZhbHVlKSBtaW4gPSB2YWx1ZTtcbiAgICAgICAgICBpZiAobWF4IDwgdmFsdWUpIG1heCA9IHZhbHVlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBbbWluLCBtYXhdO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/extent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/filter.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/filter.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ filter)\n/* harmony export */ });\nfunction filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ZpbHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ZpbHRlci5qcz9lOTkyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGZpbHRlcih2YWx1ZXMsIHRlc3QpIHtcbiAgaWYgKHR5cGVvZiB0ZXN0ICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ0ZXN0IGlzIG5vdCBhIGZ1bmN0aW9uXCIpO1xuICBjb25zdCBhcnJheSA9IFtdO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICBpZiAodGVzdCh2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgYXJyYXkucHVzaCh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiBhcnJheTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/fsum.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/fsum.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* binding */ Adder),\n/* harmony export */   fcumsum: () => (/* binding */ fcumsum),\n/* harmony export */   fsum: () => (/* binding */ fsum)\n/* harmony export */ });\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nfunction fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nfunction fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/fsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatest.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/greatest.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatest)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\n\nfunction greatest(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, maxValue) > 0\n          : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUV4QixvQ0FBb0MscURBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHlEQUFTO0FBQ3JCLFlBQVkseURBQVM7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvZ3JlYXRlc3QuanM/YmUwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBncmVhdGVzdCh2YWx1ZXMsIGNvbXBhcmUgPSBhc2NlbmRpbmcpIHtcbiAgbGV0IG1heDtcbiAgbGV0IGRlZmluZWQgPSBmYWxzZTtcbiAgaWYgKGNvbXBhcmUubGVuZ3RoID09PSAxKSB7XG4gICAgbGV0IG1heFZhbHVlO1xuICAgIGZvciAoY29uc3QgZWxlbWVudCBvZiB2YWx1ZXMpIHtcbiAgICAgIGNvbnN0IHZhbHVlID0gY29tcGFyZShlbGVtZW50KTtcbiAgICAgIGlmIChkZWZpbmVkXG4gICAgICAgICAgPyBhc2NlbmRpbmcodmFsdWUsIG1heFZhbHVlKSA+IDBcbiAgICAgICAgICA6IGFzY2VuZGluZyh2YWx1ZSwgdmFsdWUpID09PSAwKSB7XG4gICAgICAgIG1heCA9IGVsZW1lbnQ7XG4gICAgICAgIG1heFZhbHVlID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKGRlZmluZWRcbiAgICAgICAgICA/IGNvbXBhcmUodmFsdWUsIG1heCkgPiAwXG4gICAgICAgICAgOiBjb21wYXJlKHZhbHVlLCB2YWx1ZSkgPT09IDApIHtcbiAgICAgICAgbWF4ID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatestIndex.js":
/*!****************************************************!*\
  !*** ./node_modules/d3-array/src/greatestIndex.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatestIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n\n\n\nfunction greatestIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (compare.length === 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0SW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ0Y7O0FBRXRCLHlDQUF5QyxxREFBUztBQUNqRSxtQ0FBbUMsd0RBQVE7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0SW5kZXguanM/ZmU5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IG1heEluZGV4IGZyb20gXCIuL21heEluZGV4LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdyZWF0ZXN0SW5kZXgodmFsdWVzLCBjb21wYXJlID0gYXNjZW5kaW5nKSB7XG4gIGlmIChjb21wYXJlLmxlbmd0aCA9PT0gMSkgcmV0dXJuIG1heEluZGV4KHZhbHVlcywgY29tcGFyZSk7XG4gIGxldCBtYXhWYWx1ZTtcbiAgbGV0IG1heCA9IC0xO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICArK2luZGV4O1xuICAgIGlmIChtYXggPCAwXG4gICAgICAgID8gY29tcGFyZSh2YWx1ZSwgdmFsdWUpID09PSAwXG4gICAgICAgIDogY29tcGFyZSh2YWx1ZSwgbWF4VmFsdWUpID4gMCkge1xuICAgICAgbWF4VmFsdWUgPSB2YWx1ZTtcbiAgICAgIG1heCA9IGluZGV4O1xuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatestIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/group.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/group.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ group),\n/* harmony export */   flatGroup: () => (/* binding */ flatGroup),\n/* harmony export */   flatRollup: () => (/* binding */ flatRollup),\n/* harmony export */   groups: () => (/* binding */ groups),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   indexes: () => (/* binding */ indexes),\n/* harmony export */   rollup: () => (/* binding */ rollup),\n/* harmony export */   rollups: () => (/* binding */ rollups)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-array/src/identity.js\");\n\n\n\nfunction group(values, ...keys) {\n  return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\n\nfunction groups(values, ...keys) {\n  return nest(values, Array.from, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nfunction flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nfunction flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nfunction rollup(values, reduce, ...keys) {\n  return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], reduce, keys);\n}\n\nfunction rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nfunction index(values, ...keys) {\n  return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], unique, keys);\n}\n\nfunction indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new internmap__WEBPACK_IMPORTED_MODULE_1__.InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/group.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/groupSort.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/groupSort.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ groupSort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./group.js */ \"(ssr)/./node_modules/d3-array/src/group.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n\n\nfunction groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__.rollup)(values, reduce, key), (([ak, av], [bk, bv]) => (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk)))\n    : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk))))\n    .map(([key]) => key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyb3VwU29ydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVDO0FBQ0U7QUFDWjs7QUFFZDtBQUNmO0FBQ0EsTUFBTSxvREFBSSxDQUFDLGlEQUFNLGdEQUFnRCx5REFBUyxZQUFZLHlEQUFTO0FBQy9GLE1BQU0sb0RBQUksQ0FBQyxxREFBSywwREFBMEQseURBQVM7QUFDbkY7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyb3VwU29ydC5qcz81ZDIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhc2NlbmRpbmcgZnJvbSBcIi4vYXNjZW5kaW5nLmpzXCI7XG5pbXBvcnQgZ3JvdXAsIHtyb2xsdXB9IGZyb20gXCIuL2dyb3VwLmpzXCI7XG5pbXBvcnQgc29ydCBmcm9tIFwiLi9zb3J0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdyb3VwU29ydCh2YWx1ZXMsIHJlZHVjZSwga2V5KSB7XG4gIHJldHVybiAocmVkdWNlLmxlbmd0aCAhPT0gMlxuICAgID8gc29ydChyb2xsdXAodmFsdWVzLCByZWR1Y2UsIGtleSksICgoW2FrLCBhdl0sIFtiaywgYnZdKSA9PiBhc2NlbmRpbmcoYXYsIGJ2KSB8fCBhc2NlbmRpbmcoYWssIGJrKSkpXG4gICAgOiBzb3J0KGdyb3VwKHZhbHVlcywga2V5KSwgKChbYWssIGF2XSwgW2JrLCBidl0pID0+IHJlZHVjZShhdiwgYnYpIHx8IGFzY2VuZGluZyhhaywgYmspKSkpXG4gICAgLm1hcCgoW2tleV0pID0+IGtleSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/groupSort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/identity.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/identity.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ identity)\n/* harmony export */ });\nfunction identity(x) {\n  return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9pZGVudGl0eS5qcz9lYzg2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlkZW50aXR5KHgpIHtcbiAgcmV0dXJuIHg7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.Adder),\n/* harmony export */   InternMap: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternMap),\n/* harmony export */   InternSet: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternSet),\n/* harmony export */   ascending: () => (/* reexport safe */ _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   bin: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   bisect: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   bisectCenter: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectCenter),\n/* harmony export */   bisectLeft: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectLeft),\n/* harmony export */   bisectRight: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectRight),\n/* harmony export */   bisector: () => (/* reexport safe */ _bisector_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   blur: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur),\n/* harmony export */   blur2: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur2),\n/* harmony export */   blurImage: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blurImage),\n/* harmony export */   count: () => (/* reexport safe */ _count_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   cross: () => (/* reexport safe */ _cross_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   cumsum: () => (/* reexport safe */ _cumsum_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   descending: () => (/* reexport safe */ _descending_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   deviation: () => (/* reexport safe */ _deviation_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   difference: () => (/* reexport safe */ _difference_js__WEBPACK_IMPORTED_MODULE_50__[\"default\"]),\n/* harmony export */   disjoint: () => (/* reexport safe */ _disjoint_js__WEBPACK_IMPORTED_MODULE_51__[\"default\"]),\n/* harmony export */   every: () => (/* reexport safe */ _every_js__WEBPACK_IMPORTED_MODULE_43__[\"default\"]),\n/* harmony export */   extent: () => (/* reexport safe */ _extent_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   fcumsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fcumsum),\n/* harmony export */   filter: () => (/* reexport safe */ _filter_js__WEBPACK_IMPORTED_MODULE_45__[\"default\"]),\n/* harmony export */   flatGroup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatGroup),\n/* harmony export */   flatRollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatRollup),\n/* harmony export */   fsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fsum),\n/* harmony export */   greatest: () => (/* reexport safe */ _greatest_js__WEBPACK_IMPORTED_MODULE_34__[\"default\"]),\n/* harmony export */   greatestIndex: () => (/* reexport safe */ _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__[\"default\"]),\n/* harmony export */   group: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   groupSort: () => (/* reexport safe */ _groupSort_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   groups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.groups),\n/* harmony export */   histogram: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   index: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.index),\n/* harmony export */   indexes: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.indexes),\n/* harmony export */   intersection: () => (/* reexport safe */ _intersection_js__WEBPACK_IMPORTED_MODULE_52__[\"default\"]),\n/* harmony export */   least: () => (/* reexport safe */ _least_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   leastIndex: () => (/* reexport safe */ _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   map: () => (/* reexport safe */ _map_js__WEBPACK_IMPORTED_MODULE_46__[\"default\"]),\n/* harmony export */   max: () => (/* reexport safe */ _max_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   maxIndex: () => (/* reexport safe */ _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   mean: () => (/* reexport safe */ _mean_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   median: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   medianIndex: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__.medianIndex),\n/* harmony export */   merge: () => (/* reexport safe */ _merge_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   min: () => (/* reexport safe */ _min_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   minIndex: () => (/* reexport safe */ _minIndex_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   mode: () => (/* reexport safe */ _mode_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   nice: () => (/* reexport safe */ _nice_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   pairs: () => (/* reexport safe */ _pairs_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   permute: () => (/* reexport safe */ _permute_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   quantile: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   quantileIndex: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileIndex),\n/* harmony export */   quantileSorted: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileSorted),\n/* harmony export */   quickselect: () => (/* reexport safe */ _quickselect_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   range: () => (/* reexport safe */ _range_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   rank: () => (/* reexport safe */ _rank_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   reduce: () => (/* reexport safe */ _reduce_js__WEBPACK_IMPORTED_MODULE_47__[\"default\"]),\n/* harmony export */   reverse: () => (/* reexport safe */ _reverse_js__WEBPACK_IMPORTED_MODULE_48__[\"default\"]),\n/* harmony export */   rollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollup),\n/* harmony export */   rollups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollups),\n/* harmony export */   scan: () => (/* reexport safe */ _scan_js__WEBPACK_IMPORTED_MODULE_36__[\"default\"]),\n/* harmony export */   shuffle: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__[\"default\"]),\n/* harmony export */   shuffler: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__.shuffler),\n/* harmony export */   some: () => (/* reexport safe */ _some_js__WEBPACK_IMPORTED_MODULE_44__[\"default\"]),\n/* harmony export */   sort: () => (/* reexport safe */ _sort_js__WEBPACK_IMPORTED_MODULE_49__[\"default\"]),\n/* harmony export */   subset: () => (/* reexport safe */ _subset_js__WEBPACK_IMPORTED_MODULE_53__[\"default\"]),\n/* harmony export */   sum: () => (/* reexport safe */ _sum_js__WEBPACK_IMPORTED_MODULE_38__[\"default\"]),\n/* harmony export */   superset: () => (/* reexport safe */ _superset_js__WEBPACK_IMPORTED_MODULE_54__[\"default\"]),\n/* harmony export */   thresholdFreedmanDiaconis: () => (/* reexport safe */ _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   thresholdScott: () => (/* reexport safe */ _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   thresholdSturges: () => (/* reexport safe */ _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   tickIncrement: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickIncrement),\n/* harmony export */   tickStep: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickStep),\n/* harmony export */   ticks: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__[\"default\"]),\n/* harmony export */   transpose: () => (/* reexport safe */ _transpose_js__WEBPACK_IMPORTED_MODULE_40__[\"default\"]),\n/* harmony export */   union: () => (/* reexport safe */ _union_js__WEBPACK_IMPORTED_MODULE_55__[\"default\"]),\n/* harmony export */   variance: () => (/* reexport safe */ _variance_js__WEBPACK_IMPORTED_MODULE_41__[\"default\"]),\n/* harmony export */   zip: () => (/* reexport safe */ _zip_js__WEBPACK_IMPORTED_MODULE_42__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _blur_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blur.js */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/./node_modules/d3-array/src/cross.js\");\n/* harmony import */ var _cumsum_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./cumsum.js */ \"(ssr)/./node_modules/d3-array/src/cumsum.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./deviation.js */ \"(ssr)/./node_modules/d3-array/src/deviation.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _fsum_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./fsum.js */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./group.js */ \"(ssr)/./node_modules/d3-array/src/group.js\");\n/* harmony import */ var _groupSort_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./groupSort.js */ \"(ssr)/./node_modules/d3-array/src/groupSort.js\");\n/* harmony import */ var _bin_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./bin.js */ \"(ssr)/./node_modules/d3-array/src/bin.js\");\n/* harmony import */ var _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./threshold/freedmanDiaconis.js */ \"(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js\");\n/* harmony import */ var _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./threshold/scott.js */ \"(ssr)/./node_modules/d3-array/src/threshold/scott.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _mean_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./mean.js */ \"(ssr)/./node_modules/d3-array/src/mean.js\");\n/* harmony import */ var _median_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./median.js */ \"(ssr)/./node_modules/d3-array/src/median.js\");\n/* harmony import */ var _merge_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./merge.js */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _mode_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./mode.js */ \"(ssr)/./node_modules/d3-array/src/mode.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _pairs_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./pairs.js */ \"(ssr)/./node_modules/d3-array/src/pairs.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _range_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./range.js */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _rank_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rank.js */ \"(ssr)/./node_modules/d3-array/src/rank.js\");\n/* harmony import */ var _least_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./least.js */ \"(ssr)/./node_modules/d3-array/src/least.js\");\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/./node_modules/d3-array/src/leastIndex.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n/* harmony import */ var _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./greatestIndex.js */ \"(ssr)/./node_modules/d3-array/src/greatestIndex.js\");\n/* harmony import */ var _scan_js__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./scan.js */ \"(ssr)/./node_modules/d3-array/src/scan.js\");\n/* harmony import */ var _shuffle_js__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./shuffle.js */ \"(ssr)/./node_modules/d3-array/src/shuffle.js\");\n/* harmony import */ var _sum_js__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./sum.js */ \"(ssr)/./node_modules/d3-array/src/sum.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/./node_modules/d3-array/src/transpose.js\");\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/./node_modules/d3-array/src/variance.js\");\n/* harmony import */ var _zip_js__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ./zip.js */ \"(ssr)/./node_modules/d3-array/src/zip.js\");\n/* harmony import */ var _every_js__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! ./every.js */ \"(ssr)/./node_modules/d3-array/src/every.js\");\n/* harmony import */ var _some_js__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ./some.js */ \"(ssr)/./node_modules/d3-array/src/some.js\");\n/* harmony import */ var _filter_js__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ./filter.js */ \"(ssr)/./node_modules/d3-array/src/filter.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/d3-array/src/map.js\");\n/* harmony import */ var _reduce_js__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ./reduce.js */ \"(ssr)/./node_modules/d3-array/src/reduce.js\");\n/* harmony import */ var _reverse_js__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ./reverse.js */ \"(ssr)/./node_modules/d3-array/src/reverse.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _difference_js__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ./difference.js */ \"(ssr)/./node_modules/d3-array/src/difference.js\");\n/* harmony import */ var _disjoint_js__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! ./disjoint.js */ \"(ssr)/./node_modules/d3-array/src/disjoint.js\");\n/* harmony import */ var _intersection_js__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./intersection.js */ \"(ssr)/./node_modules/d3-array/src/intersection.js\");\n/* harmony import */ var _subset_js__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! ./subset.js */ \"(ssr)/./node_modules/d3-array/src/subset.js\");\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/./node_modules/d3-array/src/superset.js\");\n/* harmony import */ var _union_js__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! ./union.js */ \"(ssr)/./node_modules/d3-array/src/union.js\");\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use bin.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use leastIndex.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/intersection.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-array/src/intersection.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ intersection)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction intersection(values, ...others) {\n  values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet ? values : new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ludGVyc2VjdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQzs7QUFFckI7QUFDZixlQUFlLGdEQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSwyQkFBMkIsZ0RBQVMsZ0JBQWdCLGdEQUFTO0FBQzdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvaW50ZXJzZWN0aW9uLmpzPzhhYjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtJbnRlcm5TZXR9IGZyb20gXCJpbnRlcm5tYXBcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaW50ZXJzZWN0aW9uKHZhbHVlcywgLi4ub3RoZXJzKSB7XG4gIHZhbHVlcyA9IG5ldyBJbnRlcm5TZXQodmFsdWVzKTtcbiAgb3RoZXJzID0gb3RoZXJzLm1hcChzZXQpO1xuICBvdXQ6IGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgZm9yIChjb25zdCBvdGhlciBvZiBvdGhlcnMpIHtcbiAgICAgIGlmICghb3RoZXIuaGFzKHZhbHVlKSkge1xuICAgICAgICB2YWx1ZXMuZGVsZXRlKHZhbHVlKTtcbiAgICAgICAgY29udGludWUgb3V0O1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gdmFsdWVzO1xufVxuXG5mdW5jdGlvbiBzZXQodmFsdWVzKSB7XG4gIHJldHVybiB2YWx1ZXMgaW5zdGFuY2VvZiBJbnRlcm5TZXQgPyB2YWx1ZXMgOiBuZXcgSW50ZXJuU2V0KHZhbHVlcyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/intersection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/least.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/least.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ least)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\n\nfunction least(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, minValue) < 0\n          : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUV4QixpQ0FBaUMscURBQVM7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHlEQUFTO0FBQ3JCLFlBQVkseURBQVM7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbGVhc3QuanM/NGQzZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsZWFzdCh2YWx1ZXMsIGNvbXBhcmUgPSBhc2NlbmRpbmcpIHtcbiAgbGV0IG1pbjtcbiAgbGV0IGRlZmluZWQgPSBmYWxzZTtcbiAgaWYgKGNvbXBhcmUubGVuZ3RoID09PSAxKSB7XG4gICAgbGV0IG1pblZhbHVlO1xuICAgIGZvciAoY29uc3QgZWxlbWVudCBvZiB2YWx1ZXMpIHtcbiAgICAgIGNvbnN0IHZhbHVlID0gY29tcGFyZShlbGVtZW50KTtcbiAgICAgIGlmIChkZWZpbmVkXG4gICAgICAgICAgPyBhc2NlbmRpbmcodmFsdWUsIG1pblZhbHVlKSA8IDBcbiAgICAgICAgICA6IGFzY2VuZGluZyh2YWx1ZSwgdmFsdWUpID09PSAwKSB7XG4gICAgICAgIG1pbiA9IGVsZW1lbnQ7XG4gICAgICAgIG1pblZhbHVlID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKGRlZmluZWRcbiAgICAgICAgICA/IGNvbXBhcmUodmFsdWUsIG1pbikgPCAwXG4gICAgICAgICAgOiBjb21wYXJlKHZhbHVlLCB2YWx1ZSkgPT09IDApIHtcbiAgICAgICAgbWluID0gdmFsdWU7XG4gICAgICAgIGRlZmluZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/least.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/leastIndex.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/leastIndex.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leastIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n\n\n\nfunction leastIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (compare.length === 1) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0SW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ0Y7O0FBRXRCLHNDQUFzQyxxREFBUztBQUM5RCxtQ0FBbUMsd0RBQVE7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0SW5kZXguanM/NGM5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IG1pbkluZGV4IGZyb20gXCIuL21pbkluZGV4LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGxlYXN0SW5kZXgodmFsdWVzLCBjb21wYXJlID0gYXNjZW5kaW5nKSB7XG4gIGlmIChjb21wYXJlLmxlbmd0aCA9PT0gMSkgcmV0dXJuIG1pbkluZGV4KHZhbHVlcywgY29tcGFyZSk7XG4gIGxldCBtaW5WYWx1ZTtcbiAgbGV0IG1pbiA9IC0xO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICArK2luZGV4O1xuICAgIGlmIChtaW4gPCAwXG4gICAgICAgID8gY29tcGFyZSh2YWx1ZSwgdmFsdWUpID09PSAwXG4gICAgICAgIDogY29tcGFyZSh2YWx1ZSwgbWluVmFsdWUpIDwgMCkge1xuICAgICAgbWluVmFsdWUgPSB2YWx1ZTtcbiAgICAgIG1pbiA9IGluZGV4O1xuICAgIH1cbiAgfVxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/leastIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/map.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/map.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ map)\n/* harmony export */ });\nfunction map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21hcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21hcC5qcz8zZTQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1hcCh2YWx1ZXMsIG1hcHBlcikge1xuICBpZiAodHlwZW9mIHZhbHVlc1tTeW1ib2wuaXRlcmF0b3JdICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ2YWx1ZXMgaXMgbm90IGl0ZXJhYmxlXCIpO1xuICBpZiAodHlwZW9mIG1hcHBlciAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwibWFwcGVyIGlzIG5vdCBhIGZ1bmN0aW9uXCIpO1xuICByZXR1cm4gQXJyYXkuZnJvbSh2YWx1ZXMsICh2YWx1ZSwgaW5kZXgpID0+IG1hcHBlcih2YWx1ZSwgaW5kZXgsIHZhbHVlcykpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/max.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/max.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ max)\n/* harmony export */ });\nfunction max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWF4LmpzPzQ5YjgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWF4KHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgbWF4O1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1heCA8IHZhbHVlIHx8IChtYXggPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1heCA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1heCA8IHZhbHVlIHx8IChtYXggPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1heCA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/max.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/maxIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/maxIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ maxIndex)\n/* harmony export */ });\nfunction maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heEluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWF4SW5kZXguanM/ZjUzZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtYXhJbmRleCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1heDtcbiAgbGV0IG1heEluZGV4ID0gLTE7XG4gIGxldCBpbmRleCA9IC0xO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgICsraW5kZXg7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZSwgbWF4SW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZSwgbWF4SW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heEluZGV4O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/maxIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/mean.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/mean.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mean)\n/* harmony export */ });\nfunction mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lYW4uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tZWFuLmpzP2EwYWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWVhbih2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IGNvdW50ID0gMDtcbiAgbGV0IHN1bSA9IDA7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgKytjb3VudCwgc3VtICs9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgKytjb3VudCwgc3VtICs9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBpZiAoY291bnQpIHJldHVybiBzdW0gLyBjb3VudDtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/mean.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/median.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/median.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ median),\n/* harmony export */   medianIndex: () => (/* binding */ medianIndex)\n/* harmony export */ });\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n\n\nfunction median(values, valueof) {\n  return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, 0.5, valueof);\n}\n\nfunction medianIndex(values, valueof) {\n  return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__.quantileIndex)(values, 0.5, valueof);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lZGlhbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0Q7O0FBRXZDO0FBQ2YsU0FBUyx3REFBUTtBQUNqQjs7QUFFTztBQUNQLFNBQVMsMkRBQWE7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9tZWRpYW4uanM/ZThiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcXVhbnRpbGUsIHtxdWFudGlsZUluZGV4fSBmcm9tIFwiLi9xdWFudGlsZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtZWRpYW4odmFsdWVzLCB2YWx1ZW9mKSB7XG4gIHJldHVybiBxdWFudGlsZSh2YWx1ZXMsIDAuNSwgdmFsdWVvZik7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBtZWRpYW5JbmRleCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgcmV0dXJuIHF1YW50aWxlSW5kZXgodmFsdWVzLCAwLjUsIHZhbHVlb2YpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/median.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/merge.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/merge.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ merge)\n/* harmony export */ });\nfunction* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nfunction merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lcmdlLmpzP2M3NmYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24qIGZsYXR0ZW4oYXJyYXlzKSB7XG4gIGZvciAoY29uc3QgYXJyYXkgb2YgYXJyYXlzKSB7XG4gICAgeWllbGQqIGFycmF5O1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1lcmdlKGFycmF5cykge1xuICByZXR1cm4gQXJyYXkuZnJvbShmbGF0dGVuKGFycmF5cykpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/min.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/min.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ min)\n/* harmony export */ });\nfunction min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWluLmpzPzc3ZTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWluKHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgbWluO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1pbiA+IHZhbHVlIHx8IChtaW4gPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1pbiA+IHZhbHVlIHx8IChtaW4gPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/min.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/minIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/minIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ minIndex)\n/* harmony export */ });\nfunction minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbkluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWluSW5kZXguanM/YThlOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtaW5JbmRleCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1pbjtcbiAgbGV0IG1pbkluZGV4ID0gLTE7XG4gIGxldCBpbmRleCA9IC0xO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgICsraW5kZXg7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZSwgbWluSW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZSwgbWluSW5kZXggPSBpbmRleDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbkluZGV4O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/minIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/mode.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/mode.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mode)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction mode(values, valueof) {\n  const counts = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXJCO0FBQ2YscUJBQXFCLGdEQUFTO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbW9kZS5qcz84ODAwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuTWFwfSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1vZGUodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGNvbnN0IGNvdW50cyA9IG5ldyBJbnRlcm5NYXAoKTtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgdmFsdWUgPj0gdmFsdWUpIHtcbiAgICAgICAgY291bnRzLnNldCh2YWx1ZSwgKGNvdW50cy5nZXQodmFsdWUpIHx8IDApICsgMSk7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgdmFsdWUgPj0gdmFsdWUpIHtcbiAgICAgICAgY291bnRzLnNldCh2YWx1ZSwgKGNvdW50cy5nZXQodmFsdWUpIHx8IDApICsgMSk7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGxldCBtb2RlVmFsdWU7XG4gIGxldCBtb2RlQ291bnQgPSAwO1xuICBmb3IgKGNvbnN0IFt2YWx1ZSwgY291bnRdIG9mIGNvdW50cykge1xuICAgIGlmIChjb3VudCA+IG1vZGVDb3VudCkge1xuICAgICAgbW9kZUNvdW50ID0gY291bnQ7XG4gICAgICBtb2RlVmFsdWUgPSB2YWx1ZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1vZGVWYWx1ZTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/mode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/nice.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/nice.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nice)\n/* harmony export */ });\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n\n\nfunction nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_0__.tickIncrement)(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL25pY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7O0FBRTFCO0FBQ2Y7QUFDQTtBQUNBLGlCQUFpQix3REFBYTtBQUM5QjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbmljZS5qcz83ZWZhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dGlja0luY3JlbWVudH0gZnJvbSBcIi4vdGlja3MuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbmljZShzdGFydCwgc3RvcCwgY291bnQpIHtcbiAgbGV0IHByZXN0ZXA7XG4gIHdoaWxlICh0cnVlKSB7XG4gICAgY29uc3Qgc3RlcCA9IHRpY2tJbmNyZW1lbnQoc3RhcnQsIHN0b3AsIGNvdW50KTtcbiAgICBpZiAoc3RlcCA9PT0gcHJlc3RlcCB8fCBzdGVwID09PSAwIHx8ICFpc0Zpbml0ZShzdGVwKSkge1xuICAgICAgcmV0dXJuIFtzdGFydCwgc3RvcF07XG4gICAgfSBlbHNlIGlmIChzdGVwID4gMCkge1xuICAgICAgc3RhcnQgPSBNYXRoLmZsb29yKHN0YXJ0IC8gc3RlcCkgKiBzdGVwO1xuICAgICAgc3RvcCA9IE1hdGguY2VpbChzdG9wIC8gc3RlcCkgKiBzdGVwO1xuICAgIH0gZWxzZSBpZiAoc3RlcCA8IDApIHtcbiAgICAgIHN0YXJ0ID0gTWF0aC5jZWlsKHN0YXJ0ICogc3RlcCkgLyBzdGVwO1xuICAgICAgc3RvcCA9IE1hdGguZmxvb3Ioc3RvcCAqIHN0ZXApIC8gc3RlcDtcbiAgICB9XG4gICAgcHJlc3RlcCA9IHN0ZXA7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/nice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/number.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/number.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number),\n/* harmony export */   numbers: () => (/* binding */ numbers)\n/* harmony export */ });\nfunction number(x) {\n  return x === null ? NaN : +x;\n}\n\nfunction* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL251bWJlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFlO0FBQ2Y7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbnVtYmVyLmpzPzE0ZGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbnVtYmVyKHgpIHtcbiAgcmV0dXJuIHggPT09IG51bGwgPyBOYU4gOiAreDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uKiBudW1iZXJzKHZhbHVlcywgdmFsdWVvZikge1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgIHlpZWxkIHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/pairs.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/pairs.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pairs),\n/* harmony export */   pair: () => (/* binding */ pair)\n/* harmony export */ });\nfunction pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nfunction pair(a, b) {\n  return [a, b];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3BhaXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9wYWlycy5qcz9lMzc5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHBhaXJzKHZhbHVlcywgcGFpcm9mID0gcGFpcikge1xuICBjb25zdCBwYWlycyA9IFtdO1xuICBsZXQgcHJldmlvdXM7XG4gIGxldCBmaXJzdCA9IGZhbHNlO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmIChmaXJzdCkgcGFpcnMucHVzaChwYWlyb2YocHJldmlvdXMsIHZhbHVlKSk7XG4gICAgcHJldmlvdXMgPSB2YWx1ZTtcbiAgICBmaXJzdCA9IHRydWU7XG4gIH1cbiAgcmV0dXJuIHBhaXJzO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcGFpcihhLCBiKSB7XG4gIHJldHVybiBbYSwgYl07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/pairs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/permute.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/permute.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ permute)\n/* harmony export */ });\nfunction permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3Blcm11dGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3Blcm11dGUuanM/ZTFjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBwZXJtdXRlKHNvdXJjZSwga2V5cykge1xuICByZXR1cm4gQXJyYXkuZnJvbShrZXlzLCBrZXkgPT4gc291cmNlW2tleV0pO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/permute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quantile.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/quantile.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile),\n/* harmony export */   quantileIndex: () => (/* binding */ quantileIndex),\n/* harmony export */   quantileSorted: () => (/* binding */ quantileSorted)\n/* harmony export */ });\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n\n\n\n\n\n\n\n\n\nfunction quantile(values, p, valueof) {\n  values = Float64Array.from((0,_number_js__WEBPACK_IMPORTED_MODULE_0__.numbers)(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n  if (p >= 1) return (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values, i0).subarray(0, i0 + 1)),\n      value1 = (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction quantileSorted(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction quantileIndex(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => (0,_number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(valueof(values[i], i, values)));\n  if (p <= 0) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(numbers);\n  if (p >= 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(numbers);\n  var numbers,\n      index = Uint32Array.from(values, (_, i) => i),\n      j = numbers.length - 1,\n      i = Math.floor(j * p);\n  (0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(index, i, 0, j, (i, j) => (0,_sort_js__WEBPACK_IMPORTED_MODULE_6__.ascendingDefined)(numbers[i], numbers[j]));\n  i = (0,_greatest_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(index.subarray(0, i + 1), (i) => numbers[i]);\n  return i >= 0 ? i : -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quickselect.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-array/src/quickselect.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quickselect)\n/* harmony export */ });\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? _sort_js__WEBPACK_IMPORTED_MODULE_0__.ascendingDefined : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__.compareDefined)(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quickselect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/range.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/range.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ range)\n/* harmony export */ });\nfunction range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzPzc0MGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcmFuZ2Uoc3RhcnQsIHN0b3AsIHN0ZXApIHtcbiAgc3RhcnQgPSArc3RhcnQsIHN0b3AgPSArc3RvcCwgc3RlcCA9IChuID0gYXJndW1lbnRzLmxlbmd0aCkgPCAyID8gKHN0b3AgPSBzdGFydCwgc3RhcnQgPSAwLCAxKSA6IG4gPCAzID8gMSA6ICtzdGVwO1xuXG4gIHZhciBpID0gLTEsXG4gICAgICBuID0gTWF0aC5tYXgoMCwgTWF0aC5jZWlsKChzdG9wIC0gc3RhcnQpIC8gc3RlcCkpIHwgMCxcbiAgICAgIHJhbmdlID0gbmV3IEFycmF5KG4pO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgcmFuZ2VbaV0gPSBzdGFydCArIGkgKiBzdGVwO1xuICB9XG5cbiAgcmV0dXJuIHJhbmdlO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/range.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/rank.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/rank.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rank)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n\nfunction rank(values, valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] ? (i, j) => (0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.ascendingDefined)(V[i], V[j]) : (0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.compareDefined)(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ29COztBQUU1QyxnQ0FBZ0MscURBQVM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0EsMERBQTBELHFEQUFTO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHFEQUFTLGFBQWEsMERBQWdCLGVBQWUsd0RBQWM7QUFDN0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9yYW5rLmpzPzAwZDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCB7YXNjZW5kaW5nRGVmaW5lZCwgY29tcGFyZURlZmluZWR9IGZyb20gXCIuL3NvcnQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcmFuayh2YWx1ZXMsIHZhbHVlb2YgPSBhc2NlbmRpbmcpIHtcbiAgaWYgKHR5cGVvZiB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidmFsdWVzIGlzIG5vdCBpdGVyYWJsZVwiKTtcbiAgbGV0IFYgPSBBcnJheS5mcm9tKHZhbHVlcyk7XG4gIGNvbnN0IFIgPSBuZXcgRmxvYXQ2NEFycmF5KFYubGVuZ3RoKTtcbiAgaWYgKHZhbHVlb2YubGVuZ3RoICE9PSAyKSBWID0gVi5tYXAodmFsdWVvZiksIHZhbHVlb2YgPSBhc2NlbmRpbmc7XG4gIGNvbnN0IGNvbXBhcmVJbmRleCA9IChpLCBqKSA9PiB2YWx1ZW9mKFZbaV0sIFZbal0pO1xuICBsZXQgaywgcjtcbiAgdmFsdWVzID0gVWludDMyQXJyYXkuZnJvbShWLCAoXywgaSkgPT4gaSk7XG4gIC8vIFJpc2t5IGNoYWluaW5nIGR1ZSB0byBTYWZhcmkgMTQgaHR0cHM6Ly9naXRodWIuY29tL2QzL2QzLWFycmF5L2lzc3Vlcy8xMjNcbiAgdmFsdWVzLnNvcnQodmFsdWVvZiA9PT0gYXNjZW5kaW5nID8gKGksIGopID0+IGFzY2VuZGluZ0RlZmluZWQoVltpXSwgVltqXSkgOiBjb21wYXJlRGVmaW5lZChjb21wYXJlSW5kZXgpKTtcbiAgdmFsdWVzLmZvckVhY2goKGosIGkpID0+IHtcbiAgICAgIGNvbnN0IGMgPSBjb21wYXJlSW5kZXgoaiwgayA9PT0gdW5kZWZpbmVkID8gaiA6IGspO1xuICAgICAgaWYgKGMgPj0gMCkge1xuICAgICAgICBpZiAoayA9PT0gdW5kZWZpbmVkIHx8IGMgPiAwKSBrID0gaiwgciA9IGk7XG4gICAgICAgIFJbal0gPSByO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgUltqXSA9IE5hTjtcbiAgICAgIH1cbiAgICB9KTtcbiAgcmV0dXJuIFI7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/rank.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/reduce.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/reduce.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JlZHVjZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sYUFBYTtBQUNuQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JlZHVjZS5qcz9jMGUwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJlZHVjZSh2YWx1ZXMsIHJlZHVjZXIsIHZhbHVlKSB7XG4gIGlmICh0eXBlb2YgcmVkdWNlciAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwicmVkdWNlciBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgY29uc3QgaXRlcmF0b3IgPSB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSgpO1xuICBsZXQgZG9uZSwgbmV4dCwgaW5kZXggPSAtMTtcbiAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPCAzKSB7XG4gICAgKHtkb25lLCB2YWx1ZX0gPSBpdGVyYXRvci5uZXh0KCkpO1xuICAgIGlmIChkb25lKSByZXR1cm47XG4gICAgKytpbmRleDtcbiAgfVxuICB3aGlsZSAoKHtkb25lLCB2YWx1ZTogbmV4dH0gPSBpdGVyYXRvci5uZXh0KCkpLCAhZG9uZSkge1xuICAgIHZhbHVlID0gcmVkdWNlcih2YWx1ZSwgbmV4dCwgKytpbmRleCwgdmFsdWVzKTtcbiAgfVxuICByZXR1cm4gdmFsdWU7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/reduce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/reverse.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/reverse.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reverse)\n/* harmony export */ });\nfunction reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JldmVyc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvcmV2ZXJzZS5qcz83ZTg3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJldmVyc2UodmFsdWVzKSB7XG4gIGlmICh0eXBlb2YgdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0gIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInZhbHVlcyBpcyBub3QgaXRlcmFibGVcIik7XG4gIHJldHVybiBBcnJheS5mcm9tKHZhbHVlcykucmV2ZXJzZSgpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/reverse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/scan.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/scan.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ scan)\n/* harmony export */ });\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/./node_modules/d3-array/src/leastIndex.js\");\n\n\nfunction scan(values, compare) {\n  const index = (0,_leastIndex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, compare);\n  return index < 0 ? undefined : index;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NjYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7O0FBRTFCO0FBQ2YsZ0JBQWdCLDBEQUFVO0FBQzFCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zY2FuLmpzP2E0ZTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGxlYXN0SW5kZXggZnJvbSBcIi4vbGVhc3RJbmRleC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzY2FuKHZhbHVlcywgY29tcGFyZSkge1xuICBjb25zdCBpbmRleCA9IGxlYXN0SW5kZXgodmFsdWVzLCBjb21wYXJlKTtcbiAgcmV0dXJuIGluZGV4IDwgMCA/IHVuZGVmaW5lZCA6IGluZGV4O1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/scan.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/shuffle.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/shuffle.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   shuffler: () => (/* binding */ shuffler)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (shuffler(Math.random));\n\nfunction shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NodWZmbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxpRUFBZSxxQkFBcUIsRUFBQzs7QUFFOUI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NodWZmbGUuanM/YWExOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBzaHVmZmxlcihNYXRoLnJhbmRvbSk7XG5cbmV4cG9ydCBmdW5jdGlvbiBzaHVmZmxlcihyYW5kb20pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIHNodWZmbGUoYXJyYXksIGkwID0gMCwgaTEgPSBhcnJheS5sZW5ndGgpIHtcbiAgICBsZXQgbSA9IGkxIC0gKGkwID0gK2kwKTtcbiAgICB3aGlsZSAobSkge1xuICAgICAgY29uc3QgaSA9IHJhbmRvbSgpICogbS0tIHwgMCwgdCA9IGFycmF5W20gKyBpMF07XG4gICAgICBhcnJheVttICsgaTBdID0gYXJyYXlbaSArIGkwXTtcbiAgICAgIGFycmF5W2kgKyBpMF0gPSB0O1xuICAgIH1cbiAgICByZXR1cm4gYXJyYXk7XG4gIH07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/shuffle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/some.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/some.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ some)\n/* harmony export */ });\nfunction some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NvbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc29tZS5qcz9mNmRmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHNvbWUodmFsdWVzLCB0ZXN0KSB7XG4gIGlmICh0eXBlb2YgdGVzdCAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidGVzdCBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgaWYgKHRlc3QodmFsdWUsICsraW5kZXgsIHZhbHVlcykpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/some.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sort.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/sort.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ascendingDefined: () => (/* binding */ ascendingDefined),\n/* harmony export */   compareDefined: () => (/* binding */ compareDefined),\n/* harmony export */   \"default\": () => (/* binding */ sort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n\n\n\nfunction sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if ((f && f.length !== 2) || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return (0,_permute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\n\nfunction compareDefined(compare = _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n  if (compare === _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nfunction ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/subset.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/subset.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ subset)\n/* harmony export */ });\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/./node_modules/d3-array/src/superset.js\");\n\n\nfunction subset(values, other) {\n  return (0,_superset_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(other, values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1YnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQzs7QUFFdEI7QUFDZixTQUFTLHdEQUFRO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvc3Vic2V0LmpzPzg1ZjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHN1cGVyc2V0IGZyb20gXCIuL3N1cGVyc2V0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHN1YnNldCh2YWx1ZXMsIG90aGVyKSB7XG4gIHJldHVybiBzdXBlcnNldChvdGhlciwgdmFsdWVzKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/subset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sum.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/sum.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ sum)\n/* harmony export */ });\nfunction sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1bS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zdW0uanM/OThjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzdW0odmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBzdW0gPSAwO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgPSArdmFsdWUpIHtcbiAgICAgICAgc3VtICs9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSA9ICt2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSB7XG4gICAgICAgIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHN1bTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/superset.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/superset.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ superset)\n/* harmony export */ });\nfunction superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1cGVyc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGFBQWE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2QzLWFycmF5L3NyYy9zdXBlcnNldC5qcz8yMWMyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHN1cGVyc2V0KHZhbHVlcywgb3RoZXIpIHtcbiAgY29uc3QgaXRlcmF0b3IgPSB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSgpLCBzZXQgPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3QgbyBvZiBvdGhlcikge1xuICAgIGNvbnN0IGlvID0gaW50ZXJuKG8pO1xuICAgIGlmIChzZXQuaGFzKGlvKSkgY29udGludWU7XG4gICAgbGV0IHZhbHVlLCBkb25lO1xuICAgIHdoaWxlICgoe3ZhbHVlLCBkb25lfSA9IGl0ZXJhdG9yLm5leHQoKSkpIHtcbiAgICAgIGlmIChkb25lKSByZXR1cm4gZmFsc2U7XG4gICAgICBjb25zdCBpdmFsdWUgPSBpbnRlcm4odmFsdWUpO1xuICAgICAgc2V0LmFkZChpdmFsdWUpO1xuICAgICAgaWYgKE9iamVjdC5pcyhpbywgaXZhbHVlKSkgYnJlYWs7XG4gICAgfVxuICB9XG4gIHJldHVybiB0cnVlO1xufVxuXG5mdW5jdGlvbiBpbnRlcm4odmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlICE9PSBudWxsICYmIHR5cGVvZiB2YWx1ZSA9PT0gXCJvYmplY3RcIiA/IHZhbHVlLnZhbHVlT2YoKSA6IHZhbHVlO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/superset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js":
/*!*****************************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/freedmanDiaconis.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdFreedmanDiaconis)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n\n\n\nfunction thresholdFreedmanDiaconis(values, min, max) {\n  const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.75) - (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9mcmVlZG1hbkRpYWNvbmlzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUNNOztBQUV2QjtBQUNmLFlBQVkscURBQUssY0FBYyx3REFBUSxpQkFBaUIsd0RBQVE7QUFDaEU7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9mcmVlZG1hbkRpYWNvbmlzLmpzPzVjZDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvdW50IGZyb20gXCIuLi9jb3VudC5qc1wiO1xuaW1wb3J0IHF1YW50aWxlIGZyb20gXCIuLi9xdWFudGlsZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB0aHJlc2hvbGRGcmVlZG1hbkRpYWNvbmlzKHZhbHVlcywgbWluLCBtYXgpIHtcbiAgY29uc3QgYyA9IGNvdW50KHZhbHVlcyksIGQgPSBxdWFudGlsZSh2YWx1ZXMsIDAuNzUpIC0gcXVhbnRpbGUodmFsdWVzLCAwLjI1KTtcbiAgcmV0dXJuIGMgJiYgZCA/IE1hdGguY2VpbCgobWF4IC0gbWluKSAvICgyICogZCAqIE1hdGgucG93KGMsIC0xIC8gMykpKSA6IDE7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/scott.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/scott.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdScott)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../deviation.js */ \"(ssr)/./node_modules/d3-array/src/deviation.js\");\n\n\n\nfunction thresholdScott(values, min, max) {\n  const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_deviation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zY290dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDUTs7QUFFekI7QUFDZixZQUFZLHFEQUFLLGNBQWMseURBQVM7QUFDeEM7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zY290dC5qcz9lMDU4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjb3VudCBmcm9tIFwiLi4vY291bnQuanNcIjtcbmltcG9ydCBkZXZpYXRpb24gZnJvbSBcIi4uL2RldmlhdGlvbi5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB0aHJlc2hvbGRTY290dCh2YWx1ZXMsIG1pbiwgbWF4KSB7XG4gIGNvbnN0IGMgPSBjb3VudCh2YWx1ZXMpLCBkID0gZGV2aWF0aW9uKHZhbHVlcyk7XG4gIHJldHVybiBjICYmIGQgPyBNYXRoLmNlaWwoKG1heCAtIG1pbikgKiBNYXRoLmNicnQoYykgLyAoMy40OSAqIGQpKSA6IDE7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/scott.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/sturges.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/sturges.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdSturges)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n\n\nfunction thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log((0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values)) / Math.LN2) + 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zdHVyZ2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDOztBQUVqQjtBQUNmLHdDQUF3QyxxREFBSztBQUM3QyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zdHVyZ2VzLmpzPzdiM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvdW50IGZyb20gXCIuLi9jb3VudC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB0aHJlc2hvbGRTdHVyZ2VzKHZhbHVlcykge1xuICByZXR1cm4gTWF0aC5tYXgoMSwgTWF0aC5jZWlsKE1hdGgubG9nKGNvdW50KHZhbHVlcykpIC8gTWF0aC5MTjIpICsgMSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/sturges.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ticks.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/ticks.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ticks),\n/* harmony export */   tickIncrement: () => (/* binding */ tickIncrement),\n/* harmony export */   tickStep: () => (/* binding */ tickStep)\n/* harmony export */ });\nconst e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nfunction ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nfunction tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nfunction tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ticks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/transpose.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/transpose.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n\n\nfunction transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = (0,_min_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RyYW5zcG9zZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQjs7QUFFWjtBQUNmO0FBQ0EsdUJBQXVCLG1EQUFHLDRDQUE0QyxRQUFRO0FBQzlFLDJEQUEyRCxRQUFRO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdHJhbnNwb3NlLmpzP2Y4NTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1pbiBmcm9tIFwiLi9taW4uanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdHJhbnNwb3NlKG1hdHJpeCkge1xuICBpZiAoIShuID0gbWF0cml4Lmxlbmd0aCkpIHJldHVybiBbXTtcbiAgZm9yICh2YXIgaSA9IC0xLCBtID0gbWluKG1hdHJpeCwgbGVuZ3RoKSwgdHJhbnNwb3NlID0gbmV3IEFycmF5KG0pOyArK2kgPCBtOykge1xuICAgIGZvciAodmFyIGogPSAtMSwgbiwgcm93ID0gdHJhbnNwb3NlW2ldID0gbmV3IEFycmF5KG4pOyArK2ogPCBuOykge1xuICAgICAgcm93W2pdID0gbWF0cml4W2pdW2ldO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJhbnNwb3NlO1xufVxuXG5mdW5jdGlvbiBsZW5ndGgoZCkge1xuICByZXR1cm4gZC5sZW5ndGg7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/transpose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/union.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/union.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ union)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction union(...others) {\n  const set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3VuaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DOztBQUVyQjtBQUNmLGtCQUFrQixnREFBUztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktaW50ZWxsaWdlbnQtYXNzZXNzbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3VuaW9uLmpzP2Q4NzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtJbnRlcm5TZXR9IGZyb20gXCJpbnRlcm5tYXBcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdW5pb24oLi4ub3RoZXJzKSB7XG4gIGNvbnN0IHNldCA9IG5ldyBJbnRlcm5TZXQoKTtcbiAgZm9yIChjb25zdCBvdGhlciBvZiBvdGhlcnMpIHtcbiAgICBmb3IgKGNvbnN0IG8gb2Ygb3RoZXIpIHtcbiAgICAgIHNldC5hZGQobyk7XG4gICAgfVxuICB9XG4gIHJldHVybiBzZXQ7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/union.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/variance.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/variance.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ variance)\n/* harmony export */ });\nfunction variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3ZhcmlhbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvdmFyaWFuY2UuanM/NzU2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB2YXJpYW5jZSh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IGNvdW50ID0gMDtcbiAgbGV0IGRlbHRhO1xuICBsZXQgbWVhbiA9IDA7XG4gIGxldCBzdW0gPSAwO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgIGRlbHRhID0gdmFsdWUgLSBtZWFuO1xuICAgICAgICBtZWFuICs9IGRlbHRhIC8gKytjb3VudDtcbiAgICAgICAgc3VtICs9IGRlbHRhICogKHZhbHVlIC0gbWVhbik7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICBkZWx0YSA9IHZhbHVlIC0gbWVhbjtcbiAgICAgICAgbWVhbiArPSBkZWx0YSAvICsrY291bnQ7XG4gICAgICAgIHN1bSArPSBkZWx0YSAqICh2YWx1ZSAtIG1lYW4pO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBpZiAoY291bnQgPiAxKSByZXR1cm4gc3VtIC8gKGNvdW50IC0gMSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/variance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/zip.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/zip.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ zip)\n/* harmony export */ });\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/./node_modules/d3-array/src/transpose.js\");\n\n\nfunction zip() {\n  return (0,_transpose_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3ppcC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFeEI7QUFDZixTQUFTLHlEQUFTO0FBQ2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1pbnRlbGxpZ2VudC1hc3Nlc3NtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvemlwLmpzPzYxZmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHRyYW5zcG9zZSBmcm9tIFwiLi90cmFuc3Bvc2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gemlwKCkge1xuICByZXR1cm4gdHJhbnNwb3NlKGFyZ3VtZW50cyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/zip.js\n");

/***/ })

};
;