/**
 * OCTI智能评估系统 - 公益机构画像收集页面
 * 
 * 通过智能对话收集组织画像信息
 */

'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout, PageContainer } from '@/components/layout/main-layout';
import { DialogueChat, DialogueMessage, DialogueSession } from '@/components/dialogue/dialogue-chat';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

// 模拟对话会话
const initialSession: DialogueSession = {
  id: 'session-1',
  status: 'ACTIVE',
  currentRound: 1,
  totalRounds: 8,
  progress: 12.5,
};

// 初始系统消息
const initialMessages: DialogueMessage[] = [
  {
    id: 'msg-1',
    type: 'SYSTEM',
    content: '您好！我是OCTI智能评估系统的AI助手。接下来我将通过几轮对话来了解您的组织情况，这将帮助我们为您生成更精准的个性化评估问卷。\n\n整个对话大约需要8-10轮，每轮我会问1-2个问题，您可以自由回答。准备好了吗？',
    timestamp: new Date(),
    roundNumber: 0,
  },
];

// 模拟AI回复
const mockAIResponses = [
  {
    round: 1,
    questions: [
      '首先，请简单介绍一下您的组织名称、成立时间和主要使命是什么？',
    ],
  },
  {
    round: 2,
    questions: [
      '很好！您的组织目前有多少全职员工和志愿者？组织的规模大概是怎样的？',
    ],
  },
  {
    round: 3,
    questions: [
      '了解了。您的组织主要服务哪些群体？目前正在开展哪些核心项目或服务？',
    ],
  },
  {
    round: 4,
    questions: [
      '非常有意义的工作！您的组织主要的资金来源有哪些？比如政府资助、企业捐赠、个人捐款等？',
    ],
  },
  {
    round: 5,
    questions: [
      '在组织管理方面，您认为目前最大的挑战是什么？比如人员管理、资金筹集、项目执行等？',
    ],
  },
  {
    round: 6,
    questions: [
      '您的组织在技术应用方面如何？比如是否使用管理系统、数据分析工具、在线平台等？',
    ],
  },
  {
    round: 7,
    questions: [
      '关于组织发展，您希望在未来1-2年内重点提升哪些方面的能力？',
    ],
  },
  {
    round: 8,
    questions: [
      '最后一个问题：您对这次能力评估有什么特别的期望？希望重点了解组织的哪些方面？',
    ],
  },
];

// ============================================================================
// 公益机构画像收集页面组件
// ============================================================================

export default function NonprofitProfilePage() {
  const [session, setSession] = useState<DialogueSession>(initialSession);
  const [messages, setMessages] = useState<DialogueMessage[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);

  // 开始对话
  const handleStartDialogue = async () => {
    setHasStarted(true);
    setIsLoading(true);

    // 模拟AI发送第一个问题
    setTimeout(() => {
      const firstQuestion = mockAIResponses[0];
      const newMessage: DialogueMessage = {
        id: `msg-${Date.now()}`,
        type: 'ASSISTANT',
        content: firstQuestion.questions[0],
        timestamp: new Date(),
        roundNumber: firstQuestion.round,
      };

      setMessages(prev => [...prev, newMessage]);
      setIsLoading(false);
    }, 1500);
  };

  // 发送用户消息
  const handleSendMessage = async (content: string) => {
    // 添加用户消息
    const userMessage: DialogueMessage = {
      id: `msg-${Date.now()}-user`,
      type: 'USER',
      content,
      timestamp: new Date(),
      roundNumber: session.currentRound,
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // 模拟AI回复
    setTimeout(() => {
      const nextRound = session.currentRound + 1;
      const nextResponse = mockAIResponses.find(r => r.round === nextRound);

      if (nextResponse) {
        // 继续对话
        const aiMessage: DialogueMessage = {
          id: `msg-${Date.now()}-ai`,
          type: 'ASSISTANT',
          content: nextResponse.questions[0],
          timestamp: new Date(),
          roundNumber: nextResponse.round,
        };

        setMessages(prev => [...prev, aiMessage]);
        setSession(prev => ({
          ...prev,
          currentRound: nextRound,
          progress: (nextRound / prev.totalRounds) * 100,
        }));
      } else {
        // 对话完成
        const completionMessage: DialogueMessage = {
          id: `msg-${Date.now()}-completion`,
          type: 'SYSTEM',
          content: '非常感谢您的详细回答！我已经收集到了足够的组织信息。基于您提供的信息，我将为您生成个性化的评估问卷。\n\n接下来系统将：\n1. 分析您的组织特征\n2. 生成28道个性化问题\n3. 与32道预设标准问题融合\n4. 创建您专属的60题评估问卷\n\n这个过程大约需要2-3分钟，请稍候...',
          timestamp: new Date(),
          roundNumber: 0,
        };

        setMessages(prev => [...prev, completionMessage]);
        setSession(prev => ({
          ...prev,
          status: 'COMPLETED',
          progress: 100,
        }));
      }

      setIsLoading(false);
    }, 2000 + Math.random() * 2000); // 2-4秒随机延迟
  };

  // 完成对话
  const handleComplete = () => {
    // TODO: 跳转到问卷生成页面
    console.log('跳转到问卷生成页面');
  };

  // 取消对话
  const handleCancel = () => {
    setSession(prev => ({ ...prev, status: 'CANCELLED' }));
  };

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title="组织画像收集"
        description="通过智能对话深度了解您的组织，为您生成个性化评估问卷"
        breadcrumb={[
          { title: '首页', href: '/' },
          { title: '评估管理', href: '/assessments' },
          { title: '组织画像收集' },
        ]}
      >
        <div className="max-w-4xl mx-auto">
          {!hasStarted ? (
            // 开始前的介绍
            <div className="space-y-6">
              <div className="text-center py-8">
                <div className="text-6xl mb-4">🎯</div>
                <h2 className="text-2xl font-bold mb-4">智能组织画像收集</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  我们的AI助手将通过8-10轮智能对话，深入了解您的组织特征、发展阶段、核心业务等信息，
                  为您量身定制个性化的评估问卷。
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center p-6 border rounded-lg">
                  <div className="text-3xl mb-3">💬</div>
                  <h3 className="font-semibold mb-2">智能对话</h3>
                  <p className="text-sm text-muted-foreground">
                    AI助手会根据您的回答动态调整问题，确保收集到最相关的信息
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <div className="text-3xl mb-3">⏱️</div>
                  <h3 className="font-semibold mb-2">高效便捷</h3>
                  <p className="text-sm text-muted-foreground">
                    整个对话过程约15-20分钟，比传统表单填写更自然流畅
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <div className="text-3xl mb-3">🎨</div>
                  <h3 className="font-semibold mb-2">个性化定制</h3>
                  <p className="text-sm text-muted-foreground">
                    基于对话内容生成专属问卷，提高评估的针对性和准确性
                  </p>
                </div>
              </div>

              <Alert>
                <AlertDescription>
                  <strong>温馨提示：</strong>
                  请准备好您组织的基本信息，包括成立时间、规模、主要业务、发展挑战等。
                  对话过程中您可以随时暂停或跳过某些问题。
                </AlertDescription>
              </Alert>

              <div className="text-center">
                <Button size="lg" onClick={handleStartDialogue}>
                  开始智能对话
                </Button>
              </div>
            </div>
          ) : (
            // 对话界面
            <div className="h-[600px] border rounded-lg overflow-hidden">
              <DialogueChat
                session={session}
                messages={messages}
                onSendMessage={handleSendMessage}
                onComplete={handleComplete}
                onCancel={handleCancel}
                isLoading={isLoading}
              />
            </div>
          )}

          {/* 对话完成后的操作 */}
          {session.status === 'COMPLETED' && (
            <div className="mt-6 p-6 border rounded-lg bg-green-50">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">✅</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-green-800">画像收集完成！</h3>
                  <p className="text-sm text-green-600 mt-1">
                    我们已经成功收集到您的组织画像信息，现在可以为您生成个性化评估问卷了。
                  </p>
                </div>
                <Button onClick={handleComplete}>
                  生成问卷
                </Button>
              </div>
            </div>
          )}

          {/* 对话取消后的操作 */}
          {session.status === 'CANCELLED' && (
            <div className="mt-6 p-6 border rounded-lg bg-gray-50">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">⏸️</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800">对话已暂停</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    您可以稍后继续，或者选择使用标准问卷进行评估。
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" onClick={() => setSession(prev => ({ ...prev, status: 'ACTIVE' }))}>
                    继续对话
                  </Button>
                  <Button>
                    使用标准问卷
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </PageContainer>
    </MainLayout>
  );
}
