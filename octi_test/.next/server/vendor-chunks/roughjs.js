"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/roughjs";
exports.ids = ["vendor-chunks/roughjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/roughjs/bundled/rough.esm.js":
/*!***************************************************!*\
  !*** ./node_modules/roughjs/bundled/rough.esm.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ at)\n/* harmony export */ });\nfunction t(t,e,s){if(t&&t.length){const[n,o]=e,a=Math.PI/180*s,h=Math.cos(a),r=Math.sin(a);for(const e of t){const[t,s]=e;e[0]=(t-n)*h-(s-o)*r+n,e[1]=(t-n)*r+(s-o)*h+o}}}function e(t,e){return t[0]===e[0]&&t[1]===e[1]}function s(s,n,o,a=1){const h=o,r=Math.max(n,.1),i=s[0]&&s[0][0]&&\"number\"==typeof s[0][0]?[s]:s,c=[0,0];if(h)for(const e of i)t(e,c,h);const l=function(t,s,n){const o=[];for(const s of t){const t=[...s];e(t[0],t[t.length-1])||t.push([t[0][0],t[0][1]]),t.length>2&&o.push(t)}const a=[];s=Math.max(s,.1);const h=[];for(const t of o)for(let e=0;e<t.length-1;e++){const s=t[e],n=t[e+1];if(s[1]!==n[1]){const t=Math.min(s[1],n[1]);h.push({ymin:t,ymax:Math.max(s[1],n[1]),x:t===s[1]?s[0]:n[0],islope:(n[0]-s[0])/(n[1]-s[1])})}}if(h.sort(((t,e)=>t.ymin<e.ymin?-1:t.ymin>e.ymin?1:t.x<e.x?-1:t.x>e.x?1:t.ymax===e.ymax?0:(t.ymax-e.ymax)/Math.abs(t.ymax-e.ymax))),!h.length)return a;let r=[],i=h[0].ymin,c=0;for(;r.length||h.length;){if(h.length){let t=-1;for(let e=0;e<h.length&&!(h[e].ymin>i);e++)t=e;h.splice(0,t+1).forEach((t=>{r.push({s:i,edge:t})}))}if(r=r.filter((t=>!(t.edge.ymax<=i))),r.sort(((t,e)=>t.edge.x===e.edge.x?0:(t.edge.x-e.edge.x)/Math.abs(t.edge.x-e.edge.x))),(1!==n||c%s==0)&&r.length>1)for(let t=0;t<r.length;t+=2){const e=t+1;if(e>=r.length)break;const s=r[t].edge,n=r[e].edge;a.push([[Math.round(s.x),i],[Math.round(n.x),i]])}i+=n,r.forEach((t=>{t.edge.x=t.edge.x+n*t.edge.islope})),c++}return a}(i,r,a);if(h){for(const e of i)t(e,c,-h);!function(e,s,n){const o=[];e.forEach((t=>o.push(...t))),t(o,s,n)}(l,c,-h)}return l}function n(t,e){var n;const o=e.hachureAngle+90;let a=e.hachureGap;a<0&&(a=4*e.strokeWidth),a=Math.round(Math.max(a,.1));let h=1;return e.roughness>=1&&((null===(n=e.randomizer)||void 0===n?void 0:n.next())||Math.random())>.7&&(h=a),s(t,a,o,h||1)}class o{constructor(t){this.helper=t}fillPolygons(t,e){return this._fillPolygons(t,e)}_fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.renderLines(s,e)}}renderLines(t,e){const s=[];for(const n of t)s.push(...this.helper.doubleLineOps(n[0][0],n[0][1],n[1][0],n[1][1],e));return s}}function a(t){const e=t[0],s=t[1];return Math.sqrt(Math.pow(e[0]-s[0],2)+Math.pow(e[1]-s[1],2))}class h extends o{fillPolygons(t,e){let s=e.hachureGap;s<0&&(s=4*e.strokeWidth),s=Math.max(s,.1);const o=n(t,Object.assign({},e,{hachureGap:s})),h=Math.PI/180*e.hachureAngle,r=[],i=.5*s*Math.cos(h),c=.5*s*Math.sin(h);for(const[t,e]of o)a([t,e])&&r.push([[t[0]-i,t[1]+c],[...e]],[[t[0]+i,t[1]-c],[...e]]);return{type:\"fillSketch\",ops:this.renderLines(r,e)}}}class r extends o{fillPolygons(t,e){const s=this._fillPolygons(t,e),n=Object.assign({},e,{hachureAngle:e.hachureAngle+90}),o=this._fillPolygons(t,n);return s.ops=s.ops.concat(o.ops),s}}class i{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e=Object.assign({},e,{hachureAngle:0}));return this.dotsOnLines(s,e)}dotsOnLines(t,e){const s=[];let n=e.hachureGap;n<0&&(n=4*e.strokeWidth),n=Math.max(n,.1);let o=e.fillWeight;o<0&&(o=e.strokeWidth/2);const h=n/4;for(const r of t){const t=a(r),i=t/n,c=Math.ceil(i)-1,l=t-c*n,u=(r[0][0]+r[1][0])/2-n/4,p=Math.min(r[0][1],r[1][1]);for(let t=0;t<c;t++){const a=p+l+t*n,r=u-h+2*Math.random()*h,i=a-h+2*Math.random()*h,c=this.helper.ellipse(r,i,o,o,e);s.push(...c.ops)}}return{type:\"fillSketch\",ops:s}}}class c{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.dashedLine(s,e)}}dashedLine(t,e){const s=e.dashOffset<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashOffset,n=e.dashGap<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashGap,o=[];return t.forEach((t=>{const h=a(t),r=Math.floor(h/(s+n)),i=(h+n-r*(s+n))/2;let c=t[0],l=t[1];c[0]>l[0]&&(c=t[1],l=t[0]);const u=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let t=0;t<r;t++){const a=t*(s+n),h=a+s,r=[c[0]+a*Math.cos(u)+i*Math.cos(u),c[1]+a*Math.sin(u)+i*Math.sin(u)],l=[c[0]+h*Math.cos(u)+i*Math.cos(u),c[1]+h*Math.sin(u)+i*Math.sin(u)];o.push(...this.helper.doubleLineOps(r[0],r[1],l[0],l[1],e))}})),o}}class l{constructor(t){this.helper=t}fillPolygons(t,e){const s=e.hachureGap<0?4*e.strokeWidth:e.hachureGap,o=e.zigzagOffset<0?s:e.zigzagOffset,a=n(t,e=Object.assign({},e,{hachureGap:s+o}));return{type:\"fillSketch\",ops:this.zigzagLines(a,o,e)}}zigzagLines(t,e,s){const n=[];return t.forEach((t=>{const o=a(t),h=Math.round(o/(2*e));let r=t[0],i=t[1];r[0]>i[0]&&(r=t[1],i=t[0]);const c=Math.atan((i[1]-r[1])/(i[0]-r[0]));for(let t=0;t<h;t++){const o=2*t*e,a=2*(t+1)*e,h=Math.sqrt(2*Math.pow(e,2)),i=[r[0]+o*Math.cos(c),r[1]+o*Math.sin(c)],l=[r[0]+a*Math.cos(c),r[1]+a*Math.sin(c)],u=[i[0]+h*Math.cos(c+Math.PI/4),i[1]+h*Math.sin(c+Math.PI/4)];n.push(...this.helper.doubleLineOps(i[0],i[1],u[0],u[1],s),...this.helper.doubleLineOps(u[0],u[1],l[0],l[1],s))}})),n}}const u={};class p{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const f=0,d=1,g=2,M={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function k(t,e){return t.type===e}function b(t){const e=[],s=function(t){const e=new Array;for(;\"\"!==t;)if(t.match(/^([ \\t\\r\\n,]+)/))t=t.substr(RegExp.$1.length);else if(t.match(/^([aAcChHlLmMqQsStTvVzZ])/))e[e.length]={type:f,text:RegExp.$1},t=t.substr(RegExp.$1.length);else{if(!t.match(/^(([-+]?[0-9]+(\\.[0-9]*)?|[-+]?\\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];e[e.length]={type:d,text:`${parseFloat(RegExp.$1)}`},t=t.substr(RegExp.$1.length)}return e[e.length]={type:g,text:\"\"},e}(t);let n=\"BOD\",o=0,a=s[o];for(;!k(a,g);){let h=0;const r=[];if(\"BOD\"===n){if(\"M\"!==a.text&&\"m\"!==a.text)return b(\"M0,0\"+t);o++,h=M[a.text],n=a.text}else k(a,d)?h=M[n]:(o++,h=M[a.text],n=a.text);if(!(o+h<s.length))throw new Error(\"Path data ended short\");for(let t=o;t<o+h;t++){const e=s[t];if(!k(e,d))throw new Error(\"Param not a number: \"+n+\",\"+e.text);r[r.length]=+e.text}if(\"number\"!=typeof M[n])throw new Error(\"Bad segment: \"+n);{const t={key:n,data:r};e.push(t),o+=h,a=s[o],\"M\"===n&&(n=\"L\"),\"m\"===n&&(n=\"l\")}}return e}function y(t){let e=0,s=0,n=0,o=0;const a=[];for(const{key:h,data:r}of t)switch(h){case\"M\":a.push({key:\"M\",data:[...r]}),[e,s]=r,[n,o]=r;break;case\"m\":e+=r[0],s+=r[1],a.push({key:\"M\",data:[e,s]}),n=e,o=s;break;case\"L\":a.push({key:\"L\",data:[...r]}),[e,s]=r;break;case\"l\":e+=r[0],s+=r[1],a.push({key:\"L\",data:[e,s]});break;case\"C\":a.push({key:\"C\",data:[...r]}),e=r[4],s=r[5];break;case\"c\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"C\",data:t}),e=t[4],s=t[5];break}case\"Q\":a.push({key:\"Q\",data:[...r]}),e=r[2],s=r[3];break;case\"q\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"Q\",data:t}),e=t[2],s=t[3];break}case\"A\":a.push({key:\"A\",data:[...r]}),e=r[5],s=r[6];break;case\"a\":e+=r[5],s+=r[6],a.push({key:\"A\",data:[r[0],r[1],r[2],r[3],r[4],e,s]});break;case\"H\":a.push({key:\"H\",data:[...r]}),e=r[0];break;case\"h\":e+=r[0],a.push({key:\"H\",data:[e]});break;case\"V\":a.push({key:\"V\",data:[...r]}),s=r[0];break;case\"v\":s+=r[0],a.push({key:\"V\",data:[s]});break;case\"S\":a.push({key:\"S\",data:[...r]}),e=r[2],s=r[3];break;case\"s\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"S\",data:t}),e=t[2],s=t[3];break}case\"T\":a.push({key:\"T\",data:[...r]}),e=r[0],s=r[1];break;case\"t\":e+=r[0],s+=r[1],a.push({key:\"T\",data:[e,s]});break;case\"Z\":case\"z\":a.push({key:\"Z\",data:[]}),e=n,s=o}return a}function m(t){const e=[];let s=\"\",n=0,o=0,a=0,h=0,r=0,i=0;for(const{key:c,data:l}of t){switch(c){case\"M\":e.push({key:\"M\",data:[...l]}),[n,o]=l,[a,h]=l;break;case\"C\":e.push({key:\"C\",data:[...l]}),n=l[4],o=l[5],r=l[2],i=l[3];break;case\"L\":e.push({key:\"L\",data:[...l]}),[n,o]=l;break;case\"H\":n=l[0],e.push({key:\"L\",data:[n,o]});break;case\"V\":o=l[0],e.push({key:\"L\",data:[n,o]});break;case\"S\":{let t=0,a=0;\"C\"===s||\"S\"===s?(t=n+(n-r),a=o+(o-i)):(t=n,a=o),e.push({key:\"C\",data:[t,a,...l]}),r=l[0],i=l[1],n=l[2],o=l[3];break}case\"T\":{const[t,a]=l;let h=0,c=0;\"Q\"===s||\"T\"===s?(h=n+(n-r),c=o+(o-i)):(h=n,c=o);const u=n+2*(h-n)/3,p=o+2*(c-o)/3,f=t+2*(h-t)/3,d=a+2*(c-a)/3;e.push({key:\"C\",data:[u,p,f,d,t,a]}),r=h,i=c,n=t,o=a;break}case\"Q\":{const[t,s,a,h]=l,c=n+2*(t-n)/3,u=o+2*(s-o)/3,p=a+2*(t-a)/3,f=h+2*(s-h)/3;e.push({key:\"C\",data:[c,u,p,f,a,h]}),r=t,i=s,n=a,o=h;break}case\"A\":{const t=Math.abs(l[0]),s=Math.abs(l[1]),a=l[2],h=l[3],r=l[4],i=l[5],c=l[6];if(0===t||0===s)e.push({key:\"C\",data:[n,o,i,c,i,c]}),n=i,o=c;else if(n!==i||o!==c){x(n,o,i,c,t,s,a,h,r).forEach((function(t){e.push({key:\"C\",data:t})})),n=i,o=c}break}case\"Z\":e.push({key:\"Z\",data:[]}),n=a,o=h}s=c}return e}function w(t,e,s){return[t*Math.cos(s)-e*Math.sin(s),t*Math.sin(s)+e*Math.cos(s)]}function x(t,e,s,n,o,a,h,r,i,c){const l=(u=h,Math.PI*u/180);var u;let p=[],f=0,d=0,g=0,M=0;if(c)[f,d,g,M]=c;else{[t,e]=w(t,e,-l),[s,n]=w(s,n,-l);const h=(t-s)/2,c=(e-n)/2;let u=h*h/(o*o)+c*c/(a*a);u>1&&(u=Math.sqrt(u),o*=u,a*=u);const p=o*o,k=a*a,b=p*k-p*c*c-k*h*h,y=p*c*c+k*h*h,m=(r===i?-1:1)*Math.sqrt(Math.abs(b/y));g=m*o*c/a+(t+s)/2,M=m*-a*h/o+(e+n)/2,f=Math.asin(parseFloat(((e-M)/a).toFixed(9))),d=Math.asin(parseFloat(((n-M)/a).toFixed(9))),t<g&&(f=Math.PI-f),s<g&&(d=Math.PI-d),f<0&&(f=2*Math.PI+f),d<0&&(d=2*Math.PI+d),i&&f>d&&(f-=2*Math.PI),!i&&d>f&&(d-=2*Math.PI)}let k=d-f;if(Math.abs(k)>120*Math.PI/180){const t=d,e=s,r=n;d=i&&d>f?f+120*Math.PI/180*1:f+120*Math.PI/180*-1,p=x(s=g+o*Math.cos(d),n=M+a*Math.sin(d),e,r,o,a,h,0,i,[d,t,g,M])}k=d-f;const b=Math.cos(f),y=Math.sin(f),m=Math.cos(d),P=Math.sin(d),v=Math.tan(k/4),S=4/3*o*v,O=4/3*a*v,L=[t,e],T=[t+S*y,e-O*b],D=[s+S*P,n-O*m],A=[s,n];if(T[0]=2*L[0]-T[0],T[1]=2*L[1]-T[1],c)return[T,D,A].concat(p);{p=[T,D,A].concat(p);const t=[];for(let e=0;e<p.length;e+=3){const s=w(p[e][0],p[e][1],l),n=w(p[e+1][0],p[e+1][1],l),o=w(p[e+2][0],p[e+2][1],l);t.push([s[0],s[1],n[0],n[1],o[0],o[1]])}return t}}const P={randOffset:function(t,e){return G(t,e)},randOffsetWithRange:function(t,e,s){return E(t,e,s)},ellipse:function(t,e,s,n,o){const a=T(s,n,o);return D(t,e,o,a).opset},doubleLineOps:function(t,e,s,n,o){return $(t,e,s,n,o,!0)}};function v(t,e,s,n,o){return{type:\"path\",ops:$(t,e,s,n,o)}}function S(t,e,s){const n=(t||[]).length;if(n>2){const o=[];for(let e=0;e<n-1;e++)o.push(...$(t[e][0],t[e][1],t[e+1][0],t[e+1][1],s));return e&&o.push(...$(t[n-1][0],t[n-1][1],t[0][0],t[0][1],s)),{type:\"path\",ops:o}}return 2===n?v(t[0][0],t[0][1],t[1][0],t[1][1],s):{type:\"path\",ops:[]}}function O(t,e,s,n,o){return function(t,e){return S(t,!0,e)}([[t,e],[t+s,e],[t+s,e+n],[t,e+n]],o)}function L(t,e){if(t.length){const s=\"number\"==typeof t[0][0]?[t]:t,n=j(s[0],1*(1+.2*e.roughness),e),o=e.disableMultiStroke?[]:j(s[0],1.5*(1+.22*e.roughness),z(e));for(let t=1;t<s.length;t++){const a=s[t];if(a.length){const t=j(a,1*(1+.2*e.roughness),e),s=e.disableMultiStroke?[]:j(a,1.5*(1+.22*e.roughness),z(e));for(const e of t)\"move\"!==e.op&&n.push(e);for(const t of s)\"move\"!==t.op&&o.push(t)}}return{type:\"path\",ops:n.concat(o)}}return{type:\"path\",ops:[]}}function T(t,e,s){const n=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(t/2,2)+Math.pow(e/2,2))/2)),o=Math.ceil(Math.max(s.curveStepCount,s.curveStepCount/Math.sqrt(200)*n)),a=2*Math.PI/o;let h=Math.abs(t/2),r=Math.abs(e/2);const i=1-s.curveFitting;return h+=G(h*i,s),r+=G(r*i,s),{increment:a,rx:h,ry:r}}function D(t,e,s,n){const[o,a]=F(n.increment,t,e,n.rx,n.ry,1,n.increment*E(.1,E(.4,1,s),s),s);let h=q(o,null,s);if(!s.disableMultiStroke&&0!==s.roughness){const[o]=F(n.increment,t,e,n.rx,n.ry,1.5,0,s),a=q(o,null,s);h=h.concat(a)}return{estimatedPoints:a,opset:{type:\"path\",ops:h}}}function A(t,e,s,n,o,a,h,r,i){const c=t,l=e;let u=Math.abs(s/2),p=Math.abs(n/2);u+=G(.01*u,i),p+=G(.01*p,i);let f=o,d=a;for(;f<0;)f+=2*Math.PI,d+=2*Math.PI;d-f>2*Math.PI&&(f=0,d=2*Math.PI);const g=2*Math.PI/i.curveStepCount,M=Math.min(g/2,(d-f)/2),k=V(M,c,l,u,p,f,d,1,i);if(!i.disableMultiStroke){const t=V(M,c,l,u,p,f,d,1.5,i);k.push(...t)}return h&&(r?k.push(...$(c,l,c+u*Math.cos(f),l+p*Math.sin(f),i),...$(c,l,c+u*Math.cos(d),l+p*Math.sin(d),i)):k.push({op:\"lineTo\",data:[c,l]},{op:\"lineTo\",data:[c+u*Math.cos(f),l+p*Math.sin(f)]})),{type:\"path\",ops:k}}function _(t,e){const s=m(y(b(t))),n=[];let o=[0,0],a=[0,0];for(const{key:t,data:h}of s)switch(t){case\"M\":a=[h[0],h[1]],o=[h[0],h[1]];break;case\"L\":n.push(...$(a[0],a[1],h[0],h[1],e)),a=[h[0],h[1]];break;case\"C\":{const[t,s,o,r,i,c]=h;n.push(...Z(t,s,o,r,i,c,a,e)),a=[i,c];break}case\"Z\":n.push(...$(a[0],a[1],o[0],o[1],e)),a=[o[0],o[1]]}return{type:\"path\",ops:n}}function I(t,e){const s=[];for(const n of t)if(n.length){const t=e.maxRandomnessOffset||0,o=n.length;if(o>2){s.push({op:\"move\",data:[n[0][0]+G(t,e),n[0][1]+G(t,e)]});for(let a=1;a<o;a++)s.push({op:\"lineTo\",data:[n[a][0]+G(t,e),n[a][1]+G(t,e)]})}}return{type:\"fillPath\",ops:s}}function C(t,e){return function(t,e){let s=t.fillStyle||\"hachure\";if(!u[s])switch(s){case\"zigzag\":u[s]||(u[s]=new h(e));break;case\"cross-hatch\":u[s]||(u[s]=new r(e));break;case\"dots\":u[s]||(u[s]=new i(e));break;case\"dashed\":u[s]||(u[s]=new c(e));break;case\"zigzag-line\":u[s]||(u[s]=new l(e));break;default:s=\"hachure\",u[s]||(u[s]=new o(e))}return u[s]}(e,P).fillPolygons(t,e)}function z(t){const e=Object.assign({},t);return e.randomizer=void 0,t.seed&&(e.seed=t.seed+1),e}function W(t){return t.randomizer||(t.randomizer=new p(t.seed||0)),t.randomizer.next()}function E(t,e,s,n=1){return s.roughness*n*(W(s)*(e-t)+t)}function G(t,e,s=1){return E(-t,t,e,s)}function $(t,e,s,n,o,a=!1){const h=a?o.disableMultiStrokeFill:o.disableMultiStroke,r=R(t,e,s,n,o,!0,!1);if(h)return r;const i=R(t,e,s,n,o,!0,!0);return r.concat(i)}function R(t,e,s,n,o,a,h){const r=Math.pow(t-s,2)+Math.pow(e-n,2),i=Math.sqrt(r);let c=1;c=i<200?1:i>500?.4:-.0016668*i+1.233334;let l=o.maxRandomnessOffset||0;l*l*100>r&&(l=i/10);const u=l/2,p=.2+.2*W(o);let f=o.bowing*o.maxRandomnessOffset*(n-e)/200,d=o.bowing*o.maxRandomnessOffset*(t-s)/200;f=G(f,o,c),d=G(d,o,c);const g=[],M=()=>G(u,o,c),k=()=>G(l,o,c),b=o.preserveVertices;return a&&(h?g.push({op:\"move\",data:[t+(b?0:M()),e+(b?0:M())]}):g.push({op:\"move\",data:[t+(b?0:G(l,o,c)),e+(b?0:G(l,o,c))]})),h?g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+M(),d+e+(n-e)*p+M(),f+t+2*(s-t)*p+M(),d+e+2*(n-e)*p+M(),s+(b?0:M()),n+(b?0:M())]}):g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+k(),d+e+(n-e)*p+k(),f+t+2*(s-t)*p+k(),d+e+2*(n-e)*p+k(),s+(b?0:k()),n+(b?0:k())]}),g}function j(t,e,s){if(!t.length)return[];const n=[];n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]),n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]);for(let o=1;o<t.length;o++)n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]),o===t.length-1&&n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]);return q(n,null,s)}function q(t,e,s){const n=t.length,o=[];if(n>3){const a=[],h=1-s.curveTightness;o.push({op:\"move\",data:[t[1][0],t[1][1]]});for(let e=1;e+2<n;e++){const s=t[e];a[0]=[s[0],s[1]],a[1]=[s[0]+(h*t[e+1][0]-h*t[e-1][0])/6,s[1]+(h*t[e+1][1]-h*t[e-1][1])/6],a[2]=[t[e+1][0]+(h*t[e][0]-h*t[e+2][0])/6,t[e+1][1]+(h*t[e][1]-h*t[e+2][1])/6],a[3]=[t[e+1][0],t[e+1][1]],o.push({op:\"bcurveTo\",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}if(e&&2===e.length){const t=s.maxRandomnessOffset;o.push({op:\"lineTo\",data:[e[0]+G(t,s),e[1]+G(t,s)]})}}else 3===n?(o.push({op:\"move\",data:[t[1][0],t[1][1]]}),o.push({op:\"bcurveTo\",data:[t[1][0],t[1][1],t[2][0],t[2][1],t[2][0],t[2][1]]})):2===n&&o.push(...R(t[0][0],t[0][1],t[1][0],t[1][1],s,!0,!0));return o}function F(t,e,s,n,o,a,h,r){const i=[],c=[];if(0===r.roughness){t/=4,c.push([e+n*Math.cos(-t),s+o*Math.sin(-t)]);for(let a=0;a<=2*Math.PI;a+=t){const t=[e+n*Math.cos(a),s+o*Math.sin(a)];i.push(t),c.push(t)}c.push([e+n*Math.cos(0),s+o*Math.sin(0)]),c.push([e+n*Math.cos(t),s+o*Math.sin(t)])}else{const l=G(.5,r)-Math.PI/2;c.push([G(a,r)+e+.9*n*Math.cos(l-t),G(a,r)+s+.9*o*Math.sin(l-t)]);const u=2*Math.PI+l-.01;for(let h=l;h<u;h+=t){const t=[G(a,r)+e+n*Math.cos(h),G(a,r)+s+o*Math.sin(h)];i.push(t),c.push(t)}c.push([G(a,r)+e+n*Math.cos(l+2*Math.PI+.5*h),G(a,r)+s+o*Math.sin(l+2*Math.PI+.5*h)]),c.push([G(a,r)+e+.98*n*Math.cos(l+h),G(a,r)+s+.98*o*Math.sin(l+h)]),c.push([G(a,r)+e+.9*n*Math.cos(l+.5*h),G(a,r)+s+.9*o*Math.sin(l+.5*h)])}return[c,i]}function V(t,e,s,n,o,a,h,r,i){const c=a+G(.1,i),l=[];l.push([G(r,i)+e+.9*n*Math.cos(c-t),G(r,i)+s+.9*o*Math.sin(c-t)]);for(let a=c;a<=h;a+=t)l.push([G(r,i)+e+n*Math.cos(a),G(r,i)+s+o*Math.sin(a)]);return l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),q(l,null,i)}function Z(t,e,s,n,o,a,h,r){const i=[],c=[r.maxRandomnessOffset||1,(r.maxRandomnessOffset||1)+.3];let l=[0,0];const u=r.disableMultiStroke?1:2,p=r.preserveVertices;for(let f=0;f<u;f++)0===f?i.push({op:\"move\",data:[h[0],h[1]]}):i.push({op:\"move\",data:[h[0]+(p?0:G(c[0],r)),h[1]+(p?0:G(c[0],r))]}),l=p?[o,a]:[o+G(c[f],r),a+G(c[f],r)],i.push({op:\"bcurveTo\",data:[t+G(c[f],r),e+G(c[f],r),s+G(c[f],r),n+G(c[f],r),l[0],l[1]]});return i}function Q(t){return[...t]}function H(t,e=0){const s=t.length;if(s<3)throw new Error(\"A curve must have at least three points.\");const n=[];if(3===s)n.push(Q(t[0]),Q(t[1]),Q(t[2]),Q(t[2]));else{const s=[];s.push(t[0],t[0]);for(let e=1;e<t.length;e++)s.push(t[e]),e===t.length-1&&s.push(t[e]);const o=[],a=1-e;n.push(Q(s[0]));for(let t=1;t+2<s.length;t++){const e=s[t];o[0]=[e[0],e[1]],o[1]=[e[0]+(a*s[t+1][0]-a*s[t-1][0])/6,e[1]+(a*s[t+1][1]-a*s[t-1][1])/6],o[2]=[s[t+1][0]+(a*s[t][0]-a*s[t+2][0])/6,s[t+1][1]+(a*s[t][1]-a*s[t+2][1])/6],o[3]=[s[t+1][0],s[t+1][1]],n.push(o[1],o[2],o[3])}}return n}function N(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)}function B(t,e,s){const n=N(e,s);if(0===n)return N(t,e);let o=((t[0]-e[0])*(s[0]-e[0])+(t[1]-e[1])*(s[1]-e[1]))/n;return o=Math.max(0,Math.min(1,o)),N(t,J(e,s,o))}function J(t,e,s){return[t[0]+(e[0]-t[0])*s,t[1]+(e[1]-t[1])*s]}function K(t,e,s,n){const o=n||[];if(function(t,e){const s=t[e+0],n=t[e+1],o=t[e+2],a=t[e+3];let h=3*n[0]-2*s[0]-a[0];h*=h;let r=3*n[1]-2*s[1]-a[1];r*=r;let i=3*o[0]-2*a[0]-s[0];i*=i;let c=3*o[1]-2*a[1]-s[1];return c*=c,h<i&&(h=i),r<c&&(r=c),h+r}(t,e)<s){const s=t[e+0];if(o.length){(a=o[o.length-1],h=s,Math.sqrt(N(a,h)))>1&&o.push(s)}else o.push(s);o.push(t[e+3])}else{const n=.5,a=t[e+0],h=t[e+1],r=t[e+2],i=t[e+3],c=J(a,h,n),l=J(h,r,n),u=J(r,i,n),p=J(c,l,n),f=J(l,u,n),d=J(p,f,n);K([a,c,p,d],0,s,o),K([d,f,u,i],0,s,o)}var a,h;return o}function U(t,e){return X(t,0,t.length,e)}function X(t,e,s,n,o){const a=o||[],h=t[e],r=t[s-1];let i=0,c=1;for(let n=e+1;n<s-1;++n){const e=B(t[n],h,r);e>i&&(i=e,c=n)}return Math.sqrt(i)>n?(X(t,e,c+1,n,a),X(t,c,s,n,a)):(a.length||a.push(h),a.push(r)),a}function Y(t,e=.15,s){const n=[],o=(t.length-1)/3;for(let s=0;s<o;s++){K(t,3*s,e,n)}return s&&s>0?X(n,0,n.length,s):n}const tt=\"none\";class et{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:\"#000\",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:\"hachure\",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,e,s){return{shape:t,sets:e||[],options:s||this.defaultOptions}}line(t,e,s,n,o){const a=this._o(o);return this._d(\"line\",[v(t,e,s,n,a)],a)}rectangle(t,e,s,n,o){const a=this._o(o),h=[],r=O(t,e,s,n,a);if(a.fill){const o=[[t,e],[t+s,e],[t+s,e+n],[t,e+n]];\"solid\"===a.fillStyle?h.push(I([o],a)):h.push(C([o],a))}return a.stroke!==tt&&h.push(r),this._d(\"rectangle\",h,a)}ellipse(t,e,s,n,o){const a=this._o(o),h=[],r=T(s,n,a),i=D(t,e,a,r);if(a.fill)if(\"solid\"===a.fillStyle){const s=D(t,e,a,r).opset;s.type=\"fillPath\",h.push(s)}else h.push(C([i.estimatedPoints],a));return a.stroke!==tt&&h.push(i.opset),this._d(\"ellipse\",h,a)}circle(t,e,s,n){const o=this.ellipse(t,e,s,s,n);return o.shape=\"circle\",o}linearPath(t,e){const s=this._o(e);return this._d(\"linearPath\",[S(t,!1,s)],s)}arc(t,e,s,n,o,a,h=!1,r){const i=this._o(r),c=[],l=A(t,e,s,n,o,a,h,!0,i);if(h&&i.fill)if(\"solid\"===i.fillStyle){const h=Object.assign({},i);h.disableMultiStroke=!0;const r=A(t,e,s,n,o,a,!0,!1,h);r.type=\"fillPath\",c.push(r)}else c.push(function(t,e,s,n,o,a,h){const r=t,i=e;let c=Math.abs(s/2),l=Math.abs(n/2);c+=G(.01*c,h),l+=G(.01*l,h);let u=o,p=a;for(;u<0;)u+=2*Math.PI,p+=2*Math.PI;p-u>2*Math.PI&&(u=0,p=2*Math.PI);const f=(p-u)/h.curveStepCount,d=[];for(let t=u;t<=p;t+=f)d.push([r+c*Math.cos(t),i+l*Math.sin(t)]);return d.push([r+c*Math.cos(p),i+l*Math.sin(p)]),d.push([r,i]),C([d],h)}(t,e,s,n,o,a,i));return i.stroke!==tt&&c.push(l),this._d(\"arc\",c,i)}curve(t,e){const s=this._o(e),n=[],o=L(t,s);if(s.fill&&s.fill!==tt)if(\"solid\"===s.fillStyle){const e=L(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else{const e=[],o=t;if(o.length){const t=\"number\"==typeof o[0][0]?[o]:o;for(const n of t)n.length<3?e.push(...n):3===n.length?e.push(...Y(H([n[0],n[0],n[1],n[2]]),10,(1+s.roughness)/2)):e.push(...Y(H(n),10,(1+s.roughness)/2))}e.length&&n.push(C([e],s))}return s.stroke!==tt&&n.push(o),this._d(\"curve\",n,s)}polygon(t,e){const s=this._o(e),n=[],o=S(t,!0,s);return s.fill&&(\"solid\"===s.fillStyle?n.push(I([t],s)):n.push(C([t],s))),s.stroke!==tt&&n.push(o),this._d(\"polygon\",n,s)}path(t,e){const s=this._o(e),n=[];if(!t)return this._d(\"path\",n,s);t=(t||\"\").replace(/\\n/g,\" \").replace(/(-\\s)/g,\"-\").replace(\"/(ss)/g\",\" \");const o=s.fill&&\"transparent\"!==s.fill&&s.fill!==tt,a=s.stroke!==tt,h=!!(s.simplification&&s.simplification<1),r=function(t,e,s){const n=m(y(b(t))),o=[];let a=[],h=[0,0],r=[];const i=()=>{r.length>=4&&a.push(...Y(r,e)),r=[]},c=()=>{i(),a.length&&(o.push(a),a=[])};for(const{key:t,data:e}of n)switch(t){case\"M\":c(),h=[e[0],e[1]],a.push(h);break;case\"L\":i(),a.push([e[0],e[1]]);break;case\"C\":if(!r.length){const t=a.length?a[a.length-1]:h;r.push([t[0],t[1]])}r.push([e[0],e[1]]),r.push([e[2],e[3]]),r.push([e[4],e[5]]);break;case\"Z\":i(),a.push([h[0],h[1]])}if(c(),!s)return o;const l=[];for(const t of o){const e=U(t,s);e.length&&l.push(e)}return l}(t,1,h?4-4*(s.simplification||1):(1+s.roughness)/2),i=_(t,s);if(o)if(\"solid\"===s.fillStyle)if(1===r.length){const e=_(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else n.push(I(r,s));else n.push(C(r,s));return a&&(h?r.forEach((t=>{n.push(S(t,!1,s))})):n.push(i)),this._d(\"path\",n,s)}opsToPath(t,e){let s=\"\";for(const n of t.ops){const t=\"number\"==typeof e&&e>=0?n.data.map((t=>+t.toFixed(e))):n.data;switch(n.op){case\"move\":s+=`M${t[0]} ${t[1]} `;break;case\"bcurveTo\":s+=`C${t[0]} ${t[1]}, ${t[2]} ${t[3]}, ${t[4]} ${t[5]} `;break;case\"lineTo\":s+=`L${t[0]} ${t[1]} `}}return s.trim()}toPaths(t){const e=t.sets||[],s=t.options||this.defaultOptions,n=[];for(const t of e){let e=null;switch(t.type){case\"path\":e={d:this.opsToPath(t),stroke:s.stroke,strokeWidth:s.strokeWidth,fill:tt};break;case\"fillPath\":e={d:this.opsToPath(t),stroke:tt,strokeWidth:0,fill:s.fill||tt};break;case\"fillSketch\":e=this.fillSketch(t,s)}e&&n.push(e)}return n}fillSketch(t,e){let s=e.fillWeight;return s<0&&(s=e.strokeWidth/2),{d:this.opsToPath(t),stroke:e.fill||tt,strokeWidth:s,fill:tt}}_mergedShape(t){return t.filter(((t,e)=>0===e||\"move\"!==t.op))}}class st{constructor(t,e){this.canvas=t,this.ctx=this.canvas.getContext(\"2d\"),this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.ctx,o=t.options.fixedDecimalPlaceDigits;for(const a of e)switch(a.type){case\"path\":n.save(),n.strokeStyle=\"none\"===s.stroke?\"transparent\":s.stroke,n.lineWidth=s.strokeWidth,s.strokeLineDash&&n.setLineDash(s.strokeLineDash),s.strokeLineDashOffset&&(n.lineDashOffset=s.strokeLineDashOffset),this._drawToContext(n,a,o),n.restore();break;case\"fillPath\":{n.save(),n.fillStyle=s.fill||\"\";const e=\"curve\"===t.shape||\"polygon\"===t.shape||\"path\"===t.shape?\"evenodd\":\"nonzero\";this._drawToContext(n,a,o,e),n.restore();break}case\"fillSketch\":this.fillSketch(n,a,s)}}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2),t.save(),s.fillLineDash&&t.setLineDash(s.fillLineDash),s.fillLineDashOffset&&(t.lineDashOffset=s.fillLineDashOffset),t.strokeStyle=s.fill||\"\",t.lineWidth=n,this._drawToContext(t,e,s.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,e,s,n=\"nonzero\"){t.beginPath();for(const n of e.ops){const e=\"number\"==typeof s&&s>=0?n.data.map((t=>+t.toFixed(s))):n.data;switch(n.op){case\"move\":t.moveTo(e[0],e[1]);break;case\"bcurveTo\":t.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]);break;case\"lineTo\":t.lineTo(e[0],e[1])}}\"fillPath\"===e.type?t.fill(n):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a),a}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a),a}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a),a}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o),o}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s),s}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s),s}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i),i}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s),s}path(t,e){const s=this.gen.path(t,e);return this.draw(s),s}}const nt=\"http://www.w3.org/2000/svg\";class ot{constructor(t,e){this.svg=t,this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.svg.ownerDocument||window.document,o=n.createElementNS(nt,\"g\"),a=t.options.fixedDecimalPlaceDigits;for(const h of e){let e=null;switch(h.type){case\"path\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",s.stroke),e.setAttribute(\"stroke-width\",s.strokeWidth+\"\"),e.setAttribute(\"fill\",\"none\"),s.strokeLineDash&&e.setAttribute(\"stroke-dasharray\",s.strokeLineDash.join(\" \").trim()),s.strokeLineDashOffset&&e.setAttribute(\"stroke-dashoffset\",`${s.strokeLineDashOffset}`);break;case\"fillPath\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",\"none\"),e.setAttribute(\"stroke-width\",\"0\"),e.setAttribute(\"fill\",s.fill||\"\"),\"curve\"!==t.shape&&\"polygon\"!==t.shape||e.setAttribute(\"fill-rule\",\"evenodd\");break;case\"fillSketch\":e=this.fillSketch(n,h,s)}e&&o.appendChild(e)}return o}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2);const o=t.createElementNS(nt,\"path\");return o.setAttribute(\"d\",this.opsToPath(e,s.fixedDecimalPlaceDigits)),o.setAttribute(\"stroke\",s.fill||\"\"),o.setAttribute(\"stroke-width\",n+\"\"),o.setAttribute(\"fill\",\"none\"),s.fillLineDash&&o.setAttribute(\"stroke-dasharray\",s.fillLineDash.join(\" \").trim()),s.fillLineDashOffset&&o.setAttribute(\"stroke-dashoffset\",`${s.fillLineDashOffset}`),o}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,e){return this.gen.opsToPath(t,e)}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a)}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a)}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a)}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o)}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s)}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s)}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i)}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s)}path(t,e){const s=this.gen.path(t,e);return this.draw(s)}}var at={canvas:(t,e)=>new st(t,e),svg:(t,e)=>new ot(t,e),generator:t=>new et(t),newSeed:()=>et.newSeed()};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/roughjs/bundled/rough.esm.js\n");

/***/ })

};
;