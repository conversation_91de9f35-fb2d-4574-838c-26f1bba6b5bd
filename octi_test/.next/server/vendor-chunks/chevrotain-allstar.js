"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/chevrotain-allstar";
exports.ids = ["vendor-chunks/chevrotain-allstar"];
exports.modules = {

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/all-star-lookahead.js":
/*!*******************************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/all-star-lookahead.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLStarLookaheadStrategy: () => (/* binding */ LLStarLookaheadStrategy)\n/* harmony export */ });\n/* harmony import */ var chevrotain__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! chevrotain */ \"(ssr)/./node_modules/chevrotain/lib/src/api.js\");\n/* harmony import */ var _atn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./atn.js */ \"(ssr)/./node_modules/chevrotain-allstar/lib/atn.js\");\n/* harmony import */ var _dfa_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dfa.js */ \"(ssr)/./node_modules/chevrotain-allstar/lib/dfa.js\");\n/* harmony import */ var lodash_es_min_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash-es/min.js */ \"(ssr)/./node_modules/lodash-es/min.js\");\n/* harmony import */ var lodash_es_flatMap_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lodash-es/flatMap.js */ \"(ssr)/./node_modules/lodash-es/flatMap.js\");\n/* harmony import */ var lodash_es_uniqBy_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lodash-es/uniqBy.js */ \"(ssr)/./node_modules/lodash-es/uniqBy.js\");\n/* harmony import */ var lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es/map.js */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es_flatten_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es/flatten.js */ \"(ssr)/./node_modules/lodash-es/flatten.js\");\n/* harmony import */ var lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es/forEach.js */ \"(ssr)/./node_modules/lodash-es/forEach.js\");\n/* harmony import */ var lodash_es_isEmpty_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash-es/isEmpty.js */ \"(ssr)/./node_modules/lodash-es/isEmpty.js\");\n/* harmony import */ var lodash_es_reduce_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es/reduce.js */ \"(ssr)/./node_modules/lodash-es/reduce.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\n\n\n\n\n\n\n\n\n\n\nfunction createDFACache(startState, decision) {\n    const map = {};\n    return (predicateSet) => {\n        const key = predicateSet.toString();\n        let existing = map[key];\n        if (existing !== undefined) {\n            return existing;\n        }\n        else {\n            existing = {\n                atnStartState: startState,\n                decision,\n                states: {}\n            };\n            map[key] = existing;\n            return existing;\n        }\n    };\n}\nclass PredicateSet {\n    constructor() {\n        this.predicates = [];\n    }\n    is(index) {\n        return index >= this.predicates.length || this.predicates[index];\n    }\n    set(index, value) {\n        this.predicates[index] = value;\n    }\n    toString() {\n        let value = \"\";\n        const size = this.predicates.length;\n        for (let i = 0; i < size; i++) {\n            value += this.predicates[i] === true ? \"1\" : \"0\";\n        }\n        return value;\n    }\n}\nconst EMPTY_PREDICATES = new PredicateSet();\nclass LLStarLookaheadStrategy extends chevrotain__WEBPACK_IMPORTED_MODULE_0__.LLkLookaheadStrategy {\n    constructor(options) {\n        var _a;\n        super();\n        this.logging = (_a = options === null || options === void 0 ? void 0 : options.logging) !== null && _a !== void 0 ? _a : ((message) => console.log(message));\n    }\n    initialize(options) {\n        this.atn = (0,_atn_js__WEBPACK_IMPORTED_MODULE_1__.createATN)(options.rules);\n        this.dfas = initATNSimulator(this.atn);\n    }\n    validateAmbiguousAlternationAlternatives() {\n        return [];\n    }\n    validateEmptyOrAlternatives() {\n        return [];\n    }\n    buildLookaheadForAlternation(options) {\n        const { prodOccurrence, rule, hasPredicates, dynamicTokensEnabled } = options;\n        const dfas = this.dfas;\n        const logging = this.logging;\n        const key = (0,_atn_js__WEBPACK_IMPORTED_MODULE_1__.buildATNKey)(rule, 'Alternation', prodOccurrence);\n        const decisionState = this.atn.decisionMap[key];\n        const decisionIndex = decisionState.decision;\n        const partialAlts = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.getLookaheadPaths)({\n            maxLookahead: 1,\n            occurrence: prodOccurrence,\n            prodType: \"Alternation\",\n            rule: rule\n        }), (currAlt) => (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(currAlt, (path) => path[0]));\n        if (isLL1Sequence(partialAlts, false) && !dynamicTokensEnabled) {\n            const choiceToAlt = (0,lodash_es_reduce_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(partialAlts, (result, currAlt, idx) => {\n                (0,lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(currAlt, (currTokType) => {\n                    if (currTokType) {\n                        result[currTokType.tokenTypeIdx] = idx;\n                        (0,lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(currTokType.categoryMatches, (currExtendingType) => {\n                            result[currExtendingType] = idx;\n                        });\n                    }\n                });\n                return result;\n            }, {});\n            if (hasPredicates) {\n                return function (orAlts) {\n                    var _a;\n                    const nextToken = this.LA(1);\n                    const prediction = choiceToAlt[nextToken.tokenTypeIdx];\n                    if (orAlts !== undefined && prediction !== undefined) {\n                        const gate = (_a = orAlts[prediction]) === null || _a === void 0 ? void 0 : _a.GATE;\n                        if (gate !== undefined && gate.call(this) === false) {\n                            return undefined;\n                        }\n                    }\n                    return prediction;\n                };\n            }\n            else {\n                return function () {\n                    const nextToken = this.LA(1);\n                    return choiceToAlt[nextToken.tokenTypeIdx];\n                };\n            }\n        }\n        else if (hasPredicates) {\n            return function (orAlts) {\n                const predicates = new PredicateSet();\n                const length = orAlts === undefined ? 0 : orAlts.length;\n                for (let i = 0; i < length; i++) {\n                    const gate = orAlts === null || orAlts === void 0 ? void 0 : orAlts[i].GATE;\n                    predicates.set(i, gate === undefined || gate.call(this));\n                }\n                const result = adaptivePredict.call(this, dfas, decisionIndex, predicates, logging);\n                return typeof result === 'number' ? result : undefined;\n            };\n        }\n        else {\n            return function () {\n                const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\n                return typeof result === 'number' ? result : undefined;\n            };\n        }\n    }\n    buildLookaheadForOptional(options) {\n        const { prodOccurrence, rule, prodType, dynamicTokensEnabled } = options;\n        const dfas = this.dfas;\n        const logging = this.logging;\n        const key = (0,_atn_js__WEBPACK_IMPORTED_MODULE_1__.buildATNKey)(rule, prodType, prodOccurrence);\n        const decisionState = this.atn.decisionMap[key];\n        const decisionIndex = decisionState.decision;\n        const alts = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.getLookaheadPaths)({\n            maxLookahead: 1,\n            occurrence: prodOccurrence,\n            prodType,\n            rule\n        }), (e) => {\n            return (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e, (g) => g[0]);\n        });\n        if (isLL1Sequence(alts) && alts[0][0] && !dynamicTokensEnabled) {\n            const alt = alts[0];\n            const singleTokensTypes = (0,lodash_es_flatten_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(alt);\n            if (singleTokensTypes.length === 1 &&\n                (0,lodash_es_isEmpty_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(singleTokensTypes[0].categoryMatches)) {\n                const expectedTokenType = singleTokensTypes[0];\n                const expectedTokenUniqueKey = expectedTokenType.tokenTypeIdx;\n                return function () {\n                    return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey;\n                };\n            }\n            else {\n                const choiceToAlt = (0,lodash_es_reduce_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(singleTokensTypes, (result, currTokType) => {\n                    if (currTokType !== undefined) {\n                        result[currTokType.tokenTypeIdx] = true;\n                        (0,lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(currTokType.categoryMatches, (currExtendingType) => {\n                            result[currExtendingType] = true;\n                        });\n                    }\n                    return result;\n                }, {});\n                return function () {\n                    const nextToken = this.LA(1);\n                    return choiceToAlt[nextToken.tokenTypeIdx] === true;\n                };\n            }\n        }\n        return function () {\n            const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\n            return typeof result === \"object\" ? false : result === 0;\n        };\n    }\n}\nfunction isLL1Sequence(sequences, allowEmpty = true) {\n    const fullSet = new Set();\n    for (const alt of sequences) {\n        const altSet = new Set();\n        for (const tokType of alt) {\n            if (tokType === undefined) {\n                if (allowEmpty) {\n                    // Epsilon production encountered\n                    break;\n                }\n                else {\n                    return false;\n                }\n            }\n            const indices = [tokType.tokenTypeIdx].concat(tokType.categoryMatches);\n            for (const index of indices) {\n                if (fullSet.has(index)) {\n                    if (!altSet.has(index)) {\n                        return false;\n                    }\n                }\n                else {\n                    fullSet.add(index);\n                    altSet.add(index);\n                }\n            }\n        }\n    }\n    return true;\n}\nfunction initATNSimulator(atn) {\n    const decisionLength = atn.decisionStates.length;\n    const decisionToDFA = Array(decisionLength);\n    for (let i = 0; i < decisionLength; i++) {\n        decisionToDFA[i] = createDFACache(atn.decisionStates[i], i);\n    }\n    return decisionToDFA;\n}\nfunction adaptivePredict(dfaCaches, decision, predicateSet, logging) {\n    const dfa = dfaCaches[decision](predicateSet);\n    let start = dfa.start;\n    if (start === undefined) {\n        const closure = computeStartState(dfa.atnStartState);\n        start = addDFAState(dfa, newDFAState(closure));\n        dfa.start = start;\n    }\n    const alt = performLookahead.apply(this, [dfa, start, predicateSet, logging]);\n    return alt;\n}\nfunction performLookahead(dfa, s0, predicateSet, logging) {\n    let previousD = s0;\n    let i = 1;\n    const path = [];\n    let t = this.LA(i++);\n    while (true) {\n        let d = getExistingTargetState(previousD, t);\n        if (d === undefined) {\n            d = computeLookaheadTarget.apply(this, [dfa, previousD, t, i, predicateSet, logging]);\n        }\n        if (d === _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR) {\n            return buildAdaptivePredictError(path, previousD, t);\n        }\n        if (d.isAcceptState === true) {\n            return d.prediction;\n        }\n        previousD = d;\n        path.push(t);\n        t = this.LA(i++);\n    }\n}\nfunction computeLookaheadTarget(dfa, previousD, token, lookahead, predicateSet, logging) {\n    const reach = computeReachSet(previousD.configs, token, predicateSet);\n    if (reach.size === 0) {\n        addDFAEdge(dfa, previousD, token, _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR);\n        return _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR;\n    }\n    let newState = newDFAState(reach);\n    const predictedAlt = getUniqueAlt(reach, predicateSet);\n    if (predictedAlt !== undefined) {\n        newState.isAcceptState = true;\n        newState.prediction = predictedAlt;\n        newState.configs.uniqueAlt = predictedAlt;\n    }\n    else if (hasConflictTerminatingPrediction(reach)) {\n        const prediction = (0,lodash_es_min_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(reach.alts);\n        newState.isAcceptState = true;\n        newState.prediction = prediction;\n        newState.configs.uniqueAlt = prediction;\n        reportLookaheadAmbiguity.apply(this, [dfa, lookahead, reach.alts, logging]);\n    }\n    newState = addDFAEdge(dfa, previousD, token, newState);\n    return newState;\n}\nfunction reportLookaheadAmbiguity(dfa, lookahead, ambiguityIndices, logging) {\n    const prefixPath = [];\n    for (let i = 1; i <= lookahead; i++) {\n        prefixPath.push(this.LA(i).tokenType);\n    }\n    const atnState = dfa.atnStartState;\n    const topLevelRule = atnState.rule;\n    const production = atnState.production;\n    const message = buildAmbiguityError({\n        topLevelRule,\n        ambiguityIndices,\n        production,\n        prefixPath\n    });\n    logging(message);\n}\nfunction buildAmbiguityError(options) {\n    const pathMsg = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options.prefixPath, (currtok) => (0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.tokenLabel)(currtok)).join(\", \");\n    const occurrence = options.production.idx === 0 ? \"\" : options.production.idx;\n    let currMessage = `Ambiguous Alternatives Detected: <${options.ambiguityIndices.join(\", \")}> in <${getProductionDslName(options.production)}${occurrence}>` +\n        ` inside <${options.topLevelRule.name}> Rule,\\n` +\n        `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n`;\n    currMessage =\n        currMessage +\n            `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\\n` +\n            `For Further details.`;\n    return currMessage;\n}\nfunction getProductionDslName(prod) {\n    if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return \"SUBRULE\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return \"OPTION\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return \"OR\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return \"AT_LEAST_ONE\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return \"AT_LEAST_ONE_SEP\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return \"MANY_SEP\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return \"MANY\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Terminal) {\n        return \"CONSUME\";\n    }\n    else {\n        throw Error(\"non exhaustive match\");\n    }\n}\nfunction buildAdaptivePredictError(path, previous, current) {\n    const nextTransitions = (0,lodash_es_flatMap_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(previous.configs.elements, (e) => e.state.transitions);\n    const nextTokenTypes = (0,lodash_es_uniqBy_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(nextTransitions\n        .filter((e) => e instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.AtomTransition)\n        .map((e) => e.tokenType), (e) => e.tokenTypeIdx);\n    return {\n        actualToken: current,\n        possibleTokenTypes: nextTokenTypes,\n        tokenPath: path\n    };\n}\nfunction getExistingTargetState(state, token) {\n    return state.edges[token.tokenTypeIdx];\n}\nfunction computeReachSet(configs, token, predicateSet) {\n    const intermediate = new _dfa_js__WEBPACK_IMPORTED_MODULE_2__.ATNConfigSet();\n    const skippedStopStates = [];\n    for (const c of configs.elements) {\n        if (predicateSet.is(c.alt) === false) {\n            continue;\n        }\n        if (c.state.type === _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n            skippedStopStates.push(c);\n            continue;\n        }\n        const transitionLength = c.state.transitions.length;\n        for (let i = 0; i < transitionLength; i++) {\n            const transition = c.state.transitions[i];\n            const target = getReachableTarget(transition, token);\n            if (target !== undefined) {\n                intermediate.add({\n                    state: target,\n                    alt: c.alt,\n                    stack: c.stack\n                });\n            }\n        }\n    }\n    let reach;\n    if (skippedStopStates.length === 0 && intermediate.size === 1) {\n        reach = intermediate;\n    }\n    if (reach === undefined) {\n        reach = new _dfa_js__WEBPACK_IMPORTED_MODULE_2__.ATNConfigSet();\n        for (const c of intermediate.elements) {\n            closure(c, reach);\n        }\n    }\n    if (skippedStopStates.length > 0 && !hasConfigInRuleStopState(reach)) {\n        for (const c of skippedStopStates) {\n            reach.add(c);\n        }\n    }\n    return reach;\n}\nfunction getReachableTarget(transition, token) {\n    if (transition instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.AtomTransition &&\n        (0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.tokenMatcher)(token, transition.tokenType)) {\n        return transition.target;\n    }\n    return undefined;\n}\nfunction getUniqueAlt(configs, predicateSet) {\n    let alt;\n    for (const c of configs.elements) {\n        if (predicateSet.is(c.alt) === true) {\n            if (alt === undefined) {\n                alt = c.alt;\n            }\n            else if (alt !== c.alt) {\n                return undefined;\n            }\n        }\n    }\n    return alt;\n}\nfunction newDFAState(closure) {\n    return {\n        configs: closure,\n        edges: {},\n        isAcceptState: false,\n        prediction: -1\n    };\n}\nfunction addDFAEdge(dfa, from, token, to) {\n    to = addDFAState(dfa, to);\n    from.edges[token.tokenTypeIdx] = to;\n    return to;\n}\nfunction addDFAState(dfa, state) {\n    if (state === _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR) {\n        return state;\n    }\n    // Repetitions have the same config set\n    // Therefore, storing the key of the config in a map allows us to create a loop in our DFA\n    const mapKey = state.configs.key;\n    const existing = dfa.states[mapKey];\n    if (existing !== undefined) {\n        return existing;\n    }\n    state.configs.finalize();\n    dfa.states[mapKey] = state;\n    return state;\n}\nfunction computeStartState(atnState) {\n    const configs = new _dfa_js__WEBPACK_IMPORTED_MODULE_2__.ATNConfigSet();\n    const numberOfTransitions = atnState.transitions.length;\n    for (let i = 0; i < numberOfTransitions; i++) {\n        const target = atnState.transitions[i].target;\n        const config = {\n            state: target,\n            alt: i,\n            stack: []\n        };\n        closure(config, configs);\n    }\n    return configs;\n}\nfunction closure(config, configs) {\n    const p = config.state;\n    if (p.type === _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n        if (config.stack.length > 0) {\n            const atnStack = [...config.stack];\n            const followState = atnStack.pop();\n            const followConfig = {\n                state: followState,\n                alt: config.alt,\n                stack: atnStack\n            };\n            closure(followConfig, configs);\n        }\n        else {\n            // Dipping into outer context, simply add the config\n            // This will stop computation once every config is at the rule stop state\n            configs.add(config);\n        }\n        return;\n    }\n    if (!p.epsilonOnlyTransitions) {\n        configs.add(config);\n    }\n    const transitionLength = p.transitions.length;\n    for (let i = 0; i < transitionLength; i++) {\n        const transition = p.transitions[i];\n        const c = getEpsilonTarget(config, transition);\n        if (c !== undefined) {\n            closure(c, configs);\n        }\n    }\n}\nfunction getEpsilonTarget(config, transition) {\n    if (transition instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.EpsilonTransition) {\n        return {\n            state: transition.target,\n            alt: config.alt,\n            stack: config.stack\n        };\n    }\n    else if (transition instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.RuleTransition) {\n        const stack = [...config.stack, transition.followState];\n        return {\n            state: transition.target,\n            alt: config.alt,\n            stack\n        };\n    }\n    return undefined;\n}\nfunction hasConfigInRuleStopState(configs) {\n    for (const c of configs.elements) {\n        if (c.state.type === _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction allConfigsInRuleStopStates(configs) {\n    for (const c of configs.elements) {\n        if (c.state.type !== _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction hasConflictTerminatingPrediction(configs) {\n    if (allConfigsInRuleStopStates(configs)) {\n        return true;\n    }\n    const altSets = getConflictingAltSets(configs.elements);\n    const heuristic = hasConflictingAltSet(altSets) && !hasStateAssociatedWithOneAlt(altSets);\n    return heuristic;\n}\nfunction getConflictingAltSets(configs) {\n    const configToAlts = new Map();\n    for (const c of configs) {\n        const key = (0,_dfa_js__WEBPACK_IMPORTED_MODULE_2__.getATNConfigKey)(c, false);\n        let alts = configToAlts.get(key);\n        if (alts === undefined) {\n            alts = {};\n            configToAlts.set(key, alts);\n        }\n        alts[c.alt] = true;\n    }\n    return configToAlts;\n}\nfunction hasConflictingAltSet(altSets) {\n    for (const value of Array.from(altSets.values())) {\n        if (Object.keys(value).length > 1) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction hasStateAssociatedWithOneAlt(altSets) {\n    for (const value of Array.from(altSets.values())) {\n        if (Object.keys(value).length === 1) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceMappingURL=all-star-lookahead.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/all-star-lookahead.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/atn.js":
/*!****************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/atn.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ATN_BASIC: () => (/* binding */ ATN_BASIC),\n/* harmony export */   ATN_BLOCK_END: () => (/* binding */ ATN_BLOCK_END),\n/* harmony export */   ATN_INVALID_TYPE: () => (/* binding */ ATN_INVALID_TYPE),\n/* harmony export */   ATN_LOOP_END: () => (/* binding */ ATN_LOOP_END),\n/* harmony export */   ATN_PLUS_BLOCK_START: () => (/* binding */ ATN_PLUS_BLOCK_START),\n/* harmony export */   ATN_PLUS_LOOP_BACK: () => (/* binding */ ATN_PLUS_LOOP_BACK),\n/* harmony export */   ATN_RULE_START: () => (/* binding */ ATN_RULE_START),\n/* harmony export */   ATN_RULE_STOP: () => (/* binding */ ATN_RULE_STOP),\n/* harmony export */   ATN_STAR_BLOCK_START: () => (/* binding */ ATN_STAR_BLOCK_START),\n/* harmony export */   ATN_STAR_LOOP_BACK: () => (/* binding */ ATN_STAR_LOOP_BACK),\n/* harmony export */   ATN_STAR_LOOP_ENTRY: () => (/* binding */ ATN_STAR_LOOP_ENTRY),\n/* harmony export */   ATN_TOKEN_START: () => (/* binding */ ATN_TOKEN_START),\n/* harmony export */   AbstractTransition: () => (/* binding */ AbstractTransition),\n/* harmony export */   AtomTransition: () => (/* binding */ AtomTransition),\n/* harmony export */   EpsilonTransition: () => (/* binding */ EpsilonTransition),\n/* harmony export */   RuleTransition: () => (/* binding */ RuleTransition),\n/* harmony export */   buildATNKey: () => (/* binding */ buildATNKey),\n/* harmony export */   createATN: () => (/* binding */ createATN)\n/* harmony export */ });\n/* harmony import */ var lodash_es_map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es/map.js */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es_filter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es/filter.js */ \"(ssr)/./node_modules/lodash-es/filter.js\");\n/* harmony import */ var chevrotain__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! chevrotain */ \"(ssr)/./node_modules/chevrotain/lib/src/api.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\n\n\nfunction buildATNKey(rule, type, occurrence) {\n    return `${rule.name}_${type}_${occurrence}`;\n}\nconst ATN_INVALID_TYPE = 0;\nconst ATN_BASIC = 1;\nconst ATN_RULE_START = 2;\nconst ATN_PLUS_BLOCK_START = 4;\nconst ATN_STAR_BLOCK_START = 5;\n// Currently unused as the ATN is not used for lexing\nconst ATN_TOKEN_START = 6;\nconst ATN_RULE_STOP = 7;\nconst ATN_BLOCK_END = 8;\nconst ATN_STAR_LOOP_BACK = 9;\nconst ATN_STAR_LOOP_ENTRY = 10;\nconst ATN_PLUS_LOOP_BACK = 11;\nconst ATN_LOOP_END = 12;\nclass AbstractTransition {\n    constructor(target) {\n        this.target = target;\n    }\n    isEpsilon() {\n        return false;\n    }\n}\nclass AtomTransition extends AbstractTransition {\n    constructor(target, tokenType) {\n        super(target);\n        this.tokenType = tokenType;\n    }\n}\nclass EpsilonTransition extends AbstractTransition {\n    constructor(target) {\n        super(target);\n    }\n    isEpsilon() {\n        return true;\n    }\n}\nclass RuleTransition extends AbstractTransition {\n    constructor(ruleStart, rule, followState) {\n        super(ruleStart);\n        this.rule = rule;\n        this.followState = followState;\n    }\n    isEpsilon() {\n        return true;\n    }\n}\nfunction createATN(rules) {\n    const atn = {\n        decisionMap: {},\n        decisionStates: [],\n        ruleToStartState: new Map(),\n        ruleToStopState: new Map(),\n        states: []\n    };\n    createRuleStartAndStopATNStates(atn, rules);\n    const ruleLength = rules.length;\n    for (let i = 0; i < ruleLength; i++) {\n        const rule = rules[i];\n        const ruleBlock = block(atn, rule, rule);\n        if (ruleBlock === undefined) {\n            continue;\n        }\n        buildRuleHandle(atn, rule, ruleBlock);\n    }\n    return atn;\n}\nfunction createRuleStartAndStopATNStates(atn, rules) {\n    const ruleLength = rules.length;\n    for (let i = 0; i < ruleLength; i++) {\n        const rule = rules[i];\n        const start = newState(atn, rule, undefined, {\n            type: ATN_RULE_START\n        });\n        const stop = newState(atn, rule, undefined, {\n            type: ATN_RULE_STOP\n        });\n        start.stop = stop;\n        atn.ruleToStartState.set(rule, start);\n        atn.ruleToStopState.set(rule, stop);\n    }\n}\nfunction atom(atn, rule, production) {\n    if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Terminal) {\n        return tokenRef(atn, rule, production.terminalType, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return ruleRef(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return alternation(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return option(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return repetition(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return repetitionSep(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return repetitionMandatory(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return repetitionMandatorySep(atn, rule, production);\n    }\n    else {\n        return block(atn, rule, production);\n    }\n}\nfunction repetition(atn, rule, repetition) {\n    const starState = newState(atn, rule, repetition, {\n        type: ATN_STAR_BLOCK_START\n    });\n    defineDecisionState(atn, starState);\n    const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));\n    return star(atn, rule, repetition, handle);\n}\nfunction repetitionSep(atn, rule, repetition) {\n    const starState = newState(atn, rule, repetition, {\n        type: ATN_STAR_BLOCK_START\n    });\n    defineDecisionState(atn, starState);\n    const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));\n    const sep = tokenRef(atn, rule, repetition.separator, repetition);\n    return star(atn, rule, repetition, handle, sep);\n}\nfunction repetitionMandatory(atn, rule, repetition) {\n    const plusState = newState(atn, rule, repetition, {\n        type: ATN_PLUS_BLOCK_START\n    });\n    defineDecisionState(atn, plusState);\n    const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));\n    return plus(atn, rule, repetition, handle);\n}\nfunction repetitionMandatorySep(atn, rule, repetition) {\n    const plusState = newState(atn, rule, repetition, {\n        type: ATN_PLUS_BLOCK_START\n    });\n    defineDecisionState(atn, plusState);\n    const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));\n    const sep = tokenRef(atn, rule, repetition.separator, repetition);\n    return plus(atn, rule, repetition, handle, sep);\n}\nfunction alternation(atn, rule, alternation) {\n    const start = newState(atn, rule, alternation, {\n        type: ATN_BASIC\n    });\n    defineDecisionState(atn, start);\n    const alts = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(alternation.definition, (e) => atom(atn, rule, e));\n    const handle = makeAlts(atn, rule, start, alternation, ...alts);\n    return handle;\n}\nfunction option(atn, rule, option) {\n    const start = newState(atn, rule, option, {\n        type: ATN_BASIC\n    });\n    defineDecisionState(atn, start);\n    const handle = makeAlts(atn, rule, start, option, block(atn, rule, option));\n    return optional(atn, rule, option, handle);\n}\nfunction block(atn, rule, block) {\n    const handles = (0,lodash_es_filter_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(block.definition, (e) => atom(atn, rule, e)), (e) => e !== undefined);\n    if (handles.length === 1) {\n        return handles[0];\n    }\n    else if (handles.length === 0) {\n        return undefined;\n    }\n    else {\n        return makeBlock(atn, handles);\n    }\n}\nfunction plus(atn, rule, plus, handle, sep) {\n    const blkStart = handle.left;\n    const blkEnd = handle.right;\n    const loop = newState(atn, rule, plus, {\n        type: ATN_PLUS_LOOP_BACK\n    });\n    defineDecisionState(atn, loop);\n    const end = newState(atn, rule, plus, {\n        type: ATN_LOOP_END\n    });\n    blkStart.loopback = loop;\n    end.loopback = loop;\n    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionMandatoryWithSeparator' : 'RepetitionMandatory', plus.idx)] = loop;\n    epsilon(blkEnd, loop); // block can see loop back\n    // Depending on whether we have a separator we put the exit transition at index 1 or 0\n    // This influences the chosen option in the lookahead DFA\n    if (sep === undefined) {\n        epsilon(loop, blkStart); // loop back to start\n        epsilon(loop, end); // exit\n    }\n    else {\n        epsilon(loop, end); // exit\n        // loop back to start with separator\n        epsilon(loop, sep.left);\n        epsilon(sep.right, blkStart);\n    }\n    return {\n        left: blkStart,\n        right: end\n    };\n}\nfunction star(atn, rule, star, handle, sep) {\n    const start = handle.left;\n    const end = handle.right;\n    const entry = newState(atn, rule, star, {\n        type: ATN_STAR_LOOP_ENTRY\n    });\n    defineDecisionState(atn, entry);\n    const loopEnd = newState(atn, rule, star, {\n        type: ATN_LOOP_END\n    });\n    const loop = newState(atn, rule, star, {\n        type: ATN_STAR_LOOP_BACK\n    });\n    entry.loopback = loop;\n    loopEnd.loopback = loop;\n    epsilon(entry, start); // loop enter edge (alt 2)\n    epsilon(entry, loopEnd); // bypass loop edge (alt 1)\n    epsilon(end, loop); // block end hits loop back\n    if (sep !== undefined) {\n        epsilon(loop, loopEnd); // end loop\n        // loop back to start of handle using separator\n        epsilon(loop, sep.left);\n        epsilon(sep.right, start);\n    }\n    else {\n        epsilon(loop, entry); // loop back to entry/exit decision\n    }\n    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionWithSeparator' : 'Repetition', star.idx)] = entry;\n    return {\n        left: entry,\n        right: loopEnd\n    };\n}\nfunction optional(atn, rule, optional, handle) {\n    const start = handle.left;\n    const end = handle.right;\n    epsilon(start, end);\n    atn.decisionMap[buildATNKey(rule, 'Option', optional.idx)] = start;\n    return handle;\n}\nfunction defineDecisionState(atn, state) {\n    atn.decisionStates.push(state);\n    state.decision = atn.decisionStates.length - 1;\n    return state.decision;\n}\nfunction makeAlts(atn, rule, start, production, ...alts) {\n    const end = newState(atn, rule, production, {\n        type: ATN_BLOCK_END,\n        start\n    });\n    start.end = end;\n    for (const alt of alts) {\n        if (alt !== undefined) {\n            // hook alts up to decision block\n            epsilon(start, alt.left);\n            epsilon(alt.right, end);\n        }\n        else {\n            epsilon(start, end);\n        }\n    }\n    const handle = {\n        left: start,\n        right: end\n    };\n    atn.decisionMap[buildATNKey(rule, getProdType(production), production.idx)] = start;\n    return handle;\n}\nfunction getProdType(production) {\n    if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return 'Alternation';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return 'Option';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return 'Repetition';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return 'RepetitionWithSeparator';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return 'RepetitionMandatory';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return 'RepetitionMandatoryWithSeparator';\n    }\n    else {\n        throw new Error('Invalid production type encountered');\n    }\n}\nfunction makeBlock(atn, alts) {\n    const altsLength = alts.length;\n    for (let i = 0; i < altsLength - 1; i++) {\n        const handle = alts[i];\n        let transition;\n        if (handle.left.transitions.length === 1) {\n            transition = handle.left.transitions[0];\n        }\n        const isRuleTransition = transition instanceof RuleTransition;\n        const ruleTransition = transition;\n        const next = alts[i + 1].left;\n        if (handle.left.type === ATN_BASIC &&\n            handle.right.type === ATN_BASIC &&\n            transition !== undefined &&\n            ((isRuleTransition && ruleTransition.followState === handle.right) ||\n                transition.target === handle.right)) {\n            // we can avoid epsilon edge to next element\n            if (isRuleTransition) {\n                ruleTransition.followState = next;\n            }\n            else {\n                transition.target = next;\n            }\n            removeState(atn, handle.right); // we skipped over this state\n        }\n        else {\n            // need epsilon if previous block's right end node is complex\n            epsilon(handle.right, next);\n        }\n    }\n    const first = alts[0];\n    const last = alts[altsLength - 1];\n    return {\n        left: first.left,\n        right: last.right\n    };\n}\nfunction tokenRef(atn, rule, tokenType, production) {\n    const left = newState(atn, rule, production, {\n        type: ATN_BASIC\n    });\n    const right = newState(atn, rule, production, {\n        type: ATN_BASIC\n    });\n    addTransition(left, new AtomTransition(right, tokenType));\n    return {\n        left,\n        right\n    };\n}\nfunction ruleRef(atn, currentRule, nonTerminal) {\n    const rule = nonTerminal.referencedRule;\n    const start = atn.ruleToStartState.get(rule);\n    const left = newState(atn, currentRule, nonTerminal, {\n        type: ATN_BASIC\n    });\n    const right = newState(atn, currentRule, nonTerminal, {\n        type: ATN_BASIC\n    });\n    const call = new RuleTransition(start, rule, right);\n    addTransition(left, call);\n    return {\n        left,\n        right\n    };\n}\nfunction buildRuleHandle(atn, rule, block) {\n    const start = atn.ruleToStartState.get(rule);\n    epsilon(start, block.left);\n    const stop = atn.ruleToStopState.get(rule);\n    epsilon(block.right, stop);\n    const handle = {\n        left: start,\n        right: stop\n    };\n    return handle;\n}\nfunction epsilon(a, b) {\n    const transition = new EpsilonTransition(b);\n    addTransition(a, transition);\n}\nfunction newState(atn, rule, production, partial) {\n    const t = Object.assign({ atn,\n        production, epsilonOnlyTransitions: false, rule, transitions: [], nextTokenWithinRule: [], stateNumber: atn.states.length }, partial);\n    atn.states.push(t);\n    return t;\n}\nfunction addTransition(state, transition) {\n    // A single ATN state can only contain epsilon transitions or non-epsilon transitions\n    // Because they are never mixed, only setting the property for the first transition is fine\n    if (state.transitions.length === 0) {\n        state.epsilonOnlyTransitions = transition.isEpsilon();\n    }\n    state.transitions.push(transition);\n}\nfunction removeState(atn, state) {\n    atn.states.splice(atn.states.indexOf(state), 1);\n}\n//# sourceMappingURL=atn.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/atn.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/dfa.js":
/*!****************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/dfa.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ATNConfigSet: () => (/* binding */ ATNConfigSet),\n/* harmony export */   DFA_ERROR: () => (/* binding */ DFA_ERROR),\n/* harmony export */   getATNConfigKey: () => (/* binding */ getATNConfigKey)\n/* harmony export */ });\n/* harmony import */ var lodash_es_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es/map.js */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\nconst DFA_ERROR = {};\nclass ATNConfigSet {\n    constructor() {\n        this.map = {};\n        this.configs = [];\n    }\n    get size() {\n        return this.configs.length;\n    }\n    finalize() {\n        // Empties the map to free up memory\n        this.map = {};\n    }\n    add(config) {\n        const key = getATNConfigKey(config);\n        // Only add configs which don't exist in our map already\n        // While this does not influence the actual algorithm, adding them anyway would massively increase memory consumption\n        if (!(key in this.map)) {\n            this.map[key] = this.configs.length;\n            this.configs.push(config);\n        }\n    }\n    get elements() {\n        return this.configs;\n    }\n    get alts() {\n        return (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.configs, (e) => e.alt);\n    }\n    get key() {\n        let value = \"\";\n        for (const k in this.map) {\n            value += k + \":\";\n        }\n        return value;\n    }\n}\nfunction getATNConfigKey(config, alt = true) {\n    return `${alt ? `a${config.alt}` : \"\"}s${config.state.stateNumber}:${config.stack.map((e) => e.stateNumber.toString()).join(\"_\")}`;\n}\n//# sourceMappingURL=dfa.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/dfa.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLStarLookaheadStrategy: () => (/* reexport safe */ _all_star_lookahead_js__WEBPACK_IMPORTED_MODULE_0__.LLStarLookaheadStrategy)\n/* harmony export */ });\n/* harmony import */ var _all_star_lookahead_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./all-star-lookahead.js */ \"(ssr)/./node_modules/chevrotain-allstar/lib/all-star-lookahead.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY2hldnJvdGFpbi1hbGxzdGFyL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDa0U7QUFDbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWludGVsbGlnZW50LWFzc2Vzc21lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2NoZXZyb3RhaW4tYWxsc3Rhci9saWIvaW5kZXguanM/NTNjMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqXG4gKiBDb3B5cmlnaHQgMjAyMiBUeXBlRm94IEdtYkhcbiAqIFRoaXMgcHJvZ3JhbSBhbmQgdGhlIGFjY29tcGFueWluZyBtYXRlcmlhbHMgYXJlIG1hZGUgYXZhaWxhYmxlIHVuZGVyIHRoZVxuICogdGVybXMgb2YgdGhlIE1JVCBMaWNlbnNlLCB3aGljaCBpcyBhdmFpbGFibGUgaW4gdGhlIHByb2plY3Qgcm9vdC5cbiAqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiovXG5leHBvcnQgeyBMTFN0YXJMb29rYWhlYWRTdHJhdGVneSB9IGZyb20gJy4vYWxsLXN0YXItbG9va2FoZWFkLmpzJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/index.js\n");

/***/ })

};
;