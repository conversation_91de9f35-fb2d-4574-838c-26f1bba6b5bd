/**
 * 智能问题生成API路由
 */

import { NextRequest, NextResponse } from 'next/server';
import { LLMClient } from '@/services/llm/llm-client';
import JSON5 from 'json5';

/**
 * 强化的LLM响应清理函数
 */
function cleanLLMResponse(text: string): string {
  return text
    .trim()
    // 移除markdown代码块标记
    .replace(/^```json\s*/i, '')
    .replace(/^```\s*/, '')
    .replace(/\s*```$/g, '')
    // 移除注释
    .replace(/\/\*[\s\S]*?\*\//g, '')
    .replace(/\/\/.*$/gm, '')
    // 移除多余的逗号
    .replace(/,(\s*[}\]])/g, '$1')
    // 移除行尾空格
    .replace(/\s+$/gm, '')
    // 移除多余的换行
    .replace(/\n{3,}/g, '\n\n');
}

/**
 * 宽松的JSON解析函数
 * 尝试多种解析方式，提高成功率
 */
function parseFlexibleJSON(text: string, dimension: string): any {
  console.log(`🔍 ${dimension}维度原始响应前200字符:`, text.substring(0, 200));

  // 第一次尝试：直接解析原始文本
  try {
    const result = JSON.parse(text);
    console.log(`✅ ${dimension}维度直接解析成功`);
    return result;
  } catch (error1) {
    console.log(`⚠️ ${dimension}维度直接解析失败:`, error1.message);
  }

  // 第二次尝试：清理后解析
  try {
    const cleaned = cleanLLMResponse(text);
    console.log(`🧹 ${dimension}维度清理后前200字符:`, cleaned.substring(0, 200));
    const result = JSON.parse(cleaned);
    console.log(`✅ ${dimension}维度清理后解析成功`);
    return result;
  } catch (error2) {
    console.log(`⚠️ ${dimension}维度清理后解析失败:`, error2.message);
  }

  // 第三次尝试：使用JSON5解析
  try {
    const cleaned = cleanLLMResponse(text);
    const result = JSON5.parse(cleaned);
    console.log(`✅ ${dimension}维度JSON5解析成功`);
    return result;
  } catch (error3) {
    console.log(`❌ ${dimension}维度JSON5解析失败:`, error3.message);
    throw new Error(`所有JSON解析方式都失败了: ${error3.message}`);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { dimension, profile, count, contextualPrompt } = await request.json();

    console.log(`🤖 API: 为${dimension}维度生成${count}道智能题目...`);

    // 检查是否有有效的API密钥
    const hasValidApiKey = process.env.MINIMAX_API_KEY && process.env.MINIMAX_API_KEY.length > 50;
    
    if (!hasValidApiKey) {
      return NextResponse.json({
        success: false,
        error: '未配置有效的MiniMax API密钥'
      }, { status: 400 });
    }

    const llmClient = new LLMClient();

    const systemPrompt = `你是一位专业的公益机构组织能力评估专家，专门设计个性化的评估问题。

🎯 JSON格式要求（严格遵守）：
1. 直接返回JSON对象，不要使用markdown代码块包装
2. 不要添加 \`\`\`json 或 \`\`\` 标记
3. 使用双引号，不要使用单引号
4. 最后一个元素后不要添加逗号
5. 确保所有必需字段都存在且格式正确
6. 字符串中的引号要正确转义

📋 返回格式示例：
{
  "questions": [
    {
      "id": "${dimension}_I001",
      "text": "问题内容",
      "type": "single_choice",
      "options": ["选项1", "选项2", "选项3", "选项4"],
      "category": "${dimension}_INTELLIGENT",
      "dimension": "${dimension}",
      "weight": 1
    }
  ]
}

📝 内容要求：
1. 问题必须针对公益机构的特点和挑战
2. 体现${dimension}维度的核心评估要素
3. 根据组织画像个性化设计
4. 选项设计要有区分度和专业性
5. 问题ID格式：${dimension}_I001, ${dimension}_I002...

⚠️ 重要提醒：
- 直接返回JSON，不要任何额外的文字说明
- 确保JSON格式完全正确，可以直接被JSON.parse()解析`;

    const userPrompt = `${contextualPrompt}

请生成${count}道针对性的${dimension}维度评估问题。`;

    console.log(`📏 ${dimension}维度提示词长度:`, systemPrompt.length + userPrompt.length, '字符');

    const response = await llmClient.call({
      model: 'minimax-M1',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.8,
      max_tokens: 3000,
      response_format: { type: 'json_object' }
    });

    // 解析LLM响应
    let responseText = '';
    if (response?.choices?.[0]?.message?.content) {
      responseText = response.choices[0].message.content;
    } else if (response?.reply) {
      responseText = response.reply;
    } else {
      throw new Error('LLM响应格式异常');
    }

    try {
      // 使用强化的宽松JSON解析
      const parsedResponse = parseFlexibleJSON(responseText, dimension);
      const questions = parsedResponse.questions || [];
      
      console.log(`✅ ${dimension}维度LLM生成成功:`, questions.length, '道题目');
      
      const formattedQuestions = questions.map((q: any, index: number) => ({
        id: q.id || `${dimension}_I${String(index + 1).padStart(3, '0')}`,
        text: q.text,
        type: q.type || 'single_choice',
        options: q.options || [],
        category: `${dimension}_INTELLIGENT`,
        dimension: dimension,
        weight: q.weight || 1,
        source: 'AI_GENERATED' as const,
        order: 1000 + index, // 智能题目排在预设题目后面
        required: true,
        tags: [`${dimension.toLowerCase()}_dimension`, 'intelligent_generated'],
        metadata: {
          generatedAt: new Date().toISOString(),
          organizationType: profile.organizationType,
          serviceArea: profile.serviceArea,
          developmentStage: profile.developmentStage
        }
      }));

      return NextResponse.json({
        success: true,
        data: {
          questions: formattedQuestions,
          dimension,
          count: formattedQuestions.length
        }
      });

    } catch (parseError) {
      console.error(`❌ ${dimension}维度LLM响应解析失败:`, parseError);
      console.error('原始响应:', responseText);
      
      return NextResponse.json({
        success: false,
        error: `LLM响应解析失败: ${parseError}`
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ 智能问题生成失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '智能问题生成失败'
    }, { status: 500 });
  }
}
