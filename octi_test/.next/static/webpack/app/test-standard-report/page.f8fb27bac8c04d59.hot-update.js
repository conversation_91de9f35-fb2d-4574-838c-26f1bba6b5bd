"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standard-report/page",{

/***/ "(app-pages-browser)/./src/app/test-standard-report/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/test-standard-report/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestStandardReportPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n/**\n * 标准版分析报告样式测试页面\n */ function TestStandardReportPage() {\n    var _analysisResult_dimensions, _analysisResult_recommendations;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 生成标准版分析报告\n    const generateStandardReport = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 使用模拟数据生成标准版报告\n            const mockProfile = {\n                organizationType: \"社会服务机构\",\n                serviceArea: \"教育支持\",\n                resourceStructure: \"基金会支持型\",\n                developmentStage: \"成长期\",\n                teamSize: \"中型（21-50人）\",\n                operatingModel: \"直接服务型\",\n                impactScope: \"区域影响\",\n                organizationCulture: \"使命驱动\",\n                challengesPriorities: \"资金筹集\",\n                futureVision: \"成为区域领先的教育支持机构\"\n            };\n            // 生成模拟的60道题目答案\n            const mockResponses = [];\n            for(let i = 1; i <= 60; i++){\n                mockResponses.push({\n                    questionId: \"Q\".concat(i.toString().padStart(3, \"0\")),\n                    answer: [\n                        \"A\",\n                        \"B\",\n                        \"C\",\n                        \"D\"\n                    ][Math.floor(Math.random() * 4)],\n                    confidence: Math.floor(Math.random() * 3) + 3,\n                    timeSpent: Math.floor(Math.random() * 30) + 10\n                });\n            }\n            console.log(\"\\uD83D\\uDE80 开始生成标准版分析报告...\");\n            const response = await fetch(\"/api/assessment/analyze\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    profile: mockProfile,\n                    responses: mockResponses,\n                    version: \"standard\"\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"分析失败: \".concat(response.status));\n            }\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.error || \"分析失败\");\n            }\n            setAnalysisResult(result.data);\n            console.log(\"✅ 标准版分析报告生成成功:\", result.data);\n        } catch (err) {\n            console.error(\"❌ 生成标准版报告失败:\", err);\n            setError(err instanceof Error ? err.message : \"未知错误\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/test-full-flow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                \"返回测试页面\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"\\uD83D\\uDCCA 标准版分析报告样式预览\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"生成并查看OCTI标准版分析报告的完整样式和内容结构\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"报告生成控制\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"点击按钮生成标准版分析报告并查看样式\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: generateStandardReport,\n                            disabled: isLoading,\n                            size: \"lg\",\n                            className: \"w-full\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"生成中...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"生成标准版分析报告\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-red-800\",\n                            children: \"生成失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83D\\uDCCB 分析结果概览\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: analysisResult.overallScore || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"总体得分\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-sm\",\n                                                    children: analysisResult.level || \"N/A\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"能力等级\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: ((_analysisResult_dimensions = analysisResult.dimensions) === null || _analysisResult_dimensions === void 0 ? void 0 : _analysisResult_dimensions.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"评估维度\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: ((_analysisResult_recommendations = analysisResult.recommendations) === null || _analysisResult_recommendations === void 0 ? void 0 : _analysisResult_recommendations.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"改进建议\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    analysisResult.dimensions && analysisResult.dimensions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83C\\uDFAF 维度分析详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: analysisResult.dimensions.map((dimension, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: dimension.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-blue-600\",\n                                                                    children: dimension.score\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: dimension.level\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mb-3\",\n                                                    children: dimension.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 23\n                                                }, this),\n                                                dimension.strengths && dimension.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-green-700 mb-1\",\n                                                            children: \"优势\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-green-600 list-disc list-inside\",\n                                                            children: dimension.strengths.map((strength, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: strength\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 25\n                                                }, this),\n                                                dimension.improvements && dimension.improvements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-orange-700 mb-1\",\n                                                            children: \"改进点\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-orange-600 list-disc list-inside\",\n                                                            children: dimension.improvements.map((improvement, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: improvement\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this),\n                    analysisResult.recommendations && analysisResult.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"\\uD83D\\uDCA1 改进建议\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: analysisResult.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: rec.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: rec.priority === \"high\" ? \"destructive\" : rec.priority === \"medium\" ? \"default\" : \"secondary\",\n                                                            children: rec.priority === \"high\" ? \"高优先级\" : rec.priority === \"medium\" ? \"中优先级\" : \"低优先级\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mb-3\",\n                                                    children: rec.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 23\n                                                }, this),\n                                                rec.actions && rec.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium mb-1\",\n                                                            children: \"具体行动\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm list-disc list-inside space-y-1\",\n                                                            children: rec.actions.map((action, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: action\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"\\uD83D\\uDD0D 原始数据结构\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"用于开发调试的完整数据结构\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-96\",\n                                    children: JSON.stringify(analysisResult, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test-standard-report/page.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(TestStandardReportPage, \"3l4P3ylBbAn8j/dicBkTLmmClfY=\");\n_c = TestStandardReportPage;\nvar _c;\n$RefreshReg$(_c, \"TestStandardReportPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-standard-report/page.tsx\n"));

/***/ })

});