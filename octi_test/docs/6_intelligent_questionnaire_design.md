# OCTI 智能问卷系统设计方案 v4.0

## 概述

OCTI智能问卷系统v4.0采用配置驱动的智能体架构，实现混合问卷模式：32道预设标准题目 + 28道AI智能生成题目，确保评估的标准性与个性化并重。系统支持双模型协作分析（MiniMax + DeepSeek），提供专业版深度评估能力。

### 核心特性

- **混合问卷模式**：32道OCTI框架预设题目 + 28道组织画像驱动的智能生成题目
- **配置驱动架构**：JSON配置文件驱动的智能体系统，支持版本控制和热更新
- **双模型协作**：MiniMax主分析 + DeepSeek推理增强，提供更深度的评估洞察
- **标准化与个性化并重**：确保评估基准一致性的同时，提供个性化深度分析

本文档详细描述了OCTI智能问卷系统的核心功能实现方案，包括预设题库设计、智能提示词系统、分层生成策略和真正的智能化功能。目标是实现有针对性的动态问题生成，而非随机生成。

## 1. 系统架构概览 v4.0

### 1.1 混合问卷架构流程

```mermaid
graph TB
    A[用户输入组织信息] --> B[组织画像分析]
    B --> C[配置文件加载]
    C --> D[预设题库加载32题]
    C --> E[智能生成配置]
    E --> F[AI生成28题]
    D --> G[混合问卷融合60题]
    F --> G
    G --> H[双模型协作分析]
    H --> I[最终问卷输出]
```

### 1.2 混合问卷架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    OCTI 混合问卷系统 v4.0                        │
├─────────────────────────────────────────────────────────────────┤
│  配置层 (Config Layer)                                          │
│  ├── preset_questions.json (32道预设题目)                       │
│  ├── question_design_prompt.json (智能生成配置)                 │
│  └── organization_tutor_prompt.json (分析配置)                  │
├─────────────────────────────────────────────────────────────────┤
│  智能体层 (Agent Layer)                                         │
│  ├── HybridQuestionnaireFuser (混合问卷融合器)                  │
│  ├── ConfigDrivenQuestionDesigner (配置驱动问卷设计器)          │
│  └── DualModelCollaborationFramework (双模型协作框架)           │
├─────────────────────────────────────────────────────────────────┤
│  问卷层 (Questionnaire Layer)                                   │
│  ├── 预设题目 (32题) │ 智能生成题目 (28题) │ 融合问卷 (60题)     │
│  │  SF维度: 8题     │  SF维度: 7题        │  SF维度: 15题      │
│  │  IT维度: 8题     │  IT维度: 7题        │  IT维度: 15题      │
│  │  MV维度: 8题     │  MV维度: 7题        │  MV维度: 15题      │
│  │  AD维度: 8题     │  AD维度: 7题        │  AD维度: 15题      │
└─────────────────────────────────────────────────────────────────┘
```

## 2. 预设题库设计 (32道标准题目)

### 2.1 OCTI框架预设题库结构

基于OCTI评估框架，设计32道核心标准题目，确保评估基准的一致性和可比性。

```json
{
  "preset_questions": {
    "total_count": 32,
    "dimensions": {
      "SF": {
        "name": "战略与财务",
        "question_count": 8,
        "sub_dimensions": [
          "战略规划",
          "财务管理", 
          "资源配置",
          "风险控制"
        ]
      },
      "IT": {
        "name": "影响力与透明度",
        "question_count": 8,
        "sub_dimensions": [
          "社会影响力",
          "透明度建设",
          "公信力管理",
          "品牌建设"
        ]
      },
      "MV": {
        "name": "使命与价值观",
        "question_count": 8,
        "sub_dimensions": [
          "使命清晰度",
          "价值观践行",
          "文化建设",
          "理念传播"
        ]
      },
      "AD": {
        "name": "适应性与发展",
        "question_count": 8,
        "sub_dimensions": [
          "创新能力",
          "学习能力",
          "变革管理",
          "可持续发展"
        ]
      }
    }
  }
}
```

### 2.2 预设题目示例

#### SF维度预设题目 (8题)

```json
{
  "SF_preset_questions": [
    {
      "id": "SF_P001",
      "dimension": "SF",
      "sub_dimension": "战略规划",
      "text": "您的组织是否制定了明确的3-5年战略规划？",
      "type": "single_choice",
      "options": ["是，非常明确", "是，但需要完善", "正在制定中", "没有明确规划"],
      "weight": 1.0,
      "is_core": true
    },
    {
      "id": "SF_P002",
      "dimension": "SF",
      "sub_dimension": "财务管理",
      "text": "您的组织财务管理制度的完善程度如何？",
      "type": "single_choice",
      "options": ["制度完善，执行良好", "制度基本完善", "制度不够完善", "缺乏规范制度"],
      "weight": 1.0,
      "is_core": true
    }
  ]
}
```

## 3. 混合问卷融合策略

### 3.1 融合算法设计

混合问卷融合器负责将32道预设题目与28道智能生成题目有机融合，确保问卷的完整性和逻辑性。

```typescript
// 混合问卷融合策略
interface HybridQuestionnaireFusionStrategy {
  // 基础保障：32道预设题目确保评估基准
  presetQuestions: PresetQuestion[];
  
  // 个性化补充：28道智能生成题目提供深度洞察
  intelligentQuestions: IntelligentQuestion[];
  
  // 维度平衡：每个维度15题（8+7）
  dimensionBalance: {
    SF: { preset: 8, intelligent: 7, total: 15 };
    IT: { preset: 8, intelligent: 7, total: 15 };
    MV: { preset: 8, intelligent: 7, total: 15 };
    AD: { preset: 8, intelligent: 7, total: 15 };
  };
  
  // 难度梯度：从基础到深度的渐进式设计
  difficultyGradient: 'basic' | 'intermediate' | 'advanced';
}
```

### 3.2 融合质量控制

```typescript
class QuestionnaireFusionQualityControl {
  /**
   * 验证问卷融合质量
   */
  validateFusionQuality(fusedQuestionnaire: any): ValidationResult {
    const validations = [
      this.validateQuestionCount(fusedQuestionnaire),
      this.validateDimensionBalance(fusedQuestionnaire),
      this.validateQuestionDuplication(fusedQuestionnaire),
      this.validateDifficultyProgression(fusedQuestionnaire),
      this.validateContentRelevance(fusedQuestionnaire)
    ];
    
    return this.aggregateValidationResults(validations);
  }
  
  /**
   * A/B测试支持
   */
  generateABTestVariants(baseQuestionnaire: any): any[] {
    return [
      this.generateVariantA(baseQuestionnaire), // 更多预设题目
      this.generateVariantB(baseQuestionnaire)  // 更多智能生成题目
    ];
  }
}
```

## 4. 配置驱动的智能生成机制

### 4.1 配置文件结构

智能问卷生成完全基于JSON配置文件驱动，支持版本控制和热更新。

```json
{
  "question_design_config": {
    "version": "4.0.1",
    "templates": {
      "standard": {
        "base_prompt": "基于OCTI框架为公益机构生成评估问题...",
        "depth_level": "basic",
        "question_types": ["single_choice", "multiple_choice", "scale_rating"]
      },
      "professional": {
        "base_prompt": "基于OCTI框架为公益机构生成深度评估问题...",
        "depth_level": "advanced",
        "question_types": ["single_choice", "multiple_choice", "scale_rating", "open_ended"]
      }
    },
    "contexts": {
      "service_area": {
        "education": "教育领域的公益机构通常关注...",
        "healthcare": "医疗健康领域的公益机构需要...",
        "environment": "环保领域的公益机构应当..."
      },
      "development_stage": {
        "startup": "初创期组织需要重点关注基础建设...",
        "growth": "成长期组织应当注重规模化发展...",
        "mature": "成熟期组织需要关注可持续发展..."
      },
      "organization_scale": {
        "small": "小型组织（<10人）应当关注...",
        "medium": "中型组织（10-50人）需要...",
        "large": "大型组织（>50人）应当..."
      }
    }
  }
}
```

### 4.2 智能生成流程

```typescript
class ConfigDrivenQuestionGeneration {
  /**
   * 配置驱动的问题生成流程
   */
  async generateIntelligentQuestions(
    organizationProfile: OrganizationProfile,
    version: 'standard' | 'professional'
  ): Promise<IntelligentQuestion[]> {
    // 1. 加载配置
    const config = await this.configEngine.loadConfig('question_design_prompt');
    
    // 2. 构建上下文化提示词
    const contextualPrompt = this.buildContextualPrompt(organizationProfile, config);
    
    // 3. 生成28道智能题目（四维度各7题）
    const questions = await this.generateQuestionsByDimension(contextualPrompt, version);
    
    // 4. 质量验证和优化
    return this.validateAndOptimizeQuestions(questions, organizationProfile);
  }
  
  /**
   * 按维度生成题目
   */
  private async generateQuestionsByDimension(
    prompt: string, 
    version: string
  ): Promise<IntelligentQuestion[]> {
    const dimensions = ['SF', 'IT', 'MV', 'AD'];
    const allQuestions = [];
    
    for (const dimension of dimensions) {
      const dimensionQuestions = await this.generateDimensionQuestions(
        prompt, 
        dimension, 
        7, // 每个维度7题
        version
      );
      allQuestions.push(...dimensionQuestions);
    }
    
    return allQuestions;
  }
}
```

## 5. 双模型协作分析

### 5.1 协作分析架构

```typescript
interface DualModelAnalysisArchitecture {
  // MiniMax主分析：基础评估和维度分析
  primaryAnalysis: {
    model: 'minimax';
    focus: ['dimension_scoring', 'basic_insights', 'trend_analysis'];
    output: 'structured_analysis';
  };
  
  // DeepSeek推理增强：深度洞察和建议生成
  enhancedReasoning: {
    model: 'deepseek';
    focus: ['deep_insights', 'strategic_recommendations', 'risk_assessment'];
    output: 'enhanced_analysis';
  };
  
  // 结果融合：综合两个模型的分析结果
  resultFusion: {
    strategy: 'weighted_combination';
    confidence_scoring: boolean;
    consensus_analysis: boolean;
  };
}
```

### 5.2 智能提示词系统

### 5.2.1 组织画像驱动

#### 公益机构组织特征提取
```typescript
interface NonprofitOrganizationProfile {
  // 基础信息
  organizationType: '基金会' | '公益组织' | '社会团体' | '民办非企业' | '国际NGO' | '政府机构';
  serviceArea: string[];      // 服务领域（教育、环保、扶贫、医疗等）
  organizationScale: '微型' | '小型' | '中型' | '大型' | '超大型';
  developmentStage: '初创期' | '成长期' | '成熟期' | '转型期' | '扩张期';
  
  // 公益机构特有信息
  operatingModel: '直接服务' | '资助型' | '倡导型' | '研究型' | '平台型' | '混合型';
  impactPositioning: '本地影响' | '区域影响' | '全国影响' | '国际影响';
  organizationalCulture: '使命驱动' | '创新导向' | '协作共享' | '专业严谨' | '草根活力';
  
  // 使命愿景
  missionVision: {
    mission: string;          // 使命陈述
    vision: string;           // 愿景描述
    values: string[];         // 核心价值观
    theory: string;           // 变革理论
  };
  
  // 治理结构
  governance: {
    boardStructure: '理事会制' | '董事会制' | '委员会制' | '其他';
    decisionMaking: '集中决策' | '分权决策' | '共识决策' | '混合决策';
    transparency: '高透明' | '中等透明' | '低透明';
    accountability: string[]; // 问责机制
  };
  
  // 资源状况
  resourceProfile: {
    fundingSources: string[]; // 资金来源
    volunteerBase: '无志愿者' | '少量志愿者' | '中等规模' | '大量志愿者';
    partnerships: string[];   // 合作伙伴类型
    capacity: '资源充足' | '资源适中' | '资源紧张';
  };
  
  // 影响力测量
  impactMeasurement: {
    hasTheory: boolean;       // 是否有变革理论
    measurementTools: string[]; // 测量工具
    reportingFrequency: '月度' | '季度' | '半年度' | '年度' | '不定期';
    stakeholderFeedback: boolean; // 是否收集利益相关者反馈
  };
  
  // 通用信息
  challenges: string[];       // 当前面临的主要挑战
  goals: string[];           // 组织目标
  region: string;            // 服务地区
  foundedYear: number;       // 成立年份
  keyMetrics: Record<string, any>; // 关键指标
}
```

#### 公益机构动态提示词构建器
```typescript
class NonprofitDynamicPromptBuilder {
  /**
   * 根据公益机构画像构建智能提示词
   */
  buildContextualPrompt(profile: NonprofitOrganizationProfile, dimension: OCTIDimension): string {
    const serviceAreaContext = this.getServiceAreaContext(profile.serviceArea);
    const stageContext = this.getDevelopmentStageContext(profile.developmentStage);
    const scaleContext = this.getOrganizationScaleContext(profile.organizationScale);
    const operatingContext = this.getOperatingModelContext(profile.operatingModel);
    
    return `
你是一位专业的公益机构组织能力评估专家，正在为以下公益机构设计${dimension}维度的评估问题：

机构背景：
- 组织类型：${profile.organizationType}
- 服务领域：${profile.serviceArea.join('、')} (${serviceAreaContext})
- 组织规模：${profile.organizationScale} (${scaleContext})
- 发展阶段：${profile.developmentStage} (${stageContext})
- 运营模式：${profile.operatingModel} (${operatingContext})
- 影响力定位：${profile.impactPositioning}
- 组织文化：${profile.organizationalCulture}
- 使命：${profile.missionVision.mission}
- 主要挑战：${profile.challenges.join('、')}
- 发展目标：${profile.goals.join('、')}
- 资源状况：${profile.resourceProfile.capacity}

请基于以上背景，生成针对性的${dimension}维度评估问题。
问题应该：
1. 体现公益机构的特点和专业术语
2. 符合该发展阶段的关注重点和能力要求
3. 适应该组织规模的管理复杂度和资源约束
4. 针对具体的公益挑战和社会目标
5. 考虑公益机构的使命驱动特性和社会责任
6. 反映该运营模式的独特性和影响力测量需求
`;
  }
  
  /**
   * 获取服务领域特定上下文
   */
  private getServiceAreaContext(serviceAreas: string[]): string {
    const contexts = {
      '教育': '教育公平、学习成效、师资培养、教育创新',
      '环保': '环境保护、可持续发展、生态修复、绿色倡导',
      '扶贫': '精准扶贫、能力建设、可持续脱贫、社区发展',
      '医疗': '健康促进、医疗可及性、疾病预防、健康教育',
      '养老': '老年关怀、养老服务、代际关系、老龄化应对',
      '儿童': '儿童保护、儿童发展、教育支持、权益维护',
      '妇女': '性别平等、妇女赋权、反家暴、职业发展',
      '残障': '无障碍环境、康复服务、就业支持、权益保障',
      '文化': '文化传承、艺术推广、社区文化、文化多样性',
      '科技': '科技普及、数字鸿沟、创新教育、技术公益'
    };
    
    const relevantContexts = serviceAreas.map(area => contexts[area] || '社会服务').join('、');
    return relevantContexts || '综合性社会服务';
  }
  
  /**
   * 获取发展阶段特定上下文
   */
  private getDevelopmentStageContext(stage: string): string {
    const contexts = {
      '初创期': '团队建设、使命明确、基础能力、资源获取',
      '成长期': '项目扩展、影响力提升、系统建设、伙伴关系',
      '成熟期': '专业深化、可持续发展、创新突破、行业引领',
      '转型期': '战略调整、模式创新、能力重构、变革管理',
      '扩张期': '规模化发展、复制推广、网络建设、影响力放大'
    };
    return contexts[stage] || '组织发展';
  }
  
  /**
   * 获取组织规模特定上下文
   */
  private getOrganizationScaleContext(scale: string): string {
    const contexts = {
      '微型': '灵活机动、资源有限、人员精简、本地化服务',
      '小型': '专业聚焦、团队协作、效率优先、区域影响',
      '中型': '体系建设、专业分工、流程规范、跨区域服务',
      '大型': '系统管理、多元项目、规模效应、全国影响',
      '超大型': '平台化运营、生态建设、国际合作、行业引领'
    };
    return contexts[scale] || '组织管理';
  }
  
  /**
   * 获取运营模式特定上下文
   */
  private getOperatingModelContext(model: string): string {
    const contexts = {
      '直接服务': '一线服务、受益者接触、服务质量、需求响应',
      '资助型': '资源配置、项目评估、伙伴管理、影响力放大',
      '倡导型': '政策影响、公众教育、议题推动、社会动员',
      '研究型': '知识生产、政策建议、实证研究、智库功能',
      '平台型': '资源整合、网络建设、协调服务、生态构建',
      '混合型': '多元能力、协同效应、资源优化、综合影响'
    };
    return contexts[model] || '综合运营';
  }
}
```

### 2.2 情境化问题生成

#### 公益机构情境模板系统
```typescript
interface NonprofitContextualTemplate {
  id: string;
  dimension: OCTIDimension;
  serviceArea: string[];     // 服务领域
  organizationType: string;  // 组织类型
  developmentStage: string;  // 发展阶段
  operatingModel: string;    // 运营模式
  scenario: string;          // 情境描述
  variables: string[];       // 可替换变量
  questionPattern: string;   // 问题模式
  serviceAreaAdaptations: Record<string, string>; // 服务领域适配
}

class NonprofitContextualQuestionGenerator {
  /**
   * 生成公益机构情境化问题
   */
  generateContextualQuestion(
    template: NonprofitContextualTemplate,
    profile: NonprofitOrganizationProfile
  ): string {
    let question = template.questionPattern;
    
    // 替换公益机构特定变量
    const variables = {
      '{organization_scale}': this.getScaleDescription(profile.organizationScale),
      '{service_area}': profile.serviceArea.join('、'),
      '{organization_type}': profile.organizationType,
      '{development_stage}': profile.developmentStage,
      '{operating_model}': profile.operatingModel,
      '{impact_positioning}': profile.impactPositioning,
      '{main_challenge}': profile.challenges[0] || '组织发展',
      '{mission}': profile.missionVision.mission,
      '{resource_capacity}': profile.resourceProfile.capacity
    };
    
    Object.entries(variables).forEach(([key, value]) => {
      question = question.replace(new RegExp(key, 'g'), value);
    });
    
    // 应用服务领域特定适配
    const primaryServiceArea = profile.serviceArea[0];
    if (template.serviceAreaAdaptations[primaryServiceArea]) {
      question = template.serviceAreaAdaptations[primaryServiceArea];
    }
    
    return question;
  }
  
  /**
   * 获取组织规模描述
   */
  private getScaleDescription(scale: string): string {
    const descriptions = {
      '微型': '团队精简、资源有限的小型公益组织',
      '小型': '专业聚焦、团队协作的公益组织',
      '中型': '体系建设、专业分工的公益组织',
      '大型': '系统管理、多元项目的公益组织',
      '超大型': '平台化运营、生态建设的大型公益组织'
    };
    return descriptions[scale] || '公益组织';
  }
}
```

### 2.3 变化因子注入

#### 公益机构随机性和多样性保证
```typescript
class NonprofitVariabilityInjector {
  /**
   * 注入公益机构特有的变化因子
   */
  injectVariability(basePrompt: string, profile: NonprofitOrganizationProfile): string {
    const timestamp = Date.now();
    const randomSeed = this.generateSeed(profile, timestamp);
    const contextualFactors = this.getContextualFactors(profile);
    const variabilityFactors = this.getVariabilityFactors(profile);
    
    return `
${basePrompt}

公益机构变化因子：
- 评估时间：${new Date().toISOString()}
- 随机种子：${randomSeed}
- 上下文因子：${contextualFactors.join(', ')}
- 个性化标识：${this.generatePersonalizationId(profile)}
- 公益特色因子：${variabilityFactors.join(', ')}

请确保生成的问题具有以下特点：
1. 在保持专业性的同时，体现公益机构的独特性和使命驱动特征
2. 避免过于通用的表述，使用具体的公益服务情境描述
3. 结合当前社会环境和公益行业发展趋势
4. 体现该组织规模和发展阶段在公益领域的特殊关注点
5. 反映公益机构的社会责任和影响力测量需求
6. 考虑公益机构的资源约束和可持续发展挑战
`;
  }
  
  /**
   * 获取公益机构特有的变化因子
   */
  private getVariabilityFactors(profile: NonprofitOrganizationProfile): string[] {
    const factors = [];
    
    // 服务领域特色
    if (profile.serviceArea.length > 0) {
      factors.push(`${profile.serviceArea[0]}领域的专业特色`);
    }
    
    // 运营模式特色
    factors.push(`${profile.operatingModel}的运营特点`);
    
    // 影响力定位特色
    factors.push(`${profile.impactPositioning}的责任要求`);
    
    // 组织文化特色
    factors.push(`${profile.organizationalCulture}的文化特征`);
    
    return factors;
  }
  
  /**
   * 获取公益机构上下文因子
   */
  private getContextualFactors(profile: NonprofitOrganizationProfile): string[] {
    const factors = [];
    
    // 治理结构因子
    factors.push(`${profile.governance.boardStructure}治理模式`);
    
    // 资源状况因子
    factors.push(`${profile.resourceProfile.capacity}资源状况`);
    
    // 透明度因子
    factors.push(`${profile.governance.transparency}度透明`);
    
    // 志愿者因子
    if (profile.resourceProfile.volunteerBase !== '无志愿者') {
      factors.push(`${profile.resourceProfile.volunteerBase}参与`);
    }
    
    return factors;
  }
  
  private generateSeed(profile: NonprofitOrganizationProfile, timestamp: number): string {
    const factors = [
      profile.organizationType,
      profile.serviceArea.join('-'),
      profile.organizationScale,
      profile.developmentStage,
      timestamp.toString()
    ];
    return btoa(factors.join('-')).substring(0, 8);
  }
  
  private generatePersonalizationId(profile: NonprofitOrganizationProfile): string {
    const id = `${profile.organizationType}-${profile.serviceArea[0]}-${profile.organizationScale}-${profile.operatingModel}`;
    return btoa(id).substring(0, 12);
  }
}
```

## 3. 公益机构分层生成策略

### 3.1 第一层：公益机构核心维度题目（标准化）

#### 公益机构预设题库管理
```typescript
interface NonprofitCoreQuestion {
  id: string;
  dimension: OCTIDimension;
  subdimension: string;
  questionText: string;
  options: QuestionOption[];
  weight: number;
  validationRules: ValidationRule[];
  nonprofitContext: {
    serviceAreaRelevance: string[];  // 相关服务领域
    organizationTypeApplicability: string[];  // 适用组织类型
    missionAlignment: number;        // 使命契合度 (1-5)
    impactMeasurement: boolean;      // 是否涉及影响力测量
    stakeholderFocus: string[];      // 利益相关者焦点
    socialValueWeight: number;       // 社会价值权重 (1-5)
  };
}

class NonprofitCoreQuestionBank {
  private coreQuestions: Map<OCTIDimension, NonprofitCoreQuestion[]> = new Map();
  
  /**
   * 获取公益机构核心维度题目
   */
  getCoreQuestions(
    dimension: OCTIDimension, 
    profile: NonprofitOrganizationProfile,
    count: number = 10
  ): NonprofitCoreQuestion[] {
    const questions = this.coreQuestions.get(dimension) || [];
    const filteredQuestions = this.filterByNonprofitProfile(questions, profile);
    return this.selectBalancedQuestions(filteredQuestions, count);
  }
  
  /**
   * 根据公益机构画像过滤题目
   */
  private filterByNonprofitProfile(
    questions: NonprofitCoreQuestion[], 
    profile: NonprofitOrganizationProfile
  ): NonprofitCoreQuestion[] {
    return questions.filter(q => {
      // 服务领域相关性检查
      const serviceAreaMatch = q.nonprofitContext.serviceAreaRelevance.length === 0 ||
        q.nonprofitContext.serviceAreaRelevance.some(area => profile.serviceArea.includes(area));
      
      // 组织类型适用性检查
      const typeMatch = q.nonprofitContext.organizationTypeApplicability.length === 0 ||
        q.nonprofitContext.organizationTypeApplicability.includes(profile.organizationType);
      
      return serviceAreaMatch && typeMatch;
    }).sort((a, b) => {
      // 按使命契合度和社会价值权重排序
      const scoreA = a.nonprofitContext.missionAlignment + a.nonprofitContext.socialValueWeight;
      const scoreB = b.nonprofitContext.missionAlignment + b.nonprofitContext.socialValueWeight;
      return scoreB - scoreA;
    });
  }
  
  /**
   * 平衡选择题目（确保子维度覆盖和公益特色）
   */
  private selectBalancedQuestions(
    questions: NonprofitCoreQuestion[], 
    count: number
  ): NonprofitCoreQuestion[] {
    const subdimensions = [...new Set(questions.map(q => q.subdimension))];
    const questionsPerSubdim = Math.floor(count / subdimensions.length);
    
    const selected: NonprofitCoreQuestion[] = [];
    subdimensions.forEach(subdim => {
      const subdimQuestions = questions.filter(q => q.subdimension === subdim);
      // 优先选择高使命契合度的题目
      const sortedSubdimQuestions = subdimQuestions.sort((a, b) => 
        b.nonprofitContext.missionAlignment - a.nonprofitContext.missionAlignment
      );
      selected.push(...sortedSubdimQuestions.slice(0, questionsPerSubdim));
    });
    
    return selected;
  }
}
```

### 3.2 第二层：公益机构情境适配题目（LLM生成）

#### 公益机构智能适配生成器
```typescript
class NonprofitContextualQuestionGenerator {
  /**
   * 生成公益机构情境适配题目
   */
  async generateContextualQuestions(
    dimension: OCTIDimension,
    profile: NonprofitOrganizationProfile,
    count: number = 10
  ): Promise<GeneratedQuestion[]> {
    const prompt = this.buildNonprofitContextualPrompt(dimension, profile);
    
    const llmRequest: LLMRequest = {
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `请生成${count}道针对${dimension}维度的公益机构情境化评估问题。每道题目都应该：
1. 结合该公益机构的具体背景和使命
2. 使用公益服务相关的术语和场景
3. 体现该发展阶段的关注重点和能力要求
4. 包含5个选项（1-5分）
5. 具有明确的社会价值评估目标
6. 体现公益机构的利益相关者多样性
7. 强调透明度、问责制等公益特色`
        }
      ],
      temperature: 1.2, // 提高创造性
      maxTokens: 3000
    };
    
    const response = await this.llmClient.chat(llmRequest);
    return this.parseGeneratedQuestions(response.content);
  }
  
  /**
   * 构建公益机构情境化提示词
   */
  private buildNonprofitContextualPrompt(dimension: OCTIDimension, profile: NonprofitOrganizationProfile): string {
    const dimensionGuide = this.getDimensionGuide(dimension);
    const serviceAreaContext = this.getServiceAreaSpecificContext(profile.serviceArea, dimension);
    
    return `
你是OCTI公益机构组织能力评估的专家，专门设计${dimension}维度的评估问题。

维度说明：${dimensionGuide}

公益机构背景：
- 组织类型：${profile.organizationType}
- 服务领域：${profile.serviceArea.join('、')}（${serviceAreaContext}）
- 组织规模：${profile.organizationScale}（${this.getNonprofitSizeDescription(profile.organizationScale)}）
- 发展阶段：${profile.developmentStage}
- 运营模式：${profile.operatingModel}
- 影响力定位：${profile.impactPositioning}
- 组织文化：${profile.organizationalCulture}
- 使命：${profile.missionVision.mission}
- 主要挑战：${profile.challenges.join('、')}
- 治理结构：${profile.governance.boardStructure}
- 资源状况：${profile.resourceProfile.capacity}
- 志愿者基础：${profile.resourceProfile.volunteerBase}

公益机构特定考虑：${serviceAreaContext}

请基于以上信息，设计具有以下特点的评估问题：
1. 情境化：使用具体的公益服务场景，而非抽象概念
2. 公益化：体现该服务领域的特点和专业术语
3. 阶段化：符合该发展阶段的关注重点和能力要求
4. 使命化：针对该机构的具体使命和社会目标
5. 利益相关者导向：考虑受益者、捐赠人、志愿者、政府等多元需求
6. 社会价值导向：强调社会影响力而非经济利润
7. 透明问责：体现公益机构的透明度和问责制要求
`;
  }
  
  /**
   * 获取服务领域特定上下文
   */
  private getServiceAreaSpecificContext(serviceAreas: string[], dimension: OCTIDimension): string {
    const contextMap = {
      '教育': {
        'S/F': '教育资源配置、师资培养、学习成效评估',
        'I/T': '教育技术创新、教学方法改进、数字化教育',
        'M/V': '教育公平使命、人才培养愿景、知识传承价值',
        'A/D': '教育决策机制、学校治理结构、教育政策适应'
      },
      '环保': {
        'S/F': '环保项目管理、生态修复资源、可持续发展资金',
        'I/T': '环保技术应用、绿色创新、环境监测技术',
        'M/V': '环境保护使命、绿色发展愿景、生态文明价值',
        'A/D': '环保决策参与、生态治理协调、环境政策倡导'
      },
      '扶贫': {
        'S/F': '扶贫资源整合、能力建设投入、可持续脱贫机制',
        'I/T': '精准扶贫技术、产业扶贫创新、数字化扶贫',
        'M/V': '脱贫攻坚使命、共同富裕愿景、社会公平价值',
        'A/D': '扶贫决策机制、多方协调治理、政策对接能力'
      }
    };
    
    const primaryArea = serviceAreas[0];
    return contextMap[primaryArea]?.[dimension] || '综合性社会服务能力建设';
  }
  
  /**
   * 获取公益机构规模描述
   */
  private getNonprofitSizeDescription(scale: string): string {
    const descriptions = {
      '微型': '团队精简、资源有限、本地化服务的草根公益组织',
      '小型': '专业聚焦、团队协作、区域影响的专业公益组织',
      '中型': '体系建设、专业分工、跨区域服务的成长型公益组织',
      '大型': '系统管理、多元项目、全国影响的平台型公益组织',
      '超大型': '生态建设、国际合作、行业引领的综合性公益组织'
    };
    return descriptions[scale] || '公益组织';
  }
}
```

### 3.3 第三层：深度探索题目（动态生成）

#### 自适应深度探索
```typescript
class AdaptiveExplorationGenerator {
  /**
   * 基于前面回答生成深度探索题目
   */
  async generateExplorationQuestions(
    responses: QuestionResponse[],
    profile: OrganizationProfile,
    targetDimension: OCTIDimension
  ): Promise<GeneratedQuestion[]> {
    const analysis = this.analyzeResponses(responses);
    const focusAreas = this.identifyFocusAreas(analysis, targetDimension);
    
    const prompt = this.buildExplorationPrompt(focusAreas, profile, analysis);
    
    const llmRequest: LLMRequest = {
      messages: [
        {
          role: 'system',
          content: prompt
        },
        {
          role: 'user',
          content: `基于前面的回答分析，请生成5道深度探索问题，重点关注：${focusAreas.join('、')}`
        }
      ],
      temperature: 1.0,
      maxTokens: 2000
    };
    
    const response = await this.llmClient.chat(llmRequest);
    return this.parseGeneratedQuestions(response.content);
  }
  
  /**
   * 分析回答模式
   */
  private analyzeResponses(responses: QuestionResponse[]): ResponseAnalysis {
    const scores = responses.map(r => r.score);
    const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    const variance = this.calculateVariance(scores);
    
    return {
      averageScore: avgScore,
      variance: variance,
      consistencyLevel: variance < 0.5 ? 'high' : variance < 1.0 ? 'medium' : 'low',
      strengthAreas: this.identifyStrengthAreas(responses),
      weaknessAreas: this.identifyWeaknessAreas(responses),
      uncertainAreas: this.identifyUncertainAreas(responses)
    };
  }
  
  /**
   * 识别需要深度探索的领域
   */
  private identifyFocusAreas(
    analysis: ResponseAnalysis,
    dimension: OCTIDimension
  ): string[] {
    const focusAreas: string[] = [];
    
    // 基于弱势领域
    if (analysis.weaknessAreas.length > 0) {
      focusAreas.push(...analysis.weaknessAreas.map(area => `${area}的改进策略`));
    }
    
    // 基于不确定领域
    if (analysis.uncertainAreas.length > 0) {
      focusAreas.push(...analysis.uncertainAreas.map(area => `${area}的具体实践`));
    }
    
    // 基于优势领域的深化
    if (analysis.strengthAreas.length > 0) {
      focusAreas.push(...analysis.strengthAreas.map(area => `${area}的进一步发展`));
    }
    
    return focusAreas.slice(0, 3); // 最多3个重点领域
  }
}
```

## 4. 真正的智能化

### 4.1 多轮对话系统

#### 对话流程管理
```typescript
interface ConversationState {
  sessionId: string;
  currentStage: 'background' | 'core_assessment' | 'contextual' | 'exploration';
  organizationProfile: Partial<OrganizationProfile>;
  responses: QuestionResponse[];
  generatedQuestions: GeneratedQuestion[];
  conversationHistory: ConversationTurn[];
}

class ConversationalAssessment {
  /**
   * 开始多轮对话评估
   */
  async startConversation(userId: string): Promise<ConversationState> {
    const sessionId = this.generateSessionId();
    
    const initialState: ConversationState = {
      sessionId,
      currentStage: 'background',
      organizationProfile: {},
      responses: [],
      generatedQuestions: [],
      conversationHistory: []
    };
    
    // 开始背景了解
    const backgroundQuestions = await this.generateBackgroundQuestions();
    initialState.conversationHistory.push({
      type: 'system',
      content: '欢迎使用OCTI智能评估系统！为了为您提供最准确的评估，我需要先了解一些组织背景信息。',
      questions: backgroundQuestions
    });
    
    return initialState;
  }
  
  /**
   * 处理用户回答并推进对话
   */
  async processResponse(
    sessionId: string,
    responses: any[]
  ): Promise<ConversationState> {
    const state = await this.getConversationState(sessionId);
    
    switch (state.currentStage) {
      case 'background':
        return this.processBackgroundResponses(state, responses);
      case 'core_assessment':
        return this.processCoreResponses(state, responses);
      case 'contextual':
        return this.processContextualResponses(state, responses);
      case 'exploration':
        return this.processExplorationResponses(state, responses);
      default:
        throw new Error('Unknown conversation stage');
    }
  }
  
  /**
   * 处理背景信息收集
   */
  private async processBackgroundResponses(
    state: ConversationState,
    responses: any[]
  ): Promise<ConversationState> {
    // 更新组织画像
    state.organizationProfile = this.buildOrganizationProfile(responses);
    
    // 生成个性化的核心评估问题
    const coreQuestions = await this.generatePersonalizedCoreQuestions(
      state.organizationProfile
    );
    
    state.currentStage = 'core_assessment';
    state.conversationHistory.push({
      type: 'system',
      content: `感谢您提供的信息！基于您的组织背景，我为您准备了个性化的评估问题。`,
      questions: coreQuestions
    });
    
    return state;
  }
}
```

### 4.2 自适应调整机制

#### 动态难度调整
```typescript
class AdaptiveDifficultyAdjuster {
  /**
   * 根据回答质量调整问题难度
   */
  adjustQuestionDifficulty(
    responses: QuestionResponse[],
    nextQuestions: GeneratedQuestion[]
  ): GeneratedQuestion[] {
    const competencyLevel = this.assessCompetencyLevel(responses);
    
    return nextQuestions.map(question => {
      switch (competencyLevel) {
        case 'high':
          return this.enhanceQuestionComplexity(question);
        case 'low':
          return this.simplifyQuestion(question);
        default:
          return question;
      }
    });
  }
  
  /**
   * 评估能力水平
   */
  private assessCompetencyLevel(responses: QuestionResponse[]): 'high' | 'medium' | 'low' {
    const avgScore = responses.reduce((sum, r) => sum + r.score, 0) / responses.length;
    const consistency = this.calculateConsistency(responses);
    
    if (avgScore >= 4.0 && consistency > 0.8) return 'high';
    if (avgScore <= 2.0 || consistency < 0.4) return 'low';
    return 'medium';
  }
  
  /**
   * 增强问题复杂度
   */
  private enhanceQuestionComplexity(question: GeneratedQuestion): GeneratedQuestion {
    // 添加多维度考虑、权衡取舍、战略思考等元素
    const enhancedText = question.questionText.replace(
      /您的组织/g,
      '在复杂多变的商业环境中，您的组织'
    );
    
    return {
      ...question,
      questionText: enhancedText,
      complexity: 'high'
    };
  }
}
```

### 4.3 个性化表述系统

#### 行业语言适配
```typescript
class IndustryLanguageAdapter {
  private industryTerms: Map<string, Record<string, string>> = new Map();
  
  constructor() {
    this.initializeIndustryTerms();
  }
  
  /**
   * 将通用概念转换为行业特定表述
   */
  adaptToIndustryLanguage(
    questionText: string,
    industry: string
  ): string {
    const terms = this.industryTerms.get(industry) || {};
    let adaptedText = questionText;
    
    Object.entries(terms).forEach(([generic, specific]) => {
      adaptedText = adaptedText.replace(
        new RegExp(generic, 'gi'),
        specific
      );
    });
    
    return adaptedText;
  }
  
  /**
   * 初始化行业术语映射
   */
  private initializeIndustryTerms(): void {
    this.industryTerms.set('technology', {
      '客户': '用户',
      '产品': '产品/服务',
      '流程': '开发流程',
      '团队': '开发团队',
      '创新': '技术创新',
      '质量': '代码质量',
      '效率': '开发效率'
    });
    
    this.industryTerms.set('manufacturing', {
      '客户': '客户/供应商',
      '产品': '产品/零部件',
      '流程': '生产流程',
      '团队': '生产团队',
      '创新': '工艺创新',
      '质量': '产品质量',
      '效率': '生产效率'
    });
    
    this.industryTerms.set('finance', {
      '客户': '客户/投资者',
      '产品': '金融产品',
      '流程': '业务流程',
      '团队': '业务团队',
      '创新': '金融创新',
      '质量': '服务质量',
      '效率': '运营效率'
    });
  }
}
```

## 5. 混合架构实现

### 5.1 问卷混合生成器

```typescript
class HybridQuestionnaireGenerator {
  constructor(
    private coreQuestionBank: CoreQuestionBank,
    private contextualGenerator: ContextualQuestionGenerator,
    private explorationGenerator: AdaptiveExplorationGenerator,
    private languageAdapter: IndustryLanguageAdapter
  ) {}
  
  /**
   * 生成混合问卷
   */
  async generateHybridQuestionnaire(
    profile: OrganizationProfile,
    previousResponses?: QuestionResponse[]
  ): Promise<HybridQuestionnaire> {
    const questionnaire: HybridQuestionnaire = {
      id: this.generateQuestionnaireId(),
      profile,
      layers: {
        core: [],
        contextual: [],
        exploration: []
      },
      metadata: {
        generatedAt: new Date(),
        totalQuestions: 60,
        distribution: { preset: 32, intelligent: 28 }
      }
    };

    // 第一层：预设题目（32题）
    questionnaire.layers.preset = await this.loadPresetQuestions();

    // 第二层：智能生成题目（28题）
    questionnaire.layers.intelligent = await this.generateIntelligentQuestions(profile);

    // 混合问卷融合
    if (previousResponses && previousResponses.length > 0) {
      questionnaire.layers.exploration = await this.generateExplorationLayer(
        previousResponses,
        profile
      );
    }
    
    // 应用行业语言适配
    questionnaire = this.applyLanguageAdaptation(questionnaire, profile.industry);
    
    // 随机化问题顺序（保持维度平衡）
    questionnaire = this.shuffleQuestions(questionnaire);
    
    return questionnaire;
  }
  
  /**
   * 生成核心层问题
   */
  private async generateCoreLayer(profile: OrganizationProfile): Promise<Question[]> {
    const dimensions: OCTIDimension[] = ['S/F', 'I/T', 'M/V', 'A/D'];
    const questionsPerDimension = 10;
    
    const coreQuestions: Question[] = [];
    
    for (const dimension of dimensions) {
      const dimensionQuestions = this.coreQuestionBank.getCoreQuestions(
        dimension,
        questionsPerDimension
      );
      coreQuestions.push(...dimensionQuestions);
    }
    
    return coreQuestions;
  }
  
  /**
   * 生成情境层问题
   */
  private async generateContextualLayer(profile: OrganizationProfile): Promise<Question[]> {
    const dimensions: OCTIDimension[] = ['S/F', 'I/T', 'M/V', 'A/D'];
    const questionsPerDimension = Math.floor(15 / dimensions.length);
    
    const contextualQuestions: Question[] = [];
    
    for (const dimension of dimensions) {
      const dimensionQuestions = await this.contextualGenerator.generateContextualQuestions(
        dimension,
        profile,
        questionsPerDimension
      );
      contextualQuestions.push(...dimensionQuestions);
    }
    
    return contextualQuestions;
  }
}
```

### 5.2 质量保证机制

```typescript
class QuestionQualityAssurance {
  /**
   * 验证生成的问题质量
   */
  async validateQuestionQuality(
    questions: GeneratedQuestion[],
    profile: OrganizationProfile
  ): Promise<ValidationResult> {
    const validationResults: ValidationResult[] = [];
    
    for (const question of questions) {
      const result = await this.validateSingleQuestion(question, profile);
      validationResults.push(result);
    }
    
    return this.aggregateValidationResults(validationResults);
  }
  
  /**
   * 验证单个问题
   */
  private async validateSingleQuestion(
    question: GeneratedQuestion,
    profile: OrganizationProfile
  ): Promise<ValidationResult> {
    const checks = [
      this.checkRelevance(question, profile),
      this.checkClarity(question),
      this.checkBias(question),
      this.checkComplexity(question, profile),
      this.checkUniqueness(question)
    ];
    
    const results = await Promise.all(checks);
    
    return {
      questionId: question.id,
      passed: results.every(r => r.passed),
      issues: results.filter(r => !r.passed).map(r => r.issue),
      score: results.reduce((sum, r) => sum + r.score, 0) / results.length
    };
  }
}
```

## 6. 实施计划

### 阶段一：基础架构（2周）
1. 实现组织画像系统
2. 建立核心题库
3. 开发动态提示词构建器

### 阶段二：智能生成（3周）
1. 实现情境化问题生成
2. 开发自适应探索机制
3. 建立质量保证系统

### 阶段三：智能化功能（2周）
1. 实现多轮对话系统
2. 开发自适应调整机制
3. 建立个性化表述系统

### 阶段四：集成优化（1周）
1. 整合混合生成器
2. 性能优化
3. 测试和调试

## 7. 成功指标

1. **个性化程度**：同一维度的问题在不同组织间的差异度 > 60%
2. **相关性评分**：专家评估的问题相关性 > 4.0/5.0
3. **用户满意度**：问卷完成后的满意度评分 > 4.2/5.0
4. **预测准确性**：评估结果与实际组织表现的相关性 > 0.75
5. **生成效率**：完整问卷生成时间 < 30秒

通过以上设计，我们可以实现真正智能化的问卷生成系统，确保每个组织都能获得针对性的、有价值的评估体验。