/**
 * OCTI智能评估系统 - 问卷设计师智能体
 * 
 * 负责基于组织画像生成个性化问卷
 * 实现32道预设题目 + 28道AI生成题目的混合模式
 */

import { configEngine } from '../config-engine';
import { chatWithMinimax, LLMMessage } from '../llm-client';
import { prisma } from '@/lib/prisma';
import { generateUUID } from '@/lib/utils';
import { QUESTIONNAIRE_CONFIG } from '@/constants';
import type { 
  NonprofitProfile, 
  Question, 
  QuestionType, 
  QuestionSource,
  Questionnaire 
} from '@prisma/client';

// ============================================================================
// 类型定义
// ============================================================================

export interface QuestionGenerationContext {
  organizationProfile: NonprofitProfile;
  dialogueInsights?: any;
  targetDimensions: string[];
  existingQuestions?: Question[];
}

export interface GeneratedQuestion {
  type: QuestionType;
  source: QuestionSource;
  category: string;
  subCategory?: string;
  title: string;
  description?: string;
  options?: any;
  required: boolean;
  order: number;
  metadata?: any;
}

export interface QuestionnaireGenerationResult {
  questionnaire: Questionnaire;
  questions: Question[];
  generationMetadata: {
    presetQuestionsUsed: number;
    aiQuestionsGenerated: number;
    totalQuestions: number;
    generationTime: number;
    qualityScore: number;
  };
}

// ============================================================================
// 问卷设计师智能体类
// ============================================================================

export class QuestionDesignerAgent {
  private config: any;

  constructor() {
    this.loadConfig();
  }

  /**
   * 加载智能体配置
   */
  private async loadConfig(): Promise<void> {
    try {
      this.config = await configEngine.getAgentConfig('QUESTION_DESIGNER');
    } catch (error) {
      console.error('Failed to load question designer config:', error);
      // 使用默认配置
      this.config = {
        model: 'minimax',
        temperature: 0.7,
        maxTokens: 4000,
        systemPrompt: '你是一个专业的问卷设计师，专门为公益机构设计评估问卷。',
        userPromptTemplate: '请基于以下组织信息生成个性化问题：{context}',
      };
    }
  }

  /**
   * 生成混合问卷
   * 
   * @param context - 问卷生成上下文
   * @returns 问卷生成结果
   */
  async generateHybridQuestionnaire(context: QuestionGenerationContext): Promise<QuestionnaireGenerationResult> {
    const startTime = Date.now();

    try {
      // 1. 获取预设题目
      const presetQuestions = await this.getPresetQuestions(context.targetDimensions);
      
      // 2. 生成AI题目
      const aiQuestions = await this.generateAIQuestions(context, QUESTIONNAIRE_CONFIG.AI_GENERATED_QUESTIONS);
      
      // 3. 融合问卷
      const fusedQuestions = await this.fuseQuestionnaire(presetQuestions, aiQuestions);
      
      // 4. 创建问卷记录
      const questionnaire = await this.createQuestionnaire(context, fusedQuestions);
      
      // 5. 创建问题记录
      const questions = await this.createQuestions(questionnaire.id, fusedQuestions);

      const generationTime = Date.now() - startTime;
      const qualityScore = await this.evaluateQuestionnaireQuality(fusedQuestions);

      return {
        questionnaire,
        questions,
        generationMetadata: {
          presetQuestionsUsed: presetQuestions.length,
          aiQuestionsGenerated: aiQuestions.length,
          totalQuestions: fusedQuestions.length,
          generationTime,
          qualityScore,
        },
      };
    } catch (error) {
      console.error('Failed to generate hybrid questionnaire:', error);
      throw new Error('问卷生成失败');
    }
  }

  /**
   * 获取预设题目
   * 
   * @param dimensions - 目标维度
   * @returns 预设题目列表
   */
  private async getPresetQuestions(dimensions: string[]): Promise<GeneratedQuestion[]> {
    try {
      const presetQuestions = await prisma.presetQuestion.findMany({
        where: {
          dimension: { in: dimensions },
          isActive: true,
        },
        orderBy: [
          { dimension: 'asc' },
          { displayOrder: 'asc' },
        ],
        take: QUESTIONNAIRE_CONFIG.PRESET_QUESTIONS,
      });

      return presetQuestions.map((q, index) => ({
        type: q.questionType as QuestionType,
        source: 'PRESET' as QuestionSource,
        category: q.dimension,
        subCategory: q.subDimension,
        title: (q.questionContent as any).title,
        description: (q.questionContent as any).description,
        options: (q.questionContent as any).options,
        required: true,
        order: index + 1,
        metadata: {
          presetId: q.id,
          version: q.version,
        },
      }));
    } catch (error) {
      console.error('Failed to get preset questions:', error);
      throw new Error('获取预设题目失败');
    }
  }

  /**
   * 生成AI题目
   * 
   * @param context - 生成上下文
   * @param count - 生成数量
   * @returns AI生成题目列表
   */
  private async generateAIQuestions(context: QuestionGenerationContext, count: number): Promise<GeneratedQuestion[]> {
    try {
      const messages: LLMMessage[] = [
        {
          role: 'system',
          content: this.buildSystemPrompt(),
        },
        {
          role: 'user',
          content: this.buildUserPrompt(context, count),
        },
      ];

      const response = await chatWithMinimax({
        messages,
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
      });

      const generatedQuestions = this.parseAIResponse(response.content);
      return this.validateAndFormatAIQuestions(generatedQuestions, context);
    } catch (error) {
      console.error('Failed to generate AI questions:', error);
      throw new Error('AI题目生成失败');
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(): string {
    return `你是OCTI智能评估系统的专业问卷设计师，专门为公益机构设计个性化评估问卷。

你的任务是基于组织画像信息，生成高质量的评估问题。请遵循以下原则：

1. **针对性强**：问题必须与组织的具体情况、发展阶段和挑战相关
2. **维度平衡**：确保覆盖治理能力、战略能力、运营能力、财务能力等核心维度
3. **难度适中**：问题应该具有区分度，能够有效评估组织能力水平
4. **表达清晰**：使用公益机构熟悉的术语和表达方式
5. **实用导向**：问题应该能够为组织发展提供有价值的洞察

问题类型包括：
- SINGLE_CHOICE: 单选题
- MULTIPLE_CHOICE: 多选题  
- SCALE: 量表题（1-5分）
- TEXT: 文本题
- BOOLEAN: 是非题

请以JSON格式返回问题列表，每个问题包含：type, category, title, description, options（如适用）, required字段。`;
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(context: QuestionGenerationContext, count: number): string {
    const profile = context.organizationProfile;
    
    return `请为以下公益机构生成${count}道个性化评估问题：

**组织基本信息：**
- 组织类型：${profile.organizationType}
- 服务领域：${Array.isArray(profile.serviceArea) ? (profile.serviceArea as string[]).join('、') : profile.serviceArea}
- 组织规模：${profile.organizationScale}
- 发展阶段：${profile.developmentStage}
- 运营模式：${profile.operatingModel}
- 影响定位：${profile.impactPositioning}

**使命愿景：**
${JSON.stringify(profile.missionVision, null, 2)}

**当前挑战：**
${Array.isArray(profile.challenges) ? (profile.challenges as string[]).join('、') : profile.challenges}

**发展目标：**
${Array.isArray(profile.goals) ? (profile.goals as string[]).join('、') : profile.goals}

请生成针对这个组织特点的个性化问题，确保问题能够深入评估其能力现状和发展需求。

返回格式：
\`\`\`json
[
  {
    "type": "SINGLE_CHOICE",
    "category": "governance",
    "title": "问题标题",
    "description": "问题描述（可选）",
    "options": [
      {"text": "选项1", "value": 1},
      {"text": "选项2", "value": 2}
    ],
    "required": true
  }
]
\`\`\``;
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(content: string): any[] {
    try {
      // 提取JSON内容
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      
      // 尝试直接解析
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      throw new Error('AI响应解析失败');
    }
  }

  /**
   * 验证和格式化AI生成的问题
   */
  private validateAndFormatAIQuestions(questions: any[], context: QuestionGenerationContext): GeneratedQuestion[] {
    return questions
      .filter(q => q.title && q.type && q.category)
      .map((q, index) => ({
        type: q.type as QuestionType,
        source: 'AI_GENERATED' as QuestionSource,
        category: q.category,
        subCategory: q.subCategory,
        title: q.title,
        description: q.description,
        options: q.options,
        required: q.required ?? true,
        order: QUESTIONNAIRE_CONFIG.PRESET_QUESTIONS + index + 1,
        metadata: {
          aiGenerated: true,
          generationContext: context.organizationProfile.id,
        },
      }));
  }

  /**
   * 融合问卷（预设题目 + AI生成题目）
   */
  private async fuseQuestionnaire(presetQuestions: GeneratedQuestion[], aiQuestions: GeneratedQuestion[]): Promise<GeneratedQuestion[]> {
    // 简单融合策略：预设题目在前，AI题目在后
    const allQuestions = [...presetQuestions, ...aiQuestions];
    
    // 重新排序
    return allQuestions.map((q, index) => ({
      ...q,
      order: index + 1,
    }));
  }

  /**
   * 创建问卷记录
   */
  private async createQuestionnaire(context: QuestionGenerationContext, questions: GeneratedQuestion[]): Promise<Questionnaire> {
    return await prisma.questionnaire.create({
      data: {
        id: generateUUID(),
        title: `${context.organizationProfile.organization.name} - OCTI能力评估问卷`,
        description: '基于组织画像生成的个性化能力评估问卷',
        type: 'HYBRID',
        status: 'GENERATED',
        totalQuestions: questions.length,
        presetQuestions: questions.filter(q => q.source === 'PRESET').length,
        aiGeneratedQuestions: questions.filter(q => q.source === 'AI_GENERATED').length,
        estimatedDuration: Math.ceil(questions.length * 1.5), // 每题1.5分钟
        organizationId: context.organizationProfile.organizationId,
        nonprofitProfileId: context.organizationProfile.id,
      },
    });
  }

  /**
   * 创建问题记录
   */
  private async createQuestions(questionnaireId: string, questions: GeneratedQuestion[]): Promise<Question[]> {
    const questionData = questions.map(q => ({
      id: generateUUID(),
      questionnaireId,
      type: q.type,
      source: q.source,
      category: q.category,
      subCategory: q.subCategory,
      title: q.title,
      description: q.description,
      options: q.options,
      required: q.required,
      order: q.order,
      metadata: q.metadata,
    }));

    await prisma.question.createMany({
      data: questionData,
    });

    return await prisma.question.findMany({
      where: { questionnaireId },
      orderBy: { order: 'asc' },
    });
  }

  /**
   * 评估问卷质量
   */
  private async evaluateQuestionnaireQuality(questions: GeneratedQuestion[]): Promise<number> {
    // 简单的质量评估算法
    let score = 0;
    
    // 维度覆盖度
    const dimensions = new Set(questions.map(q => q.category));
    score += Math.min(dimensions.size / 4, 1) * 30; // 最多30分
    
    // 问题数量
    score += Math.min(questions.length / 60, 1) * 20; // 最多20分
    
    // 问题类型多样性
    const types = new Set(questions.map(q => q.type));
    score += Math.min(types.size / 3, 1) * 20; // 最多20分
    
    // AI生成题目比例
    const aiQuestions = questions.filter(q => q.source === 'AI_GENERATED').length;
    score += Math.min(aiQuestions / 28, 1) * 30; // 最多30分
    
    return Math.round(score);
  }
}

// ============================================================================
// 导出实例
// ============================================================================

export const questionDesignerAgent = new QuestionDesignerAgent();
