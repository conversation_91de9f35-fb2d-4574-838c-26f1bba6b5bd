/**
 * OCTI智能评估系统 - 报告中心页面
 * 
 * 展示所有评估报告的列表和统计信息
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { MainLayout, PageContainer, CardContainer, EmptyState } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SimpleProgressIndicator, CircularProgress } from '@/components/questionnaire/progress-indicator';

// ============================================================================
// 模拟数据
// ============================================================================

const mockUser = {
  id: '1',
  name: '张三',
  email: '<EMAIL>',
  role: 'USER',
};

const mockReports = [
  {
    id: 'report-1',
    title: '北京环保基金会 - 组织能力评估报告',
    organization: {
      name: '北京环保基金会',
      type: 'FOUNDATION',
    },
    assessment: {
      type: 'PROFESSIONAL',
      completedAt: '2024-01-20T16:30:00Z',
    },
    overallScore: 78.5,
    level: 'GOOD',
    dimensions: [
      { name: 'SF', score: 82 },
      { name: 'IT', score: 75 },
      { name: 'MV', score: 80 },
      { name: 'AD', score: 77 },
    ],
    status: 'COMPLETED',
    downloadCount: 12,
    shareCount: 3,
  },
  {
    id: 'report-2',
    title: '上海教育发展中心 - 能力评估报告',
    organization: {
      name: '上海教育发展中心',
      type: 'NGO',
    },
    assessment: {
      type: 'STANDARD',
      completedAt: '2024-01-18T14:20:00Z',
    },
    overallScore: 72.3,
    level: 'AVERAGE',
    dimensions: [
      { name: 'SF', score: 75 },
      { name: 'IT', score: 68 },
      { name: 'MV', score: 74 },
      { name: 'AD', score: 72 },
    ],
    status: 'COMPLETED',
    downloadCount: 8,
    shareCount: 1,
  },
  {
    id: 'report-3',
    title: '深圳科技创新协会 - 初步评估报告',
    organization: {
      name: '深圳科技创新协会',
      type: 'SOCIAL_ENTERPRISE',
    },
    assessment: {
      type: 'STANDARD',
      completedAt: '2024-01-15T11:45:00Z',
    },
    overallScore: 85.2,
    level: 'EXCELLENT',
    dimensions: [
      { name: 'SF', score: 88 },
      { name: 'IT', score: 82 },
      { name: 'MV', score: 86 },
      { name: 'AD', score: 85 },
    ],
    status: 'COMPLETED',
    downloadCount: 15,
    shareCount: 5,
  },
];

const mockStatistics = {
  totalReports: 24,
  averageScore: 76.8,
  topPerformingDimension: 'SF',
  improvementNeeded: 'IT',
  monthlyTrend: [
    { month: '10月', reports: 3, avgScore: 74.2 },
    { month: '11月', reports: 5, avgScore: 76.1 },
    { month: '12月', reports: 8, avgScore: 77.5 },
    { month: '1月', reports: 8, avgScore: 78.3 },
  ],
  dimensionAverages: [
    { dimension: 'SF', average: 79.2, trend: '****' },
    { dimension: 'IT', average: 71.8, trend: '****' },
    { dimension: 'MV', average: 77.6, trend: '****' },
    { dimension: 'AD', average: 75.4, trend: '****' },
  ],
};

// ============================================================================
// 报告中心页面组件
// ============================================================================

export default function ReportsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [levelFilter, setLevelFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  // 过滤和排序报告
  const filteredReports = mockReports
    .filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           report.organization.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || report.assessment.type === typeFilter;
      const matchesLevel = levelFilter === 'all' || report.level === levelFilter;
      return matchesSearch && matchesType && matchesLevel;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.overallScore - a.overallScore;
        case 'name':
          return a.organization.name.localeCompare(b.organization.name);
        case 'date':
        default:
          return new Date(b.assessment.completedAt).getTime() - new Date(a.assessment.completedAt).getTime();
      }
    });

  const getLevelBadge = (level: string) => {
    const levelConfig = {
      'EXCELLENT': { label: '优秀', color: 'bg-green-100 text-green-800' },
      'GOOD': { label: '良好', color: 'bg-blue-100 text-blue-800' },
      'AVERAGE': { label: '一般', color: 'bg-yellow-100 text-yellow-800' },
      'NEEDS_IMPROVEMENT': { label: '待改进', color: 'bg-orange-100 text-orange-800' },
      'POOR': { label: '较差', color: 'bg-red-100 text-red-800' },
    };
    const config = levelConfig[level as keyof typeof levelConfig] || { label: level, color: 'bg-gray-100 text-gray-800' };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    return (
      <Badge variant="outline">
        {type === 'PROFESSIONAL' ? '专业版' : '标准版'}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <MainLayout user={mockUser}>
      <PageContainer
        title="报告中心"
        description="查看和管理所有评估报告，分析组织发展趋势"
        breadcrumb={[
          { title: '首页', href: '/' },
          { title: '报告中心' },
        ]}
      >
        {/* 统计概览 */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总报告数</CardTitle>
              <span className="text-2xl">📊</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStatistics.totalReports}</div>
              <p className="text-xs text-muted-foreground">
                本月新增 8 份
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均评分</CardTitle>
              <span className="text-2xl">⭐</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStatistics.averageScore}</div>
              <p className="text-xs text-muted-foreground">
                较上月 **** 分
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最强维度</CardTitle>
              <span className="text-2xl">🎯</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStatistics.topPerformingDimension}</div>
              <p className="text-xs text-muted-foreground">
                战略规划与治理
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待改进维度</CardTitle>
              <span className="text-2xl">📈</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStatistics.improvementNeeded}</div>
              <p className="text-xs text-muted-foreground">
                内部治理与管理
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* 报告列表 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 搜索和筛选 */}
            <CardContainer>
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="搜索报告或组织名称..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Select value={typeFilter} onValueChange={setTypeFilter}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类型</SelectItem>
                        <SelectItem value="STANDARD">标准版</SelectItem>
                        <SelectItem value="PROFESSIONAL">专业版</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select value={levelFilter} onValueChange={setLevelFilter}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="等级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部等级</SelectItem>
                        <SelectItem value="EXCELLENT">优秀</SelectItem>
                        <SelectItem value="GOOD">良好</SelectItem>
                        <SelectItem value="AVERAGE">一般</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="排序" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="date">按日期</SelectItem>
                        <SelectItem value="score">按评分</SelectItem>
                        <SelectItem value="name">按名称</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContainer>

            {/* 报告列表 */}
            {filteredReports.length === 0 ? (
              <EmptyState
                title="暂无报告"
                description="没有找到符合条件的评估报告"
                action={
                  <Link href="/assessments">
                    <Button>创建新评估</Button>
                  </Link>
                }
              />
            ) : (
              <div className="space-y-4">
                {filteredReports.map((report) => (
                  <Card key={report.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <CardTitle className="text-lg">{report.title}</CardTitle>
                          <CardDescription>{report.organization.name}</CardDescription>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          {getLevelBadge(report.level)}
                          {getTypeBadge(report.assessment.type)}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      {/* 评分展示 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <CircularProgress
                            percentage={report.overallScore}
                            size={60}
                            label="总分"
                          />
                          <div className="grid grid-cols-4 gap-2">
                            {report.dimensions.map((dim, index) => (
                              <div key={index} className="text-center">
                                <div className="text-sm font-medium">{dim.score}</div>
                                <div className="text-xs text-muted-foreground">{dim.name}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      {/* 报告信息 */}
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>完成时间: {formatDate(report.assessment.completedAt)}</span>
                        <div className="flex items-center space-x-4">
                          <span>下载 {report.downloadCount} 次</span>
                          <span>分享 {report.shareCount} 次</span>
                        </div>
                      </div>
                      
                      {/* 操作按钮 */}
                      <div className="flex space-x-2 pt-2">
                        <Link href={`/reports/${report.id}`} className="flex-1">
                          <Button variant="outline" className="w-full">
                            查看报告
                          </Button>
                        </Link>
                        <Button variant="outline" size="sm">
                          下载
                        </Button>
                        <Button variant="outline" size="sm">
                          分享
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* 侧边栏统计 */}
          <div className="space-y-6">
            {/* 维度平均分 */}
            <CardContainer title="维度表现" description="各维度平均得分">
              <div className="space-y-4">
                {mockStatistics.dimensionAverages.map((dim, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{dim.dimension}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{dim.average}</span>
                        <Badge variant="outline" className="text-xs text-green-600">
                          {dim.trend}
                        </Badge>
                      </div>
                    </div>
                    <SimpleProgressIndicator
                      current={dim.average}
                      total={100}
                      showPercentage={false}
                    />
                  </div>
                ))}
              </div>
            </CardContainer>

            {/* 月度趋势 */}
            <CardContainer title="月度趋势" description="报告数量和平均分变化">
              <div className="space-y-3">
                {mockStatistics.monthlyTrend.map((month, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm font-medium">{month.month}</span>
                    <div className="text-right">
                      <div className="text-sm font-medium">{month.reports} 份</div>
                      <div className="text-xs text-muted-foreground">均分 {month.avgScore}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContainer>

            {/* 快捷操作 */}
            <CardContainer title="快捷操作">
              <div className="space-y-2">
                <Link href="/assessments/create">
                  <Button className="w-full justify-start">
                    ➕ 创建新评估
                  </Button>
                </Link>
                <Button variant="outline" className="w-full justify-start">
                  📊 导出统计报告
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  📈 趋势分析
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  🔍 对比分析
                </Button>
              </div>
            </CardContainer>
          </div>
        </div>
      </PageContainer>
    </MainLayout>
  );
}
