# OCTI智能评估系统 - 数据库Schema设计 (智能体配置架构 v4.0)

## 1. 设计概述
本数据库设计基于**智能体配置化架构**，支持OCTI智能评估系统的核心业务流程，包括用户管理、**公益机构组织画像管理**、**多轮对话机制**、**混合问卷生成**（32道预设+28道智能生成）、**配置驱动的AI智能体管理**、**双模型协作分析**、多版本报告以及**专业版的多源数据融合功能**。

- **技术选型**: PostgreSQL (通过Prisma ORM进行管理)
- **核心原则**: 配置驱动、智能体模块化、关系清晰、易于扩展、关键数据加密
- **架构特色**: 智能体配置化、混合问卷模式、双模型分析、版本控制、热更新
- **v4.0新特性**: 预设题目管理、配置版本控制、使用统计监控、增强安全审计

## 2. ER图 (实体关系图)

```mermaid
erDiagram
    User {
        string id PK
        string email UK
        string name
        string passwordHash
        Role role
        datetime createdAt
        datetime updatedAt
    }

    Organization {
        string id PK
        string name
        string industry
        string size
        string userId FK
        datetime createdAt
        datetime updatedAt
    }

    NonprofitProfile {
        string id PK
        string organizationId FK
        string organizationType
        json serviceArea
        string organizationScale
        string developmentStage
        string operatingModel
        string impactPositioning
        string organizationalCulture
        json missionVision
        json governance
        json resourceProfile
        json impactMeasurement
        json challenges
        json goals
        string region
        int foundedYear
        json keyMetrics
        datetime createdAt
        datetime updatedAt
    }

    DialogueSession {
        string id PK
        string organizationId FK
        string nonprofitProfileId FK
        DialogueStatus status
        int currentRound
        int totalRounds
        json conversationHistory
        json extractedInsights
        datetime createdAt
        datetime completedAt
    }

    DialogueMessage {
        string id PK
        string sessionId FK
        MessageType type "SYSTEM/USER/ASSISTANT"
        text content
        json metadata
        int roundNumber
        datetime timestamp
    }

    IntelligentQuestionnaire {
        string id PK
        string organizationId FK
        string nonprofitProfileId FK
        string sessionId FK
        json presetQuestions
        json generatedQuestions
        json questionnaireStructure
        json generationContext
        QuestionnaireStatus status
        int presetCount
        int generatedCount
        datetime createdAt
        datetime updatedAt
    }

    Assessment {
        string id PK
        string organizationId FK
        string nonprofitProfileId FK
        string questionnaireId FK
        string version "标准版/专业版"
        AssessmentStatus status
        json questionnaireData
        json answersData
        json primaryAnalysisResult
        json secondaryAnalysisResult
        json fusedAnalysisResult
        json reportData
        json contextualFactors
        json nonprofitSpecificAnalysis
        json performanceMetrics
        string primaryModel "MINIMAX/DEEPSEEK"
        string secondaryModel "MINIMAX/DEEPSEEK"
        datetime createdAt
        datetime completedAt
    }

    PresetQuestion {
        string id PK
        string dimension "SF/IT/MV/AD"
        string subDimension
        string questionType "CHOICE/SCENARIO/RANKING/SCALE"
        json questionContent
        int displayOrder
        string version
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }

    AIAgentConfig {
        string id PK
        string agentType "QUESTION_DESIGNER/ORGANIZATION_TUTOR"
        string configName
        json promptTemplate
        json parameters
        string version
        json configSchema
        boolean isActive
        string parentConfigId FK
        datetime createdAt
        datetime updatedAt
    }

    ConfigVersion {
        string id PK
        string configId FK
        string version
        json changeLog
        string status "DRAFT/ACTIVE/DEPRECATED"
        string createdBy
        datetime createdAt
        datetime activatedAt
    }

    ConfigUsageStats {
        string id PK
        string configId FK
        string agentType
        int usageCount
        json performanceMetrics
        datetime date
        datetime createdAt
    }

    ExternalDataSource {
        string id PK
        string assessmentId FK
        DataSourceType type "FILE/URL/IMPACT_DATA"
        string source "文件路径/URL"
        ProcessingStatus status "PENDING/PROCESSING/COMPLETED/FAILED"
        json metadata
        datetime createdAt
    }

    ProcessedData {
        string id PK
        string dataSourceId FK
        text extractedContent
        json structuredData
        datetime processedAt
    }

    ApiKey {
        string id PK
        string provider UK
        string keyEncrypted
        string keyIv
        string keyTag
        datetime createdAt
        datetime updatedAt
    }

    SecurityEvent {
        string id PK
        SecurityEventType type
        string userId
        string ip
        string userAgent
        json details
        datetime timestamp
    }

    User ||--o{ Organization : "拥有"
    Organization ||--o{ NonprofitProfile : "具有"
    Organization ||--o{ DialogueSession : "进行"
    NonprofitProfile ||--o{ DialogueSession : "基于"
    DialogueSession ||--o{ DialogueMessage : "包含"
    NonprofitProfile ||--o{ IntelligentQuestionnaire : "生成"
    DialogueSession ||--o{ IntelligentQuestionnaire : "产出"
    Organization ||--o{ Assessment : "进行"
    NonprofitProfile ||--o{ Assessment : "基于"
    IntelligentQuestionnaire ||--o{ Assessment : "使用"
    Assessment ||--o{ ExternalDataSource : "包含"
    ExternalDataSource ||--o{ ProcessedData : "产出"
    AIAgentConfig ||--o{ ConfigVersion : "版本管理"
    AIAgentConfig ||--o{ ConfigUsageStats : "使用统计"
    AIAgentConfig ||--o{ AIAgentConfig : "配置继承"
    PresetQuestion ||--o{ IntelligentQuestionnaire : "预设题目"
    ConfigVersion ||--o{ Assessment : "版本追踪"
```

## 3. Prisma Schema (`schema.prisma`)

```prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// --- 用户与组织模型 ---

model User {
  id             String         @id @default(uuid())
  email          String         @unique
  name           String?
  passwordHash   String
  role           Role           @default(USER)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizations  Organization[]
  securityEvents SecurityEvent[]
}

model Organization {
  id                      String                    @id @default(uuid())
  name                    String
  industry                String?
  size                    String?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  user                    User                      @relation(fields: [userId], references: [id])
  userId                  String
  assessments             Assessment[]
  nonprofitProfile        NonprofitProfile?
  dialogueSessions        DialogueSession[]
  intelligentQuestionnaires IntelligentQuestionnaire[]
}

enum Role {
  USER
  ADMIN
}

// --- 【新增】公益机构组织画像模型 ---

model NonprofitProfile {
  id                      String                    @id @default(uuid())
  organizationType        String // '基金会' | '公益组织' | '社会团体' | '民办非企业' | '国际NGO' | '政府机构'
  serviceArea             Json // string[] 服务领域
  organizationScale       String // '微型' | '小型' | '中型' | '大型' | '超大型'
  developmentStage        String // '初创期' | '成长期' | '成熟期' | '转型期' | '扩张期'
  operatingModel          String // '直接服务' | '资助型' | '倡导型' | '研究型' | '平台型' | '混合型'
  impactPositioning       String // '本地影响' | '区域影响' | '全国影响' | '国际影响'
  organizationalCulture    String // '使命驱动' | '创新导向' | '协作共享' | '专业严谨' | '草根活力'
  missionVision           Json // { mission, vision, values, theory }
  governance              Json // { boardStructure, decisionMaking, transparency, accountability }
  resourceProfile         Json // { fundingSources, volunteerBase, partnerships, capacity }
  impactMeasurement       Json // { hasTheory, measurementTools, reportingFrequency, stakeholderFeedback }
  challenges              Json // string[] 当前面临的主要挑战
  goals                   Json // string[] 组织目标
  region                  String? // 服务地区
  foundedYear             Int? // 成立年份
  keyMetrics              Json? // 关键指标
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  organization            Organization              @relation(fields: [organizationId], references: [id])
  organizationId          String                    @unique
  dialogueSessions        DialogueSession[]
  intelligentQuestionnaires IntelligentQuestionnaire[]
  assessments             Assessment[]
}

// --- 【新增】多轮对话模型 ---

model DialogueSession {
  id                    String                    @id @default(uuid())
  status                DialogueStatus            @default(ACTIVE)
  currentRound          Int                       @default(1)
  totalRounds           Int                       @default(5)
  conversationHistory   Json // 完整对话历史
  extractedInsights     Json? // 从对话中提取的组织洞察
  createdAt             DateTime                  @default(now())
  completedAt           DateTime?
  organization          Organization              @relation(fields: [organizationId], references: [id])
  organizationId        String
  nonprofitProfile      NonprofitProfile?         @relation(fields: [nonprofitProfileId], references: [id])
  nonprofitProfileId    String?
  messages              DialogueMessage[]
  intelligentQuestionnaires IntelligentQuestionnaire[]
}

model DialogueMessage {
  id           String          @id @default(uuid())
  type         MessageType // SYSTEM, USER, ASSISTANT
  content      String
  metadata     Json? // 消息元数据（如AI配置、处理时间等）
  roundNumber  Int // 对话轮次
  timestamp    DateTime        @default(now())
  session      DialogueSession @relation(fields: [sessionId], references: [id])
  sessionId    String
}

enum DialogueStatus {
  ACTIVE
  COMPLETED
  PAUSED
  FAILED
}

enum MessageType {
  SYSTEM
  USER
  ASSISTANT
}

// --- 【新增】智能问卷生成模型 ---

model IntelligentQuestionnaire {
  id                    String                    @id @default(uuid())
  presetQuestions       Json // 32道预设题目
  generatedQuestions    Json // 28道AI生成题目
  questionnaireStructure Json // 完整问卷结构（60道题）
  generationContext     Json // 生成上下文（组织画像、对话洞察等）
  status                QuestionnaireStatus       @default(GENERATED)
  presetCount           Int                       @default(32) // 预设题目数量
  generatedCount        Int                       @default(28) // 生成题目数量
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  organization          Organization              @relation(fields: [organizationId], references: [id])
  organizationId        String
  nonprofitProfile      NonprofitProfile?         @relation(fields: [nonprofitProfileId], references: [id])
  nonprofitProfileId    String?
  dialogueSession       DialogueSession?          @relation(fields: [sessionId], references: [id])
  sessionId             String?
  assessments           Assessment[]
}

enum QuestionnaireStatus {
  GENERATED
  REVIEWED
  APPROVED
  DEPRECATED
}

// --- 评估核心模型 ---

model Assessment {
  id                        String                    @id @default(uuid())
  version                   String // "standard" or "professional"
  status                    AssessmentStatus          @default(PENDING)
  questionnaireData         Json? // LLM生成的问卷结构
  answersData               Json? // 用户提交的答案
  primaryAnalysisResult     Json? // 主模型分析结果
  secondaryAnalysisResult   Json? // 副模型分析结果
  fusedAnalysisResult       Json? // 融合分析结果
  reportData                Json? // LLM生成的分析报告
  contextualFactors         Json? // 上下文因素（公益机构专用）
  nonprofitSpecificAnalysis Json? // 公益机构专用分析
  performanceMetrics        Json? // 双模型性能指标
  primaryModel              String? // 主模型类型
  secondaryModel            String? // 副模型类型
  createdAt                 DateTime                  @default(now())
  completedAt               DateTime?
  organization              Organization              @relation(fields: [organizationId], references: [id])
  organizationId            String
  nonprofitProfile          NonprofitProfile?         @relation(fields: [nonprofitProfileId], references: [id])
  nonprofitProfileId        String?
  intelligentQuestionnaire  IntelligentQuestionnaire? @relation(fields: [questionnaireId], references: [id])
  questionnaireId           String?
  externalDataSources       ExternalDataSource[]
}

enum AssessmentStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

enum AssessmentType {
  STANDARD
  PROFESSIONAL
  CUSTOM
}

enum AgentType {
  QUESTION_DESIGNER
  ORGANIZATION_TUTOR
}

enum OrganizationSize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
}

enum DevelopmentStage {
  SEED
  EARLY
  GROWTH
  MATURE
  EXPANSION
}

enum ConfigStatus {
  DRAFT
  ACTIVE
  DEPRECATED
  ARCHIVED
}

// --- 【新增】预设题目模型 ---

model PresetQuestion {
  id              String   @id @default(uuid())
  dimension       String // "SF", "IT", "MV", "AD"
  subDimension    String // 子维度
  questionType    String // "CHOICE", "SCENARIO", "RANKING", "SCALE"
  questionContent Json   // 题目内容（支持多语言）
  displayOrder    Int    // 显示顺序
  version         String // 题目版本
  isActive        Boolean @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([dimension, subDimension])
  @@index([version, isActive])
}

// --- 【新增】AI智能体配置模型（增强版）---

model AIAgentConfig {
  id             String   @id @default(uuid())
  agentType      String // "QUESTION_DESIGNER" | "ORGANIZATION_TUTOR" | "NONPROFIT_DIALOGUE"
  configName     String // 配置名称
  promptTemplate Json // 提示词模板
  parameters     Json // 配置参数
  version        String // 配置版本
  configSchema   Json? // 配置Schema验证
  isActive       Boolean  @default(true)
  parentConfigId String? // 父配置ID（支持继承）
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 关联关系
  versions       ConfigVersion[]
  usageStats     ConfigUsageStats[]
  parentConfig   AIAgentConfig? @relation("ConfigInheritance", fields: [parentConfigId], references: [id])
  childConfigs   AIAgentConfig[] @relation("ConfigInheritance")

  @@unique([agentType, configName, version])
  @@index([agentType, isActive])
  @@index([version, isActive])
}

// --- 【新增】配置版本管理模型 ---

model ConfigVersion {
  id           String   @id @default(uuid())
  configId     String
  version      String
  changeLog    Json // 变更日志
  status       String // "DRAFT", "ACTIVE", "DEPRECATED"
  createdBy    String
  createdAt    DateTime @default(now())
  activatedAt  DateTime?

  // 关联关系
  config       AIAgentConfig @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@unique([configId, version])
  @@index([status, activatedAt])
}

// --- 【新增】配置使用统计模型 ---

model ConfigUsageStats {
  id                 String   @id @default(uuid())
  configId           String
  agentType          String
  usageCount         Int      @default(0)
  performanceMetrics Json // 性能指标
  date               DateTime @db.Date
  createdAt          DateTime @default(now())

  // 关联关系
  config             AIAgentConfig @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@unique([configId, date])
  @@index([agentType, date])
}

// --- 【新增】外部数据融合模型 ---

model ExternalDataSource {
  id             String           @id @default(uuid())
  type           DataSourceType // "FILE" or "URL" or "IMPACT_DATA"
  source         String // 文件在COS中的路径或网页URL
  status         ProcessingStatus @default(PENDING)
  errorMessage   String? // 处理失败时的错误信息
  metadata       Json? // 数据源元数据（文件类型、大小、来源等）
  createdAt      DateTime         @default(now())
  assessment     Assessment       @relation(fields: [assessmentId], references: [id])
  assessmentId   String
  processedData  ProcessedData?
}

enum DataSourceType {
  FILE
  URL
  IMPACT_DATA
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

model ProcessedData {
  id               String             @id @default(uuid())
  extractedContent String? // 从文件或网页中提取的原始文本
  structuredData   Json? // LLM初步处理后的结构化数据
  processedAt      DateTime           @default(now())
  dataSource       ExternalDataSource @relation(fields: [dataSourceId], references: [id])
  dataSourceId     String             @unique
}

// --- 系统与安全模型 ---

model ApiKey {
  id           String   @id @default(uuid())
  provider     String   @unique // e.g., "minimax", "deepseek"
  keyEncrypted String
  keyIv        String
  keyTag       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model SecurityEvent {
  id        String            @id @default(uuid())
  type      SecurityEventType
  details   Json?
  ip        String?
  userAgent String?
  timestamp DateTime          @default(now())
  user      User?             @relation(fields: [userId], references: [id])
  userId    String?
}

enum SecurityEventType {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  PASSWORD_CHANGE
  ACCOUNT_LOCKOUT
  PERMISSION_CHANGE
  CONFIG_CHANGE
  API_KEY_USAGE
  SUSPICIOUS_ACTIVITY
}
```

## 4. 索引优化策略

### 4.1 配置管理相关索引
```sql
-- AIAgentConfig 索引
CREATE INDEX idx_agent_config_type_active ON "AIAgentConfig"("agentType", "isActive");
CREATE INDEX idx_agent_config_version ON "AIAgentConfig"("version", "isActive");
CREATE INDEX idx_agent_config_parent ON "AIAgentConfig"("parentConfigId");

-- ConfigVersion 索引
CREATE INDEX idx_config_version_status ON "ConfigVersion"("status", "activatedAt");
CREATE INDEX idx_config_version_created ON "ConfigVersion"("createdAt" DESC);

-- ConfigUsageStats 索引
CREATE INDEX idx_usage_stats_date ON "ConfigUsageStats"("agentType", "date");
CREATE INDEX idx_usage_stats_config ON "ConfigUsageStats"("configId", "date" DESC);
```

### 4.2 问卷生成相关索引
```sql
-- PresetQuestion 索引
CREATE INDEX idx_preset_dimension ON "PresetQuestion"("dimension", "subDimension");
CREATE INDEX idx_preset_version ON "PresetQuestion"("version", "isActive");
CREATE INDEX idx_preset_order ON "PresetQuestion"("displayOrder");

-- IntelligentQuestionnaire 索引
CREATE INDEX idx_questionnaire_profile ON "IntelligentQuestionnaire"("nonprofitProfileId");
CREATE INDEX idx_questionnaire_status ON "IntelligentQuestionnaire"("status", "createdAt");
```

### 4.3 分析结果相关索引
```sql
-- Assessment 索引
CREATE INDEX idx_assessment_models ON "Assessment"("primaryModel", "secondaryModel");
CREATE INDEX idx_assessment_completion ON "Assessment"("completedAt" DESC) WHERE "status" = 'COMPLETED';
CREATE INDEX idx_assessment_org_type ON "Assessment"("organizationId", "version");
```

## 5. 技术实施建议

### 5.1 配置管理最佳实践
- **版本控制**: 使用语义化版本号(Semantic Versioning)
- **配置继承**: 支持配置模板继承，减少重复配置
- **热更新**: 实现配置的热更新机制，无需重启服务
- **A/B测试**: 支持配置的灰度发布和A/B测试
- **回滚机制**: 提供快速回滚到上一个稳定版本的能力

### 5.2 数据库优化
- **连接池**: 配置合适的数据库连接池大小
- **分区策略**: 对大表按时间或组织ID分区
- **缓存策略**: 
  - Redis缓存热点配置数据
  - 缓存预设题目和常用配置
  - 实施多级缓存策略
- **读写分离**: 配置主从复制，读写分离

### 5.3 安全增强
- **配置加密**: 敏感配置参数使用AES-256加密存储
- **访问控制**: 实施细粒度的配置访问权限控制
- **审计日志**: 记录所有配置变更和使用情况
- **数据脱敏**: 在非生产环境中对敏感数据进行脱敏

### 5.4 监控与告警
- **配置使用监控**: 监控各配置的使用频率和性能
- **异常检测**: 检测配置使用异常和性能下降
- **容量规划**: 基于使用统计进行容量规划
- **SLA监控**: 监控双模型分析的响应时间和成功率

## 6. 数据迁移策略

### 6.1 v2.0 到 v4.0 迁移计划

#### 阶段1: 新模型创建
```sql
-- 创建新表
CREATE TABLE preset_questions (...); 
CREATE TABLE config_versions (...);
CREATE TABLE config_usage_stats (...);

-- 添加新字段到现有表
ALTER TABLE assessments ADD COLUMN primary_analysis_result JSONB;
ALTER TABLE assessments ADD COLUMN secondary_analysis_result JSONB;
ALTER TABLE assessments ADD COLUMN fused_analysis_result JSONB;
ALTER TABLE assessments ADD COLUMN performance_metrics JSONB;
ALTER TABLE assessments ADD COLUMN primary_model VARCHAR(50);
ALTER TABLE assessments ADD COLUMN secondary_model VARCHAR(50);
```

#### 阶段2: 数据迁移
```sql
-- 迁移现有Assessment数据
UPDATE assessments 
SET primary_analysis_result = analysis_result,
    primary_model = 'MINIMAX'
WHERE analysis_result IS NOT NULL;

-- 初始化预设题目数据
INSERT INTO preset_questions (dimension, sub_dimension, question_content, ...)
VALUES 
  ('SF', 'S1', '{"zh": "您的组织主要关注哪个社会问题？", "en": "What social issue does your organization primarily focus on?"}', ...),
  -- ... 32道预设题目
```

#### 阶段3: 配置升级
```sql
-- 升级现有AI配置
UPDATE ai_agent_configs 
SET config_schema = '{
  "type": "object",
  "properties": {
    "temperature": {"type": "number", "minimum": 0, "maximum": 1},
    "max_tokens": {"type": "integer", "minimum": 1}
  }
}'
WHERE config_schema IS NULL;
```

### 6.2 向后兼容性保证
- **API兼容**: 保持现有API接口不变
- **数据格式**: 新字段设为可选，避免破坏现有数据
- **渐进启用**: 新功能通过配置开关逐步启用
- **回滚支持**: 保留旧数据结构，支持快速回滚

## 7. 部署与运维

### 7.1 部署策略
- **蓝绿部署**: 使用蓝绿部署策略，确保零停机升级
- **数据库迁移**: 使用Prisma Migrate进行版本化迁移
- **配置管理**: 使用环境变量管理不同环境的配置
- **健康检查**: 实施数据库连接和关键功能的健康检查

### 7.2 监控体系

#### 数据库层监控
- **性能指标**: QPS、TPS、连接数、慢查询
- **资源使用**: CPU、内存、磁盘I/O、网络
- **复制延迟**: 主从复制延迟监控

#### 业务层监控
- **配置使用**: 各智能体配置的使用频率和性能
- **问卷生成**: 混合问卷生成成功率和耗时
- **双模型分析**: 主副模型分析成功率和一致性
- **用户行为**: 评估完成率、用户活跃度

#### 告警策略
- **P0告警**: 数据库连接失败、主要功能不可用
- **P1告警**: 性能严重下降、错误率超过阈值
- **P2告警**: 资源使用率过高、配置使用异常

### 7.3 维护计划
- **备份策略**: 
  - 每小时增量备份
  - 每日全量备份
  - 每周跨区域备份
- **性能优化**: 
  - 每周分析慢查询并优化
  - 每月进行索引使用情况分析
  - 季度性能基准测试
- **容量规划**: 
  - 基于历史数据预测增长趋势
  - 提前3个月进行容量扩展
  - 定期清理过期数据

### 7.4 灾难恢复
- **RTO目标**: 恢复时间目标 < 4小时
- **RPO目标**: 恢复点目标 < 1小时
- **备份验证**: 每月进行备份恢复测试
- **故障演练**: 每季度进行灾难恢复演练

## 8. 模型详解

### `User` & `Organization`
- 标准的用户和组织模型，用于多租户管理。
- `Organization`模型新增了与公益机构画像、对话会话、智能问卷的关联关系。

### `NonprofitProfile` (新增)
- **目的**: 存储公益机构的详细组织画像信息，支持AI智能化评估。
- `organizationType`: 公益机构类型（基金会、公益组织、社会团体等）
- `serviceArea`: 服务领域数组（教育、环保、扶贫、医疗等）
- `operatingModel`: 运营模式（直接服务、资助型、倡导型等）
- `missionVision`: 使命愿景结构化数据
- `governance`: 治理结构信息
- `resourceProfile`: 资源状况分析
- `impactMeasurement`: 影响力测量机制
- 与组织一对一关系，支持基于画像的智能化问卷生成

### `DialogueSession` & `DialogueMessage` (新增)
- **目的**: 支持公益机构组织画像的多轮对话收集机制。
- `DialogueSession`: 管理完整的对话会话，包含轮次控制、状态管理、洞察提取
- `DialogueMessage`: 存储每条对话消息，支持系统、用户、AI助手三种类型
- `conversationHistory`: 完整对话历史的JSON存储
- `extractedInsights`: 从对话中AI提取的组织洞察
- 支持暂停、恢复、失败重试等状态管理

### `IntelligentQuestionnaire` (新增)
- **目的**: 存储基于组织画像和对话洞察生成的混合智能化问卷（32道预设+28道智能生成）。
- **混合问卷结构**:
  - `presetQuestions`: 32道基于OCTI四维八极理论的标准预设题目
  - `generatedQuestions`: 28道基于组织画像和对话洞察AI生成的个性化题目
  - `questionnaireStructure`: 完整的60道题问卷结构
  - `presetCount/generatedCount`: 预设和生成题目的数量统计
- `generationContext`: 生成上下文（组织画像、对话洞察等）
- 与组织画像、对话会话、预设题目库关联，实现标准化与个性化的平衡
- 支持问卷版本管理和状态控制（生成→审核→批准→弃用）

### `Assessment` (升级)
- 核心评估模型，存储一次评估的全过程数据，支持双模型协作分析。
- `version`字段区分标准版与专业版。
- **双模型分析字段**:
  - `primaryAnalysisResult`: 主模型（MiniMax）分析结果
  - `secondaryAnalysisResult`: 副模型（DeepSeek）分析结果
  - `fusedAnalysisResult`: 双模型融合后的最终分析结果
  - `performanceMetrics`: 双模型分析的性能指标和一致性评分
  - `primaryModel/secondaryModel`: 记录使用的具体模型类型
- **公益机构专用字段**:
  - `nonprofitProfileId`: 关联公益机构画像
  - `questionnaireId`: 关联智能生成的问卷
  - `contextualFactors`: 上下文因素（公益机构专用）
  - `nonprofitSpecificAnalysis`: 公益机构专用分析结果
- `questionnaireData`, `answersData`, `reportData` 以JSON格式存储，以适应LLM输出的灵活性。

### `PresetQuestion` (新增)
- **目的**: 管理OCTI四维八极理论的32道标准预设题目。
- `dimension`: 四大维度（SF/IT/MV/AD）
- `subDimension`: 八个子维度的具体分类
- `questionType`: 题目类型（选择题/情景题/排序题/量表题）
- `questionContent`: 多语言题目内容，支持国际化
- `displayOrder`: 题目显示顺序，确保逻辑性
- `version`: 题目版本，支持题目库的迭代更新

### `AIAgentConfig` (新增)
- **目的**: 支持AI智能体的配置化管理，实现无代码业务逻辑调整。
- `agentType`: 智能体类型（问卷设计师、组织导师、公益对话等）
- `promptTemplate`: 提示词模板的JSON结构
- `parameters`: 智能体行为参数配置
- `configSchema`: JSON Schema验证配置参数的有效性
- `parentConfigId`: 支持配置继承，减少重复配置
- `version`: 支持配置版本管理和A/B测试
- `isActive`: 支持配置的热切换
- 支持多版本并存，实现渐进式升级和回滚

### `ConfigVersion` (新增)
- **目的**: 管理智能体配置的版本生命周期。
- `configId`: 关联的配置ID
- `version`: 语义化版本号（如1.2.3）
- `changeLog`: 详细的变更日志
- `status`: 版本状态（草稿/激活/弃用）
- `createdBy`: 版本创建者，用于审计
- `activatedAt`: 版本激活时间，支持定时发布

### `ConfigUsageStats` (新增)
- **目的**: 监控和分析智能体配置的使用情况。
- `configId`: 配置ID
- `agentType`: 智能体类型
- `usageCount`: 使用次数统计
- `performanceMetrics`: 性能指标（响应时间、成功率等）
- `date`: 统计日期，支持时间序列分析

### `ExternalDataSource` (升级)
- **目的**: 追踪为某次**专业版**评估添加的每一个外部数据源。
- `type`: 区分用户上传的`FILE`、提供的`URL`或`IMPACT_DATA`（影响力数据）
- `source`: 记录文件路径或URL地址。
- `status`: 追踪该数据源的异步处理状态。
- **新增字段**:
  - `metadata`: 数据源元数据（文件类型、大小、来源等）

### `ProcessedData` (保持)
- **目的**: 存储从外部数据源中异步提取和处理后的数据。
- `extractedContent`: 存储从PDF、DOCX或网页中解析出的纯文本。
- `structuredData`: （可选）可以用于存储LLM对`extractedContent`进行初步信息提取后的结果，方便后续的融合分析。

### `ApiKey`, `SecurityEvent`
- 用于系统安全和审计，存储加密的API密钥和安全相关事件。

## 5. 公益机构专用版数据库设计特点

### 5.1 核心设计理念

#### 公益机构画像驱动
- **NonprofitProfile**模型专门设计，涵盖公益机构的独特属性
- 支持服务领域、运营模式、影响力定位等公益专业维度
- 使命愿景、治理结构、资源状况的结构化存储
- 为AI智能化评估提供丰富的上下文信息

#### 多轮对话机制
- **DialogueSession**和**DialogueMessage**实现智能化组织画像收集
- 支持3-5轮渐进式深入对话，替代传统静态表单
- AI从对话中提取组织洞察，存储在`extractedInsights`字段
- 对话历史完整保存，支持后续分析和优化

#### 智能问卷生成
- **IntelligentQuestionnaire**存储基于画像和对话的个性化问卷
- `generationContext`记录生成上下文，确保问卷的针对性
- 与组织画像、对话会话的关联关系，实现真正的智能化
- 支持问卷版本管理和质量控制

#### AI配置化管理
- **AIAgentConfig**实现智能体的无代码配置管理
- 支持提示词模板、参数配置的版本化管理
- 热切换机制，支持A/B测试和渐进式优化
- 多智能体类型支持（问卷设计师、组织导师、公益对话等）

### 5.2 数据流程设计

#### 评估流程优化
```
用户注册/登录
    ↓
创建组织基础信息
    ↓
启动公益机构画像对话 (DialogueSession)
    ↓
多轮对话收集 (DialogueMessage)
    ↓
 AI提取组织洞察 (extractedInsights)
    ↓
生成公益机构画像 (NonprofitProfile)
    ↓
基于画像生成智能问卷 (IntelligentQuestionnaire)
    ↓
用户完成个性化问卷
    ↓
生成公益专用评估报告 (Assessment)
```

#### 数据关联设计
- **组织 → 画像**: 一对一关系，每个公益机构有唯一画像
- **画像 → 对话**: 一对多关系，支持多次画像完善对话
- **对话 → 问卷**: 一对多关系，一次对话可生成多版本问卷
- **问卷 → 评估**: 一对多关系，同一问卷可用于多次评估
- **评估 → 数据源**: 一对多关系，专业版支持多源数据融合

### 5.3 技术实施建议

#### 数据库优化
```sql
-- 为公益机构画像创建索引
CREATE INDEX idx_nonprofit_profile_org_type ON "NonprofitProfile"("organizationType");
CREATE INDEX idx_nonprofit_profile_service_area ON "NonprofitProfile" USING GIN("serviceArea");
CREATE INDEX idx_nonprofit_profile_stage ON "NonprofitProfile"("developmentStage");

-- 为对话会话创建索引
CREATE INDEX idx_dialogue_session_status ON "DialogueSession"("status");
CREATE INDEX idx_dialogue_session_org ON "DialogueSession"("organizationId");
CREATE INDEX idx_dialogue_message_session ON "DialogueMessage"("sessionId", "roundNumber");

-- 为智能问卷创建索引
CREATE INDEX idx_intelligent_questionnaire_org ON "IntelligentQuestionnaire"("organizationId");
CREATE INDEX idx_intelligent_questionnaire_status ON "IntelligentQuestionnaire"("status");

-- 为AI配置创建索引
CREATE INDEX idx_ai_agent_config_type ON "AIAgentConfig"("agentType", "isActive");
CREATE INDEX idx_ai_agent_config_version ON "AIAgentConfig"("agentType", "version");
```

#### 数据迁移策略
1. **渐进式迁移**: 现有组织数据逐步关联公益机构画像
2. **兼容性保证**: 新字段设为可选，确保现有功能不受影响
3. **数据验证**: 实施严格的数据验证规则，确保公益机构画像数据质量
4. **性能监控**: 监控新增JSON字段的查询性能，必要时优化索引策略

#### 安全性考虑
- **敏感信息加密**: 公益机构的关键信息（如财务数据）需要加密存储
- **访问控制**: 实施基于角色的访问控制，保护公益机构隐私
- **审计日志**: 扩展SecurityEvent模型，记录公益机构数据的访问和修改
- **数据备份**: 定期备份公益机构画像和对话数据，防止数据丢失

#### 扩展性设计
- **JSON字段优化**: 合理使用JSON字段存储灵活数据，避免频繁schema变更
- **分表策略**: 考虑对话消息等高频数据的分表存储
- **缓存策略**: 对AI配置、公益机构画像等热点数据实施缓存
- **读写分离**: 为报告生成等读密集操作配置只读副本

### 5.4 监控和维护

#### 关键指标监控
- 公益机构画像完成率
- 多轮对话平均轮次和完成率
- 智能问卷生成成功率
- AI配置切换频率和效果
- 数据库查询性能指标

#### 数据质量保证
- 定期检查公益机构画像数据的完整性
- 监控对话数据的质量和AI提取洞察的准确性
- 验证智能问卷生成的合理性
- 跟踪AI配置变更对系统性能的影响

## 9. 总结

### 9.1 v4.0架构升级亮点

#### 智能体配置化架构
- **配置驱动**: 通过`AIAgentConfig`、`ConfigVersion`、`ConfigUsageStats`实现完全配置驱动的智能体管理
- **版本控制**: 支持语义化版本控制、热更新、A/B测试和快速回滚
- **继承机制**: 配置模板继承减少重复配置，提高管理效率
- **监控统计**: 实时监控配置使用情况和性能指标

#### 混合问卷模式
- **标准化基础**: 32道基于OCTI四维八极理论的预设题目确保评估的科学性
- **个性化增强**: 28道AI生成题目基于组织画像和对话洞察，提供个性化评估
- **质量保证**: 预设题目经过专业验证，AI生成题目通过多轮优化
- **灵活配置**: 支持不同场景下的题目数量和类型调整

#### 双模型协作分析
- **主副模型**: MiniMax作为主模型，DeepSeek作为副模型，提供互补分析
- **交叉验证**: 双模型结果交叉验证，提高分析准确性
- **融合算法**: 智能融合两个模型的分析结果，生成更可靠的评估报告
- **性能监控**: 实时监控双模型的性能指标和一致性评分

### 9.2 技术创新点

1. **配置即代码**: 将业务逻辑配置化，实现无代码业务调整
2. **智能问卷生成**: 结合标准题目和AI生成，平衡科学性与个性化
3. **多模型协作**: 双模型协作分析，提高评估质量和可信度
4. **实时监控**: 全方位的使用统计和性能监控
5. **渐进式升级**: 支持平滑的版本升级和功能迭代

### 9.3 业务价值

- **提升评估质量**: 通过双模型协作和混合问卷，提供更准确的评估结果
- **降低运维成本**: 配置化管理减少代码变更，降低系统维护成本
- **加速功能迭代**: 热更新和版本控制支持快速功能迭代和A/B测试
- **增强用户体验**: 个性化问卷和智能分析提供更好的用户体验
- **保障系统稳定**: 完善的监控和回滚机制确保系统稳定运行

---

**注**: 本Schema设计基于智能体配置化架构，充分考虑了公益机构的特殊需求，通过混合问卷生成、双模型协作分析、配置驱动管理等创新功能，为公益机构提供更精准、更可靠、更易维护的组织发展评估服务。
```